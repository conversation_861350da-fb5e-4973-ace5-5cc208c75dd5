/*!
 FixedColumns 3.0.1
 ©2010-2014 SpryMedia Ltd - datatables.net/license
*/
(function(q){var r=function(d){var k=function(a,b){var c=this;if(!this instanceof k)alert("FixedColumns warning: FixedColumns must be initialised with the 'new' keyword.");else{"undefined"==typeof b&&(b={});d.fn.dataTable.camelToHungarian&&d.fn.dataTable.camelToHungarian(k.defaults,b);var g=d.fn.dataTable.Api?(new d.fn.dataTable.Api(a)).settings()[0]:a.fnSettings();this.s={dt:g,iTableColumns:g.aoColumns.length,aiOuterWidths:[],aiInnerWidths:[]};this.dom={scroller:null,header:null,body:null,footer:null,
grid:{wrapper:null,dt:null,left:{wrapper:null,head:null,body:null,foot:null},right:{wrapper:null,head:null,body:null,foot:null}},clone:{left:{header:null,body:null,footer:null},right:{header:null,body:null,footer:null}}};g._oFixedColumns=this;g._bInitComplete?this._fnConstruct(b):g.oApi._fnCallbackReg(g,"aoInitComplete",function(){c._fnConstruct(b)},"FixedColumns")}};k.prototype={fnUpdate:function(){this._fnDraw(!0)},fnRedrawLayout:function(){this._fnColCalc();this._fnGridLayout();this.fnUpdate()},
fnRecalculateHeight:function(a){delete a._DTTC_iHeight;a.style.height="auto"},fnSetRowHeight:function(a,b){a.style.height=b+"px"},fnGetPosition:function(a){var b=this.s.dt.oInstance;if(d(a).parents(".DTFC_Cloned").length){if("tr"===a.nodeName.toLowerCase())return a=d(a).index(),b.fnGetPosition(d("tr",this.s.dt.nTBody)[a]);var c=d(a).index(),a=d(a.parentNode).index();return[b.fnGetPosition(d("tr",this.s.dt.nTBody)[a]),c,b.oApi._fnVisibleToColumnIndex(this.s.dt,c)]}return b.fnGetPosition(a)},_fnConstruct:function(a){var b=
this;if("function"!=typeof this.s.dt.oInstance.fnVersionCheck||!0!==this.s.dt.oInstance.fnVersionCheck("1.8.0"))alert("FixedColumns "+k.VERSION+" required DataTables 1.8.0 or later. Please upgrade your DataTables installation");else if(""===this.s.dt.oScroll.sX)this.s.dt.oInstance.oApi._fnLog(this.s.dt,1,"FixedColumns is not needed (no x-scrolling in DataTables enabled), so no action will be taken. Use 'FixedHeader' for column fixing when scrolling is not enabled");else{this.s=d.extend(!0,this.s,
k.defaults,a);a=this.s.dt.oClasses;this.dom.grid.dt=d(this.s.dt.nTable).parents("div."+a.sScrollWrapper)[0];this.dom.scroller=d("div."+a.sScrollBody,this.dom.grid.dt)[0];this._fnColCalc();this._fnGridSetup();var c;d(this.dom.scroller).on("mouseover.DTFC touchstart.DTFC",function(){c="main"}).on("scroll.DTFC",function(){if("main"===c&&(0<b.s.iLeftColumns&&(b.dom.grid.left.liner.scrollTop=b.dom.scroller.scrollTop),0<b.s.iRightColumns))b.dom.grid.right.liner.scrollTop=b.dom.scroller.scrollTop});if(0<
b.s.iLeftColumns)d(b.dom.grid.left.liner).on("mouseover.DTFC touchstart.DTFC",function(){c="left"}).on("scroll.DTFC",function(){"left"===c&&(b.dom.scroller.scrollTop=b.dom.grid.left.liner.scrollTop,0<b.s.iRightColumns&&(b.dom.grid.right.liner.scrollTop=b.dom.grid.left.liner.scrollTop))}).on("wheel.DTFC",function(a){b.dom.scroller.scrollLeft-=-a.originalEvent.deltaX});if(0<b.s.iRightColumns)d(b.dom.grid.right.liner).on("mouseover.DTFC touchstart.DTFC",function(){c="right"}).on("scroll.DTFC",function(){"right"===
c&&(b.dom.scroller.scrollTop=b.dom.grid.right.liner.scrollTop,0<b.s.iLeftColumns&&(b.dom.grid.left.liner.scrollTop=b.dom.grid.right.liner.scrollTop))}).on("wheel.DTFC",function(a){b.dom.scroller.scrollLeft-=-a.originalEvent.deltaX});d(q).on("resize.DTFC",function(){b._fnGridLayout.call(b)});var g=!0,f=d(this.s.dt.nTable);f.on("draw.dt.DTFC",function(){b._fnDraw.call(b,g);g=!1}).on("column-sizing.dt.DTFC",function(){b._fnColCalc();b._fnGridLayout(b)}).on("column-visibility.dt.DTFC",function(){b._fnColCalc();
b._fnGridLayout(b);b._fnDraw(!0)}).on("destroy.dt.DTFC",function(){f.off("column-sizing.dt.DTFC destroy.dt.DTFC draw.dt.DTFC");d(b.dom.scroller).off("scroll.DTFC mouseover.DTFC");d(q).off("resize.DTFC");d(b.dom.grid.left.liner).off("scroll.DTFC wheel.DTFC mouseover.DTFC");d(b.dom.grid.left.wrapper).remove();d(b.dom.grid.right.liner).off("scroll.DTFC wheel.DTFC mouseover.DTFC");d(b.dom.grid.right.wrapper).remove()});this._fnGridLayout();this.s.dt.oInstance.fnDraw(!1)}},_fnColCalc:function(){var a=
this,b=0,c=0;this.s.aiInnerWidths=[];this.s.aiOuterWidths=[];d.each(this.s.dt.aoColumns,function(g,f){var e=d(f.nTh);if(e.filter(":visible").length){var h=e.outerWidth();if(0===a.s.aiOuterWidths.length)var j=d(a.s.dt.nTable).css("border-left-width"),h=h+("string"===typeof j?1:parseInt(j,10));a.s.aiOuterWidths.push(h);a.s.aiInnerWidths.push(e.width());g<a.s.iLeftColumns&&(b+=h);a.s.iTableColumns-a.s.iRightColumns<=g&&(c+=h)}else a.s.aiInnerWidths.push(0),a.s.aiOuterWidths.push(0)});this.s.iLeftWidth=
b;this.s.iRightWidth=c},_fnGridSetup:function(){var a=this._fnDTOverflow(),b;this.dom.body=this.s.dt.nTable;this.dom.header=this.s.dt.nTHead.parentNode;this.dom.header.parentNode.parentNode.style.position="relative";var c=d('<div class="DTFC_ScrollWrapper" style="position:relative; clear:both;"><div class="DTFC_LeftWrapper" style="position:absolute; top:0; left:0;"><div class="DTFC_LeftHeadWrapper" style="position:relative; top:0; left:0; overflow:hidden;"></div><div class="DTFC_LeftBodyWrapper" style="position:relative; top:0; left:0; overflow:hidden;"><div class="DTFC_LeftBodyLiner" style="position:relative; top:0; left:0; overflow-y:scroll;"></div></div><div class="DTFC_LeftFootWrapper" style="position:relative; top:0; left:0; overflow:hidden;"></div></div><div class="DTFC_RightWrapper" style="position:absolute; top:0; left:0;"><div class="DTFC_RightHeadWrapper" style="position:relative; top:0; left:0;"><div class="DTFC_RightHeadBlocker DTFC_Blocker" style="position:absolute; top:0; bottom:0;"></div></div><div class="DTFC_RightBodyWrapper" style="position:relative; top:0; left:0; overflow:hidden;"><div class="DTFC_RightBodyLiner" style="position:relative; top:0; left:0; overflow-y:scroll;"></div></div><div class="DTFC_RightFootWrapper" style="position:relative; top:0; left:0;"><div class="DTFC_RightFootBlocker DTFC_Blocker" style="position:absolute; top:0; bottom:0;"></div></div></div></div>')[0],
g=c.childNodes[0],f=c.childNodes[1];this.dom.grid.dt.parentNode.insertBefore(c,this.dom.grid.dt);c.appendChild(this.dom.grid.dt);this.dom.grid.wrapper=c;0<this.s.iLeftColumns&&(this.dom.grid.left.wrapper=g,this.dom.grid.left.head=g.childNodes[0],this.dom.grid.left.body=g.childNodes[1],this.dom.grid.left.liner=d("div.DTFC_LeftBodyLiner",c)[0],c.appendChild(g));0<this.s.iRightColumns&&(this.dom.grid.right.wrapper=f,this.dom.grid.right.head=f.childNodes[0],this.dom.grid.right.body=f.childNodes[1],this.dom.grid.right.liner=
d("div.DTFC_RightBodyLiner",c)[0],b=d("div.DTFC_RightHeadBlocker",c)[0],b.style.width=a.bar+"px",b.style.right=-a.bar+"px",this.dom.grid.right.headBlock=b,b=d("div.DTFC_RightFootBlocker",c)[0],b.style.width=a.bar+"px",b.style.right=-a.bar+"px",this.dom.grid.right.footBlock=b,c.appendChild(f));if(this.s.dt.nTFoot&&(this.dom.footer=this.s.dt.nTFoot.parentNode,0<this.s.iLeftColumns&&(this.dom.grid.left.foot=g.childNodes[2]),0<this.s.iRightColumns))this.dom.grid.right.foot=f.childNodes[2]},_fnGridLayout:function(){var a=
this.dom.grid,b=d(a.wrapper).width(),c=d(this.s.dt.nTable.parentNode).height(),g=d(this.s.dt.nTable.parentNode.parentNode).height(),f=this._fnDTOverflow(),e=this.s.iLeftWidth,h=this.s.iRightWidth;f.x&&(c-=f.bar);a.wrapper.style.height=g+"px";0<this.s.iLeftColumns&&(a.left.wrapper.style.width=e+"px",a.left.wrapper.style.height="1px",a.left.body.style.height=c+"px",a.left.foot&&(a.left.foot.style.top=(f.x?f.bar:0)+"px"),a.left.liner.style.width=e+f.bar+"px",a.left.liner.style.height=c+"px");0<this.s.iRightColumns&&
(b-=h,f.y&&(b-=f.bar),a.right.wrapper.style.width=h+"px",a.right.wrapper.style.left=b+"px",a.right.wrapper.style.height="1px",a.right.body.style.height=c+"px",a.right.foot&&(a.right.foot.style.top=(f.x?f.bar:0)+"px"),a.right.liner.style.width=h+f.bar+"px",a.right.liner.style.height=c+"px",a.right.headBlock.style.display=f.y?"block":"none",a.right.footBlock.style.display=f.y?"block":"none")},_fnDTOverflow:function(){var a=this.s.dt.nTable,b=a.parentNode,c={x:!1,y:!1,bar:this.s.dt.oScroll.iBarWidth};
a.offsetWidth>b.clientWidth&&(c.x=!0);a.offsetHeight>b.clientHeight&&(c.y=!0);return c},_fnDraw:function(a){this._fnGridLayout();this._fnCloneLeft(a);this._fnCloneRight(a);null!==this.s.fnDrawCallback&&this.s.fnDrawCallback.call(this,this.dom.clone.left,this.dom.clone.right);d(this).trigger("draw.dtfc",{leftClone:this.dom.clone.left,rightClone:this.dom.clone.right})},_fnCloneRight:function(a){if(!(0>=this.s.iRightColumns)){var b,c=[];for(b=this.s.iTableColumns-this.s.iRightColumns;b<this.s.iTableColumns;b++)this.s.dt.aoColumns[b].bVisible&&
c.push(b);this._fnClone(this.dom.clone.right,this.dom.grid.right,c,a)}},_fnCloneLeft:function(a){if(!(0>=this.s.iLeftColumns)){var b,c=[];for(b=0;b<this.s.iLeftColumns;b++)this.s.dt.aoColumns[b].bVisible&&c.push(b);this._fnClone(this.dom.clone.left,this.dom.grid.left,c,a)}},_fnCopyLayout:function(a,b){for(var c=[],g=[],f=[],e=0,h=a.length;e<h;e++){var j=[];j.nTr=d(a[e].nTr).clone(!0,!0)[0];for(var i=0,n=this.s.iTableColumns;i<n;i++)if(-1!==d.inArray(i,b)){var l=d.inArray(a[e][i].cell,f);-1===l?(l=
d(a[e][i].cell).clone(!0,!0)[0],g.push(l),f.push(a[e][i].cell),j.push({cell:l,unique:a[e][i].unique})):j.push({cell:g[l],unique:a[e][i].unique})}c.push(j)}return c},_fnClone:function(a,b,c,g){var f=this,e,h,j,i,n,l,k,m,o;if(g){null!==a.header&&a.header.parentNode.removeChild(a.header);a.header=d(this.dom.header).clone(!0,!0)[0];a.header.className+=" DTFC_Cloned";a.header.style.width="100%";b.head.appendChild(a.header);m=this._fnCopyLayout(this.s.dt.aoHeader,c);i=d(">thead",a.header);i.empty();e=0;
for(h=m.length;e<h;e++)i[0].appendChild(m[e].nTr);this.s.dt.oApi._fnDrawHead(this.s.dt,m,!0)}else{m=this._fnCopyLayout(this.s.dt.aoHeader,c);o=[];this.s.dt.oApi._fnDetectHeader(o,d(">thead",a.header)[0]);e=0;for(h=m.length;e<h;e++){j=0;for(i=m[e].length;j<i;j++)o[e][j].cell.className=m[e][j].cell.className,d("span.DataTables_sort_icon",o[e][j].cell).each(function(){this.className=d("span.DataTables_sort_icon",m[e][j].cell)[0].className})}}this._fnEqualiseHeights("thead",this.dom.header,a.header);
"auto"==this.s.sHeightMatch&&d(">tbody>tr",f.dom.body).css("height","auto");null!==a.body&&(a.body.parentNode.removeChild(a.body),a.body=null);a.body=d(this.dom.body).clone(!0)[0];a.body.className+=" DTFC_Cloned";a.body.style.paddingBottom=this.s.dt.oScroll.iBarWidth+"px";a.body.style.marginBottom=2*this.s.dt.oScroll.iBarWidth+"px";null!==a.body.getAttribute("id")&&a.body.removeAttribute("id");d(">thead>tr",a.body).empty();d(">tfoot",a.body).remove();var p=d("tbody",a.body)[0];d(p).empty();if(0<this.s.dt.aiDisplay.length){h=
d(">thead>tr",a.body)[0];for(k=0;k<c.length;k++)n=c[k],l=d(this.s.dt.aoColumns[n].nTh).clone(!0)[0],l.innerHTML="",i=l.style,i.paddingTop="0",i.paddingBottom="0",i.borderTopWidth="0",i.borderBottomWidth="0",i.height=0,i.width=f.s.aiInnerWidths[n]+"px",h.appendChild(l);d(">tbody>tr",f.dom.body).each(function(a){var b=this.cloneNode(false);b.removeAttribute("id");a=f.s.dt.oFeatures.bServerSide===false?f.s.dt.aiDisplay[f.s.dt._iDisplayStart+a]:a;for(k=0;k<c.length;k++){var e=f.s.dt.aoData[a].anCells||
f.s.dt.oApi._fnGetTdNodes(f.s.dt,a);n=c[k];if(e.length>0){l=d(e[n]).clone(true,true)[0];b.appendChild(l)}}p.appendChild(b)})}else d(">tbody>tr",f.dom.body).each(function(){l=this.cloneNode(true);l.className=l.className+" DTFC_NoData";d("td",l).html("");p.appendChild(l)});a.body.style.width="100%";a.body.style.margin="0";a.body.style.padding="0";g&&"undefined"!=typeof this.s.dt.oScroller&&b.liner.appendChild(this.s.dt.oScroller.dom.force.cloneNode(!0));b.liner.appendChild(a.body);this._fnEqualiseHeights("tbody",
f.dom.body,a.body);if(null!==this.s.dt.nTFoot){if(g){null!==a.footer&&a.footer.parentNode.removeChild(a.footer);a.footer=d(this.dom.footer).clone(!0,!0)[0];a.footer.className+=" DTFC_Cloned";a.footer.style.width="100%";b.foot.appendChild(a.footer);m=this._fnCopyLayout(this.s.dt.aoFooter,c);b=d(">tfoot",a.footer);b.empty();e=0;for(h=m.length;e<h;e++)b[0].appendChild(m[e].nTr);this.s.dt.oApi._fnDrawHead(this.s.dt,m,!0)}else{m=this._fnCopyLayout(this.s.dt.aoFooter,c);b=[];this.s.dt.oApi._fnDetectHeader(b,
d(">tfoot",a.footer)[0]);e=0;for(h=m.length;e<h;e++){j=0;for(i=m[e].length;j<i;j++)b[e][j].cell.className=m[e][j].cell.className}}this._fnEqualiseHeights("tfoot",this.dom.footer,a.footer)}b=this.s.dt.oApi._fnGetUniqueThs(this.s.dt,d(">thead",a.header)[0]);d(b).each(function(a){n=c[a];this.style.width=f.s.aiInnerWidths[n]+"px"});null!==f.s.dt.nTFoot&&(b=this.s.dt.oApi._fnGetUniqueThs(this.s.dt,d(">tfoot",a.footer)[0]),d(b).each(function(a){n=c[a];this.style.width=f.s.aiInnerWidths[n]+"px"}))},_fnGetTrNodes:function(a){for(var b=
[],c=0,d=a.childNodes.length;c<d;c++)"TR"==a.childNodes[c].nodeName.toUpperCase()&&b.push(a.childNodes[c]);return b},_fnEqualiseHeights:function(a,b,c){if(!("none"==this.s.sHeightMatch&&"thead"!==a&&"tfoot"!==a)){var g,f,e=b.getElementsByTagName(a)[0],c=c.getElementsByTagName(a)[0],a=d(">"+a+">tr:eq(0)",b).children(":first");a.outerHeight();a.height();for(var e=this._fnGetTrNodes(e),b=this._fnGetTrNodes(c),h=[],c=0,a=b.length;c<a;c++)g=e[c].offsetHeight,f=b[c].offsetHeight,g=f>g?f:g,"semiauto"==this.s.sHeightMatch&&
(e[c]._DTTC_iHeight=g),h.push(g);c=0;for(a=b.length;c<a;c++)b[c].style.height=h[c]+"px",e[c].style.height=h[c]+"px"}}};k.defaults={iLeftColumns:1,iRightColumns:0,fnDrawCallback:null,sHeightMatch:"semiauto"};k.version="3.0.1";d.fn.dataTable.FixedColumns=k;return d.fn.DataTable.FixedColumns=k};"function"===typeof define&&define.amd?define("datatables-fixedcolumns",["jquery","datatables"],r):jQuery&&!jQuery.fn.dataTable.FixedColumns&&r(jQuery,jQuery.fn.dataTable)})(window,document);
