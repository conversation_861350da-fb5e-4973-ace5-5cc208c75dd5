[
    [1, "al<PERSON>", "Altro"],
    [2,"agcom","Agenti Commercio"],
    [3,"art","Artisti"],
    [4,"lavdom","Lavoro Domestico"],
    [5,"comm","Commercianti"],
    [6,"doc","Docenti"],
    [7,"lavaut","Lavoro Autonomo"],
    [8,"lavdip","Lavoro Dipendente"],
    [9,"mil","Militari"],
    [10,"pens","Pensionati"],
    [11,"tempind","Tempo Determinato"]
].each do |id, code, description|
  JobKind.seed do |s|
    s.id = id
    s.code = code
    s.description = description
  end
end