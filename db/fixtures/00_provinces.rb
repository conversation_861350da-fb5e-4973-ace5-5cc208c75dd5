# coding: utf-8
PROVS_TO_REGIONS = { "AO" => "1", "MI" => "4", "AG" => "19", "AL" => "2", "AN" => "11", "AQ" => "13", "AR" => "9", "AP" => "11", "AT" => "2", "AV" => "15", "BA" => "16", "BT" => "16", "BL" => "5", "BN" => "15", "BG" => "4", "BI" => "2", "BO" => "8", "BZ" => "6", "BS" => "4", "BR" => "16", "CA" => "20", "CL" => "19", "CB" => "14", "CI" => "20", "CE" => "15", "CT" => "19", "CZ" => "18", "CH" => "13", "CO" => "4", "CS" => "18", "CR" => "4", "KR" => "18", "CN" => "2", "EN" => "19", "FM" => "11", "FE" => "8", "FI" => "9", "FG" => "16", "FC" => "8", "FR" => "12", "FU" => "7", "PL" => "7", "ZA" => "7", "GE" => "3", "GO" => "7", "GR" => "9", "IM" => "3", "IS" => "14", "LT" => "12", "LE" => "16", "LC" => "4", "LI" => "9", "LO" => "4", "LU" => "9", "MC" => "11", "MN" => "4", "MS" => "9", "MT" => "17", "VS" => "20", "ME" => "19", "MO" => "8", "MB" => "4", "NA" => "15", "NO" => "2", "NU" => "20", "OG" => "20", "OT" => "20", "OR" => "20", "PD" => "5", "PA" => "19", "PR" => "8", "PV" => "4", "PG" => "10", "PU" => "11", "PE" => "13", "PC" => "8", "PI" => "9", "PT" => "9", "PN" => "7", "PZ" => "17", "PO" => "9", "RG" => "19", "RA" => "8", "RC" => "18", "RE" => "8", "RI" => "12", "RN" => "8", "RM" => "12", "RO" => "5", "SA" => "15", "SS" => "20", "SV" => "3", "SI" => "9", "SR" => "19", "SO" => "4", "SP" => "3", "TA" => "16", "TE" => "13", "TR" => "10", "TO" => "2", "TP" => "19", "TN" => "6", "TV" => "5", "TS" => "7", "UD" => "7", "VA" => "4", "VE" => "5", "VB" => "2", "VC" => "2", "VR" => "5", "VV" => "18", "VI" => "5", "VT" => "12", "EE" => "23", "SC" => "22", "SM" => "21" }

[
  [1, "Agrigento", "AG", "Provincia di Agrigento", "AGRIGENTO", "4", 0, nil],
  [2, "Alessandria", "AL", "Provincia di Alessandria", "ALESSANDRIA", "1", 10, nil],
  [3, "Ancona", "AN", "Provincia di Ancona", "ANCONA", "3", 20, nil],
  [4, "Aosta", "AO", "Valle d'Aosta", "AOSTA", "1", 30, nil],
  [5, "Arezzo", "AR", "Provincia di Arezzo", "AREZZO", "3", 40, nil],
  [6, "Ascoli Piceno", "AP", "Provincia di Ascoli Piceno", "ASCOLI PICENO", "3", 50, nil],
  [7, "Asti", "AT", "Provincia di Asti", "ASTI", "1", 60, nil],
  [8, "Avellino", "AV", "Provincia di Avellino", "AVELLINO", "4", 70, nil],
  [9, "Bari", "BA", "Città Metropolitana di Bari", "BARI", "4", 80, nil],
  [10, "Barletta-Andria-Trani", "BT", "Provincia di Barletta-Andria-Trani", "BARLETTA-ANDRIA-TRANI", "4", 90, nil],
  [11, "Belluno", "BL", "Provincia di Belluno", "BELLUNO", "2", 100, nil],
  [12, "Benevento", "BN", "Provincia di Benevento", "BENEVENTO", "4", 110, nil],
  [13, "Bergamo", "BG", "Provincia di Bergamo", "BERGAMO", "1", 120, nil],
  [14, "Biella", "BI", "Provincia di Biella", "BIELLA", "1", 130, nil],
  [15, "Bologna", "BO", "Città Metropolitana di Bologna", "BOLOGNA", "2", 140, nil],
  [16, "Bolzano", "BZ", "Alto Adige", "BOLZANO", "2", 150, nil],
  [17, "Brescia", "BS", "Provincia di Brescia", "BRESCIA", "1", 160, nil],
  [18, "Brindisi", "BR", "Provincia di Brindisi", "BRINDISI", "4", 170, nil],
  [19, "Cagliari", "CA", "Provincia di Cagliari", "CAGLIARI", "3", 180, nil],
  [20, "Caltanissetta", "CL", "Provincia di Caltanissetta", "CALTANISSETTA", "4", 190, nil],
  [21, "Campobasso", "CB", "Provincia di Campobasso", "CAMPOBASSO", "3", 200, nil],
  [22, "Carbonia-Iglesias", "CI", "Provincia di Carbonia-Iglesias", "CARBONIA-IGLESIAS", "3", 1200, Date.new(2024, 3, 4)],
  [23, "Caserta", "CE", "Provincia di Caserta", "CASERTA", "4", 220, nil],
  [24, "Catania", "CT", "Provincia di Catania", "CATANIA", "4", 230, nil],
  [25, "Catanzaro", "CZ", "Provincia di Catanzaro", "CATANZARO", "4", 240, nil],
  [26, "Chieti", "CH", "Provincia di Chieti", "CHIETI", "3", 250, nil],
  [27, "Como", "CO", "Provincia di Como", "COMO", "1", 260, nil],
  [28, "Cosenza", "CS", "Provincia di Cosenza", "COSENZA", "4", 270, nil],
  [29, "Cremona", "CR", "Provincia di Cremona", "CREMONA", "1", 280, nil],
  [30, "Crotone", "KR", "Provincia di Crotone", "CROTONE", "4", 290, nil],
  [31, "Cuneo", "CN", "Provincia di Cuneo", "CUNEO", "1", 300, nil],
  [32, "Enna", "EN", "Provincia di Enna", "ENNA", "4", 310, nil],
  [33, "Fermo", "FM", "Provincia di Fermo", "FERMO", "3", 320, nil],
  [34, "Ferrara", "FE", "Provincia di Ferrara", "FERRARA", "2", 330, nil],
  [35, "Firenze", "FI", "Città Metropolitana di Firenze", "FIRENZE", "3", 340, nil],
  [36, "Foggia", "FG", "Provincia di Foggia", "FOGGIA", "4", 350, nil],
  [37, "Forlì-Cesena", "FC", "Provincia di Forlì-Cesena", "FORLI'", "2", 360, nil],
  [38, "Frosinone", "FR", "Provincia di Frosinone", "FROSINONE", "3", 370, nil],
  [39, "Genova", "GE", "Città Metropolitana di Genova", "GENOVA", "1", 380, nil],
  [40, "Gorizia", "GO", "Provincia di Gorizia", "GORIZIA", "2", 390, nil],
  [41, "Grosseto", "GR", "Provincia di Grosseto", "GROSSETO", "3", 400, nil],
  [42, "Imperia", "IM", "Provincia di Imperia", "IMPERIA", "1", 410, nil],
  [43, "Isernia", "IS", "Provincia di Isernia", "ISERNIA", "3", 420, nil],
  [44, "La Spezia", "SP", "Provincia della Spezia", "L' AQUILA", "1", 430, nil],
  [45, "L'Aquila", "AQ", "Provincia dell'Aquila", "LA SPEZIA", "3", 440, nil],
  [46, "Latina", "LT", "Provincia di Latina", "LATINA", "3", 450, nil],
  [47, "Lecce", "LE", "Provincia di Lecce", "LECCE", "4", 460, nil],
  [48, "Lecco", "LC", "Provincia di Lecco", "LECCO", "1", 470, nil],
  [49, "Livorno", "LI", "Provincia di Livorno", "LIVORNO", "3", 480, nil],
  [50, "Lodi", "LO", "Provincia di Lodi", "LODI", "1", 490, nil],
  [51, "Lucca", "LU", "Provincia di Lucca", "LUCCA", "3", 500, nil],
  [52, "Macerata", "MC", "Provincia di Macerata", "MACERATA", "3", 510, nil],
  [53, "Mantova", "MN", "Provincia di Mantova", "MANTOVA", "1", 520, nil],
  [54, "Massa-Carrara", "MS", "Provincia di Massa e Carrara", "MASSA", "3", 530, nil],
  [55, "Matera", "MT", "Provincia di Matera", "MATERA", "4", 540, nil],
  [56, "Messina", "ME", "Provincia di Messina", "MESSINA", "4", 550, nil],
  [57, "Milano", "MI", "Città Metropolitana di Milano", "MILANO", "1", 560, nil],
  [58, "Modena", "MO", "Provincia di Modena", "MODENA", "2", 570, nil],
  [59, "Monza e della Brianza", "MB", "Provincia di Monza e della Brianza", "MONZA BRIANZA", "1", 580, nil],
  [60, "Napoli", "NA", "Città Metropolitana di Napoli", "NAPOLI", "4", 590, nil],
  [61, "Novara", "NO", "Provincia di Novara", "NOVARA", "1", 600, nil],
  [62, "Nuoro", "NU", "Provincia di Nuoro", "NUORO", "3", 610, nil],
  [63, "Olbia-Tempio", "OT", "Provincia di Olbia-Tempio", "OLBIA-TEMPIO", "3", 1220, Date.new(2024, 3, 4)],
  [64, "Oristano", "OR", "Provincia di Oristano", "ORISTANO", "3", 630, nil],
  [65, "Padova", "PD", "Provincia di Padova", "PADOVA", "2", 640, nil],
  [66, "Palermo", "PA", "Provincia di Palermo", "PALERMO", "4", 650, nil],
  [67, "Parma", "PR", "Provincia di Parma", "PARMA", "2", 660, nil],
  [68, "Pavia", "PV", "Provincia di Pavia", "PAVIA", "1", 670, nil],
  [69, "Perugia", "PG", "Provincia di Perugia", "PERUGIA", "3", 680, nil],
  [70, "Pesaro e Urbino", "PU", "Provincia di Pesaro e Urbino", "PESARO", "3", 690, nil],
  [71, "Pescara", "PE", "Provincia di Pescara", "PESCARA", "3", 700, nil],
  [72, "Piacenza", "PC", "Provincia di Piacenza", "PIACENZA", "2", 710, nil],
  [73, "Pisa", "PI", "Provincia di Pisa", "PISA", "3", 720, nil],
  [74, "Pistoia", "PT", "Provincia di Pistoia", "PISTOIA", "3", 730, nil],
  [75, "Pordenone", "PN", "Provincia di Pordenone", "PORDENONE", "2", 740, nil],
  [76, "Potenza", "PZ", "Provincia di Potenza", "POTENZA", "4", 750, nil],
  [77, "Prato", "PO", "Provincia di Prato", "PRATO", "3", 760, nil],
  [78, "Ragusa", "RG", "Provincia di Ragusa", "RAGUSA", "4", 770, nil],
  [79, "Ravenna", "RA", "Provincia di Ravenna", "RAVENNA", "2", 780, nil],
  [80, "Reggio Calabria", "RC", "Provincia di Reggio Calabria", "REGGIO DI CALABRIA", "4", 790, nil],
  [81, "Reggio Emilia", "RE", "Provincia di Reggio Emilia", "REGGIO NELL' EMILIA", "2", 800, nil],
  [82, "Rieti", "RI", "Provincia di Rieti", "RIETI", "3", 810, nil],
  [83, "Rimini", "RN", "Provincia di Rimini", "RIMINI", "2", 820, nil],
  [84, "Roma", "RM", "Città Metropolitana di Roma", "ROMA", "3", 830, nil],
  [85, "Rovigo", "RO", "Provincia di Rovigo", "ROVIGO", "2", 840, nil],
  [86, "Salerno", "SA", "Provincia di Salerno", "SALERNO", "4", 850, nil],
  [87, "Medio Campidano", "VS", "Provincia di Medio Campidano", "MEDIO CAMPIDANO", "3", 1230, Date.new(2024, 3, 4)],
  [88, "Sassari", "SS", "Provincia di Sassari", "SASSARI", "3", 870, nil],
  [89, "Savona", "SV", "Provincia di Savona", "SAVONA", "1", 880, nil],
  [90, "Siena", "SI", "Provincia di Siena", "SIENA", "3", 890, nil],
  [91, "Siracusa", "SR", "Provincia di Siracusa", "SIRACUSA", "4", 900, nil],
  [92, "Sondrio", "SO", "Provincia di Sondrio", "SONDRIO", "1", 910, nil],
  [93, "Taranto", "TA", "Provincia di Taranto", "TARANTO", "4", 920, nil],
  [94, "Teramo", "TE", "Provincia di Teramo", "TERAMO", "3", 930, nil],
  [95, "Terni", "TR", "Provincia di Terni", "TERNI", "3", 940, nil],
  [96, "Torino", "TO", "Città Metropolitana di Torino", "TORINO", "1", 950, nil],
  [97, "Ogliastra", "OG", "Provincia dell'Ogliastra", "OGLIASTRA", "3", 1210, Date.new(2024, 3, 4)],
  [98, "Trapani", "TP", "Provincia di Trapani", "TRAPANI", "4", 970, nil],
  [99, "Trento", "TN", "Trentino", "TRENTO", "2", 980, nil],
  [100, "Treviso", "TV", "Provincia di Treviso", "TREVISO", "2", 990, nil],
  [101, "Trieste", "TS", "Provincia di Trieste", "TRIESTE", "2", 1000, nil],
  [102, "Udine", "UD", "Provincia di Udine", "UDINE", "2", 1010, nil],
  [103, "Varese", "VA", "Provincia di Varese", "VARESE", "1", 1020, nil],
  [104, "Venezia", "VE", "Città Metropolitana di Venezia", "VENEZIA", "2", 1030, nil],
  [105, "Verbano-Cusio-Ossola", "VB", "Provincia di Verbano-Cusio-Ossola", "VERBANIA", "1", 1040, nil],
  [106, "Vercelli", "VC", "Provincia di Vercelli", "VERCELLI", "1", 1050, nil],
  [107, "Verona", "VR", "Provincia di Verona", "VERONA", "2", 1060, nil],
  [108, "Vibo Valentia", "VV", "Provincia di Vibo Valentia", "VIBO VALENTIA", "4", 1070, nil],
  [109, "Vicenza", "VI", "Provincia di Vicenza", "VICENZA", "2", 1080, nil],
  [110, "Viterbo", "VT", "Provincia di Viterbo", "VITERBO", "3", 1090, nil],
  [111, "Estero", "EE", "Estero", "EE - (ESTERO)", nil, 1100, nil],
  [112, "Sud Sardegna", "SU", "Provincia del Sud Sardegna", "SUD SARDEGNA", "3", 915, nil],
  [113, "Fiume", "FU", "Fiume", "FIUME", "2", 1240, Date.new(2024, 4, 3)],
  [114, "Pola", "PL", "Pola", "POLA", "2", 1250, Date.new(2024, 4, 3)],
  [115, "Zara", "ZA", "Zara", "ZARA", "2", 1260, Date.new(2024, 4, 3)]
].each do |id, description, code, google_description, peoplesoft_code, area_code, position, deleted_at|
  Province.seed do |s|
    s.id                 = id
    s.code               = code
    s.description        = description
    s.google_description = google_description
    s.peoplesoft_code    = peoplesoft_code
    s.area_code          = area_code
    s.position           = position
    s.deleted_at         = deleted_at
    region               = Region.where(code: PROVS_TO_REGIONS[code]).first
    s.region_id          = region.id if region.present?
  end
end


