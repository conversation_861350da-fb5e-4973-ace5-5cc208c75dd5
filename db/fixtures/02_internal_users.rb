# [
#   ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "user", "210", nil, "<PERSON><PERSON><PERSON>.<EMAIL>", "Temporanea00"],
#   ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "agent", nil, nil, "<EMAIL>", "<PERSON><PERSON>ranea00"],
#   ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "user", "213", nil, "<PERSON><PERSON>.<EMAIL>", "Temporanea00"],
#   ["<PERSON><PERSON>", "<PERSON>", "user", "208", nil, "<PERSON><PERSON><PERSON>@kolme.it", "Temporanea00"],
#   ["<PERSON>", "<PERSON><PERSON><PERSON>", "admin", "201", "0287124000", "<PERSON>.<EMAIL>", "Temporanea00"],
#   ["<PERSON><PERSON><PERSON>", "<PERSON> Salvo", "agent", nil, nil, "<PERSON><PERSON><PERSON>.<EMAIL>", "Temporanea00"],
#   ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "admin", "203", nil, "<PERSON><PERSON>@kolme.it", "<PERSON><PERSON>ranea00"],
#   ["<PERSON>", "<PERSON><PERSON>", "user", "205", nil, "<PERSON>.<EMAIL>", "<PERSON><PERSON>ranea00"],
#   ["<PERSON>", "<PERSON>oletto", "admin", "202", nil, "<EMAIL>", "<PERSON>mporanea00"],
#   ["<PERSON>", "<PERSON><PERSON>", "super_user", "204", nil, "<PERSON>.<PERSON><PERSON>@kolme.it", "<PERSON><PERSON>ranea00"],
#   ["<PERSON>", "<PERSON><PERSON>ne", "user", "209", nil, "<PERSON>.<PERSON><PERSON><EMAIL>", "Temporanea00"],
#   ["Rosanna", "Bucci", "user", "207", nil, "<EMAIL>", "Temporanea00"],
#   ["Sara", "Thabet", "user", "212", nil, "<EMAIL>", "Temporanea00"],
#   ["Stefano", "Dal Sasso", "agent", nil, "3479632864", "Stefano.Dal <EMAIL>", "Temporanea00"],
#   ["Valentina", "Giarnetti", "user", "211", nil, "<EMAIL>", "Temporanea00"],
#   ["Veronica", "Cavallo", "user", "206", nil, "<EMAIL>", "Temporanea00"],
#   ["Virginia", "Formisano", "user", "215", nil, "<EMAIL>", "Temporanea00"]
# ].each do |nome, cognome, ruolo, pbx, phone, email, password|
#   InternalUserDetail.seed(:first_name, :last_name) do |s|
#     user = User.seed(:email) do |u|
#       u.email = email
#       u.username = email
#       u.password = password
#       u.password_confirmation = password
#       u.role = ruolo
#       u.uuid = UUIDTools::UUID.random_create.to_s
#       u.confirmed_at = Time.now
#     end
#     s.user = user.first
#     s.first_name=nome
#     s.last_name=cognome
#     s.internal_phone=pbx
#     s.phone=phone
#     s.access_level = (ruolo == "agent" ? "only_md" : "all")
#   end
# end
