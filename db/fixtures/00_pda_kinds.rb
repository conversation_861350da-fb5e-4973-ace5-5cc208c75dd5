[
    [1, "3 Ricaricabile", "3 Ricaricabile",
     "Cover.pdf", "3 - Ricaricabile PF.pdf", nil, nil, nil,
     "Cover.pdf", "3 - Ricaricabile PG.pdf", nil, nil, nil,
     "Cover.pdf", "3 - Ricaricabile PF.pdf", nil, nil, nil],
    [2, "3 Abbonamento", "3 Abbonamento",
     "Cover.pdf", "3 - Abbonamento PF.pdf", "Modulo 3 - RID.pdf", nil, "Modulo 3 - MNP.pdf",
     "Cover.pdf", "3 - Abbonamento PG.pdf", "Modulo 3 - RID.pdf", nil, "Modulo 3 - MNP.pdf",
     "Cover.pdf", "3 - Abbonamento PF.pdf", "Modulo 3 - RID.pdf", nil, "Modulo 3 - MNP.pdf"],
    [3, "<PERSON><PERSON>", "Gene<PERSON>",
     nil, "Generica <PERSON>lme.pdf", nil, nil, nil,
     nil, "Generica Kolme.pdf", nil, nil, nil,
     nil, "Generica Kolme.pdf", nil, nil, nil],
    [4, "3 Scegli", "3 Scegli",
     "Cover.pdf", "3 - Scegli PF.pdf", "Modulo 3 - RID.pdf", nil, "Modulo 3 - MNP.pdf",
     "Cover.pdf", "3 - Scegli PG.pdf", "Modulo 3 - RID.pdf", nil, "Modulo 3 - MNP.pdf",
     "Cover.pdf", "3 - Scegli PF.pdf", "Modulo 3 - RID.pdf", nil, "Modulo 3 - MNP.pdf"],
    [5, "Fastweb Mobile", "Fastweb Mobile",
     "Cover.pdf", "FW - Mobile PF.pdf", "", nil, "Modulo FW - MNP.pdf",
     "Cover.pdf", "FW - Mobile PG.pdf", "", nil, "Modulo FW - MNP PG.pdf",
     "Cover.pdf", "FW - Mobile PG.pdf", "", nil, "Modulo FW - MNP PG.pdf"],
    [6, "Fastweb Fissa", "Fastweb Fissa",
     "Cover.pdf", "FW - Fissa PF.pdf", "", nil, nil,
     "Cover.pdf", "FW - Fissa PG.pdf", "", nil, nil,
     "Cover.pdf", "FW - Fissa PG.pdf", "", nil, nil],
    [7, "Aria Indoor", "Aria Indoor",
     "Cover.pdf", "Aria - Indoor PF.pdf", "Modulo Aria - RID.pdf", nil, nil,
     "Cover.pdf", "Aria - Indoor PG.pdf", "Modulo Aria - RID.pdf", nil, nil,
     "Cover.pdf", "Aria - Indoor PG.pdf", "Modulo Aria - RID.pdf", nil, nil],
    [8, "Aria Oudoor + Verbale", "Aria Oudoor + Verbale",
     "Cover.pdf", "Aria - Outdoor PF.pdf", "Modulo Aria - RID.pdf", nil, nil,
     "Cover.pdf", "Aria - Outdoor PG.pdf", "Modulo Aria - RID.pdf", nil, nil,
     "Cover.pdf", "Aria - Outdoor PG.pdf", "Modulo Aria - RID.pdf", nil, nil],
    [9, "3 Rivincolo Abbonamento", "3 Rivincolo Abbonamento",
     "Cover.pdf", "3 - Rivincolo Abbonamento PF.pdf", "Modulo 3 - RID.pdf", nil, nil,
     "Cover.pdf", "3 - Rivincolo Abbonamento PG.pdf", "Modulo 3 - RID.pdf", nil, nil,
     "Cover.pdf", "3 - Rivincolo Abbonamento PF.pdf", "Modulo 3 - RID.pdf", nil, nil],
    [10, "3 Rivincolo Ricaricabile", "3 Rivincolo Ricaricabile",
     "Cover.pdf", "3 - Rivincolo Ricaricabile PF.pdf", "Modulo 3 - RID.pdf", nil, nil,
     "Cover.pdf", "3 - Rivincolo Ricaricabile PG.pdf", "Modulo 3 - RID.pdf", nil, nil,
     "Cover.pdf", "3 - Rivincolo Ricaricabile PF.pdf", "Modulo 3 - RID.pdf", nil, nil],
    [11, "Wind Biz Fissa", "Wind Biz Fissa",
     "Cover.pdf", "Wind Biz - Fissa.pdf", nil, nil, nil,
     "Cover.pdf", "Wind Biz - Fissa.pdf", nil, nil, nil,
     "Cover.pdf", "Wind Biz - Fissa.pdf", nil, nil, nil],
    [12, "Wind Biz Dati", "Wind Biz Dati",
     "Cover.pdf", "Wind Biz - Dati.pdf", nil, nil, nil,
     "Cover.pdf", "Wind Biz - Dati.pdf", nil, nil, nil,
     "Cover.pdf", "Wind Biz - Dati.pdf", nil, nil, nil],
    [13, "Wind Biz Voce", "Wind Biz Voce",
     "Cover.pdf", "Wind Biz - Voce.pdf", nil, nil, nil,
     "Cover.pdf", "Wind Biz - Voce.pdf", nil, nil, nil,
     "Cover.pdf", "Wind Biz - Voce.pdf", nil, nil, nil],
    [14, "Open Sky", "Open Sky",
     "Cover.pdf", "Opensky.pdf", nil, nil, nil,
     "Cover.pdf", "Opensky.pdf", nil, nil, nil,
     "Cover.pdf", "Opensky.pdf", nil, nil, nil],
    [15, "3 Finanziamento Compass", "3 Finanziamento Compass",
     "Cover.pdf", "3 - Finanziamento Compass PF.pdf", "Modulo 3 - RID.pdf", nil, "Modulo 3 - MNP.pdf",
     "Cover.pdf", "3 - Finanziamento Compass PG.pdf", "Modulo 3 - RID.pdf", nil, "Modulo 3 - MNP.pdf",
     "Cover.pdf", "3 - Finanziamento Compass PF.pdf", "Modulo 3 - RID.pdf", nil, "Modulo 3 - MNP.pdf"],
    [16, "Fastweb+SKY Fissa CL1", "Fastweb+SKY Fissa CL1",
     "Cover.pdf", "FW SKY - Fissa PF.pdf", nil, nil, "Modulo FW - Recesso.pdf",
     nil, "Generica Kolme.pdf", nil, nil, nil,
     nil, "Generica Kolme.pdf", nil, nil, nil],
    [17, "Linkem Abbonamento", "Linkem Abbonamento",
     "Cover.pdf", "Linkem - Abbonamento PF.pdf", "Modulo Linkem - RID.pdf", "Modulo Linkem - CDC.pdf", nil,
     "Cover.pdf", "Linkem - Abbonamento PG.pdf", "Modulo Linkem - RID.pdf", "Modulo Linkem - CDC.pdf", nil,
     "Cover.pdf", "Linkem - Abbonamento PG.pdf", "Modulo Linkem - RID.pdf", "Modulo Linkem - CDC.pdf", nil],
    [18, "Linkem Ricaricabile", "Linkem Ricaricabile",
     "Cover.pdf", "Linkem - Ricaricabile PF.pdf", nil, nil, nil,
     "Cover.pdf", "Linkem - Ricaricabile PG.pdf", nil, nil, nil,
     "Cover.pdf", "Linkem - Ricaricabile PG.pdf", nil, nil, nil],
    [19, "Fastweb+SKY Fissa CL2", "Fastweb+SKY Fissa CL2",
     "Cover.pdf", "FW SKY CL2 - Fissa PF.pdf", nil, nil, "Modulo FW - Recesso.pdf",
     nil, "Generica Kolme.pdf", nil, nil, nil,
     nil, "Generica Kolme.pdf", nil, nil, nil],
    [20, "3 Tieni", "3 Tieni",
     "Cover.pdf", "3 - Tieni PF.pdf", "Modulo 3 - RID.pdf", nil, "Modulo 3 - MNP.pdf",
     "Cover.pdf", "3 - Tieni PG.pdf", "Modulo 3 - RID.pdf", nil, "Modulo 3 - MNP.pdf",
     "Cover.pdf", "3 - Tieni PF.pdf", "Modulo 3 - RID.pdf", nil, "Modulo 3 - MNP.pdf"]
].each do |id, code, description, pf_cover, pf_pda, pf_sdd, pf_cdc, pf_mnp, pg_cover, pg_pda, pg_sdd, pg_cdc, pg_mnp, di_cover, di_pda, di_sdd, di_cdc, di_mnp|
  PdaKind.seed do |s|
    s.id = id
    s.code = code
    s.description = description

    s.pf_cover = pf_cover
    s.pf_pda = pf_pda
    s.pf_sdd = pf_sdd
    s.pf_cdc = pf_cdc
    s.pf_mnp = pf_mnp

    s.pg_cover = pg_cover
    s.pg_pda = pg_pda
    s.pg_sdd = pg_sdd
    s.pg_cdc = pg_cdc
    s.pg_mnp = pg_mnp

    s.di_cover = di_cover
    s.di_pda = di_pda
    s.di_sdd = di_sdd
    s.di_cdc = di_cdc
    s.di_mnp = di_mnp
  end
end
