class CreateAceaDataImports < ActiveRecord::Migration[7.0]
  def change
    create_table :acea_datas do |t|
      t.string :id_pdc, limit: 50
      t.string :cod_pod_pdr, limit: 100
      t.string :num_item_in_pdc, limit: 10
      t.string :flg_dual, limit: 10
      t.string :des_tipologia_commodity, limit: 10
      t.date :tst_creazione_pdc
      t.date :dat_creazione_pdc
      t.date :tst_firma_pdc
      t.date :dat_firma_pdc
      t.date :tst_acquisizione_pdc
      t.date :dat_acquisizione_pdc
      t.date :tst_annullamento_pdc
      t.date :dat_annullamento_pdc
      t.string :des_causale_annullamento_pdc, limit: 100
      t.string :des_categoria_uso_pdc_item, limit: 100
      t.string :des_nome_listino_pdc_item, limit: 100
      t.string :cod_listino_pdc_item, limit: 20
      t.string :imp_canone_listino_netto_pdc_item, limit: 10
      t.string :des_tipologia_listino_pdc_item, limit: 10
      t.string :des_metodo_di_pagamento_pdc_item, limit: 20
      t.string :cod_utenza_acea, limit: 50
      t.string :cod_pdv, limit: 50
      t.string :des_tipo_ordine, limit: 50
      t.string :des_stato_oli, limit: 50
      t.date :tst_inserimento_oli
      t.date :dat_inserimento_oli
      t.date :tst_aggiornamento_oli
      t.date :dat_aggiornamento_oli
      t.date :tst_annullamento_oli
      t.date :dat_annullamento_oli
      t.date :des_causale_annullamento_oli
      t.date :dat_fine_ripensamento
      t.date :tst_chiusura_oli
      t.date :dat_chiusura_oli
      t.date :data_nascita
      t.string :des_sesso, limit: 50
      t.string :des_nazione_nascita, limit: 50
      t.string :des_nazione_residenza, limit: 50
      t.string :des_provincia_residenza, limit: 50
      t.string :des_comune_residenza, limit: 50
      t.string :cod_cap_residenza, limit: 50
      t.string :flg_stato_consenso_commerciale, limit: 50
      t.string :flg_stato_consenso_cessione_terzi, limit: 50
      t.string :flg_stato_consenso_profilazione, limit: 50
      t.string :flg_stato_consenso_arricchimento, limit: 50
      t.date :tst_aggiornamento_consenso_commerciale
      t.date :dat_aggiornamento_consenso_commerciale
      t.date :tst_aggiornamento_consenso_cessione_terzi
      t.date :dat_aggiornamento_consenso_cessione_terzi
      t.date :tst_aggiornamento_consenso_profilazione
      t.date :dat_aggiornamento_consenso_profilazione
      t.date :tst_aggiornamento_consenso_arricchimento
      t.date :dat_aggiornamento_consenso_arricchimento
      t.string :des_sistema_agg_consensi, limit: 50
      t.string :num_pdc_item, limit: 50
      t.string :flg_acquisizione_pdc, limit: 50
      t.string :flg_annullamento_pdc, limit: 50
      t.string :flg_annullamento_oli, limit: 50
      t.string :flg_chiusura_oli, limit: 50
      t.string :potenza_contatore_pdc, limit: 50
      t.string :provincia_fornitura, limit: 50
      t.string :comune_fornitura, limit: 50
      t.date :data_decorrenza
      t.string :canale_provenienza_attivazione, limit: 50
      t.string :sottocanale, limit: 50
      t.string :tipologia_prestazione, limit: 50
      t.date :date_load
      t.string :ragione_sociale_dealer, limit: 100
      t.string :id_dim_area_geografica_dealer, limit: 50
      t.string :comune_dealer, limit: 50
      t.string :regione_dealer, limit: 50
      t.string :cluster_vari_dealer, limit: 50
      t.string :des_area_geografica_dealer, limit: 50
      t.string :flg_cliente_telco, limit: 50
      t.string :flg_cliente_telco_creaz, limit: 50
      t.string :num_ba_mobile_customer, limit: 50
      t.string :num_ba_fisso_dati_customer, limit: 50
      t.string :num_ba_voip_customer, limit: 50
      t.string :num_ba_fisso_voce_customer, limit: 50
      t.string :flg_ba_mobile_customer, limit: 50
      t.string :flg_ba_fisso_dati_customer, limit: 50
      t.string :flg_ba_voip_customer, limit: 50
      t.string :flg_fisso_voce_customer, limit: 50
      t.date :data_attivazione_asset
      t.string :flg_attivazione_asset, limit: 50
      t.string :data_cessazione_asset, limit: 50
      t.string :flg_cessazione_asset, limit: 50
      t.string :id_utenza, limit: 100
      t.string :flg_tel_contatto_wind3, limit: 50
      t.string :flg_cliente_business, limit: 50
      t.string :des_segmento_mercato, limit: 50
      t.string :ragionesociale, limit: 100
      t.string :canale, limit: 50
      t.string :area, limit: 50
      t.string :comune, limit: 50
      t.string :regione, limit: 50
      t.string :capoarea, limit: 50
      t.string :funzionario, limit: 50
      t.string :account_lg, limit: 50
      t.string :codicepdvaggregato, limit: 50
      t.references :warehouse

      t.timestamps
    end
  end
end
