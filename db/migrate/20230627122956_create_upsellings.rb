class CreateUpsellings < ActiveRecord::Migration[5.2]
  def change
    create_table :upsellings do |t|
      t.belongs_to :plan
      t.belongs_to :product
      t.string :cluster
      t.string :product_code
      t.decimal :installment, precision: 10, scale: 2, default: 0.0
      t.decimal :advance_cdc, precision: 10, scale: 2, default: 0.0
      t.decimal :advance_sdd, precision: 10, scale: 2, default: 0.0
      t.text :note

      t.timestamps
    end

    add_index :upsellings, [:plan_id, :product_code], unique: true
  end
end
