class CreateDhlShipmentTrackers < ActiveRecord::Migration[5.2]
  def change
    create_table :dhl_shipment_trackers do |t|
      t.string :flow_type, limit: 3, null: false
      t.string :record_type, limit: 3, null: false
      t.string :order_number, limit: 30, null: false
      t.string :ddt_number, limit: 10, null: false
      t.string :awb_number, limit: 10, null: false
      t.date :delivery_date, null: false
      t.string :delivery_time, limit: 6, null: false
      t.string :signature, limit: 30

      t.references :dhl_shipment_state, foreign_key: true, null: false

      t.timestamps
    end
  end
end
