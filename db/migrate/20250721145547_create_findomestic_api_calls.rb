class CreateFindomesticApiCalls < ActiveRecord::Migration[7.0]
  def change
    create_table :findomestic_api_calls do |t|
      t.references :imei_reservation, null: false, foreign_key: true
      t.string :call_type, null: false
      t.string :endpoint_url
      t.json :request_payload
      t.json :response_payload
      t.integer :http_status
      t.string :status, default: 'pending'
      t.text :error_message
      t.integer :retry_count, default: 0
      t.datetime :last_retry_at
      t.datetime :next_retry_at
      t.string :application_id
      t.string :issuer_installment_id

      t.timestamps
    end

    add_index :findomestic_api_calls, [:imei_reservation_id, :call_type]
    add_index :findomestic_api_calls, :status
    add_index :findomestic_api_calls, :next_retry_at
  end
end
