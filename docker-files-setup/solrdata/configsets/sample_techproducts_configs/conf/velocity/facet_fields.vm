#**
 *  Display facets based on field values
 *  e.g.: fields specified by &facet.field=
 *#

#if($response.facetFields)
  <h2 #annTitle("Facets generated by adding &facet.field= to the request")>
    Field Facets
  </h2>
  #foreach($field in $response.facetFields)
    ## Hide facets without value
    #if($field.values.size() > 0)
      <span class="facet-field">$field.name</span>
      <ul>
        #foreach($facet in $field.values)
          <li>
            <a href="#url_for_facet_filter($field.name, $facet.name)" title="$esc.html($facet.name)">
              #if($facet.name!=$null)$esc.html($display.truncate($facet.name,20))#else<em>missing</em>#end</a> ($facet.count)
          </li>
        #end
      </ul>
    #end  ## end if > 0
  #end    ## end for each facet field
#end      ## end if response has facet fields
