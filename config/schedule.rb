# coding: utf-8
if @environment == 'staging'
  every 30.minutes do
    set :output, 'log/sap_import_fido.log'
    rake 'sap:import_fido'
  end
end

if @environment == 'staging'
  every 30.minutes do
    set :output, 'log/import_credit_transfers_sap.log'
    rake 'sap:import_credit_transfers'
  end
  every 30.minutes do
    set :output, 'log/import_credit_notes_sap.log'
    rake 'sap:import_credit_notes'
  end
else
  every 1.day, at: '6:00 am' do
    set :output, 'log/import_credit_transfers_sap.log'
    rake 'sap:import_credit_transfers'
  end
  every 1.day, at: '6:00 am' do
    set :output, 'log/import_credit_notes_sap.log'
    rake 'sap:import_credit_notes'
  end
end

if @environment == 'production'
  every 1.day, at: '9:30 pm' do
    set :output, 'log/check_kolme_credit_on_paymat_or_euronet.log'
    rake 'kolme:app:check_kolme_credit_on_paymat_or_euronet'
  end

  every '0 2 15 * *' do
    set :output, 'log/phone_call_files_remote_cleaner_job.log'
    rake 'phone_calls:delete_files_older_than_six_months'
  end

  every '0 2 30 * *' do
    set :output, 'log/clean_unindexed_orders.log'
    rake 'orders:clean_unindexed_orders'
  end

  every 1.day, at: '7:00 am' do
    set :output, 'log/attiva_import_product_list.log'
    rake 'attiva_service:import_product_list'
  end

  every '0 5,13,20 * * *' do
    set :output, 'log/attiva_import_availability.log'
    rake 'attiva_service:import_availability'
  end

  every '10 1,6,13,17,19 * * *' do
    set :output, 'log/attiva_service.log'
    rake 'attiva_service:update_orders'
  end

  every 1.day, at: '1 am' do
    set :output, 'log/dealer_stats.log'
    rake 'dealers:fwa:update_stats'
  end

  every 1.day, at: '5 am' do
    set :output, 'log/sap_import_fido.log'
    rake 'sap:import_fido'
  end

  every 10.minutes do
    set :output, 'log/high_frequency_tasks.log'
    rake 'kolme:app:high_frequency_tasks'
  end

  every 1.day, at: '3:00 am' do
    set :output, 'log/import_dealer_contract_signatures.log'
    rake 'kolme:dealer_contract_signatures:import'
  end

  every 1.day, at: '4:00 am' do
    set :output, 'log/promo_devices_redo_burn.log'
    rake 'promo_devices:redo_burn'
  end

  every 1.day, at: '23:00 am' do
    set :output, 'log/burn_imei.log'
    rake 'burn_imei:burn_old'
  end
end

every 1.day, at: '00:30 am' do
  set :output, 'log/sap_export_orders.log'
  rake 'sap_export:export_orders_to_sap'
end

every 2.days, at: '2:00 am' do
  rake 'tmp:cache:clear'
end

every 1.day, at: '11:00 pm' do
  set :output, 'log/export_appointments.log'
  rake 'appointment_details:export_appointments'
end

every 1.day, at: '11:10 pm' do
  set :output, 'log/easycare_export.log'
  rake 'insurance:export'
end

# Clean uncompleted phone activations
every '10 10-23 * * *' do
  set :output, 'log/clean_uncompleted_phone_activations.log'
  rake 'kolme:app:clean_uncompleted_phone_activations'
end

every '10 10-23 * * *' do
  set :output, 'log/clean_uncompleted_energy_contracts.log'
  rake 'kolme:app:clean_uncompleted_energy_contracts'
end

every 10.minutes do
  set :output, 'log/update_not_signed_energy_contracts.log'
  rake 'kolme:app:update_not_signed_energy_contracts'
end

every 15.minutes do
  set :output, 'log/update_energy_contract_state.log'
  rake 'kolme:app:update_energy_contract_state'
end

every '0 23 * * *' do
  set :output, 'log/update_energy_contract_in_activation.log'
  rake 'kolme:app:update_energy_contract_in_activation'
end

every '10 * * * *' do
  set :output, 'log/energy_contract_external_error.log'
  rake 'kolme:app:resend_energy_contract_external_error'
end

every '20 10-23 * * *' do
  set :output, 'log/clean_duplicated_operation_phone_activations.log'
  rake 'kolme:app:clean_duplicated_operation_phone_activations'
end

# Paymat password check
every 1.day, at: '9:00 pm' do
  set :output, 'log/check_paymat_password.log'
  rake 'kolme:app:check_paymat_password'
end

every 1.day, at: '1:30 am' do
  set :output, 'log/notify_expired_appointments'
  rake 'appointment_details:notify_expired_appointments'
end

every 1.day, at: '7:00 am' do
  set :output, 'log/notify_today_appointments'
  rake 'appointment_details:notify_today_appointments'
end

every 1.day, at: '4:10 am' do
  set :output, 'log/after_sale_thread.log'
  rake 'after_sale_thread:reindex'
end

every 1.day, at: '4:30 am' do
  set :output, 'log/automatic_invoices.log'
  rake 'automatic_invoices:create_attachments_for_old_automatic_invoices'
end

every 1.day, at: '2 am' do
  set :output, 'log/dealer_delta_fido.log'
  rake 'dealers:set_delta_fido'
end

every 1.day, at: '4:30 am' do
  set :output, 'log/notify_expiring_fwa_documents.log'
  rake 'dealers:notify_expiring_fwa_documents'
end

every 1.day, at: '5:00 am' do
  set :output, 'log/process_expired_documents_dealers.log'
  rake 'dealers:process_expired_documents'
end

every 1.day, at: '6:00 am' do
  set :output, 'log/notify_missing_fwa_products_alerts.log'
  rake 'dealers:notify_missing_fwa_products_alerts'
end

every 30.minutes do
  set :output, 'log/remind_to_schedule_appointment.log'
  rake 'dealers:fwa:remind_to_schedule_appointment'
end

every '15 0,7,9,11,13,15,17,19,21 * * *' do
  set :output, 'log/update_euronet_credit.log'
  rake 'kolme:app:update_euronet_credit'
end

# Release busy operations locked from more than 20mins
every '40 0,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23 * * *' do
  set :output, 'log/release_busy_operations.log'
  rake 'kolme:app:release_busy_operations'
end

# Recharges
every '20 1,3,5,7,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23 * * *' do
  set :output, 'log/execute_failed_recharges.log'
  rake 'kolme:app:execute_failed_recharges'
end

every '50 0,8,14,20 * * *' do
  set :output, 'log/confirm_unconfirmed_recharges.log'
  rake 'kolme:app:confirm_unconfirmed_recharges'
end

every '15 * * * *' do
  runner 'Jobs::DhlShipmentTrackerImporterJob.new.perform'
end

every '30 * * * *' do
  set :output, 'log/findomestic_retry_processor.log'
  runner 'FindomesticApiCall.ready_for_retry.find_each { |api_call| Delayed::Job.enqueue(Jobs::FindomesticRetryJob.new(api_call.id), queue: "findomestic_retry") }'
end

every '0 23 * * *' do
  set :output, 'log/dispatch_monthly_reports.log'
  rake 'kolme:app:dispatch_end_of_month_monthly_reports'
end

every '0 5 * * *' do
  set :output, 'log/dispatch_monthly_reports.log'
  rake 'kolme:app:dispatch_monthly_reports'
end

every 1.day, at: '5:30 am' do
  set :output, 'log/delete_stored_cc_numbers_from_activations.log'
  rake 'credit_cards:delete_stored_cc_numbers_from_activations'
end

every 1.day, at: '7:30 am' do
  set :output, 'log/clean_cc_numbers.log'
  rake 'credit_cards:clean_cc_numbers'
end

# rimette in lista le attivazioni rimandate se il tempo è trascorso
every '5 6,7,8,9,10,11,12,13,14,15,16,17,18,19,21,22,23 * * *' do
  set :output, 'log/check_expired_delayed_phone_activations.log'
  rake 'kolme:app:check_expired_delayed_phone_activations'
end

# aggiorna gli alert non archiviati e in todo false piú vecchi di 15 giorni
every 1.day, at: '7:10 am' do
  set :output, 'log/archive_alerts.log'
  rake 'kolme:app:alert:archive_alerts'
end

every 1.day, at: '4:30 pm' do
  set :output, 'log/archive_free_shipping_alerts.log'
  rake 'kolme:app:alert:archive_free_shipping_alerts'
end

every 1.day, at: '5:00 am' do
  set :output, 'log/fail_expired_tls_upfronts.log'
  rake 'tls_upfronts:fail_expired'
end

every '55 8,9,10,11,12,13,14,15,16,17,20 * * *' do
  set :output, 'log/check_uncompleted_cc_orders.log'
  rake 'kolme:app:check_uncompleted_cc_orders'
end

# Remove expired credit cards
every '0 2 1 * *' do
  set :output, 'log/remove_expired_credit_card.log'
  rake 'kolme:credit_card:remove_expired_credit_card'
end

# Notify customer agreed will expire in 6 month
every 1.day, at: '5:00 am' do
  set :output, 'log/notify_expire_alert_message.log'
  rake 'alert_message:notify_expire'
end

# expire customer marketing agreed
every 1.day, at: '6:00 am' do
  set :output, 'log/expire_customer_marketing_agreed.log'
  rake 'customer:expire_customer_marketing_agreed'
end

# delete old unindexed orders
every '0 2 30 * *' do
  set :output, 'log/clean_unindexed_orders.log'
  rake 'orders:clean_unindexed_orders'
end

every '0 4 * * *' do
  set :output, 'log/remove_unfulfilled_quantities_orders.log'
  rake 'orders:remove_unfulfilled_quantities'
end

every 1.day, at: '11:00 pm' do
  set :output, 'log/dhl_stock_file_importer.log'
  rake 'dhl:dhl_stock_file_importer'
end

# Check di nuovi documenti contabili
every 1.day, at: '1:00 am' do
  set :output, 'log/check_new_financial_documents.log'
  rake 'kolme:ws:update_new_document_presence'
end

every 1.day, at: ['2:00 am', '1:00 pm'] do
  set :output, 'log/import_ddts_dhl.log'
  rake 'dhl:import_ddts'
end

every 1.day, at: ['3:00 pm', '8:00 pm'] do
  set :output, 'log/gamma_exchange_import.log'
  rake 'gamma_exchange:download_drop_pay_files'
end

every 1.day, at: ['10:00 am', '12:00 am', '2:00 pm', '4:00 pm'] do
  set :output, 'log/atono_check_authorization_statuses.log'
  rake 'atono:check_authorization_statuses'
end

every 1.day, at: ['5:00 pm'] do
  set :output, 'log/atono_drop_pay_handler.log'
  rake 'atono:drop_pay_handler'
end

every '0 2 * * 1' do
  set :output, 'log/atono_transactions_import.log'
  rake 'atono:transactions_import'
end

every '30 2 * * 7' do
  set :output, 'log/purge_orphaned_draft_customers.log'
  rake 'customers:purge_orphaned_draft_customers'
end

every '0 10-23 * * *' do
  set :output, 'log/unlock_missed_callbacks_phone_activations.log'
  rake 'phone_activations:unlock_missed_callbacks'
end

every '0 10-23 * * *' do
  set :output, 'log/clean_draft_and_expired_floa_financings.log'
  rake 'floa_financings:clean_draft_and_expired'
end

every 1.day, at: '2:30 am' do
  set :output, 'log/refresh_hybrid_activations_table.log'
  rake 'phone_activations:refresh_hybrid_activations_table'
end

every 1.day, at: '2:00 am' do
  command 'find /home/<USER>/reportistica -mtime +15 -type f -delete'
end

every 1.day, at: '1:20 am' do
  set :output, 'log/acea_importer.log'
  rake 'acea_importer:import_daily'
end

every 1.day, at: '1:40 am' do
  set :output, 'log/clean_unpaid_payment_requests.log'
  rake 'payment_requests:clean_unpaid'
end

every '1 0 1 * *' do
  set :output, 'log/clean_energy_cache_key.log'
  rake 'cache_keys:clean_energy'
end
