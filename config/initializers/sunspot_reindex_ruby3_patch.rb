Sunspot::Rails::Searchable::ClassMethods.module_eval do
  # ADDED "**" to find_in_batches
  def solr_index(opts={})
    options = {
      :batch_size => Sunspot.config.indexing.default_batch_size,
      :batch_commit => true,
      :include => self.sunspot_options[:include],
      :start => opts.delete(:first_id)
    }.merge(opts)

    if options[:batch_size].to_i > 0
      batch_counter = 0
      self.includes(options[:include]).find_in_batches(**options.slice(:batch_size, :start)) do |records|

        solr_benchmark(options[:batch_size], batch_counter += 1) do
          Sunspot.index(records.select(&:indexable?))
          Sunspot.commit if options[:batch_commit]
        end

        options[:progress_bar].increment!(records.length) if options[:progress_bar]
      end
    else
      Sunspot.index! self.includes(options[:include]).select(&:indexable?)
    end

    # perform a final commit if not committing in batches
    Sunspot.commit unless options[:batch_commit]
  end
end