Rails.application.config.after_initialize do
  Thredded::PostsController.class_eval do
    after_action :destroy_attachments, only: [:update]

    private

    def destroy_attachments
      post.attachments.where(id: post.destroy_attachments.split(',')).each do |attachment|
        attachment.purge
      end
    end
  end

  Thredded::Messageboard.class_eval do
    after_update  -> (messageboard) { remove_messageboard_group(saved_changes[:messageboard_group_id][0]) }, if: -> { saved_change_to_messageboard_group_id? }
    after_destroy -> (messageboard) { remove_messageboard_group(messageboard.messageboard_group_id) }

    def remove_messageboard_group(messageboard_group_id)
      return unless messageboard_group_id

      group = Thredded::MessageboardGroup.find(messageboard_group_id)
      group.destroy if group.messageboards.empty?
    end
  end

  Thredded::PostForm.module_eval do
    attr_accessor :destroy_attachments
  end

  Thredded::TopicForm.module_eval do
    attr_accessor :attachments, :destroy_attachments

    def initialize(params = {})
      @title = params[:title]
      @category_ids = params[:category_ids]
      @locked = params[:locked] || false
      @sticky = params[:sticky] || false
      @content = params[:content]
      @attachments = params[:attachments] || []
      @user = params[:user] || fail('user is required')
      @messageboard = params[:messageboard]
    end

    def post
      @post ||= topic.posts.build(
        content: content,
        attachments: attachments,
        user: non_null_user,
        messageboard: messageboard
      )
    end
  end

  Thredded::NewTopicParams.module_eval do
    protected

    def new_topic_params
      params
        .fetch(:topic, {})
        .permit(:title, :locked, :sticky, :content, attachments: [], category_ids: [])
        .merge(
          messageboard: messageboard,
          user: thredded_current_user,
          )
    end
  end

  Thredded::NewPostParams.module_eval do
    protected

    def new_post_params
      params.fetch(:post, {})
            .permit(:content, :quote_post_id, :destroy_attachments, attachments: [])
            .tap do |p|
        quote_id = p.delete(:quote_post_id)
        if quote_id
          post = Thredded::Post.find(quote_id)
          authorize_reading post
          p[:quote_post] = post
        end
      end
    end
  end

  Thredded::ContentFormatter.module_eval do
    # @param content [String]
    # @return [String] a quote containing the formatted content
    def self.quote_content(content)
      result = String.new(content)
      result = "<blockquote>#{result}</blockquote>"
      result
    end
  end

  Thredded::CreateMessageboard.module_eval do
    # @return [boolean] true if the messageboard was created and seeded with a topic successfully.
    def run
      Thredded::Messageboard.transaction do
        fail ActiveRecord::Rollback unless @messageboard.save
        true
      end
    end
  end

  Thredded::PostPolicy.module_eval do
    def create?
      !@user.is?(User::AGENT_ROLES)
    end

    def destroy?
      !@post.first_post_in_topic? && @user.is?(User::THREDDED_ADMIN_ROLES)
    end

    def update?
      own_post? || @user.is?(User::THREDDED_ADMIN_ROLES)
    end
  end

  Thredded::TopicPolicy.module_eval do
    def create?
      @messageboard_policy.post? && @user.thredded_admin?
    end
  end
end