it:
  activerecord:
    attributes:
      accounting_document:
        CLI_DDT: DDT (Documenti di trasporto)
        CLI_FTACC-CLI_FTDIFFD: Fattura
        CLI_FTACC: Fattura
        CLI_FTDIFFD: Fattura
        CLI_FTDIFFHQ: Fattura
        CLI_NCDIFFD: Nota di credito
        CLI_NCDIFFDR: Nota di credito
        CLI_PRENC: Pre nota di credito
        KOLME-PNCPROMO: Pre nota di credito
        DATADOC: Data documento
        FOR_FTPROV: Fatture provvigioni
        IMPONIBILE: Imponibile
        NUMDOC: Numero documento
        NUMREG: Numero documento
        TIPODOC: Tipo documento
        TOTALE: Totale
        dealer: Partner
      affiliation:
        affiliation_document: Documento
        agent: Agente
        agent_id: Agente
        agent_sub_agent_label: Agente/Sub-Agente
        ceo: Amministratore unico
        company_kind: Forma giuridica
        company_kinds/srl: Srl
        company_kinds/srls: Srls
        company_kinds/snc: Snc
        company_kinds/sas: Sas
        company_kinds/scrl: Scrl
        company_kinds/spa: Spa
        company_name_change: Cambio Ragione Sociale
        created_at: Data richiesta
        dealer_address: Indirizzo
        dealer_bank: Banca
        dealer_bank_branch: Nome della filiale della banca
        dealer_bank_address: Indirizzo della filiale della banca
        dealer_billing_address_city: Comune [Provincia]
        dealer_billing_street_type: Indirizzo (via)
        dealer_billing_street_type_id: Indirizzo (via)
        dealer_company_kind: Forma giuridica
        dealer_contact_address: Indirizzo
        dealer_contact_birth_city: Comune di nascita
        dealer_contact_birth_province: Provincia di nascita
        dealer_contact_birth_province_id: Provincia di nascita
        dealer_contact_city: Città/Provincia
        dealer_contact_city_id: Città/Provincia
        dealer_contact_email: Indirizzo email
        dealer_contact_full_address: Indirizzo completo di residenza
        dealer_contact_number: Numero civico
        dealer_contact_phone_number: Numero di cellulare
        dealer_contact_role: Qualifica
        dealer_contact_street_type: Indirizzo (via)
        dealer_contact_street_type_id: Indirizzo (via)
        dealer_contact_zip: CAP
        dealer_contact_roles/ceo: Amministratore unico
        dealer_contact_roles/legal_representative: Legale rappresentante
        dealer_contact_roles/owner: Titolare firmatario
        dealer_email: Email operativa
        dealer_operators: Operatore
        dealer_operators/windtre_very_mobile: Windtre + Very Mobile
        dealer_operators/very_mobile: Solo Very Mobile
        dealer_type: Tipologia Partner
        dealer_types/etnico: Etnico
        dealer_types/standard: Standard
        etnico: Etnico
        form_kinds/w3_scheda_affiliazione: Scheda Affiliazione Windtre
        form_kinds/w3_modulo_coordinate_bancarie: Modulo Coordinate Bancarie Windtre
        form_kinds/w3_dichiarazione_trasparenza: Dichiarazione di Trasparenza Windtre
        form_kinds/w3_scheda_informativa: Scheda Informativa
        form_kinds/w3_scheda_informativa_filename: Scheda Informativa
        form_kinds/dichiarazione_recipient: Dichiarazione del Percipiente
        form_kinds/dichiarazione_recipient_filename: dichiarazione_percipiente
        form_kinds/dichiarazione_flat_rate: Dichiarazione regime forfettario
        form_kinds/dichiarazione_flat_rate_filename: dichiarazione_regime_forfettario
        form_kinds/dichiarazione_minimum: Dichiarazione regime dei minimi
        form_kinds/dichiarazione_minimum_filename: dichiarazione_minimi
        form_kinds/id_documents: Documenti di identità
        form_kinds/id_documents_filename: documenti_di_identita
        form_kinds/kolme_scheda_affiliazione: Scheda Affiliazione Kolme
        form_kinds/kolme_modulo_sdd: Modulo SDD Kolme
        form_kinds/lettera_contatto_corriere: Lettera contatto corriere
        form_kinds/visura_camerale: Visura Camerale
        legal_representative: Legale rappresentante
        owner: Titolare firmatario
        second_dealer_contact: Socio 2 se con firma congiunta
        second_dealer_contact_first_name: Nome
        second_dealer_contact_last_name: Cognome
        second_dealer_contact_email: Indirizzo email
        source: Fonte
        sources/agent: Agente
        sources/business_name_change: Cambio Ragione Sociale
        sources/channel_change: Cambio canale
        sources/very_mobile_source: Very Mobile
        sources/website: Sito
        standard: Standard
        status: Stato
        statuses/completed: Completata
        statuses/in_progress: In corso
        statuses/rejected: Bocciata
        sub_agent: Sub-Agente
        sub_agent_id: Sub-Agente
        third_dealer_contact: Socio 3 se con firma congiunta
        third_dealer_contact_first_name: Nome
        third_dealer_contact_last_name: Cognome
        third_dealer_contact_email: Indirizzo email
        very_mobile: Solo Very Mobile
        very_mobile_source: Very Mobile
        warehouse_city: Comune [Provincia]
        warehouse_phone_number_note: Questo numero verrà riportato sul sito Windtre/Very Mobile
        warehouse_street_type: Indirizzo (via)
        warehouse_street_type_id: Indirizzo (via)
        windtre_very_mobile: Windtre + Very Mobile
        website: Sito
      affiliation/required_affiliation_documents:
        ko_reason: Motivazione
      affiliation/dealer:
        email_amministrativa: Email amministrativa
        pec: Pec
        telefono_amministrativo: Telefono della sede legale
      affiliation/dealer/dealer_contacts:
        date_of_birth: Data di nascita
        cellphone_1: Cellulare
        cf: Codice fiscale
        first_name: Nome
        last_name: Cognome
        number: Numero civico
      affiliation/additional_data/second_dealer_contact:
        first_name: Nome
        last_name: Cognome
        email: Indirizzo email
      affiliation/additional_data/third_dealer_contact:
        first_name: Nome
        last_name: Cognome
        email: Indirizzo email
      affiliation/dealer/warehouses:
        address: Indirizzo
        city: Comune/Provincia
        ensign: Insegna
        name: Ragione sociale
        number: Numero
        company_name: Denominazione CC
        opening_hours: Orari apertura
        affiliation_opening_hours: Orari negozio
        weekly_closing_day: Chiusura settimanale
        telefono1: Numero di telefono
        zip: CAP
      affiliation_document:
        approved: Approvato
        rejected: Da recuperare
      affiliation_outcome:
        affiliation_form: Invio Mod. Abilitazione
        affiliation_form_popover: Inserisci Invio Mod. Abilitazione
        affiliation_granted: Ricezione Abilitazione
        affiliation_granted_popover: Inserisci Ricezione Abilitazione
        affiliation_proposal: Proposta Affiliazione
        affiliation_proposal_popover: Inserisci Proposta Affiliazione
        approval: Approvazione affiliazione
        balance_feedback: Feedback Credito
        balance_feedback_popover: Inserisci Feedback Credito
        certification_invitation: Invito alla Certificazione
        certification_invitation_popover: Inserisci Invito alla Certificazione
        contract_delivery: Invio Contratto
        contract_delivery_popover: Inserisci Invio Contratto
        contract_signature: Firma Contratto
        contract_signature_popover: Inserisci Firma Contratto
        digital_signature: Stato Firma Digitale
        digital_signature_popover: Inserisci Firma Digitale
        documents_check: Controllo Documenti
        documents_check_popover: Visualizza documenti caricati
        female_ko: Bocciata
        female_ko_r: Sospesa
        female_ok: Approvata
        insert_affiliation: Inserimento Affiliazione
        male_ko: Bocciato
        male_ko_r: Sospeso
        male_ok: Approvato
        male_: Approvato
        notes: Note
        ok: Approvato
        private_notes: Note
        r_s_tre_code: Codice 8mld
        sales_admin: Invio Amm. Vendite
        sales_admin_popover: Inserisci Invio Amm. Vendite
        send_agent_sms: Invio sms agente
        status: Stato
        strategy_feedback: Feedback Strategy
        strategy_feedback_popover: Inserisci Feedback Strategy
        territory_feedback: Feedback Territorio
        territory_feedback_popover: Inserisci feedback territorio
        tre_code: Codice 9mld
        welcome_call: Welcome Call
        welcome_call_popover: Inserisci Welcome Call
        windtre_code: Ricezione 8/9mld
        windtre_code_popover: Inserisci Ricezione 8/9mld
      after_sale_thread:
        activated: Attivato
        added: Inserita
        all: Mostra tutto
        canceled: Annullata
        charge_kolme: In carico a Kolme
        charge_operator: In carico all'Operatore
        charge_partner: In carico al Partner
        closed: Chiusa
        created_at: Data richiesta
        created_at_from: Data richiesta da
        created_at_to: Data richiesta a
        customer_full_name: Cliente
        customer_name: Cliente
        days_after_last_operation: Ultima Operazione (Giorni)
        days_from_insertion: Inserimento (Giorni)
        dealer_contact_full_name: Partner
        dealer_name: Partner
        dealer_name_with_tre_code: Partner
        energy_contract_hex_id: ID contratto
        id: Id Richiesta
        open: Mostra aperte
        operator_name: Operatore
        phone_activation_hex_id: ID attivazione
        pre_sale: Pre Vendita
        progress: Avanzamento
        rechargeable: Ricaricabile
        sold: Venduto
        state: Stato
        state_placeholder: Tutti gli stati
        subscription: Abbonamento
        thread_kind: Tipologia richiesta
        thread_kind_class_name: Tipologia richiesta
        thread_kind_placeholder: Tutte le tipologie
        todo: Mostra da fare
        warehouse_name: Punto Vendita
      alert:
        archived: Archiviato?
        created_at: Data/ora creazione
        dealer: Partner
        dealer_id: Partner
        dealer_name: Nome partner
        imei_reservation: Lok-me
        message: Messaggio
        todo: TODO?
        updated_at: Data/ora modifica
        user: Archiviato da
        user_id: Archiviato da
        warehouse: Punto vendita
        warehouses: Punti vendita
        warehouse_id: Punto vendita
      application_setting:
        code: Codice
        description: Descrizione
        value: Valore
      appointment_detail:
        antenna_serial: Seriale Antenna
        appointment_date: Giorno dell'appuntamento
        appointment_datetime: Appuntamento
        customer: Cliente
        customer_preferences: Preferenze del cliente
        dealer: Partner
        dealer_id: Installatore
        failed_operation_status/ko: Installazione KO
        failed_operation_status/suspend: Sospensione on-field
        installation_address: Indirizzo Installazione
        installation_fee: Compenso Installazione
        installation_kind: Tipologia
        installation_kind/activation: Attivazione
        installation_kind/activation_rescheduled: Attivazione Desospesa
        installation_kind/assurance: Assurance
        installation_kind/post_delivery: Post Delivery
        location_city: Comune
        modem_serial: Seriale Modem
        notes: Note installazione
        np_x_order_code: Codice Ordine NP-X
        operation_kind: Tipo
        operation_kind_show: Tipologia Intervento
        operation_kind/customer_cancel: Annullato
        operation_kind/failed: Uscita a vuoto
        operation_kind/roof: Tetto
        operation_kind/thermal_coat: Cappotto termico
        operation_kind/wall: Parete
        phone_activation_activated_at: Data assegnazione
        phone_activation_activated_at_show: Data attivazione su Pos EVO
        phone_activation_hex_id: ID Attivazione
        phone_activation_id: RIF.
        phone_number: Recapito telefonico cliente per installazione
        pin: Pin IV Referente
        reassignment_history: Storico riassegnazioni
        sim_serial: Seriale SIM
        status: Stato
        status/completed: Installazione completata
        status/confirmed: Appuntamento confermato
        status/customer_cancel: Annullato
        status/draft: Inserimento PosEvo in corso
        status/ko: Installazione KO
        status/modified: Appuntamento modificato
        status/reschedule: Appuntamento da spostare
        status/set: Appuntamento fissato
        status/dummy_set: Appuntamento fittizio
        status/suspend: Sospensione on field
        status/unset: Installata da System
        status/void: Attivazione KO
        status/waiting: In attesa accettazione
        status/waiting_appointment: In attesa appuntamento
        support_kind: Tipo assistenza
        support_kind/fix: Fix
        support_kind/materials_replace: Sostituzione materiali
        support_kind_note: Note
        system_name: System
        time_slot: Fascia oraria indicativa
        unable_to_proceed: "Non posso procedere a questa installazione.<br/><strong>NB: Verrà assegnata ad un altro installatore della zona che procederà a contattare il cliente</strong>"
        wished_appointment_date: Appuntamento desiderato
        wished_time_slot: Fascia oraria desiderata
      ast_additional_documents_upload:
        add_attachment: Allega il documento
        attachment: Documento allegato
        cause: Motivazione
        customer_company_report: Visura camerale cliente
        customer_document: Documento di identità cliente
        notes: Note
        operator_invoice: Fattura operatore di provenienza
        other_documents: Altri documenti richiesti
      ast_additional_information:
        add_attachment: Allega il documento
        notes: Note
      ast_ask_id_request:
        ask_id: ID ASK
        attachment: Allega la PDC se attivato su Pos Evo
        ko: KO
        request_id: ID segnalazione / ID ASK
        request_outcome: Esito segnalazione
        request_phone_id: ID segnalazione telefonica
        requested_at: Data apertura segnalazione Dealer Care
        unknown: Non gestito
      ast_company_report_issue:
        company_name: Ragione sociale
        notes: Note
        vat: Partita IVA
      ast_convergence_rebuild:
        convergence_fixed_number: Numero Fisso oggetto di convergenza
        convergence_mobile_number: Numero Mobile oggetto di convergenza
        notes: Note
        request_kind: Tipologia richiesta
        request_kind/energy: Energia
        request_kind/not_energy: Non energia
        traffic_share_mobile_numbers: Numeri Mobile condivisione giga
      ast_credit_remaining_replacement:
        name: Nome e cognome subentrante
        fiscal_code: Codice Fiscale subentrante
        phone_number: Numero telefonico subentrante
        add_attachment: Allega i documenti richiesti
        attachments: Documenti allegati
        notes: Note
      ast_customer_administrative_complaint:
        notes: Note
      ast_easy_pay_replacement:
        add_attachment: Allega i documenti richiesti
        attachments: Documenti allegati
        replacement_number: Numerazione oggetto di Subentro
        originator_name: Nome e Cognome/Ragione sociale cedente
        assignee_name: Nome e Cognome/Ragione sociale cessionario
        notes: Note
      ast_edit_payment_type:
        credit_card_cvv: Codice CVV
        credit_card_expire_month: Mese di scadenza
        credit_card_expire_year: Anno di scadenza
        credit_card_number: Numero carta di credito
        credit_card_type: Tipo carta di credito
        iban: IBAN
        notes: Note
        operator_name: Operatore
        parsed_iban: IBAN spezzato
        payment_method: Metodo di pagamento
        phone_number: Numero di telefono
      ast_energy_contract:
        add_attachment: Allega il documento
        notes: Note
      ast_fee_verification:
        attachments: Allegati
      ast_fixed_warning:
        add_attachment: Allega modulo malpractice per segnalazione specifica
        attachment: Documento allegato
        request_kind: Tipologia richiesta
        request_kind/malpractice: Segnalazione Malpractice
        request_kind/fault: Segnalazione Guasto (linea degradata, mancanza segnale)
        request_kind/provisioning_progress: Richiesta avanzamento provisioning (dopo 15gg dall’inserimento)
        notes: Note
      ast_luce_gas:
        notes: Note
      ast_ndc_check:
        activated_at: Data di attivazione
        cessione_del_credito: Cessione del Credito
        nota_di_credito_per_bonus_fibra: Nota di Credito per Bonus Fibra
        nota_di_credito_per_delta_prezzo: Nota di Credito per Delta Prezzo
        nota_di_credito_promo_device: Nota di Credito Promo Device
        request_kind: Tipologia di richiesta
        rimborso_per_bonus_fibra_e_tablet: Rimborso per Bonus Fibra e Tablet
        serial: IMEI/Seriale da verificare
      ast_number_portability:
        accountholder_birth_country_id: Nazione di nascita
        accountholder_birth_country_name: Nazione di nascita
        accountholder_birth_date: Data di nascita
        accountholder_birth_place: Luogo di nascita
        accountholder_cf_or_iva: Codice Fiscale o (Partita IVA)
        accountholder_first_name: Nome
        accountholder_id_doc_date: Data rilascio documento
        accountholder_id_doc_kind: Tipo documento d'identità
        accountholder_id_doc_number: Numero documento
        accountholder_last_name: Cognome (o Ragione Sociale)
        contract_kinds:
          subscription: Abbonamento
          rechargeable: Ricaricabile
        credit_transfer: Trasferimento del credito
        custom_source_operator: Nome operatore di provenienza
        customer_uniq_name: Cliente
        different_accountholder: Intestatario della linea diverso dal richiedente
        formatted_target_sim_serial: Seriale SIM su cui portare
        formatted_source_sim_serial: Seriale SIM da portare
        notes: Note
        operator: Operatore
        source_contract_kind: Tipologia contratto operatore di provenienza
        source_number: Numero da portare
        source_portability_operator: Operatore di provenienza
        source_sim_serial: Seriale SIM da portare
        target_number: Numero su cui portare
        target_sim_serial: Seriale SIM su cui portare
      ast_return_request:
        activated: Attivato
        attachment: Copia dello scontrino di vendita
        formatted_pickup_date: Data di ritiro preferita
        item_serial: IMEI
        max_usage_time_acknowledged: Minutaggio di utilizzo inferiore ai 15 minuti
        notes: Note
        operator: Operatore
        pickup_date: Data di ritiro preferita
        pickup_warehouse_name: Punto vendita
        pre_sale: Pre Vendita
        product: Prodotto
        product_name: Prodotto
        product_state: Tipo
        return_kind_description: Tipo di guasto
        serial: Imei articolo
        sold: Venduto
      ast_promo_entry_failure:
        promo_name: Indica la promozione da applicare
        notes: Note
      ast_provisioning:
        add_attachment: Allega il documento
        notes: Note
      ast_sim_replacement:
        customer_full_name: Cliente
        notes: Note
        operator: Operatore
        operator_name: Operatore
        sim_serial: Seriale SIM sostitutiva
        source_number: Numero da sostituire
      ast_unsolved_payment_proof:
        cro: CRO
        notes: Note
        payment_method: Metodo di pagamento
        phone_number: Numero di telefono
        unsolved_amount: Importo insoluto
        vcy: VCY (codice a quattro cifre presente sul bollettino postale)
      ast_w3_mnp_resubmission:
        request_kind: Tipologia richiesta
        request_kind/failed_mnp_resubmission: Risottomissione MNP fallita
        request_kind/active_number_mnp: MNP su numero attivo
        source_number: Numero da portare
        source_sim_serial: Seriale SIM da portare
        source_portability_operator: Operatore di provenienza
        notes: Note
      ast_w3_very_sim_replacement:
        otp_phone_number: Numero telefonico da sostituire
        sim_id: ICCID Sim Sostitutiva
        request_kind: Tipologia richiesta
        request_kind/upgrade: Upgrade
        request_kind/unreadable_sim_id: ICCID non leggibile
        request_kind/broken_sim: Sim guasta
        request_kind/theft_or_loss: Furto o smarrimento
        add_attachment: Allega i documenti richiesti
        attachments: Documenti allegati
        notes: Note
      ast_waiver_or_stash:
        notes: Note
      ast_wind_ngpos_malfunctioning:
        activation_type: Tipologia attivazione
        anagrafica_cliente: Inserimento/modifica anagrafica cliente
        attachment: Allega almeno una schermata in cui si visualizzi l'errore
        browser: Browser utilizzato
        customer_cf: Codice Fiscale o Partita IVA
        customer_type: Tipologia Cliente
        failing_operation: Operazione non riuscita
        failure_occurred_at: Data
        failure_occurred_at_date: Data
        failure_occurred_at_time: Ora
        inserimento_sim: Inserimento SIM
        internet_explorer: Internet Explorer
        mac_os: MacOS
        mnp: Inserimento portabilità (MNP)
        modalita_pagamento: Inserimento modalità di pagamento
        os: Sistema Operativo utilizzato
        selezione_offerta: Selezione Offerta
        upload_documenti: Upload documenti
        windows_7: Windows 7
        windows_8: Windows 8
        windows_10: Windows 10
        windows_xp: Windows XP
      ast_wrong_offer_activation:
        add_attachment: Allega la PDC quando previsto
        attachment: Documento allegato
        plan_name: Offerta da attivare
        phone_imei: IMEI Telefono incluso da disattivare
        notes: Note
      blocked_payment_method:
        error_message: "Messaggio di errore"
        operators: "Operatori"
        internal_notes: "Note interne"
        payment_method_id: "Metodo di pagamento"
        offer_categories: "Tipologia offerta"
        service_kinds: "Tipologia di Servizio (Tariffa)"
      bundle_items:
        agent_profit_a: Margine Ag a
        agent_profit_b: Margine Ag b
        agent_profit_c: Margine Ag c
        dealer_price_vat_excluded_a: Prezzo al partner a
        dealer_price_vat_excluded_b: Prezzo al partner b
        dealer_price_vat_excluded_c: Prezzo al partner c
        item: Il componente del bundle
        quantity: Quantità dei componenti
      chat_transcription:
        date: Data
        dealer: Partner
        dealer_contact: Contatto
        dealer_contact_full_name: Contatto del Partner
        dealer_ip: Dealer IP
        dealer_name: Nome Partner
        duration: Durata
        internal_user: Operatore
        internal_user_full_name: Utente
      city:
        code: Codice città
      cluster_option:
        estera-generica: Estera generica
        estera-specifica: Estera specifica
        eta: Età
        eta-doppio-cluster: Età (doppio cluster)
        eta-e-nazionalita: Età e Nazionalità
        nazionalita: Nazionalità
        standard: Standard
      cms_post:
        bw_file: Stampa in bianco e nero
        color_file: Stampa a colori
        kolme_master_visibility: 'Visibile a:'
        kolme_master_visibility/hidden: Non Kolme Master - Nexus
        kolme_master_visibility/exclusive: Kolme Master - Nexus
      cms_template:
        code: Codice template CMS
      commercial_promotion:
        product_code: Codice prodotto
        image: Immagine 750x500 pixel
      company_brand:
        code: Codice marchio
        easycare_enabled: Abilitato EasyCare+
        floa_commissions_checkbox: Floa - sconto commissione
        floa_commissions: Sconto percentuale commissione
        sap_code: Codice SAP
      contract_term:
        document: File
        kind: Tipo
        kind/: Manuale
        kind/required: Obbligatorio
        md_very_enabled: Presente in contratto More
        title: Titolo
        visible: Visibile
      compass_branch:
        name: Nome
        address: Indirizzo filiale
        city: Comune
        phone_number: Recapito filiale
        visible: Visibile
      contract_kind:
        code: Codice tipo contratto
        created_at: Data/ora creazione
        description: Descrizione
        updated_at: Data/ora modifica
      cost_matrix:
        created_at: Data/ora creazione
        delete_and_add: Cancella e aggiungi
        kind:
          monthly: Matrice canoni
          upfront: Matrice anticipi
        monthly: Canone mensile
        monthly_variation: Canone mensile SDD (se diverso)
        plan: Tariffa
        plan_id: Tariffa
        product: Prodotto
        product_id: Prodotto
        update_and_add: Sovrascrivi e aggiungi
        updated_at: Data/ora modifica
        upfront: Anticipo
        upfront_variation: Anticipo SDD (se diverso)
      country:
        code: Codice paese
        description: Descrizione
      coverage_technology:
        service_value: Tecnologia
        value: Copertura
        visible: Visibile
        coverage_typology: Tecnologia tariffa
      credit_card_kind:
        code: Codice tipo di carta
      customer:
        acea_ateco: Codice Ateco
        address: Indirizzo
        address_street_name: Indirizzo - Indirizzo
        address_street_type: Indirizzo - Particella toponomastica
        altro: Altro
        birth_country: Nazione di nascita
        birth_country_id: Nazione di nascita
        birth_date: Data di nascita
        birth_place: Luogo di nascita
        birth_province: Provincia di nascita
        birth_province_id: Provincia di nascita
        business_name: Denominazione
        cadrastalcode: Codice catastale
        cf: Codice fiscale
        cf_or_vat: C.F. o P.IVA
        city: Comune
        city_and_province: Comune [Provincia]
        company_cf: Codice fiscale azienda
        company_email: Email Azienda
        company_kind: Forma giuridica
        company_name: Ragione sociale
        country: Nazione
        country_id: Nazione
        created_at: Data/ora creazione
        customer_kind: Tipo cliente
        doc_issued_by: Rilasciato da ente
        email: Indirizzo email
        email_form: Indirizzo email
        first_name: Nome
        fiscal_code: Codice fiscale
        gender: Genere
        gender/F: Femmina
        gender/M: Maschio
        headquarters_address: Indirizzo - sede legale
        headquarters_address_street_name: Indirizzo sede legale
        headquarters_address_street_type: Particella toponomastica sede legale
        headquarters_cadrastalcode: Codice catastale - sede legale
        headquarters_city: Comune - sede legale
        headquarters_number: Numero - sede legale
        headquarters_province: Provincia - sede legale
        headquarters_province_id: Provincia - sede legale
        headquarters_zip: CAP - sede legale
        id_doc_date: Data rilascio documento
        id_doc_expire_date: Data scadenza documento
        identity_document_kind: Tipo documento
        identity_document_kind_id: Tipo documento
        id_doc_kind: Tipo documento
        id_doc_number: Numero documento
        id_doc_province: Provincia rilascio
        id_doc_province_id: Provincia rilascio
        id_doc_country: Nazione emissione
        id_doc_country_id: Nazione emissione
        last_name: Cognome
        marketing_communications_agreed: Consensi trattamento dati personali
        marketing_communications_agreed_at: Data consenso comunicazioni commerciali
        marketing_communications_agreed_no: Nega il consenso
        marketing_communications_agreed_yes: Presta il consenso
        marketing_communications_expire_at: Data scadenza autorizzazione consenso
        mobile_phone: Telefono mobile
        nationality: Cittadinanza
        number: Numero
        pec: PEC
        province: Provincia
        province_id: Provincia
        sas: Sas - Società in Accomandita Semplice
        sc: Sc - Società Cooperativa
        scrl: Scrl - Società Cooperativa a Responsabilità Limitata
        sdi_forwarder_code: Codice destinatario SDI (facoltativo)
        senza_fine_di_lucro: Onlus - Senza Fini Di Lucro
        snc: Snc - Società in Nome Collettivo
        spa: Spa - Società Per Azioni
        srl: Srl - Società a Responsabilità Limitata
        srls: Srls - Società a Responsabilità Limitata Semplificata
        uniq_cf: CF Univoco
        uniq_id: Id Univoco
        uniq_name: Nome Univoco
        uniq_pi: PI Univoco
        updated_at: Data/ora modifica
        uuid: Codice UUID customer
        vat: Partita IVA
        vulnerable: Vulnerabile
        windtre_data_enrichment_agreed: WINDTRE - Arricchimento
        windtre_data_transfer_agreed: WINDTRE - Trasferimento
        windtre_geolocation_agreed: WINDTRE - Geolocalizzazione
        windtre_marketing_communications_agreed: WINDTRE - Comunicazioni commerciali
        windtre_profiling_agreed: WINDTRE - Profilazione
        zip: CAP
      customer_kind:
        code: Codice customer_kind
        created_at: Data/ora creazione
        description: Descrizione
        updated_at: Data/ora modifica
        code/pg: Persona Giuridica
        code/tu: Turista
        code/pf: Persona Fisica
        code/di: Ditta Individuale
      dealer:
        all: Tutti
        agent_note: Note Agente
        assigned_fido: Fido assegnato
        ateco_code: ATECO
        billing_address_cadrastalcode: Codice Catastale
        billing_address_city: 'Comune [Provincia]'
        billing_address_number: Numero
        billing_address_postalcode: CAP
        billing_address_province: Provincia
        billing_address_via: Via
        cf_piva: P.Iva/ C.F.
        city: Comune
        codice_fiscale: Codice Fiscale
        codice_pos: Codice POS
        codice_pos_attivazione: Codice POS Attivazione
        cognome_di: Cognome ditta individuale
        contratto: Contratto
        controllante: Controllante
        country_id: Nazione
        created_at: Data/ora creazione
        credit: Credito
        date_entered: Data creato
        dealer_operators: Operatori abilitati
        dealer_payment_methods: Metodi di pagamento su attivazione abilitati
        delta_assigned_fido: Variazione Fido
        disabled: Disattivo
        ditta_individuale: Ditta individuale?
        documento_di_identita: Documento di Identità
        enabled_for_chat: Abilitato assistenza via chat
        end_month_transfer: Da bonificare a fine mese
        extra_fido: Extra Fido
        fax: Fax
        fido: Fido
        floa_commissions_checkbox: Floa - sconto commissione
        floa_commissions: Sconto percentuale commissione
        floa_registered: Censito FLOA
        fonte_del_lead: Fonte del Lead
        foreign_city: 'Città [Estero]'
        franchising: Windtre Diretto
        fwa_installer_only: FWA - Installatore Puro
        fwa_installer_status: Stato Installatore
        fwa_installer_status/active: Attivo
        fwa_installer_status/awaiting_documents: Attesa documenti
        fwa_installer_status/awaiting_partial_validation_documents: Attesa validazione parziale documenti
        fwa_installer_status/awaiting_validation_documents: Attesa validazione documenti
        fwa_installer_status/partially_active: Parzialmente attivo
        fwa_installer_status/ready: Pronto
        gallery_gd: Gallery GD
        gamma_payment_method: Metodo di pagamento
        gamma_payment_method_id: Metodo di pagamento
        gamma_code_gen: Gamma Generale
        gamma_code_cli: Gamma Cliente
        gamma_code_for: Gamma Fornitore
        generic: Generico
        iban: IBAN
        insegna: Insegna o C/O
        intermediate_code: Codice intermedio Gamma
        iva_invito_fatturare: IVA applicata
        jackpotapple: Jackpotappl
        kind: Tipologia
        kit_di_benvenuto: Kit di Benvenuto
        klab: Klab
        kolme_agent: Agente Kolme
        kolme_master: Kolme Master - Nexus
        last_balance_at: Data ultimo bilancio
        last_balance_revenue: Fatturato ultimo bilancio
        operator_tls: Operatore TLS
        malus_installer: Malus Installatore
        mandatory_mnp: MNP Obbligatoria
        md_very: More (Master Dealer Very)
        md_very_show: More
        mdv_affiliation_status: Stato Affiliazione More
        mdv_affiliation_status/awaiting_documents: In attesa documenti
        mdv_affiliation_status/awaiting_validation_documents: Documenti da validare
        mdv_affiliation_status/awaiting_digital_signature: In attesa firma digitale
        mdv_affiliation_status/affiliated: Affiliato
        name: Partner
        nome_di: Nome ditta individuale
        numero_contratto: Numero Contratto
        orari: Orari
        partita_iva: Partita Iva
        payments_with_credit: Pagamenti con credito?
        prejudicial: Contenzioso
        price_list_category: Tipologia Listino
        private_note_kolme: Note Kolme
        previous_vat: Partita IVA Precedente
        protested: Protesti
        province: Provincia
        ragione_sociale: Ragione Sociale
        recharges_credit_available: Credito ricariche disponibile
        regione: Regione
        reverse_charge_enabled: Abilitato Reverse Charge 17RC
        role: Ruolo
        r_s_tre_code: Codice 8mld
        r_s_wind_code: Codice R.S.Wind
        sap_business_partner_taxes: Sap - Codici/Valori paese
        sap_business_partner_taxes_bp_type: Sap - Codice paese
        sap_business_partner_taxes_bp_number: Sap - Valore paese
        sap_internal_bank_institute_id: Banca Interna
        sap_customer_payment_kind_id: Metodo di pagamento Cliente
        sap_customer_payment_term_id: Condizioni di pagamento Cliente
        sap_supplier_payment_kind_id: Metodo di pagamento Fornitore
        sap_supplier_payment_term_id: Condizioni di pagamento Fornitore
        sap_withholding_tax_id: Ritenuta Acconto
        second_last_balance_at: Data penultimo bilancio
        second_last_balance_revenue: Fatturato penultimo bilancio
        segnalato_kolme: Segnalato Kolme
        sdi_forwarder_code: Codice destinatario SDI
        shield: Scudo
        shipping_address_city: Ind.Sped. Città
        shipping_address_co: 'Ind.Sped.: Presso'
        shipping_address_number: 'Ind.Sped.: Numero'
        shipping_address_postalcode: 'Ind.Sped.: CAP'
        shipping_address_province: 'Ind.Sped.: Provincia'
        shipping_address_via: 'Ind.Sped.: Via'
        statement_mail_to: Ricevi l'estratto conto all'indirizzo
        superdealer_id: Superdealer
        swift_code: Codice SWIFT
        tax_regime: Regime fiscale
        not_interested_in_energy_and_gas: Non interessato Luce & Gas
        tax_regimes/flat_rate: Forfettario
        tax_regimes/minimum: Minimi
        tax_regimes/regular: Ordinario
        tax_regimes/recipient: Percipiente
        telefono1: Telefono 1
        telefono2: Telefono 2
        telefono3: Telefono 3
        third_party_installations_disabled_until_at: Fino al
        tipo_gara: Tipo gara
        trustpro_recovery_email: Email recupero password Trustpro
        trustpro_user_id: User ID Trustpro
        updated_at: Data/ora modifica
        uuid: Codice UUID partner
        visura: Visura
        website: Sito web
        wind_tre_supplier_code: Codice Fornitore WINDTRE
        windtre_ecommerce: E-commerce Windtre
      dealer_category:
        code: Codice categoria partner
      dealer_contact:
        access_activations: Attivazioni
        access_admin: Amministrazione
        access_disable_download_operations: Limita operazioni
        access_floa_financings: Vendita in 3/4 rate
        access_fwa_installer: Installazioni FWA
        access_orders: Ordini
        access_device_sales: Vendita Device
        access_recharges: Ricariche
        access_imei_reservations: Lok-me
        can_activate_simulation_plans: Tester
        cellphone_1: Cellulare 1
        cellphone_2: Cellulare 2
        cellphone_otp: Cellulare per OTP e firma digitale
        cf: Codice Fiscale
        created_at: Data/ora creazione
        date_of_birth: Data di nascita
        dealer: Partner associato
        dealer_id: Partner
        dealer_name: Nome del partner
        email: Email (username)
        Finanziamenti: Resolvo
        first_name: Nome
        flag_activations: Operative
        flag_mtkg: Marketing
        flag_orders: Ordini
        flag_rewards: Amministrative
        fwa_installer_phone_number: Numero di telefono Windtre
        fwa_installer_email: E-Mail Installatore
        fwa_installer_warehouse_id: Punto Vendita di Riferimento
        Installazioni FWA: Installazioni FWA
        last_activity: Ultima attività (audit)
        last_name: Cognome
        legal_representative: Legale rappresentante
        note: Note Kolme
        password_changed_at: Ultimo cambio password
        phone: Telefono
        phone_1: Telefono 1
        phone_2: Telefono 2
        portal_enabled: Utente attivo
        Prenotazioni: Lok-me
        primary_contact: Contatto principale
        show_only_collection: Mostra solo Partner in collection
        show_disabled: Mostra anche i disattivi
        system_name: System di Riferimento
        tls_primary_contact: Team Leader
        updated_at: Data/ora modifica
        user_id: Utente
        uuid: Codice UUID contatto del partner
      dealer_operator:
        status: Stato
      destination:
        code: Codice destinazione
      document:
        contract_signature_log: Audit firma digitale
        document_name: Nome del documento
        expires_at: Data di scadenza
        notes: Note
        page_title_upload: Caricamento documenti e PDC
        page_title_upload_mnp: Caricamento documenti e PDC
        page_title_upload_mnp_different_owner: Caricamento documenti e PDC
        page_title_upload_financing: Upload documenti Compass
        page_title_upload_pda: Caricamento PDC
        page_title_upload_pdc: Upload PDC ufficiale firmata
        page_title_upload_report: Caricamento verbale di installazione
        page_title_upload_serial_in_use: Carica la PDC con il seriale utilizzato oppure segnala il ko
        upload_dealer_documents: Documenti del partner
        upload_financing_phone_activation_documents: Documenti Compass
        upload_fwa_document_content_company_registration_report: Visura Camerale aggiornata
        upload_fwa_document_content_company_registration_report_di: Visura Camerale aggiornata
        upload_fwa_document_content_identity_document: Documento di identità del Legale Rappresentante
        upload_fwa_document_content_identity_document_di: Documento di identità del Titolare
        upload_fwa_document_content_certificate_of_competence: Autocertificazione di Idoneità Tecnico Professionale
        upload_fwa_document_content_certificate_of_competence_di: Autocertificazione di Idoneità Tecnico Professionale
        upload_fwa_document_content_durc: DURC
        upload_fwa_document_content_durc_di: DURC
        upload_fwa_document_content_insurance: Assicurazione Civile (RCTP) con relativa quietanza (RTCQ)
        upload_fwa_document_content_insurance_di: Assicurazione Civile (RCTP) con relativa quietanza (RTCQ)
        upload_fwa_document_content_work_at_height_certificate: DVR
        upload_fwa_document_content_work_at_height_certificate_di: Attestato di lavori in quota
        upload_fwa_document_content_rco: RCO
        upload_fwa_document_content_rco_di: RCO
        upload_fwa_kolme_document_content_cciaa: CCIAA con nulla osta fallimentare
        upload_fwa_kolme_document_content_company_registration_report: Visura Camerale aggiornata
        upload_fwa_kolme_document_content_durc: DURC
        upload_fwa_kolme_document_content_identity_document: Documento di identità del Legale Rappresentante
        upload_fwa_kolme_document_content_insurance: Assicurazione Civile (RCTP) con relativa quietanza (RTCQ)
        upload_fwa_kolme_document_content_w3_subcontracting_auto_certificate: Autocertificazione Subappalto Windtre
        upload_fwa_kolme_document_content_w3_subcontracting_transparent_statement: Dichiarazione Trasparenza Subappalto Windtre
        upload_fwa_kolme_document_content_work_at_height_certificate: DVR (o attestato lavori in quota)
        upload_mdv_document_content_company_registration_report: Visura camerale ordinaria
        upload_mdv_document_content_identity_document: Documento d'identità
        upload_mdv_document_content_upload_company_registration_report: Carica la tua Visura Camerale Ordinaria
        upload_mdv_document_content_upload_identity_document: Carica il tuo documento d'identità
        upload_mdv_document_content_contract: Contratto firmato
        upload_mdv_document_content_contract_signature_log: Audit firma digitale
        upload_mdv_document_dealer_mdv_affiliation_documents: Documento di affiliazione
        upload_mnp_phone_activation_contract: Contratto Firmato (PDC)
        upload_mnp_phone_activation_documents: Documenti cliente
        upload_mnp_phone_activation_sim_card: SIM
        upload_mnp_different_owner_phone_activation_contract: Contratto Firmato (PDC)
        upload_mnp_different_owner_phone_activation_documents: Documenti Cliente
        upload_mnp_different_owner_phone_activation_sim_card: SIM
        upload_mnp_different_owner_phone_activation_different_owner_mnp_documents: Diverso intestatario MNP
        upload_official_pda_documents: PDC Ufficiale
        upload_order_documents: Evidenza di pagamento
        upload_pda_phone_activation_contract: Contratto Firmato (PDC)
        upload_pdc_phone_activation_documents: Pdc ufficiale Firmata
        upload_phone_activation_contract: Contratto Firmato (PDC)
        upload_phone_activation_documents: Documenti cliente
        upload_phone_activation_sim_card: SIM
        upload_phone_activation_different_owner_mnp_documents: Diverso intestatario MNP
        upload_wind_ngpos_activation_contract: Contratto Firmato (PDC)
        upload_wind_ngpos_activation_documents: Documenti cliente
        upload_report_phone_activation_documents: Verbale di installazione
        upload_serial_in_use_phone_activation_documents: Carica la PDC
        upload_sim_phone_activation_sim_card: Carica la foto della SIM
      easycare_price_range:
        from: Da
        to: A
        kolme_price: Costo a Kolme
        dealer_price: Costo al Partner
        customer_price: Costo al pubblico
        agent_profit: Margine AM
      energy_contract:
        address: Indirizzo
        city: Comune
        gas_cancellation_total: Importo Storno Gas
        gas_cancellation_date: Data Storno Gas
        gas_cancellation_cause: Causale Storno Gas
        energy_cancellation_total: Importo Storno Luce
        energy_cancellation_date: Data Storno Luce
        energy_cancellation_cause: Causale Storno Luce
        contact_email: E-mail
        contact_mobile_phone: Cellulare
        extra_reward: Extra compenso
        number: Numero Civico
        pdr_code: Codice PDR
        pod_code: Codice POD
        pdc_code: Codice Contratto
        resend/fifteen_minutes: 'Sarà possibile reinviare il contratto trascorsi 15 minuti dal precedente invio. In alternativa, modifica il numero di telefono per reinviare il contratto immediatamente.'
        resend/next_day: 'Sarà possibile reinviare il contratto da domani mattina alle 8:00. In alternativa, modifica il numero di telefono per reinviare il contratto immediatamente.'
        resend/update_number: 'Numero di tentativi esaurito, aggiornare il numero per un nuovo invio'
        total_reward: Compenso
        warehouse_id: Punto vendita
        zip_code: CAP
      energy_contract/state:
        activated: Attivata
        added: Inserita
        cancelled: Annullata
        multiple_states: Stati multipli
        send_to_acea: Attesa invio ad ACEA
        to_recover: Da Recuperare
        waiting_for_acea: Attesa Sistemi Acea
        waiting_for_customer_signature: Attesa firma cliente
        check_in_progress: In verifica
        ko: KO
        activation_in_progress: In attivazione
      energy_contract_energy_plan/state:
        activated: Attivata
        added: Inserita
        cancelled: Annullata
        waiting_for_acea: Attesa Sistemi Acea
        to_recover: Da Recuperare
        waiting_for_customer_signature: Attesa firma cliente
        check_in_progress: In verifica
        ko: KO
        activation_in_progress: In attivazione
      energy_contract_payment:
        different_owner_birth_date: Data di nascita
        different_owner_birth_place: Città di nascita
        different_owner_birth_province: Provincia di nascita
        different_owner_cf: Codice fiscale
        different_owner_email: Email
        different_owner_first_name: Nome
        different_owner_last_name: Cognome
        different_owner_mobile_phone: Numero di cellulare
      energy_contract_state_matrix:
        canc_reason_opp: cancReasonOpp
        canc_status_reason: cancStatusReason
        kolme_master_message: Messaggio per Kolme Master - Nexus
        message: Messaggio
        otp_status: otpStatus
        pdc_stage: pdcStage
        position: ordinamento
        precheck: precheck
        progress: Avanzamento
        progress_status: progressStatus
        status: Stato
      energy_plan:
        plan_kind: "Tipo Tariffa"
        visibility: "Visibilità"
        position: "Ordinamento Tariffa"
        name: "Nome Tariffa"
        subtitle: "Sottotitolo Tariffa"
        product_code: "Codice Prodotto"
        destination: "Tipo d'uso"
        windtre_customer: "Cliente Windtre"
        price_rate: "Prezzo"
        plan_category: "Categoria Tariffa"
        pun_f1: "PUN F1"
        pun_f1_time_slot_description: Dal Lunedi al Venerdi dalle 08:00 alle 19:00, festivi esclusi.
        pun_f2: "PUN F2"
        pun_f2_time_slot_description: Dal Lunedì al Venerdì dalle 07:00 alle 08:00 e dalle 19:00 alle 23:00, Sabato dalle 07:00 alle 23:00.
        pun_f3: "PUN F3"
        pun_f3_time_slot_description: Dal Lunedì al Sabato dalle 23:00 alle 07:00, Domenica e festivi tutto il giorno.
        psv: "PSV"
        reference_month_indices: "Mese di riferimento Indici"
        spread: "Spread (Corrispettivo al consumo)"
        fixed_fee: "Contributo Fisso"
        description: "Descrizione Tariffa"
        energy_subtitle: "Sottotitolo prezzo della componente energia"
        gas_subtitle: "Sottotitolo prezzo della componente gas"
        fixed_fee_subtitle: "Sottotitolo contributo fisso"
        destination/domestic: Domestico
        destination/other: Altri Usi
        locked_spread_months_count: Numero mesi Spread bloccato
        plan_category/one_hourly_rate: Monoraria
        plan_category/two_hourly_rate: Bioraria
        plan_category/three_hourly_rate: Trioraria
        plan_kind/gas: Gas
        plan_kind/energy: Luce
        price_rate/fixed: Fisso
        price_rate/variable: Variabile
        visibility/visible: Visibile
        visibility/not_visible: Non visibile
        visibility/visible_only_internal_users: Solo Interni
        visible_only_internal_users: Solo Interni
        windtre_customer/all: Tutti
        windtre_customer/already_customer: Già Cliente Windtre
        windtre_customer/no_windtre_customer: Non Cliente Windtre
      energy_supply_detail:
        destination: Uso
        power_used: Potenza impegnata
        reference_market: Mercato di riferimento
        supply_voltage: Tensione di Alimentazione
        tisg_category: Categoria TISG
        use_category: Categoria d'uso
        withdrawal_class: Classe Prelievo
      error_log:
        gamma_get_pdf: 'GETPDF - Validazione - '
        gamma_get_pdf_exc: 'GETPDF - Eccezione - '
        gamma_reganag: 'REGANAG - Validazione - '
        gamma_ven_ord: 'REGDOCv2 > VEN > ORD - Validazione - '
        gamma_ven_ord_exc: 'REGDOCv2 > VEN > ORD - Eccezione - '
        gamma_ven_sped: 'REGDOCv2 > VEN > SPED - Validazione - '
        gamma_ven_sped_exc: 'REGDOCv2 > VEN > SPED - Eccezione - '
      event_log:
        message: Messaggio
        params: Parametri
        reference_object: Oggetto
        remote_ip: IP
        url: Url
        user: Utente
      family_product:
        code: Codice famiglia prodotto
      fast_item_importer:
        product_id: Prodotto
        serials: Lista seriali
        state: Stato
        warehouse_id: Punto vendita
      financing:
        approved: Approvato
        awaiting_serial_assignment: Attesa assegnazione seriale
        canceled: Annullato da sistema
        code: Codice
        created_at: Data inserimento
        customer_address: Indirizzo
        customer_birth_date: Data di nascita
        customer_cadrastal_code: Codice catastale
        customer_cf: Codice fiscale
        customer_city: 'Città'
        customer_city_name: 'Città'
        customer_city_id: 'Città'
        customer_email: Email
        customer_first_name: Nome
        customer_gender: Genere
        customer_last_name: Cognome
        customer_mobile_phone: Cellulare
        customer_name: Cliente
        customer_postal_code: CAP
        customer_province: Provincia
        customer_street_number: Numero
        customer_uuid: UUID cliente
        dealer: Partner
        dealer_contact: Utente
        dealer_contact_full_name: Utente
        dealer_contact_id: Utente
        dealer_name: Partner
        dealer_price: Costo al partner
        edited_public_price: Prezzo di vendita
        financing_structure: Tipo di finanziamento
        financing_structure_name: Tipo di finanziamento
        forwarder: Spedizioniere
        id: Id
        in_progress: Processo in corso
        months: Mesi
        on_hold: In valutazione
        original_public_price: Prezzo al pubblico suggerito
        pre_approved: Pre-approvato
        product: Prodotto
        product_name: Prodotto
        product_shipping_name: Nome
        progress: Avanzamento
        refused: Rifiutato
        retired: Ritirato
        serial: Seriale prodotto
        shipped: Spedito
        show_canceled_by_system: Mostra anche annullati da sistema
        state: Stato
        total: Totale finanziato
        tracking: Tracking
        upfront: Anticipo
        warehouse: Punto vendita
        warehouse_id: Punto vendita
        warehouse_name: Punto vendita
        warehouse_address: Indirizzo
        waiting_for_consel: In attesa di risposta da Consel
      financing_structure:
        active: Attivo
        active_states:
          "yes": "Sì"
          "no": "No"
          from_product: Da prodotto
        collection: Collection
        divider: Divisore
        maxirata_code: Percentuale Maxirata (codice)
        minimum_amount: Importo minimo
        months: Mesi
        name: Nome
        percent: 'Costo % finanziamento'
        position: Posizione
        tabfin_code: Codice Tabfin
      findomestic_referent:
        name: Nome referente
        email: Email referente
        number: Numero referente
        visible: Visibile
      fixed_line_detail:
        created_at: Data/ora creazione
        location_address: Indirizzo
        location_address_street_name: Indirizzo
        location_cap: CAP
        location_city: Comune
        city: Comune
        location_number: Numero civico
        location_province: Provincia
        location_province_id: Provincia
        phone_activation_id: Attivazione
        updated_at: Data/ora modifica
      floa_financing:
        amount: Importo
        created_at: Data inserimento
        created_at_list: Data
        customer: Cliente
        customer_mobile_phone: Numero di telefono
        customer_email: Email
        dealer: Partner
        floa_customer_url: Link sms
        floa_errors: Errori Floa
        floa_reference: ID Floa
        installment_amount: Importo rata
        installments: Numero rate mensili
        privacy_floa_general: Scegliendo questo metodo di pagamento, accetti le <a href="https://www.floapay.com/images/PDF/GTC_FLOAPAY_Italy_bilingue.pdf" target="_blank">condizioni generali di pagamento a rate</a>.
        privacy_floa_data: Prendo atto che i miei dati personali saranno trattati da FLOA ai fini dell'esame della mia richiesta di finanziamento, della gestione del contratto di credito e, ove applicabile, del recupero crediti. Per ulteriori informazioni, <a href="https://www.floapay.com/images/PDF/Privacy_Policy_IT.pdf" target="_blank">fare click qui</a>.
        privacy_kolme_data: Trattamento dei dati personali da parte di Kolme Srl - <a href="https://www.kolme.it/gdpr" target="_blank">Informativa</a>.
        product: Prodotto
        profit: Margine
        reject_reason: Motivazione rifiuto
        status: Esito
        status/accepted: Approvata
        status/rejected: Rifiutata
        status/waiting: Attesa cliente
        reject_reason/customer_not_acquirable: Rifiutata cliente
        reject_reason/payment_method_refused: Rifiutata MdP
      floa_financing_product:
        serial: Seriale
      forwarder:
        commercial_name: Nome commerciale
        enabled: Attivo
        forwarder_kind: Tipo corriere
        gsped_code: Codice Gsped
        gsped_service_level: Livello di servizio Gsped
        name: Nome
        sort_index: Ordinamento
        url: Url
      fulfill:
        created_at: Data/ora creazione
        ddt: Documento Contabile
        forwarder: Corriere
        fulfill_recharges: Evadi ricariche
        note: Nota visibile
        order: Ordine
        tracking_number: Codice Spedizione
        tracking_url: Tracciatura spedizione
        updated_at: Data/ora modifica
      fwa_installation_fee:
        kind/assurance: Assurance
        kind/ko: Installazione KO
        kind/suspend: Sospensione
        kind/wall: Parete
        kind/wall_saturday: Parete Sabato
        kind/roof: Tetto
        kind/thermal_coat: Cappotto termico
      gamma_payment_method:
        code: Codice
        description: Descrizione
        position: Posizione
      gsped_call:
        call_name: Nome chiamata
        client_error_message: Messaggio di errore
        colli: Numero colli
        created_at: Data/ora creazione
        id: ID
        peso: Peso
        pretty_request: Dati request
        pretty_response: Dati response
        pretty_status: Stato
        reference_object_id: ID oggetto
        rcpt_addr: Indirizzo di spedizione
        rcpt_cap: CAP
        rcpt_city: Città di spedizione
        rcpt_contact: Contatto primario del partner
        rcpt_country_code: Codice paese di spedizione
        rcpt_email: Email partner
        rcpt_name: Nome partner
        rcpt_phone: Numero di telefono del partner
        rcpt_prov: Provincia di spedizione
        shipment_id: ID Spedizione
        shipment_shippable_id: ID oggetto
        shippable_id: ID oggetto
        shippable_type: Tipo oggetto
        successful: Stato
      home_md_very_box:
        bottom: Bottone
        bottom/activate: Attiva
        bottom/order: Ordina
        bottom_link: Link bottone
        click_count: Click
        middle: Immagine centrale
        mouseover: Testo mouseover
        plan_id: ID tariffa
        top: Titolo
        top/1: Iliad, CoopVoce, Fastweb e altri
        top/2: TIM, Vodafone, Kena e altri
        top/3: Nuovi numeri
        top/4: Offerta speciale
      ii_row:
        dealer: Partner
        dealer_id: Partner
        dealer_vat: Partita IVA Partner
        description: Descrizione
        extra_reward: Extracompenso
        implicit_reward: Margine su vendita
        invoice_payment_id: Fattura compenso
        operator: Appartenenza
        phone_activation_id: Id Attivazione
        phone_activation_hex_id: Id Attivazione
        pos_code: Codice POS
        referred_date: Data di riferimento
        reward: Compenso
        row_date: Data
        row_type: Tipo riga
        vat_code: Codice IVA
      imei_reservation:
        canceled: Annullata
        confirmed: Confermata
        expired: Scaduta
        requested: Richiesta in corso
        sent_to_dhl: In partenza
        shipped: Spedita
        warehouse_id: Punto vendita
        wished: Desiderato
        shipment_address: Indirizzo di spedizione
        shipment_city_ui: Comune
        shipment_first_name: Nome
        shipment_last_name: Cognome
        shipment_email: Email
        shipment_phone_number: Numero di telefono
        shipment_number: Numero civico
        shipment_zip: CAP
        shipment_care_of: Presso (facoltativo)
      insurance:
        canceled: Annullata
        created_at: Creata il
        customer_address: Indirizzo
        customer_birth_date: Data di nascita
        customer_city: Città
        customer_cf: Codice fiscale
        customer_company_name: Società
        customer_firstname: Nome
        customer_firstname_company: Nome referente aziendale
        customer_gender: Genere
        customer_house_number: Numero civico
        customer_lastname: Cognome
        customer_lastname_company: Cognome referente aziendale
        customer_name: Cliente
        customer_province: Provincia
        customer_type: Tipo Cliente
        customer_vat: Partita IVA
        customer_zip_code: CAP
        dealer: Partner
        email: Indirizzo Email
        fattura: Fattura
        mobile_phone: Telefono cellulare
        operator_contract: Attivazione
        price: Prezzo vendita
        phone_number: Telefono fisso
        product_brand: Marchio
        product_invoice_number: Numero scontrino/fattura
        product_invoice_type: Vendita
        product_invoiced_at: Data scontrino/fattura
        product_model: Modello
        product_name: Prodotto
        product_price: Prezzo prodotto IVA inclusa
        product_serial: IMEI
        product_type: Tipologia prodotto
        scontrino: Scontrino
        sent: Attiva
        service_number: ServiceNumber
        status: Stato
        to_be_confirmed: In lavorazione
        used: Utilizzata
        voucher_code: Codice voucher
        warehouse: Punto vendita
      internal_user_detail:
        all: Tutto
        access_debug: Accesso debug chiamate
        access_level: Livello di accesso
        deleted: Disattivo
        email: Email
        execute: Solo esecuzione
        execute_and_edit: Esecuzione e modifica
        first_name: Nome
        full_name: Nome e Cognome
        gamma_code: ID Gamma
        internal_phone: Interno
        last_name: Cognome
        no_access: Nessun accesso
        notes: Note
        only_md: Solo MD
        only_wind_tre: Solo Wind Tre
        phone: Cellulare (OTP)
        printer: Zebra predefinita
        report_access_level: Accesso report
        sap_code: ID SAP
        show_deleted: Mostra anche i disattivi
        partner_type: Tipologia partner
        partner_type/tutti: Tutti
        partner_type/w3sp: Solo W3SP
        partner_type/kolme_master_nexus: Solo Kolme Master - Nexus
      invoice_payment:
        invoice_date: Data
        invoice_number: Numero fattura
        ImeiReservation: 'Lok-Me Gallery'
        Financing: 'Finanziamento'
        PhoneActivation: 'Attivazione'
      item:
        activated_at: Data Attivazione
        additional_serial: Seriale addizionale
        administrative_notes: Note
        attiva_sold: Venduto da Attiva
        created_at: Creato il
        created_at_from: Data creazione DA
        created_at_to: Data creazione A
        credit_note: Nota di Credito
        credit_note_date: Data Nota di Credito
        credit_note_amount: Importo Nota di Credito
        credit_transfer_code: Codice Cessione Credito
        credit_transfer_date: Data Cessione Credito
        customer: Cliente
        customer_name: Cliente
        ddt: DDT fornitore
        imei_reservation: Prenotazione
        invoice_expiry_at: Scadenza Fattura
        invoice_issued_at: Data Fattura
        invoice_number: Numero Fattura
        invoice_recipient: Ragione Sociale Fattura
        logistic_number: Numero logistico
        notes: Note
        order: Ordine
        phone_activation: Attivazione
        phone_number: Numero telefonico
        product: Prodotto
        product_code: Codice prodotto
        product_id: Prodotto
        product_type: Tipologia prodotto
        promo_device: Promo Device
        sale_amount: Importo Cessione Riconosciuto
        selling_price: Prezzo Acquisto Dealer
        serial: Seriale
        serials: Seriali
        state: Stato
        state/activated: Attivato
        state/activating: In attivazione
        state/booked: Prenotato
        state/instock: In giacenza
        state/quarantined: Quarantena
        state/reserved: Riservato
        state/sold: Utilizzato
        to_dealer_date: Data al partner
        type/p: Prodotto non attivabile
        type/pa: Prodotto attivabile
        type/s: SIM
        type/non_sim: Prodotto non SIM
        uuid: Codice UUID item
        warehouse: Sede
        warehouse_id: Sede
      item_order:
        item: Articolo magazzino
        order: Ordine
        serial: Seriale
      job_kind:
        code: Codice job_kind
        created_at: Data/ora creazione
        description: Descrizione
        updated_at: Data/ora modifica
      ko_reason:
        action: Azione
        action_description: Descrizione estesa
        charge_operator: In carico all'operatore
        description: Descrizione
        operation_id: Operazione
        ngpos_operation_id: Operazione POS Evo
        recoverable: Recuperabile
        responsible: Responsabile
        visible_in_activation: Visibile in attivazione
        triggers_item_removal: Riporta in giacenza il seriale
      lov_behavior:
        code: Codice
        description: Descrizione
      macro_category:
        accessori: Accessori
        prodotti: Prodotti
        prodotti_attiva: Prodotti Attiva
        open_market: Open Market
        sim: SIM
        telequale: Telequale
        trade_marketing: Marketing
        ricariche: Ricariche
        utilita: Utilità
        wind_tre: Wind Tre
        wind_tre_other: WINDTRE - Altro
        wind_tre_smartphone: WINDTRE - Smartphone
      marital_status:
        code: Codice stato civile
        created_at: Data/ora creazione
        description: Descrizione
        updated_at: Data/ora modifica
      massive_item:
        ddt: Documento Contabile
        product: Prodotto
        product_id: Prodotto
        serials: Seriali
        state: Stato
        warehouse: Sede
        warehouse_id: Sede
      notice:
        body: Testo
        creator: Creatore
        last_modifier: Ultimo a modificare
        publish_ends_at: Pubblicata al
        publish_starts_at: Pubblicata dal
      number_portability_detail:
        adsl_migration_code: Codice migrazione ADSL
        activation_line_path: "Percorso attivazione"
        additional_associated_numbers_1: Numero 1
        additional_associated_numbers_2: Numero 2
        additional_associated_numbers_3: Numero 3
        additional_associated_numbers_4: Numero 4
        additional_associated_numbers_5: Numero 5
        additional_associated_numbers_6: Numero 6
        additional_associated_numbers_7: Numero 7
        contract_kind: Tipologia contratto
        contract_kind_id: Tipologia contratto
        created_at: Data/ora creazione
        credit_transfer: Richiesta trasferimento credito
        different_location: Ubicazione linea (da compilare solo se diversa dalla residenza)
        different_owner: Diverso intestatario numero MNP
        has_additional_associated_numbers: Altre numerazioni associate (esclusa la seconda linea)
        location_address: Indirizzo
        location_address_street_type_id: Indirizzo
        location_zip: CAP
        location_city: Comune
        location_number: Numero civico
        location_province: Provincia
        location_province_id: Provincia
        otp: Codice OTP
        owner_birth_country_id: Nazione di nascita
        owner_birth_date: Data di nascita
        owner_birth_place: Luogo di nascita
        owner_birth_province_id: Provincia di nascita
        owner_cf: Codice Fiscale
        owner_company_kind: Forma Giuridica
        owner_company_name: Ragione Sociale
        owner_document_date: Data rilascio documento
        owner_document_expiry: Data scadenza documento
        owner_document_kind: Tipo documento di identità
        owner_document_number: Numero documento
        owner_gender: Sesso
        owner_gender/F: Femminile
        owner_gender/M: Maschile
        owner_identity_document_kind_id: Tipo documento di identità
        owner_first_name: Nome
        owner_kind: Tipo cliente
        owner_kind/pf: Cliente privato
        owner_kind/di: Ditta individuale
        owner_kind/pg: Persona giuridica - Azienda
        owner_last_name: Cognome
        owner_vat: Partita IVA
        phone_activation_id: Attivazione
        phone_number: Numero di telefono
        prefix: Prefisso
        sim_lost_or_stolen: Denuncia furto/smarrimento SIM
        sim_serial: Seriale SIM
        source_operator: Operatore di provenienza
        source_operator_id: Operatore di provenienza
        updated_at: Data/ora modifica
        voice_migration_code: Codice migrazione Voce
      number_rebinding_detail:
        created_at: Data/ora creazione
        imei_serial: IMEI vecchio prodotto
        phone_activation_id: Attivazione
        phone_number: Numero di telefono
        updated_at: Data/ora modifica
      offer:
        alternative_name: Nome alternativo per PDC
        bypass_rid_block: Bypass blocco ABI/CAB
        created_at: Data/ora creazione
        financing: Finanziamento
        in_installments: Rateale
        length: Durata o Vincolo contrattuale
        loaned: Comodato
        matrix: Matrice di riferimento
        name: Nome Offerta
        not_from_kolme: No da Kolme
        number_portability: 'Attiva avviso: Promo solo con portabilità'
        offer_category: Offerta
        operator: Operatore
        partner_area_name: Nome area partner
        payment_method_ids: Metodi di pagamento
        payment_methods: Metodi di pagamento
        plan_kind_list: Tipologie tariffa
        plan_kinds: Tipologie tariffa
        product_kind_list: Tipologie prodotto
        product_kinds: Tipologie prodotto
        reward: Compenso Dealer
        service_kind: Tipologia servizio
        show_disabled: Mostra anche disattive
        sim_kind_list: Tipologie SIM
        sim_kinds: Tipologie SIM
        simulation: Simulazione
        store_tag_list: Categorie negozio
        store_tags: Categorie negozio
        tag_list: Lista Tag
        tags: Tag
        updated_at: Data/ora modifica
        uuid: Codice UUID offerta
        view_warehouse: Visualizzazione sede
        visibility: Attiva
      offer_category:
        code: Codice offer_category
      offer_instance:
        created_at: Data/ora creazione
        data_time_threshold: Tempo (dati)
        gb_threshold: GB
        in_installments: Rateale
        length: vincolo
        loaned: Comodato
        mb_threshold: MB
        monthly: Canone
        number_portability: Promo solo con portabilità
        offer_id: Offerta
        offer_name: Nome offerta
        offer_partner_area_name: Nome offerta area partner
        operator_id: Operatore
        option_ids: Opzioni
        payment_method_id: Metodo di pagamento
        plan_alternative_name: Nome alternativo tariffa
        plan_customer_kind_id: Tipologia cliente
        plan_id: Tariffa
        plan_kind: ' Tipologia Tariffa'
        plan_minutes_vs_kind: Minuti vs
        plan_name: Nome tariffa
        plan_no_over_threshold: Extrasoglia dati
        plan_no_voice: No voce
        plan_threshold_kind: Tipo soglia
        product_category_id: Categoria Tariffa/Prodotto
        product_id: Prodotto
        product_kind: Tipologia Prodotto
        product_name: Nome prodotto
        product_sim_format: Formato SIM
        reward: Compenso
        sim_id: SIM
        sim_kind: Tipologia Sim
        sim_name: Nome SIM
        sms_threshold: SMS
        updated_at: Data/ora modifica
        upfront: Anticipo
        voice_minutes_threshold: Minuti (voce)
      official_pda:
        code: Codice contratto
        file: PDC ufficiale
      operation_outcome:
        activation_date: Data attivazione
        activation_date_fixed_w3: Data inserimento
        awb_shipping_number: Numero spedizione
        coverage_control_type: Tipo copertura
        created_at: Data/ora creazione
        credit_check_code: Codice Credit Check
        customer_saleability: Vendibilità cliente
        forwarder: Spedizioniere
        include_last_robot_screenshot: Aggiungi ultimo screenshot di Victor
        is_deferred: Differita
        ko_r: KO Recuperabile
        ko_reason_id: Motivazione
        main_phone_number: Numero Telefonico Principale
        note: Note
        operation_id: Operazione
        operator_created_at: Data inserimento operatore
        pdc_confirmed_at: Data conferma PDC
        phone_activation_id: Attivazione
        pos_code: Codice POS
        practice_code: Codice Pratica
        private_note: Note Private Kolme
        product_item_serial: Seriale prodotto
        receiving_date: Data di consegna
        recover: Rilavora
        recovered_by_dealer: Recuperato da dealer
        recovery_note: Note di recupero
        reopening_saleability_missing_days: Giorni mancanti riapertura vendibilità
        report_confirmated_at: Data conferma verbale
        sim_item_serial: Seriale SIM
        sim_select: Sim selezionata
        status: Stato
        terminal_received: Terminale Ricevuto
        tracking_url: URL tracciamento
        updated_at: Data/ora modifica
        user_id: Utente
        w3_customer_code: Codice cliente Windtre
      operator:
        Amazon: Amazon
        BetFlag: BetFlag
        CoopVoce: CoopVoce
        DAZN: DAZN
        Daily Telecom: 'Daily Telecom'
        Fastweb PIN: Fastweb PIN
        Fastweb manual: "Fastweb (man.)"
        GT Mobile: GT Mobile
        Gioco Digitale: Gioco Digitale
        GoldBet: GoldBet
        Google Play: Google Play
        H3G: '3'
        Ho: 'ho.'
        Infinity: Infinity
        Apple: Apple
        Kaspersky PIN: Kaspersky PIN
        Lebara: Lebara
        Lycamobile: Lycamobile
        Lycamobile PIN: Lycamobile PIN
        Vodafone PIN: Vodafone PIN
        Mediaset Premium: Mediaset Premium
        Microsoft Office: Microsoft Office
        Microsoft PIN: Microsoft PIN
        Microsoft Xbox: Microsoft Xbox
        PMobile: Poste Mobile
        SKY: SKY
        Telecom Internazionale: Telecom Internazionale
        TIM: TIM
        TIM Ricarica +: TIM Ricarica +
        William Hill: William Hill
        UNO Mobile: UNO Mobile
        UNO Mobile PIN: UNO Mobile PIN
        access_level: Competenza
        area_partner_visible: Visibile Partner
        bwin: bwin
        client_support: Telefono Servizio clienti
        cms_column: Colonna in CMS
        cms_position: Posizione CMS
        created_at: Data/ora creazione
        disabled: Disabilitato
        hide_logo_in_store_card: Nascondi logo in inserimento ordine
        left: Sinistra
        name: Nome Operatore
        needs_pdf: Prevede PDF - Lettera di gara
        only_md: MD
        only_wind_tre: Wind Tre
        paysafecard: paysafecard
        pdf_name: Nome PDF - Lettera di gara
        position: Posizione per attivazione
        right: Destra
        service_kinds: Tipologie servizio
        tender_letter_position: Posizione per lettera di gara
        updated_at: Data/ora modifica
        url: Url Servizio Clienti
        uuid: Codice UUID operatore
        Very Mobile: Very Mobile
        WindTre: WINDTRE
      option:
        activable: Attivabile
        additional_service: Servizio aggiuntivo
        created_at: Data/ora creazione
        contextual: Contestuale
        data_time_threshold: Tempo (dati)
        do_not_send_to_the_robot: Non inviare al robot
        eligible_for_product_label: Mostra per etichetta
        enabled_for_target: Valida per target
        fee: Canone
        has_promotional_code: Codice Promozionale
        mb_threshold: MB
        minutes_vs_kind: Minuti inclusi verso
        name: Nome Opzione
        no_over_threshold: No oltre-soglia
        notes: Note (informazioni visibili in lavorazione attivazione)
        operator: Operatore
        operator_id: Operatore
        pda_filename: Modulo PDC
        pda_placeholder: Placeholder PDC
        product_label_name: Testo per etichetta
        promotional_code: Codice Promozionale
        plan_description: Descrizione opzione
        pre_flagged: Pre-flaggata
        reward: Compenso Partner
        rpa_ready: "RPA ready"
        show_disabled: Mostra anche disattive
        sms_threshold: SMS
        system_name: Nome interno Opzione
        system_name_operator: Nome sistema Operatore
        threshold_kind: Tipo soglia
        updated_at: Data/ora modifica
        uuid: Codice UUID opzione
        visibility: Visibile
        voice_minutes_threshold: Minuti (voce)
      order:
        THIRTY_DAYS: Pagamento a 30 giorni
        BT: Pagamento immediato (Bonifico Bancario Anticipato)
        C: Pagamento immediato (utilizzo Credito)
        CC: Pagamento immediato (Carta di Credito)
        DP: Pagamento immediato (DropPay)
        F: Pagamento dilazionato (utilizzo Fido)
        aasm_state: Stato
        all_unfulfilled: Tutti gli ordini
        awaiting_cc_confirmation: In attesa di pagamento differito
        awaiting_confirmation: Evidenza di bonifico in verifica
        bank_transfer: Copia Bonifico Bancario
        cancelled: Cancellato
        contains_attiva_kolme: Con prodotti Attiva
        contains_system_kolme: Con prodotti Kolme
        contains_system_no_recharges_kolme: Con prodotti Kolme (NO ricariche)
        created_at: Data/ora creazione
        credit_card: Pagamento in corso
        dealer: Partner
        dealer_notes: Note partner
        decrease_shopping_cart_item_quantity: Quantità prodotto decrementata
        destroied_shopping_cart_item: Prodotto rimosso
        dhl_unsent_with_allocated_items_kolme: Con prodotti assegnati
        dhl_unsent_without_allocated_items_kolme: Senza prodotti assegnati
        documents: Documenti
        failed: In errore
        force_zero_discount: Forza sconto a zero
        frozen_credit: Credito congelato
        frozen_overdraft: Fido congelato
        invoiced: Fatturato
        motivation: Motivo
        not_processed_with_recharges: Ordini Credito per Ricariche evadibili
        paid: Evadibile
        partially_processed: Evaso parzialmente
        pay_thirty_days: Pagamento a 30 giorni
        pay_with_credit: Pagamento con il credito
        pay_with_credit_card: Pagamento mediante carta di credito
        payment_failed: Pagamento fallito
        payment_in_progress_available: L'ultimo tentativo di pagamento è stato avviato %{elapsed} fa, se hai abbandonato la procedura ora puoi riprovare.
        payment_in_progress_not_available: Hai appena avviato la procedura di pagamento di quest'ordine, tra %{remaining_time} potrai ritentare.
        payment_recap: Resoconto pagamento
        private_notes: Note private
        processed: Evaso totalmente
        skip_autofulfill: Bypass evasione automatica
        to_pay: In attesa dell'evidenza di bonifico
        to_pay_credit_card: In attesa di pagamento
        unfulfilled_system_kolme: Prodotti Kolme da evadere
        unfulfilled_system_in_stock_kolme: Prodotti Kolme da evadere giac. > 0
        unfulfilled_attiva_kolme: Prodotti Attiva da evadere
        updated_at: Data/ora modifica
        user: Utente
        void: Annullato
        waiting_for_dhl: Merce approntata in attesa di evasione
        warehouse: Indirizzo di spedizione
        warehouse_id: Indirizzo di spedizione
        zero_shipping_costs: Spedizione gratuita
      otp_request:
        otp: Inserisci il codice OTP
        otp_phone_number: Numero di telefono per invio OTP
      ngpos_operation_outcome:
        ko_reason_id: Motivazione
        ok_date: Data approvazione
        recover: Rilavora
      page:
        body: Testo
        position: Posizione
        published: Pubblicata
        title: Titolo
        subtitle: Sottotitolo
      payment:
        accounted: Contabilizzato
        amount: Importo
        bank: Banca
        created_at: Data/ora creazione
        credit_note_code: Nota di credito
        payment_date: Data pagamento
        payment_method: Metodo di pagamento
        product_invoice_code: Fattura prodotto
        reward_invoice_code: Fattura compenso
        updated_at: Data/ora modifica
        uuid: Codice UUID pagamento
      payment_method:
        code: Codice modalità di pagamento
        created_at: Data/ora creazione
        description: Descrizione
        enabled: Abilitato
        updated_at: Data/ora modifica
      payment_method_detail:
        accountholder_cf_or_vat: Codice fiscale / Partita IVA (intestatario del conto)
        accountholder_name_or_company_name: Nome / Ragione sociale (intestatario del conto)
        cc_cvv: Codice di sicurezza (CVV2)
        cc_description: Tipo carta di credito
        cc_expiry: Scadenza carta di credito
        cc_expiry_month: Mese scadenza
        cc_expiry_year: Anno scadenza
        cc_number: Ultime 4 cifre della carta di credito
        code: Codice dettaglio modalità di pagamento
        created_at: Data/ora creazione
        credit_card_kind: Tipo carta di credito
        credit_card_kind_id: Tipo carta di credito
        description: Descrizione
        different_accountholder_address: Indirizzo di residenza / Sede legale
        different_accountholder_city: Comune
        different_accountholder_first_name: Nome  (delegato o titolare del conto)
        different_accountholder_last_name: Cognome  (delegato o titolare del conto)
        different_accountholder_cc_name: Nome (come compare sulla carta)
        different_accountholder_cf_or_vat: Codice fiscale / Partita IVA
        different_accountholder_birth_date: Data di nascita
        different_accountholder_birth_place: Luogo di nascita
        different_accountholder_id_doc_kind: Documento
        different_accountholder_identity_document_kind: Tipo documento
        different_accountholder_identity_document_kind_id: Tipo documento
        different_accountholder_id_doc_number: Numero documento
        different_accountholder_id_doc_date: Data rilascio
        different_accountholder_number: Numero civico
        different_accountholder_phone_number: Numero di telefono
        different_accountholder_zip: CAP
        different_accountholder_province: Provincia
        pinpad_code: Codice autorizzativo PinPad
        rid_iban: IBAN
        rid_iban_formatted: IBAN Spezzato
        updated_at: Data/ora modifica
      payment_request:
        amount: Importo
        created_at_from: Data inserimento DA
        created_at_to: Data inserimento A
        dealer: Partner
        dealer_name: Partner
        paid_at_from: Data pagamento DA
        paid_at_to: Data pagamento A
        payment_requestable: Finanziamento / attivazione
        payment_requestable_id: Finanziamento / attivazione
        payment_requestable_reference_id: ID riferimento
        request_kind_generic: Generico
        request_kind_accounting_document: Pagamento fattura %{invoice_number}
        request_kind_easycare_plus: EasyCare+
        request_kind_financing: Anticipo Finanziamento
        request_kind_phone_activation: Anticipo Attivazione
        state_canceled: Cancellato
        state_paid: Pagato
        state_payment_pending: Pagamento in corso
        state_registered: Registrato
        state_to_pay: Da pagare
        state_void: Annullato
      payments_iwbank:
        amount: Importo
        created_at: Data/ora creazione
        custom: Custom
        memo: Note
        notified_at: Data Notifica
        order: Ordine
        order_id: Ordine
        payer_email: Email Cliente
        payer_id: Id Cliente
        payer_name: Nome Cliente
        payment_date: Data pagamento
        payment_status: Stato Pagamento
        qta: Q.ta
        redirected_at: Data Redir.
        request_data: Dati richiesta
        state: Stato
        thx_id: Id Thx
        updated_at: Data/ora modifica
        verify_result: Risultato Verifica
        verify_sign: Codice Verifica
      pda_kind:
        code: Codice tipo PDC
        created_at: Data/ora creazione
        description: Descrizione
        di_cdc_offer_length_threshold: Di cdc (per offerte > 24 mesi)
        di_generic_transfer: Di Cessione Generico
        di_kolme_master_transfer: Di Cessione Kolme Master - Nexus
        di_pda_offer_length_threshold: Di pda (per offerte > 24 mesi)
        di_sdd_offer_length_threshold: Di sdd (per offerte > 24 mesi)
        not_visible: Non visibile
        offer_length_threshold/24: Offerta > 24 mesi
        pf_cdc_offer_length_threshold: Pf cdc (per offerte > 24 mesi)
        pf_generic_transfer: Pf Cessione Generico
        pf_kolme_master_transfer: Pf Cessione Kolme Master - Nexus
        pf_pda_offer_length_threshold: Pf pda (per offerte > 24 mesi)
        pf_sdd_offer_length_threshold: Pf sdd (per offerte > 24 mesi)
        pg_cdc_offer_length_threshold: Pg cdc (per offerte > 24 mesi)
        pg_generic_transfer: Pg Cessione Generico
        pg_kolme_master_transfer: Pg Cessione Kolme Master - Nexus
        pg_pda_offer_length_threshold: Pg pda (per offerte > 24 mesi)
        pg_sdd_offer_length_threshold: Pg sdd (per offerte > 24 mesi)
        updated_at: Data/ora modifica
      phone_activation:
        activated_at: Data attivazione
        activated_at_from: Data attivazione
        activated_at_to: Data attivazione (fino al)
        activated_label: Attivata
        area_nielsen: Area Nielsen
        automatic_recharge: Ricarica Automatica
        cancellation_cause: Causale storno
        cancellation_date: Data storno
        cancellation_total: Importo storno
        cellphone_number_1: Numero Giga Illimitati 1 – Convergenza Super Fibra
        cellphone_number_2: Numero Giga Illimitati 2
        cellphone_number_3: Numero Giga Illimitati 3
        completed_recharges_amount: Ricarica Partner effettiva
        completed_recharges_count: Ricarica Partner effettiva (numero ricariche)
        contract_rebinding: Contratto da rivincolare
        contract_rebinding_title: Inserisci qui il codice contratto su cui vuoi eseguire la Consegna Modem
        contract_rebinding_wind_title: Inserisci qui il codice contratto su cui vuoi eseguire la Consegna Modem
        coverage_control_type: Tipo copertura
        created_at: Data/ora creazione
        created_at_from: Data creazione
        created_at_to: Data creazione (fino al)
        customer: Cliente
        customer_name: Nome Cliente
        customer_base_assignee: Assegnatario nel modulo Customer Base
        customer_cellphone: Cellulare cliente
        customer_email: Email cliente
        cvp_offline: CVP Offline
        dealer: Partner
        dealer_name: Nome Partner
        dealer_price: Prezzo al partner
        dealer_signer_cellphone: Cellulare Dealer
        dealer_signer_email: Email Dealer
        dealer_signer_fullname: Utente Dealer
        deferred: Spedizione da magazzino Kolme
        deferred_subscription_rechargeable: Abbonamento con corriere
        deferred_to_operator: Differita a operatore
        delayed: Rimandata
        digital_signature_email: Email firma digitale cliente
        different_payer_cellphone: Cellulare diverso pagatore
        different_payer_email: Email diverso pagatore
        disabled: Disattivata
        do_add_friend_promo_code: Sono in possesso di un codice
        do_add_promo_device: Vuoi aggiungere a quest'attivazione la vendita di un prodotto in Promo Device?
        do_number_portability: Effettuare portabilità?
        do_easycare_insurance: Desideri proporre EasyCare+?
        editing_step: Fase modifica
        e_sim: È una E-sim?
        extra_reward: Extra compenso
        financing_code: Codice Finanziamento Compass
        fixed_number_portability: NP (portabilità)
        force_cvp_offline: Forzatura sgancio prodotto
        friend_promo_code: Codice Coupon
        has_contextual_promo_device: Con promo device contestuale
        h3g_area: Area H3G
        immediate: Utilizzo solo magazzino Partner
        immediate_in_installments: Immediata rateale
        immediate_rechargeable: Immediata ricaricabile
        immediate_subscription: Immediata abbonamento solo SIM
        immediate_subscription_hybrid: Immediata abbonamento ibrida
        implicit_reward: Margine su vendita
        inserted_label: Inserita
        installments: Rate
        is_deferred: Attivazione differita
        kind: Tipo Attivazione
        ko: KO
        ko_at: Data KO
        ko_r: Sospeso
        ko_recoverable: Sospesi
        ko_recoverable_kolme: Attesa azione Kolme
        ko_recoverable_not_managed_partner: Non gestiti da Partner
        ko_recoverable_partner: Attesa azione Partner
        ko_recovered: Recuperati da Partner
        kor_facets: Sospesi
        last_edit_by: Utente ultima modifica
        mnp_different_owner_signer_phone_number: Cellulare titolare Sim
        mnp_different_owner_signer_email: Email titolare Sim
        monthly: Canone
        main_phone_number: Numero telefonico principale
        next_operation_is_administrative_control: Controllo Amministrativo
        next_operation_is_confirm_activation: Da confermare
        next_operation_is_cover_check: Controllo Copertura
        next_operation_is_credit_check: Credit Check
        next_operation_is_document_check: Documenti da verificare
        next_operation_is_insert_activation_date: Da attivare
        next_operation_is_insert_registry_data: Anagrafica
        next_operation_is_line_installation: Installazione Linea
        next_operation_is_receive_original_documents: Originali da ricevere
        next_operation_is_report_check: Verbale da verificare
        next_operation_is_pdc_check: Pdc da confermare
        next_operation_is_tecnical_check: Verifica Tecnica
        next_operation_is_tracking: Da spedire
        new_line: Nuova Linea
        np: In Portabilità
        number_portability: MNP (portabilità)
        number_rebinding: Numero da rivincolare
        number_rebinding_title: Inserisci qui il numero di telefono da rivincolare o su cui effettuare il cambio piano
        number_rebinding_tls_title: Inserisci qui il numero di telefono che vuoi rivincolare
        number_rebinding_wind_title: Inserisci qui il numero di telefono su cui vuoi aggiungere l'offerta Telefono Incluso
        offer: Offerta
        offer_category: Categoria offerta
        offer_due_date: Data di scadenza del vincolo
        offer_due_date_from: Data scadenza
        offer_due_date_to: Data scadenza (fino al)
        ok: OK
        operation_ko_reason: Tipologia sospeso
        operator: Operatore
        operator_created_at: Data inserimento operatore
        option_names: Opzione
        options: Opzioni
        payment: Pagamento
        payment_method: Metodo di pagamento
        pda_received_at: Data PDC Ricevuta
        pda_received_at_from: Data PDC Ricevuta
        pda_received_at_to: Data PDC Ricevuta (fino al)
        phone_activation_id: ID attivazione
        phone_activation_kind: Tipo attivazione
        phone_activation_kind_code: Codice tipo attivazione
        phone_activation_kind_financing: Finanziamento
        phone_activation_kind_fixed: Fisse
        phone_activation_kind_in_installments_or_loaned: Rateale/Comodato
        phone_activation_kind_rechargeable: Ricaricabili
        phone_activation_kind_subscription: Abbonamento
        phone_number: Numero telefonico
        plan: Tariffa
        plan_name: Tariffa
        post_insert_status_facets: Operazioni Post Inserimento
        preferred_forwarder: Abbonamento con Pony
        priority: Priorità
        priority_facets: Priorità
        product: Prodotto
        product_category: Categoria prodotto
        product_color: Colore
        product_item: Articolo - Prodotto
        product_item_id: Articolo - Prodotto
        product_name: Prodotto
        product_serial: Seriale Prodotto
        product_sim_serial: Seriale Prodotto / Seriale Sim
        promo_device_price: Prezzo Promo Device IVA inclusa
        promo_device_serial: Seriale prodotto
        promo_unocontrouno: Ritiro Terminale Offerta SuperValuta
        promotional_code_1: "Codice promozionale 1"
        promotional_code_2: "Codice promozionale 2"
        promotional_code_3: "Codice promozionale 3"
        public_price: Prezzo al pubblico in PDC
        recharge: Ricarica
        recharge_chosen: Ricarica scelta
        recharge_size: Ricarica contestuale Dealer
        recharge_size_id: Taglio ricarica
        second_line: Seconda linea
        second_line_kind: Tipo seconda linea
        second_line_prefix: Prefisso
        second_line_phone_number: Numero di telefono
        send_to_robot: Attiva via robot
        send_cc_to_robot: Salva carta via robot
        send_convergence_to_robot: Attiva via robot
        shipping_facets: Spedizione
        signature_data/signature_kind/digital: Digitale
        signature_data/signature_kind/paper: Cartacea
        sim: SIM
        sim_item: Articolo - SIM
        sim_item_id: Articolo - SIM
        sim_serial: Seriale Sim
        status: Stato
        status_facets: Stato
        super_dealer: Super
        total_reward: Compenso attivazione
        typology_facets: Tipologia
        updated_at: Data/ora modifica
        upfront: Anticipo
        user: Responsabile contratto
        user_id: Responsabile contratto
        uuid: Codice UUID attivazione
        vat: IVA
        warehouse: Sede
        warehouse_name: Punto Vendita
        warehouse_select: Seleziona la sede da cui stai operando
        w3_customer_code: Codice Cliente WindTre
      phone_activation_delay:
        created_at: Data/ora creazione
        hours: Fra quante ore?
        updated_at: Data/ora modifica
      phone_activation_kind:
        allow_partial_customer_data: Consenti anagrafica ridotta
        description: Descrizione
        code: Codice
        final_operations_group: Gruppo attivazione completata
      phone_activation_option:
        created_at: Data/ora creazione
        option: Opzione
        phone_activation: Attivazione
        updated_at: Data/ora modifica
      phone_call:
        called_at: Data
        call_type: Direzione
        dealer: Partner
        duration_in_seconds: Durata
        note: Note
        user: Operatore
      phone_call_report:
        agent_id: Nominativo Agente
        motivation: Motivo di contatto
        motivation_macro_category: Categoria
        motivation_short: Motivazione
        notes: Note
      plan:
        activable: Attivabile
        activation_fee: Contributo attivazione
        ad_hoc: Tariffa ad hoc (su base Punto Vendita)
        age: Età
        allow_friend_promo_code: Codice Coupon
        allowed_to_testers: Abilitata ai Tester
        alternative_name: Nome alternativo per PDC
        app_ready: App Ready
        automatic_recharge: Ricarica automatica
        automatic_recharges/no: 'NO'
        automatic_recharges/optional: Opzionale
        automatic_recharges/mandatory: Obbligatoria
        check_subtitle_system_name_operator: Controllo sottotitolo Tariffa
        cluster_option: Cluster cliente
        comparator_category: Categoria comparatore
        contract_kind: Tipologia contratto
        country_code: Codice Paese
        created_at: Data/ora creazione
        coverage_typology: Tipologia copertura
        dedicated_to_sms_campaign: Tariffa dedicata campagna SMS
        digital_signature: Firma digitale
        direct_installation: Installazione diretta
        customer_kinds: Tipologia cliente
        data_time_threshold: Tempo (dati)
        doppia_anagrafica: Doppia anagrafica
        extra_discount: Extra Sconto dealer (NO promo device cliente)
        first_estimate_description: Descrizione preventivo 1
        has_specific_np_operators: Operatori specifici portabilità
        hide_convergence_and_unlimited_giga: Nascondi Convergenza e Giga illimitati
        include_tcg: Prevede TCG
        jackpotapple: Jackpotapple
        kolme_avg_extra_reward: Extracompenso medio Kolme
        kolme_master_visibility: Visibilità tariffa
        kolme_master_visibility/: Tutti
        kolme_master_visibility/hidden: Solo W3SP
        kolme_master_visibility/exclusive: Solo Kolme Master - Nexus
        kolme_reward: Compenso Kolme
        kolme_shipping: 'SIM spedibile da magazzino Kolme '
        linked_selection: Mostra selezione contestuale
        mandatory_mnp: Forza MNP obbligatoria
        mandatory_friend_promo_code: Codice Coupon Obbligatorio
        max_age: Età massima
        mb_threshold: MB
        min_age: Età minima
        minimum_age: Età minima
        minutes_vs_kind: Minuti inclusi verso
        monthly_description: Messaggio da mostrare nella schermata di inserimento attivazione
        name: Tariffa
        nationality: Nazionalità
        no_over_threshold: No oltre-soglia
        no_voice: No voce
        note: Note (informazioni visibili in lavorazione attivazione)
        only_voice: Solo voce
        operator: Operatore
        options: Opzioni
        options_discount: Sconto per opzioni
        pda_kind: Tipo PDC
        pda_placeholder: Placeholder PDC
        pda_text: Testo per PDC
        plan_description: Descrizione tariffa
        plan_kind_list: Tipologie tariffa
        plan_kinds: Tipologie tariffa
        plan_options: Opzione
        portability_flags: Flag MNP
        portability_operators: Operatori specifici portabilità
        posng_summary_key: Nome sintesi contrattuale Pos
        product_category: Categoria tariffa
        product_kind_list: Tipologie prodotto
        product_kinds: Tipologie prodotto
        quality_parameter: Parametro di qualità
        request_mnp: Chiedere portabilità?
        reward: Compenso Dealer
        rpa_ready: "RPA ready"
        rpa: RPA
        second_business_line: Seconda linea Business
        second_estimate_description: Descrizione Preventivo 2
        service_kind: Tipologia servizio
        show_recharge_detail: Mostra ricarica contestuale in attivazione
        sim_kind_list: Tipologie SIM
        sim_kinds: Tipologie SIM
        simulation: Simulazione
        sms_campaign: Campagna SMS
        sms_threshold: SMS
        sorting: Parametro ordinamento
        sorting_short: Ordin.
        specific_operators: MNP Specifica
        standard: Standard
        store_tag_list: Categorie negozio
        store_tags: Categorie negozio
        subtitle_system_name_operator: Sottotitolo
        summary_link: Link sintesi precontrattuale
        system_name_operator: Nome sistema Operatore
        tag_list: Lista Tag
        tags: Tag
        target: Target
        target_type: Tipo Target
        target_rule_id: Regola Target
        target_type_f: Target F.
        target_type_m: Target M.
        tariffa_cb: Tariffa CB
        tariffa_tls: Tariffa Teleselling
        threshold_kind: Tipo soglia
        updated_at: Data/ora modifica
        uuid: Codice UUID tariffa
        value_kind: Valore
        visibility: Visibile
        voice_minutes_threshold: Minuti (voce)
        warehouse_visible: Visualizza sede
        white_area: Area Bianca
      plan_option:
        uuid: Codice UUID opzione tariffa
      portability_operator:
        iccid_start: Inizio ICCID
        label: Nome
        level: Livello
        group_value: Valore di raggruppamento
        operator_kind: Tipo operatore
        operator_system_name: Nome sistema Windtre
        sort_index: Indice per ordinamento
        very_system_name: Nome sistema Very
        visible: Visibile
      price_list_image:
        hover_title: Testo link immagine
        link: Link (http:// ... )
        related_file: File da scaricare
        target: Target
      printer:
        default_printer: Stampante default
        enabled: Attiva
        gsped_client_id_code: Cod. client_id Gsped
        gsped_user_id_code: Cod. user_id Gsped
        name: Nome
        sort_index: Ordinamento
      product:
        0-100: Meno di € 100
        100-200: Tra € 100 e € 200
        200-400: Tra € 200 e € 400
        400-700: Tra € 400 e € 700
        700+: Più di € 700
        activable: Attivabile
        activable_generic: Attivabile generico
        activable_system_owner: Attivabile da magazzino Differita Kolme
        allow_sim_without_number: SIM senza numero di telefono
        attiva: Attiva
        attiva_brand: Marchio Attiva
        amount_in_system_owner_warehouse: Giacenza
        archived: Archiviato
        agent_fulfillable: Evadibile da Agenti
        agent_profit_a: Margine AM (A)
        agent_profit_b: Margine AM (B)
        agent_profit_c: Margine AM (C)
        allow_sim_without_number: SIM senza numero di telefono
        amount_in_system_owner_warehouse: Giacenza
        archived: Archiviato
        attiva: Attiva
        attiva_brand: Marchio Attiva
        available_promo_device_very: Utilizzabile in Promo Device Very
        bookable_quantity: Quantità assegnabile (bookable_quantity)
        brand: Produttore
        bundle_items:
          dealer_price_vat_excluded_a: Prezzo al partner a
          dealer_price_vat_excluded_b: Prezzo al partner b
          dealer_price_vat_excluded_c: Prezzo al partner c
          quantity: Quantità
        can_be_requested: Ordinabile
        code: Codice prodotto
        commercial_name: Nome commerciale
        company_brand: Marchio
        company_brand_id: Marchio
        competence: Competenza
        computer_gross_requestable: Ordinabile Computer Gross
        rid_guaranteed: RID garantito
        consignment: Prodotto in Conto Deposito
        contract_kind: Tipologia contratto
        credit_note_amount: Importo Nota di Credito
        created_at: Data/ora creazione
        dealer_price: Prezzo rivenditore
        dealer_price_vat_excluded_a: Prezzo rivenditore a
        dealer_price_vat_excluded_a_asc: Prezzo crescente
        dealer_price_vat_excluded_a_desc: Prezzo decrescente
        dealer_price_vat_excluded_b: Prezzo B IVA esclusa - Differito canale Kolme
        dealer_price_vat_excluded_b_asc: Prezzo crescente
        dealer_price_vat_excluded_b_desc: Prezzo decrescente
        dealer_price_vat_excluded_c: Prezzo C IVA esclusa - Differito canale Windtre Diretti
        dealer_price_vat_excluded_c_asc: Prezzo crescente
        dealer_price_vat_excluded_c_desc: Prezzo decrescente
        default_percent_discount: Sconto percentuale
        delete_and_add: Cancella e aggiungi
        depth: Profondità (mm)
        description: Descrizione
        dhl_supply_chain: DHL Supply Chain
        dhl_pigeon_house: Il prodotto verrà posizionato nella 'piccionaia' DHL
        dont_show_public_price: Non mostrare prezzo al pubblico
        ean_code: Codice EAN
        activatable_for_windtre_ecommerce: Utilizzabile in Lok-me per Ecommerce Windtre
        enabled_for_imei_reservation: Utilizzabile in Lok-Me
        enabled_for_imei_reservation_franchising: Utilizzabile in Lok-Me per Windtre Diretti
        enabled_for_imei_reservation_gallery: Utilizzabile in Lok-Me per Gallery GD
        family_product: Famiglia
        family_product_id: Famiglia
        final_installment: Rata finale
        floa_commissions_checkbox: Floa - sconto commissione
        floa_commissions: Sconto percentuale commissione
        floa_top_seller: Top seller Floa
        force_zero_shipping_cost: Forza spese di spedizione ordine a zero
        gamma_purchase_price: Prezzo di acquisto Gamma
        has_default_color: Colore per immagine di default
        has_usim_bundled: Comprende USIM?
        height: Altezza (mm)
        imei_reservation_curtailed: Prodotto contingentato Lok-me (solo attivazioni)
        immaterial: Prodotto immateriale
        is_usim: È USIM?
        italy_purchase: Piano dei conti - Acquisto Italia
        italy_purchase_id: Piano dei conti - Acquisto Italia
        italy_purchase_return: Piano dei conti - Reso Acquisto Italia
        italy_purchase_return_id: Piano dei conti - Reso Acquisto Italia
        italy_sales: Piano dei conti - Vendite Italia
        italy_sales_id: Piano dei conti - Vendite Italia
        italy_sales_return: Piano dei conti - Reso Vendite Italia
        italy_sales_return_id: Piano dei conti - Reso Vendite Italia
        items: Articoli magazzino
        jackpotapple: Jackpotapple
        system_owner_stocks_count: Giacenza
        loanable: Comodabile
        macro_category: Categorizzazione
        mandatory_mnp: Forza MNP obbligatoria
        margin: Margine percentuale
        max_qty_orderable: Quantità massima ordinabile
        max_qty_visible: Quantità massima visualizzata
        max_agent_discount_percentage: '% Max di sconto'
        name: Prodotto
        name_asc: Ordine alfabetico A-Z
        name_bookkeeping: Nome contabilità
        name_desc: Ordine alfabetico Z-A
        name_or_code: Prodotto (nome o codice)
        name_partner_order: Nome ordine partner
        operators: Operatori
        origin: Origine
        dhl_pigeon_management: Gestione piccionaia XL
        plan_kind_list: Tipologie tariffa
        plan_kinds: Tag tipologie tariffa
        product_categories: Categoria prodotto
        product_color: Colore
        product_color_id: Colore
        product_image: Immagine
        product_images: Immagini
        product_kind_list: Tipologie prodotto
        product_kinds: Tipologie prodotto
        product_macro_category: Categorizzazione
        product_type: Tipologia prodotto
        promo_device_price: Prezzo Promo Device IVA inclusa
        public_price_list: Prezzo al pubblico
        recharge_size: Taglio ricarica
        recharge_size_id: Taglio ricarica
        returnable: Reso accettato
        reward: Compenso
        ribbon: Visibilità
        sale_not_recommended: Vendita sconsigliata
        sap_distribution_chain: SAP - Catena di distribuzione
        sap_distribution_chain_id: SAP - Catena di distribuzione
        sap_exported: Esportato a SAP
        sap_exported_at: Data esportazione a SAP
        sap_name: Nome SAP
        sap_product_cluster: SAP - Cluster prodotto
        sap_product_cluster_id: SAP - Cluster prodotto
        sap_product_kind: SAP - Tipo prodotto
        sap_product_kind_id: SAP - Tipo prodotto
        sap_product_supplier: SAP - Fornitore prodotto
        sap_product_supplier_id: SAP - Fornitore prodotto
        sap_valuation_class: SAP - Classe di valorizzazione
        sap_valuation_class_id: SAP - Classe di valorizzazione
        serial: Prodotto con seriale
        short_description: Descrizione breve
        show_archived: Mostra anche gli archiviati
        show_disabled: Mostra anche non visibili
        sim_format: Formato Sim
        sim_kind_list: Tipologie SIM
        sim_kinds: Tipologie SIM
        statistic_group1: Gruppo statistico 1
        statistic_group1_id: Gruppo statistico 1
        statistic_group2: Gruppo statistico 2
        statistic_group2_id: Gruppo statistico 2
        statistic_group3: Gruppo statistico 3
        statistic_group3_id: Gruppo statistico 3
        statistic_group4: Gruppo statistico 4
        statistic_group4_id: Gruppo statistico 4
        statistic_group_1: Gruppo statistico 1
        statistic_group_2: Gruppo statistico 2
        statistic_group_3: Gruppo statistico 3
        statistic_group_4: Gruppo statistico 4
        store_subtag_list: Sottocategorie negozio
        store_subtags: Sottocategorie negozio
        store_tag_list: Categorie negozio
        store_tags: Categorie negozio
        system: Kolme
        tag_list: Lista Tag
        tags: Tag
        update_and_add: Sovrascrivi e aggiungi
        delete: Elimina Promo Device
        updated_at: Data/ora modifica
        uuid: Codice UUID prodotto
        value_multiplier: Moltiplicatore valore
        vat: Iva
        vat_type: IVA
        view_in_matrix: Visualizza in matrice
        visibility: Visibile
        weight: Peso (g)
        width: Larghezza (mm)
      product_category:
        code: Codice categoria prodotto
        created_at: Data/ora creazione
        description: Descrizione
        updated_at: Data/ora modifica
      product_color:
        name: Nome colore
        code: Codice colore
      product_image:
        created_at: Data/ora creazione
        image: Immagine
        position: Posizione
        product_id: Prodotto
        title: Titolo
        updated_at: Data/ora modifica
      product_macro_category:
        name: Nome
        dealer_kinds: Tipologia Partner
        position: Ordinamento
        visibility: Visibilità
        visibility/visible: Visibile
        visibility/not_visible: Non visibile
        visibility/visible_only_internal_users: Solo Interni
      promo_device:
        activated: Attivate
        activated_row: Attivata
        all: Tutte
        canceled: Annullate
        canceled_row: Annullata
        created_at: Data/Ora
        created_at_from: Data inserimento da
        created_at_to: Data inserimento a
        dealer: Partner
        dealer_name: Nome del partner
        in_progress: In corso
        in_progress_row: In corso
        phone_number: Numero di telefono
        pending_burn: Burn IMEI In Corso
        pending_burn_row: Burn IMEI In Corso
        processing: In lavorazione
        processing_row: In lavorazione
        product: Prodotto
        rejected: Respinte
        rejected_row: Respinta
        robot_error: Robot Error
        robot_ko: Robot KO
        robot_ko_r: Robot KO_R
        serial: Seriale
        state: Stato
        title: Promo Device Plus
        title_legacy: Promo Device Old
      province:
        code: Codice provincia
        created_at: Data/ora creazione
        description: Descrizione
        updated_at: Data/ora modifica
      purchase_order:
        delivered_quantity: Consegnati
        delivery_date: Data di consegna effettiva
        expected_delivery_date: Data consegna prevista
        order_date: Data
        order_date_from: Data da
        order_date_to: Data a
        order_line: Riga ordine
        ordered_quantity: Ordinati
        product_code: Codice
        product_company_brand_description: Vendor
        product_name: Prodotto
        purchase_price: Prezzo di acquisto
        quantity: Ordinati
        state: Stato
        supplier_gamma_id: ID fornitore su Gamma
        supplier_name: Fornitore
        supplier_order_number: Ordine
      recharge:
        created_at: Inserita il
        dealer: Partner
        dealer_name: Nome del partner
        deleted: Cancellata
        feedback_paymat: Feedback Paymat
        id: ID Ricarica
        kind: Tipo
        mail_sent: Email inviata
        manually_executed: Eseguita manualmente
        notes: Note
        notes_kolme: Note private
        phone_activation: Attivazione
        phone_number: Numero
        recharge_amount: Importo
        recharge_attempts: Tentativi
        recharge_kind: Tipologia
        recharge_operator: Operatore
        recharge_size: Taglio
        recharge_status: Stato
        recharged_at: Data
        recharged_at_from: Data ricarica
        recharged_at_to: Data ricarica (fino al)
        status: Esito
        updated_at: Data/ora modifica
        user: Utente
        visible_notes: Note visibili
      recharge_size:
        activation_visible: Visibile in attivazione
        aggregated_operator: Operatore aggregato
        aggregated_operator_id: Operatore aggregato
        amount: Valore Facciale
        amount_charged: Costo Partner
        automatic: Automatica
        bypass_phone_number_check: Bypass controllo numero cellulare
        code: Codice Paymat
        commercial_approach_type: Tipologia di approccio commerciale
        created_at: Data/ora creazione
        dealer_reward: Compenso Partner Contestuale
        euronet_base_size: Taglia base Euronet (VF)
        euronet_quantity: Quantità Euronet
        manual: Manuale
        name: Nome taglio di ricarica
        note: Note
        offer_code: Tag Offer Code
        operator: Operatore contestuale
        operator_id: Operatore contestuale
        paymat: Paymat
        paymat_operator_id: Operatore ricarica
        paymat_quantity: Quantità Paymat
        pin: Tipo
        pin_srv: Tag SRV
        post_sale_visible: Visibile nel modulo ricariche
        provider: Tipologia
        retry_on_ko: Ritenta esecuzione su KO
        sorting: Parametro ordinamento
        updated_at: Data/ora modifica
        uuid: Codice UUID taglio ricarica
        vat_type: Tipologia IVA
      region:
        code: Codice regione
      renew_email:
        body: Il testo
        customer_email: Email del cliente
      renew_note:
        body: Il testo
      renew_sms:
        body: Il testo
        phone_number: Il numero di telefono
        sender_alias: Il mittente
      reports/generic:
        agent: Agente
        agents: Agenti
        code: Testo codice
        created_at: Data/ora creazione
        dispatch_kind: Tipo di Invio
        dispatch_kinds/email: Email
        dispatch_kinds/ftp: FTP
        email: Indirizzi email destinatari (separati da virgola)
        email_text: Testo email
        format: Formato
        hour: Ora esecuzione
        hour_1: Ora esecuzione 1
        hour_2: Ora esecuzione 2
        hour_3: Ora esecuzione 3
        id: Id report
        monthly: Cadenza
        name: Report
        overwrite_file: Sovrascrivere il file al posto di aggiungerlo
        schedule_kind: Tipologia di programmazione
        scheduled: Prog.
        sent_at: Ultimo invio
        separator: Separatore
        specific_date_1: Data specifica 1
        specific_date_2: Data specifica 2
        specific_date_3: Data specifica 3
        sql: Query
        updated_at: Data/ora modifica
        week_day: Giorno della settimana
        weekday_1: Giorno 1
        weekday_2: Giorno 2
        weekday_3: Giorno 3
      sap_api_call:
        code: Codice risposta
        created_at: Data/ora creazione
        full_url: URL completa
        http_method: Metodo
        kind/add_es_role: (ES)
        origin: Origine
        origin_id: Origine
        path: Path
        relaunched_at: Data/ora rilancio
        relaunching_user: Utente rilancio
        relaunching_user_id: Utente rilancio
        request: Richiesta
        response: Risposta
        success: Successo
        updated_at: Data/ora modifica
      sap_internal_bank_institute:
        code: Codice SAP
        description: Istituto di credito
        iban: IBAN
        position: Posizione
        swift: Codice SWIFT
      sap_payment_kind:
        code: Codice SAP
        description: Metodo di pagamento
        kind: Tipologia
        kind/customer: Cliente
        kind/supplier: Fornitore
        position: Posizione
      sap_payment_term:
        code: Codice SAP
        description: Condizione di pagamento
        kind: Tipologia
        kind/customer: Cliente
        kind/supplier: Fornitore
        position: Posizione
      sap_withholding_tax:
        code: Codice SAP
        description: Descrizione
        kind: Tipo
        notes: Note
        position: Posizione
        target: Destinato a
        target/dealer: Dealer
        target/kolme_agent: Agente Kolme
      service_kind:
        code: Codice service_kind
        created_at: Data/ora creazione
        description: Descrizione
        updated_at: Data/ora modifica
      shipment:
        transport_document: Documento di trasporto (DDT)
        forwarder: Corriere
        printer: Stampante
        weight: Peso
        number_of_packages: Numero colli
        operation_outcome: Attivazione
        fulfill: Ordine
        imei_reservation: Prenotazione
        financing: Finanziamento
      street_type:
        value: Valore
        ps: Valore Robot
      switch_transfer_data:
        request_start_date: Data decorrenza richiesta
        property_possession_date: Data presa possesso dell'immobile
        ownership_title: Titolo di possesso
        act_type: Tipologia atto
        other_act_type: Tipologia altro atto
        act_date: Data dell'atto
        acquisition_deed_num: Numero atto acquisizione
        tax_agency_registration_in_progress: Registrazione Agenzia delle Entrate è in corso
        contract_registry_number: Repertorio avvenuta stipula N°
        certificate_number: Raccolta certificato avvenuta stipula N°
        tax_agency_registration_place: Registrazione Agenzia delle Entrate presso
        tax_agency_registration_date: Data di registrazione Agenzia delle Entrate
        registration_number: Num. Registrazione
        cadastral_registration_method: Modalità Accatastamento
        administrative_municipality: Comune Amministrativo
        cadastral_sub_identifier: Subalterno Catastale
        urban_section: Sezione Urbana
        cadastral_unit_type: Tipo Unità Catastale
        cadastral_sheet: Foglio Catastale
        cadastral_plot_number: Particella Catastale
        tabular_system_plot_identifier: Particella Sistema Tavolare
        plot_type: Tipo Particella
        acquisition_act_number: Numero atto acquisizione
        gas_self_reading_date: Data Acquisizione Autolettura GAS
        gas_self_reading_value: Autolettura GAS
      taggable:
        plan_kind_list: Tipologie tariffa
        plan_kinds: Tipologie tariffa
        product_kind_list: Tipologie prodotto
        product_kinds: Tipologie prodotto
        sim_kind_list: Tipologie SIM
        sim_kinds: Tipologie SIM
        store_tag_list: Categorie negozio
        store_tags: Categorie negozio
        tag_list: Lista Tag
        tags: Tag
      thread_kind:
        allowed_to_agents: Lavorabile da utente Agente
        description: Descrizione
        enabled: Visibile
        enabled_collection: Abilitato per Collection
        enabled_for_pa_in_progress: Visibile per attivazioni in corso
        franchising_enabled: Visibile Windtre Diretto
        full_description: Descrizione completa
        gallery_gd_partner_enabled: Visibile Gallery GD
        internal_users_only: Solo per utenti interni
        kolme_master_enabled: Visibile Kolme Master - Nexus
        md_very_enabled: Visibile a Master Dealer Very
        name: Nome
        operators: Operatori
        operators_list: Operatori
        pdf_required: Richiesta generazione pdf
        sort_index: Ordinamento
        sort_index_short: Ordin.
        visible_tls: Visibile Teleselling
        generic_partner_enabled: Visibile Partner Generico
        pure_installer_fwa_enabled: Visibile FWA Installatore Puro
        kolme_agent_partner_enabled: Visibile Partner Agente Kolme
      thread_message:
        message: Testo del messaggio
        attachment: Allegato
      timestamped:
        created_at: Data/ora creazione
        updated_at: Data/ora modifica
      tls_customer_detail:
        care_of: Presso
        city: Comune
        contact_id: ID WCM
        created_at: Data/ora creazione
        first_name: Nome
        last_name: Cognome
        location_address_street_name: Indirizzo
        location_cap: CAP
        location_city: Comune
        location_number: Numero civico
        location_province: Provincia
        location_province_id: Provincia
        phone_number: Numero di telefono
        phone_activation_id: Attivazione
        street_type_id: Indirizzo
        updated_at: Data/ora modifica
        upfront_kind: Modalità di pagamento anticipo
      tls_upfront:
        created_at: Data/ora
        customer: Cliente
        dealer: Partner
        phone_activation: Attivazione
        status: Stato
        status/canceled: Annullato
        status/failed: Fallito
        status/paid: Pagato
        status/preapproved: Prevalidato
        status/refunded: Rimborsato
        status/waiting: In attesa
        upfront: Anticipo
      two_factor_authentication:
        code: Codice
      upselling:
        delete_and_add: Cancella e aggiungi
        update_and_add: Sovrascrivi e aggiungi
      user:
        avatar: Immagine profilo
        created_at: Data/ora creazione
        company_name: Società
        dealer: Partner
        dealer_contact: Contatto Partner
        deleted: Disabilitato
        email: Email
        first_name: Nome
        full_name: Nome e cognome
        internal_phone: Interno centralino
        last_name: Cognome
        parent_id: Agente di riferimento
        password: Password
        password_confirmation: Conferma password
        password_without_expiration: Password senza scadenza
        role: Ruolo
        roles:
          admin: Amministratore
          agent: Agente
          dealer: Contatto del partner
          sub_agent: Sub-Agente
          super_user: Super user
          user: User
        session_expires_in: Scadenza forzata dopo n ore (0 nessuna scadenza)
        updated_at: Data/ora modifica
        username: Nome Utente
        uuid: Codice UUID utente
      uuid:
        uuid: Codice UUID
      wind_login:
        PWD_L1: 58166 - Password Primo Livello POS Evo
        PWD_L2: VICTORS - Password Secondo Livello POS Evo
      value_kind:
        code: Codice value_kind
        created_at: Data/ora creazione
        description: Descrizione
        updated_at: Data/ora modifica
      view_warehouse:
        code: Codice view_warehouse
      warehouse:
        acea_enabled: Abilitato ACEA (Luce & Gas)
        acea_enabled/true: Sì
        acea_enabled/false: 'No'
        acea_enabled_at: Data abilitazione ACEA
        additional_w3sp_warehouse: Sede Addizionale W3SP
        ad_hoc_enabled: Abilitato a tariffe ad hoc
        address: Indirizzo
        agent_sub_agent_label: Agente/Sub-Agente
        area_code: Area
        agent: Agente
        sub_agent_wind_tre: Sub-Agente WINDTRE
        affiliation_opening_hours: Orari negozio
        agent_wind_tre: Agente WINDTRE
        agent_products: Agente Prodotti
        sub_agent_wind_tre_id: Sub-Agente WINDTRE
        agent_wind_tre_id: Agente WINDTRE
        agent_products_id: Agente Prodotti
        sub_agent_wind_tre_email: Email Sub-Agente WINDTRE
        brands: Marchi trattati
        cadrastalcode: Codice catastale
        catena: Catena
        city: Città
        city_province: ' Città [Provincia]'
        cluster: Cluster PDV
        code: Codici
        company_name: Denominazione CC
        compass_code: Codice Compass
        compass_branch: Filiale Compass
        compass_enabled: Abilitato Compass
        compass_enabled/yes: Sì
        compass_enabled/no: 'No'
        compass_enabled/proceeding: 'In corso'
        country: Nazione
        country_id: Nazione
        created_at: Data/ora creazione
        dealer: Partner
        dealer_id: Partner
        destinations: Destinazioni
        disabled: Punto Vendita disabilitato
        disabled_at: Disabilitato in data
        drop_pay_pos_serial: Seriale
        drop_pay_pos_state: Stato POS
        drop_pay_pos_state_changed_at: Data cambio stato
        enabled_to_work_at_height: Abilitato lavori in quota
        enabled_to_work_at_height/true: Sì
        enabled_to_work_at_height/false: 'No'
        enabled_to_work_at_height/: ''
        ensign: Insegna Negozio
        facebook_address: "Indirizzo Facebook"
        findomestic_enabled: Abilitato Findomestic
        findomestic_enabled/yes: Sì
        findomestic_enabled/no: 'No'
        findomestic_enabled/proceeding: 'In corso'
        findomestic_referent_id: Referente Findomestic
        foreign_city: 'Città [Estero]'
        formation_topic: Argomento
        formation_topics: Argomento
        formation_visited_at: Data visita
        forwarder: Corriere preferito
        forwarder_id: Corriere preferito
        franchising: Windtre Diretto
        fwa_installer: Installatore FWA
        fwa_phone_number: Recapito Gestione Appuntamenti
        fwa_whatsapp_enabled: Utilizzabile Whatsapp
        fwa_whatsapp_enabled/true: Sì
        fwa_whatsapp_enabled/false: 'No'
        fwa_whatsapp_enabled/: 'No'
        gallery_gd: Gallery GD
        gamma_code_dest_cli: Cliente
        gamma_code_dest_for: Fornitore
        has_facebook: "Facebook"
        has_pinpad: "Possiede PinPad"
        has_whatsapp: "Whatsapp"
        h3g_area_code: Area H3G
        installation_area_cities: Area di installazione (Comuni)
        kolme_code: Codice Kolme
        kmd_code: Codice KMD
        location_change: Cambio Location
        main_w3sp_warehouse: Prima Sede W3SP
        name: Punto Vendita
        non_w3sp: Sede Non W3SP
        no_kolme_installation: No installazioni Kolme
        no_shop: No negozio
        notes: Note
        number: Numero
        opening_hours: Orari apertura
        weekly_closing_day: Chiusura settimanale
        passcom_code: Codice Passcom
        pda_code: Codice Attivazione (PDC)
        place: Località
        province: Provincia
        province_id: Provincia
        purchase_only: Solo Acquisti
        region: Regione
        region_id: Regione
        shopping_mall: Centro commerciale
        third_party_installations: Installazioni conto terzi
        third_party_installations/true: Sì
        third_party_installations/false: 'No'
        third_party_installations/: ''
        tipo_dealer: Tipo Partner
        trade_in_enabled: Abilitato Trade In
        trade_in_enabled/true: Sì
        trade_in_enabled/false: 'No'
        pos_code: Override Codice POS
        pos_code_id: Override Codice POS
        tre_code: Codice 9mld
        updated_at: Data/ora modifica
        user_id_peoplesoft: User ID PeopleSoft
        uuid: Codice UUID magazzino
        whatsapp_number: "Numero Whatsapp"
        w3sp_commercial_state: 'Stato Commerciale'
        w3sp_commercial_state_id: 'Stato Commerciale'
        w3sp_commercial_state_enabled_at: "Data abilitazione W3SP"
        w3sp_commercial_state_disabled_at: "Data disabilitazione W3SP"
        warehouse_brands_warehouses_updated_at: Data ultimo aggiornamento
        wind_code: Codice Wind
        zip: CAP
      warehouse_brands_warehouse:
        Coop_voce: Coop Voce
        Digi_mobil: DIGI mobil
        ho.: "ho."
        Kena_mobile: Kena Mobile
        NT_Mobile: NT Mobile
        partnership_accepted: "Partnership accettata"
        partnership_accepted/y: "Sì"
        partnership_accepted/n: "No"
        partnership_accepted/unknown: "Sconosciuto"
        Rabona_mobile: Rabona Mobile
        Uno_mobile: UNO Mobile
        Very_mobile: Very Mobile
        WINDTRE: WINDTRE
      wind_ngpos_activation:
        approved: Approvata
        awaiting: In attesa
        billing_address: Indirizzo di fatturazione
        company_name: Ragione sociale
        created_at: Record creato il
        credit_note_amount: Importo nota di credito
        customer_name: Cliente
        dealer_code: Codice rivenditore
        dealer_name: Rivenditore
        dealer_company_name: Nome rivenditore WINDTRE
        discount_amount: Importo sconto
        documents_approved_at: Documenti approvati il
        entry_fee: Anticipo
        first_name: Nome cliente
        fiscal_code: Codice Fiscale
        id: ID
        imei_or_serial: Seriale
        installments_number: Numero rate
        installment_amount: Importo rata
        item/credit_note: Numero Nota di Credito
        item/credit_note_amount: Importo Nota di Credito
        item/credit_note_date: Data Nota di Credito
        item/credit_transfer_code: Codice cessione credito
        item/credit_transfer_date: Data cessione credito
        item/sale_amount: Importo cessione credito
        item/invoice_number: Numero Fattura
        item/invoice_issued_at: Data Fattura
        item/selling_price: Prezzo Acquisto
        kolme_registered: Registrato in Kolme il
        ko_r: Segnala anomalia
        last_name: Cognome cliente
        maxi_installment: Importo maxirata
        material_code: Codice prodotto
        offer_kind: Tipo di offerta
        ok: Approva
        product_name: Prodotto
        phone_number: Numero di telefono
        registered: Attivata il
        status: Stato documentazione
        system_registered_at: Registrato in WINDTRE il
        terminal_amount: Importo terminale
        to_reupload: Da integrare
        to_upload: Da caricare
        uploaded: Caricata
        vat: Partita IVA
        vat_or_cf: C.F. /P.IVA
        warehouse: Punto vendita
      zoom_link:
        active: Attivo
        meeting_url: Link Zoom
        restricted: Limitare agli utenti abilitati agli operatori Windtre e/o Very Mobile
        restricted_short: Limitato Op.
        url_alias: Alias Spazio
    events:
      item:
        complete_activation: Attiva
        complete_sale: Completa vendita
        return_instock: Ritorna in giacenza
        start_activation: Inizia attivazione
    models:
      accounting_document:
        one: Documento contabile
        other: Documenti contabili
      affiliation:
        one: Affiliazione
        other: Affiliazioni
      after_sale_thread: Gestione
      aggregated_operator_category:
        one: Categoria operatori aggregati
        other: Categorie operatori aggregati
      aggregated_operator:
        one: Operatore aggregato
        other: Operatori aggregati
      alert: Alert
      application_setting: Setting
      appointment_detail:
        one: Installazione FWA
        other: Installazioni FWA
      automatic_invoice: Documenti Clienti Finali
      blacklisted_email: Email Blacklist
      chat_transcription: Chat
      commercial_promotion: Immagine Ordini MD Very
      company_brand: Marchio
      compass_branch: Filiali Compass
      compass_branches: Filiali Compass
      contract_kind: Tipologia contratto
      cost_matrix: Matrice costi - anticipi
      country: Nazione
      customer: Cliente
      customer_kind: Tipologia cliente
      dealer: Partner
      dealer_contact: Contatto partner
      dealer_contract_signature:
        one: Firma contrattuale
        other: Firme contrattuali
      destination: Destinazione
      energy_contract:
        one: Attivazione luce&gas
        other: Attivazioni luce&gas
      energy_plan:
        one: Tariffa luce&gas
        other: Tariffe luce&gas
      family_product: Famiglia
      financing:
        article_def: "il "
        a_preposition_def: "al "
        article_indef: "un "
        one: Finanziamento
        other: Finanziamenti
      financing_structure:
        one: Struttura finanziaria
        other: Strutture finanziarie
      findomestic_referent: Referenti Findomestic
      floa_financing:
        one: Vendita a rate
        other: Vendite a rate
      forwarder: Corriere
      fulfill:
        article_def: "l'"
        a_preposition_def: "all'"
        one: Evasione
        other: Evasioni
      gamma_payment_method: Mdp partner
      gamma_call:
        one: Chiamata Gamma
        other: Chiamate Gamma
      gsped_call:
        one: Chiamata Gsped
        other: Chiamate Gsped
      home_md_very_box: Riquadro
      ii_row: Riga
      imei_reservation:
        article_def: "la "
        a_preposition_def: "alla "
        one: Prenotazione Lok-me
        other: Prenotazioni Lok-me
      installation_area:
        one: Comuni Installatore
        other: Comuni Installatori
      insurance:
        one: Easycare+
        other: Easycare+
      invoice_payment: Fattura compensi
      italy_purchase: Piano dei conti - Acquisto Italia
      italy_purchase_return: Piano dei conti - Reso Acquisto Italia
      italy_sales: Piano dei conti - Vendite Italia
      italy_sales_return: Piano dei conti - Reso Vendite Italia
      item: Articolo magazzino
      massive_item: Azione Massiva su articoli magazzino
      notice: Notizia
      offer: Offerta
      offer_compatibility: Offerte campatibili
      operator: Operatore
      option: Opzione
      order:
        article_def: "l'"
        article_indef: "un "
        one: Ordine
        other: Ordini
      page: Pagina
      payment: Pagamento
      payment_method: Metodo di pagamento
      payment_method_detail: Dettaglio metodo di pagamento
      payment_request: Pagamento
      pda_kind: Tipo PDC
      phone_activation:
        article_def: "l'"
        a_preposition_def: "all'"
        article_indef: "un'"
        one: Attivazione
        other: Attivazioni
      phone_activation_option: Opzione Attivazione
      plan: Tariffa
      plan_option: Opzione Tariffa
      portability_operator: Operatore NP
      printer: stampante
      product: Prodotto
      product_category: Categoria Tariffa/Prodotto
      product_image: Immagine prodotto
      promo_device: Promo Device
      province: Provincia
      purchase_order:
        one: Ordine a fornitore
        other: Ordini a fornitore
      recharge:
        one: Ricarica
        other: Ricariche
      recharge_size: Taglio ricarica
      renew_email: Email
      renew_hidden: Nascondi
      renew_note: Nota
      renew_sms: SMS
      reports/generic: Report
      sap_api_call:
        one: Chiamata SAP
        other: Chiamate SAP
      sap_distribution_chain: SAP - Catena di distribuzione
      sap_fido_import_row:
        one: Documenti aperti
        other: Documenti aperti
      sap_internal_bank_institute:
        one: Banca interna
        other: Banche interne
      sap_payment_kind:
        one: Metodo di pagamento
        other: Metodi di pagamento
      sap_payment_term:
        one: Condizione di pagamento
        other: Condizioni di pagamento
      sap_product_cluster: SAP - Cluster prodotto
      sap_product_inforecord:
        one: InfoRecord
        other: InfoRecord
      sap_product_kind: SAP - Tipo prodotto
      sap_product_supplier: SAP - Fornitore prodotto
      sap_valuation_class: SAP - Classe di valorizzazione
      sap_withholding_tax: Ritenuta d'acconto
      service_kind: Tipologia servizio
      shipment:
        one: Spedizione
        other: Spedizioni
      statistic_group1: Gruppo statistico 1
      statistic_group2: Gruppo statistico 2
      statistic_group3: Gruppo statistico 3
      statistic_group4: Gruppo statistico 4
      street_type:
        one: Particella toponomastica
        many: Particelle toponomastiche
      thread_kind:
        one: Tipologia di gestione
        other: Tipologie di gestione
      tls_upfront:
        one: Anticipo
        other: Anticipi
      user: Utente
      value_kind: Valore
      warehouse: Sede
      wind_ngpos_activation:
        article_def: "l'"
        article_indef: "un'"
        one: Cessione credito
        other: Cessioni credito
    errors:
      models:
        dealer:
          attributes:
            previous_vat:
              not_found: La Partita Iva inserita in Partita Iva Precedente non risulta presente su Spazio Kolme.
        payment_request:
          attributes:
            dealer:
              blank: deve essere un partner esistente
            payment_requestable:
              blank: non trovato a sistema
        promo_device:
          attributes:
            serial:
              imei_status_not_valid: Il seriale risulta già utilizzato su POS. Non è possibile procedere con l'inserimento.
              imei_not_found: Il seriale inserito non è valido
              non_kolme_warehouse: Il magazzino del punto vendita selezionato non è collegato a Kolme
  prompts:
    product:
      company_brand: Scegli marchio...
      family_product: Scegli famiglia...
      italy_purchase_return: Scegli reso vendite Italia...
      italy_sales: Scegli vendite Italia...
      italy_sales_return: Scegli reso vendite Italia...
      statistic_group1: Scegli gruppo statistico 1...
      statistic_group2: Scegli gruppo statistico 2...
      statistic_group3: Scegli gruppo statistico 3...
      sap_distribution_chain: Scegli Catena di distribuzione...
      sap_product_cluster: Scegli Cluster prodotto...
      sap_product_kind: Scegli Tipo prodotto...
      sap_product_supplier: Scegli Fornitore prodotto...
      sap_valuation_class: Scegli Classe di valorizzazione...
  values:
    models:
      accounting_documents:
        add_filter: Applica almeno il filtro Partner per visualizzare i documenti contabili.
      ast_edit_payment_type:
        credit_card_type:
          amex: American Express
          diners: Diners
          mastercard: Mastercard
          visa: Visa
        payment_method:
          credit_card: Carta di credito
          sdd: SDD
      ast_unsolved_payment_proof:
        payment_method:
          bank_transfer: Bonifico bancario
          postal_payment_slip: Bollettino postale
      dealer_contacts:
        add_filter: Applica almeno un filtro per visualizzare i contatti del partner.
        empty_search_result_list: Non ci sono risultati per la ricerca effettuata
      dealers:
        add_filter: Applica almeno un filtro per visualizzare i partner.
        empty_search_result_list: Non ci sono risultati per la ricerca effettuata
      customers:
        add_filter: Applica almeno un filtro per visualizzare i clienti.
        empty_search_result_list: Non ci sono risultati per la ricerca effettuata
      items:
        add_filter: Applica almeno un filtro per visualizzare la lista articoli.
      macro_category:
        accessori: Accessori
        prodotti: Prodotti
        prodotti_attiva: Prodotti Attiva
        open_market: Open Market
        fwa: FWA
        sim: SIM
        telequale: Telequale
        trade_marketing: Marketing
        ricariche: Ricariche
        utilita: Utilità
        wind_tre: WINDTRE
        wind_tre_other: WINDTRE - Altro
        wind_tre_smartphone: WINDTRE - Smartphone
      minutes_vs_kind:
        f: Fisso
        fm: Fisso+Mobile
        m: Mobile
        undef: Non definito
      payment_method_requests_request_kinds:
        easycare_plus: EasyCare+
        financing: Anticipo finanziamento
        generic: Generico
        phone_activation: Anticipo attivazione
      product_origin:
        attiva: Attiva
        kolme: Kolme
      product_type:
        b: Bundle
        non_sim: Prodotto non SIM
        p: Prodotto
        pa: Prodotto attivabile
        r: Ricarica
        s: SIM
        undef: Non definita
      product_vat_type:
        17RC: 17RC - Inversione contabile IVA, art. 17, comma 6, lett. B del DPR 633/72
        '7403': 7403 - IVA assolta ex art. 74/E da Wind Tre SpA P.IVA 13378520152
        74FW: 74FW - IVA assolta ex art. 74/E da Fastweb SpA P.IVA 12878470157
        E74: E74 - IVA assolta alla fonte ex art. 74/D
        I21: I21 - IVA al 21%
        '22': 22 - IVA al 22%
        A02: A02 - Escluso art. 2
        74LK: 74LK - IVA assolta ex art. 74/E da Linkem SpA P.IVA 13456840159
        17RT: 17RT - Inversione contabile IVA, art. 17, comma 6, lett. C del DPR 633/72
      ribbon:
        featured: In evidenza
        incoming: In arrivo
        news: Novità
        normal: Normale
        promo: Promo
      sap_api_call:
        http_method:
          post: "creazione"
          patch: "modifica"
      sim_format:
        m: Micro
        'n': Nano
        s: Standard
        sm: Standard+Micro
        smn: Standard+Micro+Nano
        undef: Non definito
      threshold_kind:
        b: Bimestrali
        g: Giornalieri
        m: Mensili
        s: Settimanali
        undef: Non definita
      visibility_kind:
        i: Solo Int.
        'n': 'No'
        undef: Non definita
        'y': Sì
      warehouses:
        add_filter: Applica almeno un filtro per visualizzare i punti vendita.
        empty_search_result_list: Non ci sono risultati per la ricerca effettuata
