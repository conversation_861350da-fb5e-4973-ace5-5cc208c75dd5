class Util::StringUtil
  def self.clean_unpermitted_chars_from(text)
    text.gsub(/[^[:alnum:][:blank:][:punct:]]/, '').squeeze(' ').strip
  end

  def self.dhl_delivery_order_padded(shipment_id)
    format("%011d", shipment_id)
  end

  def self.pad_left(string, number, char = "0")
    string.ljust(number, char)
  end

  def self.pad_right(string, number, char = "0")
    string.rjust(number, char)
  end

  def self.unpad(string, char)
    loop { break if string.sub!(/^#{char}/, "").nil? }

    string
  end

  def self.to_bool(string)
    return true if string == true || string =~ /(true|t|yes|y|1)$/i
    return false if string == false || string.empty? || string =~ /(false|f|no|n|0)$/i

    raise ArgumentError.new("invalid value for Boolean: \"#{string}\"")
  end
end

