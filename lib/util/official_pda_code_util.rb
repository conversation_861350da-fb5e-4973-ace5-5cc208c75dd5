class Util::OfficialPdaCodeUtil
  attr_reader :url
  def initialize(url)
    @url= url
  end

  def get_code
    io = URI.open(url)
    reader = PDF::Reader.new(io)

    reader.pages.each do |page|
      codes = page.text.split("\n").select{ |text| text.downcase.include?('codice contratto') }
      codes_number = page.text.split("\n").select{ |text| text.downcase.include?('numero contratto') }

      if codes.any?
        code = codes.first.split(':').last.strip

        return code
      end

      if codes_number.any?
        code = codes_number.first.split(' ').last.strip

        return code
      else
        return 'unavailable_code'
      end
    end
  rescue StandardError => e
    'unavailable_code'
  end
end
