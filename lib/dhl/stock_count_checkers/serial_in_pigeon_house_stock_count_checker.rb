class Dhl::StockCountCheckers::SerialInPigeonHouseStockCountChecker < Dhl::StockCountCheckers::DhlStockCount<PERSON>he<PERSON>
  def find_mismatches
    error = {}

    if calculated_bookable_quantity != product.bookable_quantity
      bookable_quantity_hash = {
        actual: calculated_bookable_quantity,
        on_db:  product.bookable_quantity
      }
      error.merge!(bookable_quantity: bookable_quantity_hash)
    end

    total_count = Repositories::ItemRepository.in_system_owner_warehouse(product.id)

    if product.amount_in_system_owner_warehouse != total_count
      aisow_error_hash = {
        actual: total_count,
        on_db:  product.amount_in_system_owner_warehouse
      }
      error.merge!(amount_in_system_owner_warehouse: aisow_error_hash)
    end

    unless error.blank?
      product.update(amount_in_system_owner_warehouse: kolme_total_count,
                     bookable_quantity:                calculated_bookable_quantity)
    end

    error
  end
end
