class Dhl::Importers::DhlSerialCheckImporter < Importers::BaseImporter
  attr_reader :extra_serials_in_kolme, :file_path, :missing_items, :missing_serials_in_kolme, :serials_in_file

  def initialize(args = {})
    super({ file_path: args.fetch(:file_path) })
    @serials_in_file = []
    @missing_serials_in_kolme = []
    @extra_serials_in_kolme = []
    @missing_items = []
  end

  def import(success_callbacks, failure_callbacks)
    ensure_file_exists

    csv = CSV.new(read_file_content_and_replace_unknown_chars,
                  col_sep:     ";",
                  skip_blanks: true,
                  headers: :first_row).read

    csv.each do |row|
      process(row)
    end

    product_code = csv.first ? csv.first[0] : File.basename(file_path, ".*")

    product = Product.find_by_code(product_code)

    check_extra_serials_for(product.id)

    success_callbacks.each do |success_callback|
      success_callback.call(filename: filename,
                            missing_serials_in_kolme: missing_serials_in_kolme,
                            extra_serials_in_kolme: extra_serials_in_kolme,
                            missing_items: missing_items)
    end
  rescue StandardError => e
    failure_callbacks.each do |failure_callback|
      failure_callback.call(exception: e, filename: File.try(:basename, file_path || ""))
    end
  end

  private

  def check_extra_serials_for(product_id)
    serials = Item.dhl_warehouse.where(product_id: product_id).where(state: "instock").pluck(:serial)

    extra_serials_in_kolme << serials - serials_in_file

    extra_serials_in_kolme.flatten!
  end

  def process(row)
    serial = row[1].gsub(/"|\[|\]/, '')

    serials_in_file << serial
    item = Item.find_by_serial(serial)

    unless item
      missing_items << serial
      return
    end

    if item.warehouse != Warehouse.dhl_warehouse
      missing_serials_in_kolme << serial
    end
  end
end
