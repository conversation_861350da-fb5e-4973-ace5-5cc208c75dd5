class AfterSaleThreadProgressBar < CommonProgressBar
  STATES_MAPPING = {
    added: {
      percentage: 10,
      color: "info"
    },
    charge_kolme: {
      percentage: 50,
      color: "info"
    },
    charge_partner:  {
      percentage: 50,
      color: "info"
    },
    charge_operator: {
      percentage: 50,
      color: "info"
    },
    closed: {
      percentage: 100,
      color: "info"
    },
    canceled: {
      percentage: 0,
      color: "info"
    }
  }

  attr_accessor :after_sale_thread

  def initialize(after_sale_thread)
    @after_sale_thread = after_sale_thread
    super({ states_mapping: STATES_MAPPING, model: after_sale_thread.class })
  end

  def state
    return SpazioEnv.translate(after_sale_thread.state).to_s unless after_sale_thread.thread_messages.count.zero? && !canceled_or_closed?

    :added
  end

  def canceled_or_closed?
    after_sale_thread.canceled? || after_sale_thread.closed?
  end
end
