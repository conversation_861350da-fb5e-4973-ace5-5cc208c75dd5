module ConsoleUtils
  def detach_product_item(phone_activation_id, final_state = "instock")
    phone_activation = PhoneActivation.find(phone_activation_id)
    product_item = phone_activation.product_item
    update_and_log(product_item, :state, final_state)
    update_and_log(product_item, :phone_activation_id, nil)
  end

  def attach_product_item(phone_activation_id, item_serial, final_state = "activated")
    phone_activation = PhoneActivation.find(phone_activation_id)
    product_item = Item.find_by_serial(item_serial)
    update_and_log(phone_activation, :product_item_id, product_item.id)
    update_and_log(product_item, :state, final_state)
    update_and_log(product_item, :phone_activation_id, phone_activation.id)
  end

  def detach_sim_item(phone_activation_id, final_state = "instock")
    phone_activation = PhoneActivation.find(phone_activation_id)
    sim_item = phone_activation.sim_item
    update_and_log(sim_item, :state, final_state)
    update_and_log(sim_item, :phone_activation_id, nil)
  end

  def attach_sim_item(phone_activation_id, item_serial, final_state = "activated")
    phone_activation = PhoneActivation.find(phone_activation_id)
    sim_item = Item.find_by_serial(item_serial)
    update_and_log(phone_activation, :sim_item_id, sim_item.id)
    update_and_log(sim_item, :state, final_state)
    update_and_log(sim_item, :phone_activation_id, phone_activation.id)
  end

  def update_and_log(target_obj, field_name, new_value)
    new_value_to_s = (new_value.nil? ? "nil" : new_value)
    target_obj_to_s = "#{target_obj.class.name} with id #{target_obj.id}:"
    puts "#{target_obj_to_s} #{field_name} from #{target_obj.send(field_name)} to #{new_value_to_s}"
    target_obj.send(:update_column, field_name, new_value)
  end

  module_function :attach_product_item, :detach_product_item, :attach_sim_item, :detach_sim_item, :update_and_log
end
