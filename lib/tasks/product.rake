# encoding: utf-8

namespace :products do
  desc "Insert initial operator"
  task add_default_operator: [:environment] do
    Product.find_each do |product|
      print "."
      product.operators << product.operator if product.operator
    end
  end

  desc "Make dismissed products invisible"
  task make_dismissed_products_invisible: [:environment] do
    ActiveRecord::Base.connection.execute("update products set visibility = 'n' where dismissed_at is not null")
  end

  desc "Remove view_in_matrix flag from invisible products"
  task remove_view_in_matrix_flag_from_invisible_products: [:environment] do
    ActiveRecord::Base.connection.execute("update products set view_in_matrix = false where visibility = 'n'")
  end

  task check_and_set_serial_quantity: [:environment] do
    SerialAmountCheckerJob.new.perform
  end
end