require 'task_logging'
$stdout.sync = true

namespace :promo_devices do
  desc 'Re-does the IMEI burn operations for the promo devices that got a timeout error'
  task redo_burn: :environment do |t, args|
    include ::TaskLogging
    write_log(t, "Start IMEI burn redo")

    scope = PromoDevice.pending_burn.where(burn_imei_timeout: true)
    todo = scope.count
    done = 0
    errors = 0
    write_log(t, "total = #{todo}")
    scope.find_in_batches(batch_size: 5000).each do |group|
      group.each do |promo_device|
        begin
          res = PromoDeviceRetryBurn.run!(promo_device: promo_device)
          if res
            done += 1
          else
            error += 1
          end
        rescue Exception => exc
          errors += 1
          write_log(t, "#{promo_device.id} - exception: #{exc.message}")
          KolmeLogger.error(exc)
        end
      end
    end
    write_log(t, "End IMEI burn redo (todo = #{todo}, done = #{done}, errors = #{errors})")
  end
end
