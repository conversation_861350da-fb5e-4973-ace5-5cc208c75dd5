namespace :kolme do
  # This is a collection of one time scripts. Namespace them on the appropriate model,
  # when possible, and do keep them sorted by date.
  # Convention for task name: one_time:<model_name>:<timestamp>_<task description>

  namespace :credit_card do
    desc 'Setup warehouse brands by seeding brands and associating them from warehouse#brand column'
    task 'remove_expired_credit_card' => [:environment] do
      CreditCardService.remove_expired!
    end
    
    desc 'Masks credit card information from PaymentMethodDetail and AstEditPaymentType instances'
    task 'mask_everything' => [:environment] do
      CreditCardService.mask_everything!
    end
  end

  namespace :export_encrypted_data do
    task export_credit_cards: [:environment] do
      PaymentMethodDetail.where.not(encrypted_cc_number: nil).each do |pmd|
        CSV.open("tmp/credit_cards.csv", "a+", { col_sep: ";", row_sep: "\r\n" }) do |csv|
          csv << [pmd.id, pmd.cc_number, pmd.cc_cvv]
        end
      end
    end
  end

  namespace :import_credit_card_data do
    task import_credit_card_data: [:environment] do
      old_credit_cards = ["0000000000000000",  "111111111111111", "1111111111111111"]
      errors = []
      not_updated = []
      CSV.read("tmp/credit_cards.csv", { col_sep: ";", row_sep: "\r\n" }).each do |row|
        pmd = PaymentMethodDetail.where(id: row[0]).first
        if pmd && !old_credit_cards.include?(row[1])
          pmd.cc_number = row[1]
          pmd.cc_cvv = row[2]
          begin pmd.save!
            if pmd.cc_number != row[1]
              puts "#{pmd.id} was not updated correctly"
              not_updated << pmd
            end
          rescue StandardError => e
            puts "#{pmd.id}, #{e}"
            errors << pmd
          end
        end
      end
      puts "#{errors.size} errors"
      puts "#{not_updated.size} not updated correctly"
    end
  end

  task clean_duplicated_cards: [:environment] do
    credit_card_ids_to_remove = []
    duplicated_cards = ActiveRecord::Base.connection.execute("SELECT GROUP_CONCAT(id), pan, COUNT(*) c FROM payments_credit_cards Group BY pan HAVING c > 1")
    duplicated_cards_ids = duplicated_cards.map { |card| card[0] }
    
    duplicated_cards_ids.each do |ids_group|
      credit_card_ids_to_remove << ids_group.split(",")[0...-1]
    end

    ActiveRecord::Base.connection.execute("delete from payments_credit_cards where id in (#{credit_card_ids_to_remove.join(',')})")
  end
end
