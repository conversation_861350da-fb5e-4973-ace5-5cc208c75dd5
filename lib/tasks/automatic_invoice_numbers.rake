# encoding: utf-8
namespace :automatic_invoice_numbers do
  desc %(Setup automatic invoice numbers for each year)
  task setup: [:environment] do
    first_year = AutomaticInvoice.first.created_at.year
    last_year = Time.now.year

    (first_year..last_year).each  do |year|
      number = AutomaticInvoice.fattura_cliente_finale.of_year(year).last.invoice_number
      number = number.match(/SC(\d+)/)[1].to_i
      AutomaticInvoiceNumber.create!(number: number, year: year)
      puts "#{year}: invoice numbers set to start from #{number}"
    end
  end
end
