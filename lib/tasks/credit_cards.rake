# encoding: utf-8

namespace :credit_cards do
  desc 'Delete stored cc_numbers from activations'
  task delete_stored_cc_numbers_from_activations: [:environment] do
    phone_activations = Repositories::PhoneActivationRepository.new.find_stored_cc_number_to_delete
    phone_activations.each do |phone_activation|
      puts "Deleting cc_number and cvv for payment_method_detail##{phone_activation.payment_method_detail.id} for phone_activation##{phone_activation.id}"
      phone_activation.payment_method_detail.cc_number = '0000'
      phone_activation.payment_method_detail.cc_cvv = '000'
      phone_activation.payment_method_detail.save(validate: false)
      puts "Deleted cc_number and cvv for phone_activation##{phone_activation.id}"
    end
  end

  desc 'Clean credit cards written in phone_activation notes'
  task clean_cc_numbers: [:environment] do
    errors = []

    PhoneActivation.not_cc_cleaned.find_in_batches(start: 0, batch_size: 100) do |phone_activations|
      phone_activations.each_with_index do |phone_activation, _index|
        begin
          CreditCardUpdater.update(phone_activation)
        rescue => ex
          puts ex.backtrace
          errors << %( errori: #{ex.message} )
        end
      end
    end
  end
end
