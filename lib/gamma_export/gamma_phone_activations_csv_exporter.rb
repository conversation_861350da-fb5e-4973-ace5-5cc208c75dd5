class GammaExport::GammaPhoneActivationsCsvExporter < GammaExport::BaseGammaCsvExporter
  attr_reader :phone_activations

  DOC_KIND = 'Documenti_FA'

  def initialize(args = {})
    super(DOC_KIND)
    @phone_activations = args.fetch(:phone_activations, PhoneActivationService.gamma_exportables)
  end

  def export
    return if phone_activations.empty?

    collect_rows
    write_rows if rows.any?
    send_error_mail if errors.any?
  end

  private

  def collect_rows
    phone_activations.each do |phone_activation|
      begin
        rows << phone_activation.gamma_csv_row
      rescue StandardError => e
        errors << I18n.t('gamma_exporter.error_message', model: "Attivazione #{phone_activation.hex_id}", error: e)
        KolmeLogger.error I18n.t('gamma_exporter.error_message', model: "Attivazione #{phone_activation.id}", error: e)
      end
    end
  end
end
