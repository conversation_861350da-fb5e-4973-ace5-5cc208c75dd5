class GammaExport::GammaImeiReservationsCsvExporter < GammaExport::BaseGammaCsvExporter
  attr_reader :imei_reservations

  DOC_KIND = 'Documenti_LK'

  def initialize(args = {})
    super(DOC_KIND)
    @imei_reservations = args.fetch(:imei_reservations, ImeiReservation.gamma_exportables)
  end

  def export
    return if imei_reservations.empty?

    collect_rows
    write_rows if rows.any?
    send_error_mail if errors.any?
  end

  private

  def collect_rows
    imei_reservations.each do |imei_reservation|
      begin
        rows << imei_reservation.gamma_csv_row
      rescue StandardError => e
        errors << I18n.t('gamma_exporter.error_message', model: "Lok-me #{imei_reservation.id}", error: e)
        KolmeLogger.error I18n.t('gamma_exporter.error_message', model: "Lok-me #{imei_reservation.id}", error: e)
      end
    end
  end
end
