class Repositories::FinancingRepository < Repositories::BaseRepository
  SEARCH_STATE_GROUP_WORKABLE       = "workable".freeze
  SEARCH_STATE_GROUP_ALL            = "all".freeze
  SEARCH_STATE_GROUP_ALL_AND_SYSTEM = "all_and_system".freeze

  SEARCH_STATE_GROUP_FILTERS = [
    ["Mostra da lavorare", SEARCH_STATE_GROUP_WORKABLE],
    ["Mostra tutto", SEARCH_STATE_GROUP_ALL],
    ["Mostra tutto + sistema", SEARCH_STATE_GROUP_ALL_AND_SYSTEM]
  ].freeze

  def initialize(args = {})
    super
  end

  def self.default_search_state_group_for(user)
    user.internal_user? ? SEARCH_STATE_GROUP_WORKABLE : nil
  end

  def find(id)
    Financing.find(id)
  end

  def badge_count(statuses)
    Sunspot.search(Financing) do
      with :access_levels, access_levels_for_user if access_levels_for_user.any?
      statuses.each do |key, value|
        with key, value
      end
    end.total
  end

  def search(params)
    state = provide_financing_state(params)

    Sunspot.search(Financing) do
      with :access_levels, access_levels_for_user if access_levels_for_user.any?
      with_field :dealer_id, user_access.dealer_id
      with :id, params[:id] if params[:id].present?
      with_field :dealer_name, params[:dealer_name]
      with_field :product_name, params[:product_name]

      with(:state, params[:state][0]) if params[:state].present? && params[:state][0].present?

      fulltext_field params[:customer_full_name], [:customer_full_name]

      if params[:created_at_from].present?
        with(:created_at).greater_than(params[:created_at_from].to_date)
        params[:created_at_to] = params[:created_at_from] unless params[:created_at_to].present?
      end

      with(:created_at).less_than(params[:created_at_to].to_date.end_of_day) if params[:created_at_to].present?

      if user.is?(:agent)
        any_of do
          with(:sub_agent_wind_tre_id, user.id)
          with(:agent_wind_tre_id, user.id)
        end
      end

      if user.is?(:dealer, :agent)
        if params[:show_canceled_by_system].blank?
          with(:canceled_by_system, false)
        end
      end

      with_field :state, state

      order_by(:id, :desc)
      paginate page: (params[:page] || 1)
    end
  end

  def waiting_for_serial
    result = Sunspot.search(Financing) { with :state, "awaiting_serial_assignment" }

    result.results
  end

  private

  def provide_financing_state(params)
    state_group = (params[:state_group] || [Repositories::FinancingRepository.default_search_state_group_for(user)]).first

    case state_group
    when SEARCH_STATE_GROUP_WORKABLE
      return %w(awaiting_serial_assignment on_hold approved pre_approved)
    when SEARCH_STATE_GROUP_ALL
      return %w(awaiting_serial_assignment on_hold approved pre_approved refused shipped retired)
    when SEARCH_STATE_GROUP_ALL_AND_SYSTEM
      return (Financing::STATES | [Financing::WAITING_FOR_CONSEL_STATE])
    else
      []
    end
  end
end
