class Repositories::WindTreDataRepository
  attr_reader :scope

  def initialize(args = {})
    @scope = args.fetch(:scope, WindTreData)
  end

  def fixed_counts(warehouse_ids)
    return 0 unless WindTreData.any?

    WindTreData.where(warehouse_id: warehouse_ids)
               .where(plans: WindTreData::FIXED_PLANS)
               .where(data_attivazione_portfolio: WindTreData.maximum(:data_attivazione_portfolio).beginning_of_month..WindTreData.maximum(:data_attivazione_portfolio).end_of_month)
               .sum(:numero_portfoli_netto)

    # total = Sunspot.search(WindTreData) do
    #   with :warehouse_id, warehouse_ids
    #   with :plans, WindTreData::FIXED_PLANS
    #
    #   with(:data_attivazione_portfolio).between(WindTreData.maximum(:data_attivazione_portfolio).beginning_of_month..WindTreData.maximum(:data_attivazione_portfolio).end_of_month)
    # end.total
    #
    # return 0 unless total > 0
    #
    # Sunspot.search(WindTreData) do
    #   with :warehouse_id, warehouse_ids
    #   with :plans, WindTreData::FIXED_PLANS
    #
    #   with(:data_attivazione_portfolio).between(WindTreData.maximum(:data_attivazione_portfolio).beginning_of_month..WindTreData.maximum(:data_attivazione_portfolio).end_of_month)
    #
    #   stats :numero_portfoli_netto
    #
    #   paginate page: 1, per_page: total
    # end.stats(:numero_portfoli_netto).sum.to_i
  end

  def mobile_counts(warehouse_ids)
    scope
      .current_month_datas
      .mobile
      .windtre_branded
      .with_warehouses(warehouse_ids)
      .sum(:numero_portfoli_netto)
  end

  def mobile_counts_for_counters(warehouse_ids)
    scope
      .where(data_attivazione_portfolio: WindTreData.maximum(:data_attivazione_portfolio).beginning_of_month..WindTreData.maximum(:data_attivazione_portfolio).end_of_month)
      .mobile
      .windtre_branded
      .with_warehouses(warehouse_ids)
      .sum(:numero_portfoli_netto)
  end

  def mobile_counts_by_plans(warehouse_ids, plans_type)
    return 0 unless WindTreData.any?

    like_condition = plans_type == WindTreData::TIED_FULLTEXT ? WindTreData::TIED_FULLTEXT : "%#{plans_type}%"

    WindTreData.where(warehouse_id: warehouse_ids)
               .where(brand: WindTreData::WINDTRE_BRAND)
               .where.not(plans: WindTreData::FIXED_PLANS)
               .where(data_attivazione_portfolio: WindTreData.maximum(:data_attivazione_portfolio).beginning_of_month..WindTreData.maximum(:data_attivazione_portfolio).end_of_month)
               .where('plans like ?', like_condition)
               .sum(:numero_portfoli_netto)

    # total = Sunspot.search(WindTreData) do
    #   with :warehouse_id, warehouse_ids
    #   fulltext plans_type, fields: [:plans_fulltext]
    #   with :brand, WindTreData::WINDTRE_BRAND
    #
    #   without :plans, WindTreData::FIXED_PLANS
    #   with(:data_attivazione_portfolio).between(WindTreData.maximum(:data_attivazione_portfolio).beginning_of_month..WindTreData.maximum(:data_attivazione_portfolio).end_of_month)
    # end.total

    # return 0 unless total > 0

    # Sunspot.search(WindTreData) do
    #   with :warehouse_id, warehouse_ids
    #   fulltext plans_type, fields: [:plans_fulltext]
    #   with :brand, WindTreData::WINDTRE_BRAND
    #
    #   without :plans, WindTreData::FIXED_PLANS
    #   with(:data_attivazione_portfolio).between(WindTreData.maximum(:data_attivazione_portfolio).beginning_of_month..WindTreData.maximum(:data_attivazione_portfolio).end_of_month)
    #
    #   stats :numero_portfoli_netto
    #
    #   paginate page: 1, per_page: total
    # end.stats(:numero_portfoli_netto).sum.to_i
  end

  def very_counts(warehouse_ids)
    return 0 unless WindTreData.any?
    # WindTreData.where(warehouse_id: warehouse_ids)
    #            .where(brand: WindTreData::VERY_BRAND).where(data_attivazione_portfolio: WindTreData.maximum(:data_attivazione_portfolio).beginning_of_month..WindTreData.maximum(:data_attivazione_portfolio).end_of_month).sum(:numero_portfoli_netto)
    WindTreData.where(warehouse_id: warehouse_ids)
               .where(brand: WindTreData::VERY_BRAND)
               .where.not(plans: WindTreData::FIXED_PLANS)
               .where(data_attivazione_portfolio: WindTreData.maximum(:data_attivazione_portfolio).beginning_of_month..WindTreData.maximum(:data_attivazione_portfolio).end_of_month)
               .sum(:numero_portfoli_netto)

    # total = Sunspot.search(WindTreData) do
    #   with :warehouse_id, warehouse_ids
    #   with :brand, WindTreData::VERY_BRAND
    #   without :plans, WindTreData::FIXED_PLANS
    #   with(:data_attivazione_portfolio).between(WindTreData.maximum(:data_attivazione_portfolio).beginning_of_month..WindTreData.maximum(:data_attivazione_portfolio).end_of_month)
    # end.total
    #
    # return 0 unless total > 0
    #
    # Sunspot.search(WindTreData) do
    #   with :warehouse_id, warehouse_ids
    #   with :brand, WindTreData::VERY_BRAND
    #
    #   without :plans, WindTreData::FIXED_PLANS
    #   with(:data_attivazione_portfolio).between(WindTreData.maximum(:data_attivazione_portfolio).beginning_of_month..WindTreData.maximum(:data_attivazione_portfolio).end_of_month)
    #
    #   stats :numero_portfoli_netto
    #
    #   paginate page: 1, per_page: total
    # end.stats(:numero_portfoli_netto).sum.to_i
  end

  def current_month_count(warehouse_ids)
    scope
      .current_month_datas
      .with_warehouses(warehouse_ids)
      .sum(:numero_portfoli_netto)
  end

  def last_month_count(warehouse_ids)
    scope
      .last_month_datas
      .with_warehouses(warehouse_ids)
      .sum(:numero_portfoli_netto)
  end

  def two_months_back_count(warehouse_ids)
    scope
      .two_months_back_datas
      .with_warehouses(warehouse_ids)
      .sum(:numero_portfoli_netto)
  end
end
