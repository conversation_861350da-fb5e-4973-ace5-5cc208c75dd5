class Repositories::WindNgposActivationRepository < Repositories::BaseRepository
  def search(params)
    dealer_ids = user_access.dealer_ids_for_solr_with_dealer_fallback
    search_params = params[:search]

    Sunspot.search(WindNgposActivation) do
      with_field :warehouse_id, user.warehouse_ids if user.is?(:agent)

      unless params.dig(:search, :show)
        with :to_work, true unless user.is?(:dealer) || user.is?(:agent)
      end

      if search_params
        with_field :material_code, search_params[:material_code]
        with_field :offer_kind, search_params[:offer_kind]
        if search_params[:vat_or_fiscal_code].present?
          any_of do
            with :vat, search_params[:vat_or_fiscal_code]
            with :fiscal_code, search_params[:vat_or_fiscal_code]
          end
        end
        with search_params[:status].first.to_sym, true if search_params[:status]&.first&.present?
        with_date_gte(:created_at, search_params[:created_at_gteq])
        with_date_lte(:created_at, search_params[:created_at_lteq])
        with :id, search_params[:id] if search_params[:id].present?
        with_field :imei_or_serial, search_params[:imei_or_serial]
        with_field :warehouse_name, search_params[:warehouse_name]
        fulltext_field search_params[:dealer_name], [:dealer_name], exact_match: true
        fulltext_field search_params[:complete_customer_name], [:complete_customer_name], exact_match: true

        if search_params[:show].present? && search_params[:show].first != "all"
          with search_params[:show].first.to_sym, true
        end
      end

      with_field :dealer_id, dealer_ids

      if user.is?(:dealer)
        with_field :offer_kind, WindNgposActivation::INSTALLMENTS_OFFER_KINDS.first
        any_of do
          without :imei_or_serial, nil
          with :to_work_and_upload, true unless params.dig(:search, :show)
          with search_params[:show].first.to_sym, true if params.dig(:search, :show)
        end
      end

      order_by(:created_at, :desc)

      paginate page: (params[:page] || 1), per_page: params[:per_page]
    end
  end

  def company_name_or_full_name_contains(term)
    dealer_ids = dealer_ids_for_user

    user_scope = if dealer_ids.empty?
                   WindNgposActivation
                 else
                   WindNgposActivation.joins(warehouse: [:dealer]).where(warehouses: { dealer_id: dealer_ids })
                 end

    user_scope
      .where("wind_ngpos_activations.company_name LIKE ? OR concat_ws(' ', wind_ngpos_activations.first_name, wind_ngpos_activations.last_name) LIKE ?", "%#{term}%", "%#{term}%")
  end

  def dealer_name_contains(term)
    dealer_ids = dealer_ids_for_user

    filtered_dealer_ids = Sunspot.search(WindNgposActivation) do
      with :dealer_id, dealer_ids
      fulltext_field term, :dealer_name, like: true

      facet :dealer_id

      paginate page: 1, per_page: 30
    end.facet(:dealer_id).rows.map(&:value)

    Dealer.where(id: filtered_dealer_ids)

    # if dealer_ids.empty?
    #   WindNgposActivation.left_outer_joins(warehouse: [:dealer])
    #     .where("dealers.name LIKE ? OR wind_ngpos_activations.dealer_company_name LIKE ?", "%#{term}%", "%#{term}%")
    # else
    #   WindNgposActivation.joins(warehouse: [:dealer])
    #     .where("dealers.name LIKE ? AND dealers.id IN (?)", "%#{term}%", dealer_ids)
    # end
  end

  def to_work
    Sunspot.search(WindNgposActivation) do
      with :to_work, true
    end.total
  end

  def with_imei_or_serial(serial)
    Sunspot.search(WindNgposActivation) do
      with :imei_or_serial, serial
    end.results.last
  end

  private

  def dealer_ids_for_user
    user_access.dealer_ids_for_solr
  end
end
