class Repositories::UserRepository < Repositories::BaseRepository
  delegate :find, to: User

  def initialize(args = {})
    super
  end

  def agent_list
    if user.is?(:agent)
      agents_scope = user.is?(:sub_agent) ? 'sub_agents' : 'primary_agents'
    else
      agents_scope = 'agents'
    end

    User
      .with_includes
      .enabled_internal_users
      .send(agents_scope)
      .with_access_levels(user.access_levels)
      .order("internal_user_details.first_name ASC")
  end

  def agent_list_for_disable_agent
    if user.is?(:sub_agent)
      return unless user.parent_agent

      self.class.sub_agents_of(user.parent_agent)
    else
      agent_list
    end
      .where('users.id <> ?', user.id)
  end

  def self.agents_for(agent_kind, access_level = nil)
    scope = User.enabled_internal_users.agents.order('internal_user_details.first_name ASC').includes(:internal_user_detail)
    case agent_kind
    when 'primary_agent'
      scope.primary_agents
    when 'agent_parent_agent'
      scope.primary_agents.where('internal_user_details.access_level in (?)', access_level == AccessLevel::ALL ? AccessLevel::ALL_ACCESS_LEVELS : [access_level, AccessLevel::ALL])
    when 'agent_wind_tre'
      scope.primary_agents.where('internal_user_details.access_level in (?)', [AccessLevel::ONLY_WIND_TRE, AccessLevel::ALL])
    when 'sub_agent_wind_tre'
      scope.sub_agents.where('internal_user_details.access_level in (?)', [AccessLevel::ONLY_WIND_TRE, AccessLevel::ALL])
    else
      scope
    end
  end

  def self.agents_for_partner_type(partner_types)
    User.enabled_internal_users
        .primary_agents
        .joins(:internal_user_detail)
        .where('internal_user_details.partner_type IN (?)', partner_types)
        .order('internal_user_details.first_name ASC')
  end

  def self.sub_agents_of(agent)
    agent.child_agents
      .includes(:internal_user_detail)
      .enabled_internal_users
      .order('internal_user_details.first_name ASC')
  end
end
