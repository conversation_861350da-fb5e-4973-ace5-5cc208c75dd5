class DataComposer::PlanData<PERSON><PERSON><PERSON>
  def self.fetch_ids(dealer, _user, options)
    dealer_category_id  = dealer.dealer_category_id
    dealer_province_ids = dealer.warehouse_province_ids

    search = Sunspot.new_search(Plan)

    search.build do
      with :enabled_for_phone_activation_insert, true
      with :ad_hoc, dealer.ad_hoc_ids

      any_of do
        with :province_ids, dealer_province_ids
        with :province_ids, nil
      end

      with(:tariffa_tls, dealer.operator_tls?)
      with(:dealer_category_ids, [dealer_category_id])

      if options
        with(:portability_operator_ids, options[:portability_operator_id]) if options[:portability_operator_id].present?
        with(:customer_kind_ids,        options[:customer_kind_id])        if options[:customer_kind_id].present?
        with(:coverage_typology,        options[:coverage_typology]) if options[:coverage_typology].present?
      end
    end

    search = dealer.add_md_very_filters_to(search)
    search = dealer.add_dealer_kind_visiblity_filters_to(search)

    total = search.execute.total

    search.build do
      paginate(per_page: total)
    end

    search.execute.hits.map { |h| h.to_param.to_i }
  end
end
