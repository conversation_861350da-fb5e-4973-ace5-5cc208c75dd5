# frozen_string_literal: true

class DataComposer::OptionDataComposer < DataComposer::DataComposer
  attr_accessor :data

  def initialize(options = {})
    if options[:plan_id]
      data = DataComposer::DataFetcher.fetch(:option_id, sorting: 'name ASC')

      # if operator is selected, limit options to operator specific ones
      operator_ids_from_params           = options[:operator_id].presence || Operator.pluck(:id)
      operator_ids_from_params_filter    = DataComposer::DataFilter.new(operator_id: operator_ids_from_params)
      visible_options_from_params_filter = DataComposer::DataFetcher.fetch(:option_id, filter: operator_ids_from_params_filter.filters)
      data &= visible_options_from_params_filter

      if options[:dealer_id]
        user = DataComposer::DataFetcher.fetch_record(:user, options[:user_id])
        visibility = user&.is?(:dealer) ? 'y' : %w[y i]

        visible_options = Option.where(visibility: visibility)
        dealer = DataComposer::DataFetcher.fetch_record(:dealer, options[:dealer_id])

        if dealer
          operator_ids = filter_operators(dealer.operator_ids, options[:operator_id], options)

          data_filter = DataComposer::DataFilter.new(operator_id: operator_ids)
          data_filter.add(:plan_id, options)

          dealer_options = DataComposer::DataFetcher.fetch(:option_id, filter: data_filter.filters)
          data = data & dealer_options & visible_options
        end
      end
    end

    @data = data || []
  end
end
