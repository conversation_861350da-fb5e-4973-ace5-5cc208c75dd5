require 'activerecord-import'

class PurchaseOrders::PurchaseOrdersImporter < Importers::BaseImporter
  attr_reader :creator, :file_path, :session_timestamp

  def initialize(creator, file_path, session_timestamp)
    @creator           = creator
    @session_timestamp = session_timestamp

    super(file_path: file_path)
  end

  def import
    purchase_orders = []

    read_file_by_rows do |row|
      next unless row.length > 2

      purchase_orders << purchase_order_from(row)
    end

    PurchaseOrder.import(purchase_orders)
    Sunspot.index PurchaseOrder.last(purchase_orders.count)
    Sunspot.commit(true)
    PurchaseOrder.last(purchase_orders.count).each do |purchase_order|
      purchase_order.product&.update_arriving_count!
    end

    PurchaseOrdersImporterResponse.new(errors: [], imported_rows: purchase_orders.count)
  rescue StandardError => e
    PurchaseOrdersImporterResponse.new(errors: [e.message])
  end

  private

  def attributes_from(row)
    {
      supplier_order_number:  row[2],
      order_date:             Date.strptime(row[3], '%Y%m%d'),
      order_line:             row[4],
      supplier_name:          row[6],
      supplier_gamma_id:      row[7],
      product_code:           row[8],
      ordered_quantity:       row[9],
      expected_delivery_date: Date.strptime(row[10], '%Y%m%d'),
      purchase_price:         row[11],
      note:                   row[12],
      session_timestamp:      session_timestamp,
      creator:                creator
    }
  end

  def purchase_order_from(row)
    purchase_order = PurchaseOrder.new(attributes_from(row))

    if Sunspot.search(PurchaseOrder) { with :unique_activerecod_import_index, purchase_order.unique_activerecod_import_index }.total > 0
      raise StandardError.new('Le righe nel file di import sono già presenti a database')
    end
    raise StandardError.new(purchase_order.errors.full_messages) unless purchase_order.valid?

    purchase_order
  end

  class PurchaseOrdersImporterResponse < OpenStruct
    def mail_subject
      success? ? I18n.t('mail.purchase_order.subject.success') : I18n.t('mail.purchase_order.subject.failure')
    end

    def success?
      errors.empty?
    end
  end
end
