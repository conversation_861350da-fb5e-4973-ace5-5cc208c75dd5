require 'csv'

class DrmsCsvProcessor
  class << self
    def read_and_create_hash(file_path)
      csv_content = CSV.read(file_path, headers: false)

      return {} unless csv_content.size >= 2

      headers = parse_csv_row(csv_content[0].join(','))
      csv_content[1..-1].map do |row|
        values = parse_csv_row(row.join(','))

        adjusted_values = adjust_cliente_and_segmento_client(headers, values)

        headers.zip(adjusted_values).to_h
      end
    end

    def parse_csv_row(row_string)
      cleaned_row = row_string.gsub(/\\\"/, '"').gsub(/\"/, '')
      columns = cleaned_row.split(',')
      columns
    end

    def adjust_cliente_and_segmento_client(headers, values)
      segmento_client_index = headers.index("segmento_client")
      cliente_index = headers.index("cliente")

      segmento_client = values[segmento_client_index]

      if segmento_client == "S"
        values.delete_at(segmento_client_index)

        if values[segmento_client_index] == ""
          values.delete_at(segmento_client_index)
        end
      elsif !["CONS", "MIB", "NULL"].include?(segmento_client)
        values[cliente_index] = "#{values[cliente_index]}, #{segmento_client}"
        values.delete_at(segmento_client_index)
      end

      values
    end
  end
end
