class Exporters::PhoneActivationDocumentsExporter < Exporters::CommonExporter
  attr_reader :document_root, :model, :zip_exported_path, :zip_path

  def initialize(phone_activation)
    @model         = phone_activation
    @document_root = [Rails.root, Settings.phone_activation_exporter.path].join("/")
    @zip_path      = Settings.phone_activation_exporter.path

    super({})
  end

  def run
    prepare_storage_directory
    download_documents
    zip_documents
  end
end
