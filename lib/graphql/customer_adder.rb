class Graphql::CustomerAdder < Graphql::PersonAdder
  REGULA_DOC_KINDS_TRANSLATION = {
    'identity card'   => {
      national: 'Carta Identità',
      foreign:  'CI straniera'
    },
    'passport'        => {
      national: 'Passaporto',
      foreign:  'PP straniero'
    },
    'driving license' => {
      national: 'Patente',
      foreign:  'PA straniera'
    },
    'stay permit'     => {
      national: 'Permesso di soggiorno',
      foreign:  'Permesso di soggiorno'
    },
    'health card'     => {
      national: 'Tesserino Sanitario (solo per minori)',
      foreign:  'Tesserino Sanitario (solo per minori)'
    }
  }.freeze

  attr_accessor :id_doc_country

  def add_person
    customer_parameters_hash = args[:customer_input_type].to_h

    sanitize_fiscal_code(customer_parameters_hash)

    phone_activation = PhoneActivation.find(args[:id])

    if phone_activation.customer&.draft?
      customer_kind = phone_activation.customer.customer_kind
      phone_activation.customer.destroy if ((phone_activation.customer&.linked_models || []) - [phone_activation])&.blank?
    else
      customer_kind = CustomerKind.find_by_code(CustomerKind::CLIENTE_PRIVATO)
    end

    incoming_cf_or_vat = customer_parameters_hash[:vat].presence || customer_parameters_hash[:cf]
    customer           = if incoming_cf_or_vat.present?
                           Customer.find_or_initialize_by(cf_or_vat: incoming_cf_or_vat)
                         else
                           Customer.new
                         end

    phone_activation.update_column(:customer_document_kind, customer_parameters_hash[:regula_document_name])

    if customer.new_record?
      customer               = Customer.new(build_attributes(customer_parameters_hash))
      customer.uuid          = UUIDTools::UUID.random_create.to_s
      customer.customer_kind = customer_kind

      customer.set_cf_or_vat
    else
      customer_parameters_hash.except!(*Customer::APP_READONLY_FIELDS)
      customer_params   = build_attributes(customer_parameters_hash)
      encrypted_changes = customer.build_encrypted_changes(customer_params)
      changes           = if customer.update(customer_params)
                            customer.previous_changes.merge(encrypted_changes)
                          else
                            customer.changes.merge(encrypted_changes)
                          end

      changes.except!(*Customer::APP_READONLY_FIELDS + changes.keys.select { |k| k.start_with? 'encrypted' })

      customer.app_changes = changes.merge({ 'preexisting_customer': true })
    end

    customer.save(validate: false)

    if phone_activation.minor_customer&.draft? &&
      ((phone_activation.minor_customer&.linked_models || []) - [phone_activation])&.blank?
      phone_activation.minor_customer.destroy
    end

    if args[:minor_customer_input_type] && phone_activation.plan.doppia_anagrafica?
      minor_customer_parameters_hash = args[:minor_customer_input_type].to_h
      sanitize_fiscal_code(minor_customer_parameters_hash)

      if minor_customer_parameters_hash[:cf].present? && minor_customer_parameters_hash[:cf].upcase == customer.cf
        return [false, ['Le due persone risultano avere lo stesso codice fiscale. Prego ripetere la scansione']]
      end

      minor_customer = Customer.find_or_initialize_by(cf_or_vat: minor_customer_parameters_hash[:cf])

      if minor_customer.new_record? || minor_customer == customer # handle case where neither customers have cf (e.g. tourists)
        minor_customer               = Customer.new(build_attributes(minor_customer_parameters_hash))
        minor_customer.uuid          = UUIDTools::UUID.random_create.to_s
        minor_customer.customer_kind = customer_kind

        minor_customer.set_cf_or_vat
      else
        minor_customer_parameters_hash.except!(*Customer::APP_READONLY_FIELDS)
        minor_customer_params = build_attributes(minor_customer_parameters_hash)
        encrypted_changes     = minor_customer.build_encrypted_changes(minor_customer_params)
        changes               = if minor_customer.update(minor_customer_params)
                                  minor_customer.previous_changes.merge(encrypted_changes)
                                else
                                  minor_customer.changes.merge(encrypted_changes)
                                end
        changes.except!(*Customer::APP_READONLY_FIELDS + changes.keys.select { |k| k.start_with? 'encrypted' })
        minor_customer.app_changes = changes.merge({ 'preexisting_customer': true })
      end

      minor_customer.save(validate: false)
    end

    phone_activation.update_columns(customer_id: customer.id, minor_customer_id: minor_customer&.id)
  end

  private

  def build_attributes(raw_attributes)
    remove_disallowed_fields(raw_attributes)

    # to avoid dealing with empty strings in each handling method
    remove_empty_fields(raw_attributes)
    # clean bad characters coming from app OCR
    remove_unwanted_characters(raw_attributes)

    handle_dates(raw_attributes)

    handle_id_doc_country(raw_attributes)
    handle_id_doc_kind(raw_attributes)

    handle_full_address(raw_attributes)
    handle_birthplace(raw_attributes)

    # clear out any null values that would overwrite existing customers' fields
    remove_empty_fields(raw_attributes)
    # remove unwanted characters coming from Google Maps addresses
    remove_unwanted_characters(raw_attributes)

    adjust_case(raw_attributes)

    # the customer will have to be edited in Spazio in the full form
    # and this requires a non-customer origin
    raw_attributes.delete(:regula_document_name)
    raw_attributes.merge(
      origin: 'phone_activation',
      state:  'draft'
    )
  end

  def handle_id_doc_country(raw_attributes)
    self.id_doc_country = Country.where(
      peoplesoft_code: raw_attributes[:id_doc_country] || Country.italia.first.peoplesoft_code
    ).or(
      Country.where("json_contains(peoplesoft_code_lookup, '\"#{raw_attributes[:id_doc_country]}\"')")
    ).first
    raw_attributes[:id_doc_country_id] = id_doc_country&.id
    raw_attributes.except!(:id_doc_country)
  end

  def handle_id_doc_kind(raw_attributes)
    if raw_attributes[:id_doc_country_id].present?
      is_national = raw_attributes[:id_doc_country_id] == Country.italia.first.id

      spazio_id_doc_name = REGULA_DOC_KINDS_TRANSLATION
                             .dig(raw_attributes[:id_doc_kind], is_national ? :national : :foreign)

      doc_kind           = IdentityDocumentKind.find_by_posng_value spazio_id_doc_name

      raw_attributes[:identity_document_kind_id] = doc_kind&.id
    end

    raw_attributes.except!(:id_doc_kind)
  end

  def handle_full_address(raw_attributes)
    return raw_attributes.except!(:city, :address_street_name, :number, :zip) if id_doc_kind_is_passport?(raw_attributes) ||
      (raw_attributes[:city].nil? && !raw_attributes[:regula_document_name]&.include?('Italy - Id Card (2016)'))

    full_address_query = "#{raw_attributes[:address_street_name]} " +
      "#{raw_attributes[:number]} " + "#{raw_attributes[:city]} " +
      id_doc_country&.description.to_s

    full_address = GoogleMapsService.address_search(full_address_query)

    if full_address.present?
      raw_attributes[:address_street_type_id] = full_address.street_type&.id
      raw_attributes[:address_street_name]    = full_address.street_name if full_address.street_name.present?
      raw_attributes[:number]                 = full_address.street_number if full_address.street_number.present?
      raw_attributes[:city_id]                = full_address.city&.id
      raw_attributes[:zip]                    = full_address.zip
    end

    raw_attributes.except!(:city)
  end

  def handle_birthplace(raw_attributes)
    full_address_query = "#{raw_attributes[:birth_place]} " +
      "#{raw_attributes[:birth_province]} " +
      raw_attributes[:birth_country].to_s

    full_address = GoogleMapsService.address_search(full_address_query)

    if full_address.present?
      raw_attributes[:birth_place]       = full_address.city_description if full_address.city_description.present?
      raw_attributes[:birth_province_id] = full_address.province&.id
      raw_attributes[:birth_country_id]  = full_address.country&.id
    end

    raw_attributes[:birth_place]&.gsub!(/\(.*\)/, '')&.strip!

    raw_attributes.except!(:birth_province, :birth_country)
  end

  def id_doc_kind_is_passport?(raw_attributes)
    IdentityDocumentKind.find_by_id(raw_attributes[:identity_document_kind_id])&.passport?
  end
end
