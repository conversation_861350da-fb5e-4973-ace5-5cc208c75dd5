class Downloaders::DocumentDownloader
  attr_reader :downloader, :consumer, :model

  def initialize(args = {})
    downloaders_factory       = args.fetch(:downloaders_factory, Factories::DownloadersFactory.new)
    download_consumer_factory = args.fetch(:download_consumer_factory, Factories::DownloadConsumerFactory.new)

    @model      = args.fetch(:model, nil)
    @downloader = downloaders_factory.for(model.class.name.underscore)
    @consumer   = download_consumer_factory.for(model.class.name.underscore).new(model)
  end

  def download(args = {})
    downloader.new(model, args).run(
      lambda do |downloaded_file|
        consumer.success(downloaded_file)
      end,
      lambda do |message|
        consumer.failure(message)
      end
    )
  end
end
