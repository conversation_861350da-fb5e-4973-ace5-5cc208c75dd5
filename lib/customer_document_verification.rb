class CustomerDocumentVerification
  def initialize(customer, phone_activation = nil)
    @customer = customer
    @phone_activation = phone_activation
  end

  def verify
    return true unless @phone_activation
    return true if @customer.turista?

    format_document_number if needs_document_number_formatting?

    verification_service = WindtreDocumentVerificationService.new(@customer, @phone_activation)
    verification_service.verify_document
  end

  private

  def needs_document_number_formatting?
    return false unless @phone_activation.operator.very_mobile?
    return false unless @customer.identity_document_kind.name == 'Carta di Identità straniera'
    true
  end

  def format_document_number
    number = @customer.id_doc_number.to_s

    letters = number.scan(/[A-Za-z]/)
    digits = number.scan(/[0-9]/)
    letters = (letters + ["A", "A", "A", "A"])[0...4]
    digits = (digits + ["0", "0", "0", "0", "0"])[0...5]

    formatted = letters[0] + letters[1] + digits.join + letters[2] + letters[3]

    @customer.id_doc_number = formatted
  end
end