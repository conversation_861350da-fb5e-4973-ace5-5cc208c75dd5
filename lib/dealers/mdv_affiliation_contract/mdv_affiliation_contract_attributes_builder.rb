class Dealers::MdvAffiliationContract::MdvAffiliationContractAttributesBuilder
  attr_reader :dealer

  def initialize(dealer)
    @dealer = dealer
  end

  def build
    {
      date: Date.today.strftime('%d/%m/%Y'),
      dealer_codice_fiscale: dealer.codice_fiscale,
      dealer_full_address: dealer.full_address,
      dealer_name: dealer.name,
      dealer_pec: dealer.pec
    }.merge!(signature_attributes)
  end

  private

  def signature_attributes
    signature = dealer.signature_obj
    {
      # #Digital signature
      automatic_signature: I18n.t('helpers.mdv_affiliation_contract.digital_signature.automatic'),
      dealer_signature: signature.dealer&.signature
    }
  end
end
