module Notifications
  class Notifications::UploadDocuments
    attr_reader :alert_message, :operation_id, :phone_activation

    def initialize(parameters)
      @operation_id     = parameters[:operation_id]
      @phone_activation = PhoneActivation.find(parameters[:phone_activation_id])
      @alert_message    = Alerts::PhoneActivationAlertMessage.new(@phone_activation)
    end

    def available?
      return if phone_activation.plan.tariffa_tls?

      operation_id == OperationConstants::INSERITA_DA_PARTNER &&
        phone_activation.phone_activation_kind.code != "FCPSMK" &&
        !phone_activation.upload_documents_completed? &&
        phone_activation.paper_signature? &&
        !phone_activation.has_all_required_documents?
    end

    def notify
      if phone_activation.documents.pluck(:kind).include? "documents"
        message = Alerts::PhoneActivationAlertMessage::PHONE_ACTIVATION_UPLOAD_CONTRACT
      else
        message = Alerts::PhoneActivationAlertMessage::PHONE_ACTIVATION_UPLOAD_DOCUMENTS
      end

      alert_message.insert_message(message)
    end
  end
end
