module Notifications
  class Notifications::PhoneActivationKo
    attr_reader :alert_message, :operation_id, :phone_activation, :status, :operation_outcome

    OPERATIONS = [OperationConstants::UPLOAD_DOCUMENTI,
                  OperationConstants::CONTROLLO_ANAGRAFICA,
                  OperationConstants::CONTROLLO_METODO_DI_PAGAMENTO,
                  OperationConstants::CONTROLLO_DOCUMENTI,
                  OperationConstants::ATTIVAZIONE_COMPLETATA,
                  OperationConstants::CONTROLLO_COPERTURA,
                  OperationConstants::CONTROLLO_AMMINISTRATIVO,
                  OperationConstants::TECHNICAL_VERIFICATION,
                  OperationConstants::LINE_INSTALLATION,
                  OperationConstants::AT<PERSON><PERSON><PERSON><PERSON><PERSON>_INSTALLATA_CONFERMA_VERBALE,
                  OperationConstants::ESITO_RICARICA_CONTESTUALE,
                  OperationConstants::CONTROLLO_DOCUMENTI_COMPASS,
                  OperationConstants::ATTIVAZIONE_INSERI<PERSON>,
                  OperationConstants::PHONE_ACTIVATION_CONFIRMED,
                  OperationConstants::ATTIVAZIONE_DIFFERITA,
                  OperationConstants::IMEI_VERIFICATION,
                  OperationConstants::PDC_CONFERMATA,
                  OperationConstants::ASSEGNAZIONE_SERIALE,
                  OperationConstants::SEGNALA_UTILIZZO_SERIALE,
                  OperationConstants::ATTIVAZIONE_VERIFICATA,
                  OperationConstants::VERIFICA_METODO_DI_PAGAMENTO,
                  OperationConstants::PAGAMENTO_ANTICIPO,
                  OperationConstants::VERIFICA_CONVERGENZA].freeze

    def initialize(parameters)
      @operation_id     = parameters[:operation_id]
      @phone_activation = PhoneActivation.find(parameters[:phone_activation_id])
      @alert_message    = Alerts::PhoneActivationAlertMessage.new(phone_activation)
      @status           = parameters.fetch(:status, nil)
    end

    def available?
      status == "ko" && OPERATIONS.include?(operation_id)
    end

    def notify
      phone_activation.alerts.where(todo: true, archived: false).update_all(archived: true)
      alert_message
        .insert_message(Alerts::PhoneActivationAlertMessage::PHONE_ACTIVATION_FAILED)
    end
  end
end
