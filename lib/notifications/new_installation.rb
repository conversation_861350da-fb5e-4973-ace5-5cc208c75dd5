module Notifications
  class NewInstallation
    HOURS_EXPIRATION = 4

    attr_reader :operation_id, :phone_activation, :appointment_detail, :status

    def initialize(parameters)
      @operation_id       = parameters[:operation_id]
      @status             = parameters.fetch(:status, nil)
      @phone_activation   = PhoneActivation.find(parameters[:phone_activation_id])
      @appointment_detail = phone_activation.appointment_detail
    end

    def available?
      status == 'ok' &&
        operation_id == OperationConstants::INSERITA_DA_PARTNER &&
        appointment_detail&.third_party_installation? &&
        appointment_detail&.dealer.present?
    end

    def notify
      AppointmentDetailService.new(appointment_detail).notify_new_installation_to([appointment_detail.dealer])
    end
  end
end