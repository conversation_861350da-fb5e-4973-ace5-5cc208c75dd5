module Alerts
  class PhoneActivationAlertMessage < AlertMessageCommon
    PHONE_ACTIVATION_UPLOAD_DOCUMENTS                  = { type: "PhoneActivationUploadDocuments", operation_id: OperationConstants::UPLOAD_DOCUMENTI, todo: true }.freeze
    PHONE_ACTIVATION_UPLOAD_CONTRACT                   = { type: "PhoneActivationUploadContract", operation_id: OperationConstants::UPLOAD_DOCUMENTI, todo: true }.freeze
    PHONE_ACTIVATION_UPLOAD_PDC                        = { type: "PhoneActivationUploadPdc", operation_id: OperationConstants::UPLOAD_PDC_FIRMATA, todo: true }.freeze
    PHONE_ACTIVATION_UPLOAD_PDC_AND_RECEIPT            = { type: "PhoneActivationUploadPdcAndReceipt", operation_id: OperationConstants::UPLOAD_PDC_FIRMATA, todo: true }.freeze
    PHONE_ACTIVATION_UPLOAD_FINANCING                  = { type: "PhoneActivationUploadFinancing", operation_id: 31, todo: true }.freeze
    PHONE_ACTIVATION_SUCCESS                           = { type: "PhoneActivationSuccess", operation_id: nil, todo: false }.freeze
    PHONE_ACTIVATION_UPLOAD_INSTALLATION_DOCUMENTATION = { type: "PhoneActivationUploadInstallationDocumentation", operation_id: 28, todo: true }.freeze
    PHONE_ACTIVATION_OPERATION_OUTCOME                 = { type: "PhoneActivationOperationOutcome", operation_id: nil, todo: true }.freeze
    PHONE_ACTIVATION_FAILED                            = { type: "PhoneActivationFailed", operation_id: nil, todo: false }.freeze
    PHONE_ACTIVATION_ADD_NUMBER_REBINDING              = { type: "PhoneActivationAddNumberRebinding", operation_id: OperationConstants::SEGNALAZIONE_NUMERO_ATTIVO, todo: true }.freeze
    PHONE_ACTIVATION_ASSIGN_SERIAL                     = { type: "PhoneActivationAssignSerial", operation_id: OperationConstants::ASSEGNAZIONE_SERIALE, todo: true }.freeze
    PHONE_ACTIVATION_LANDLINE_SUCCESS                  = { type: "PhoneActivationLandlineSuccess", operation_id: nil, todo: false }.freeze

    def initialize(phone_activation = nil, operation_outcome = nil, alert = nil)
      phone_activation_obj = phone_activation.nil? ? alert.alertable : phone_activation
      action_description   = alert.ko_reason.action_description unless alert.nil? || alert.ko_reason.nil?

      params                = {
        dealer_id:      phone_activation_obj.dealer_id,
        warehouse_id:   phone_activation_obj.warehouse_id,
        alertable_type: phone_activation_obj.class,
        alertable_id:   phone_activation_obj.id,
        alertable:      phone_activation_obj,
        customer_id:    phone_activation_obj.customer.id
      }

      params[:ko_reason_id] = operation_outcome.ko_reason.id unless operation_outcome.nil?
      params[:operation_id] = operation_outcome.operation.id unless operation_outcome.nil?

      placeholders = {
        "#phone_activation.hex_id"                        => phone_activation_obj.hex_id,
        "#phone_activation.customer.calculate_uniq_name"  => phone_activation_obj.customer.calculate_uniq_name,
        "#operation_outcome.ko_reason.action_description" => action_description,
        "#phone_activation.product_item.serial"           => phone_activation_obj&.product_item&.serial
      }

      prepare(params, placeholders)
    end
  end
end
