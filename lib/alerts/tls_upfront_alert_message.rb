module Alerts
  class TlsUpfrontAlertMessage < AlertMessageCommon
    attr_accessor :tls_upfront

    UPFRONT_FAILED = { type: "TlsUpfrontFailed", operation_id: nil, todo: true }.freeze
    
    def initialize(tls_upfront)
      @tls_upfront = tls_upfront

      params = {
        dealer_id: tls_upfront.dealer.id,
        warehouse_id: tls_upfront.dealer.main_warehouse.id,
        alertable_type: tls_upfront.class,
        alertable_id: tls_upfront.id,
        alertable: tls_upfront,
      }

      placeholders = {
        "#tls_upfront_id" => "AN#{tls_upfront.id}",
        "#customer_name" => tls_upfront.customer.to_s,
        "#phone_activation_id" => tls_upfront.phone_activation.hex_id
      }

      prepare(params, placeholders) 
    end
  end
end