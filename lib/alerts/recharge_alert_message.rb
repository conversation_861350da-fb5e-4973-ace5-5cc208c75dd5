module Alerts
  class RechargeAlertMessage < AlertMessageCommon
    RECHARGE_FAILED = { type: "RechargeFailed", operation_id: nil, todo: true }.freeze

    def initialize(recharge = nil, alert = nil)
      recharge_obj = recharge.nil? ? alert.alertable : recharge

      params = {
        dealer_id:      recharge_obj.dealer.id,
        warehouse_id:   recharge_obj.dealer.main_warehouse.id,
        alertable_type: recharge_obj.class,
        alertable_id:   recharge_obj.id,
        alertable:      recharge_obj
      }

      placeholders = {
        "#phone_activation.hex_id"             => recharge_obj.phone_activation.hex_id,
        "#phone_activation.customer.uniq_name" => recharge_obj.phone_activation.customer.uniq_name
      }

      prepare(params, placeholders)
    end
  end
end