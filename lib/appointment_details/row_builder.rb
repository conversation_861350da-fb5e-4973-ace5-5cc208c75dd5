class AppointmentDetails::Row<PERSON>uilder
  attr_reader :appointment_detail

  def initialize(appointment_detail)
    @appointment_detail = appointment_detail
  end

  def self.header_row(system_name)
    row = [
      'Codice Ordine CRM',
      'Numero Telefonico Principale',
      'Indirizzo Cliente',
      'Data appuntamento',
      'Inizio appuntamento'
    ]
    row.unshift('Nominativo cliente') if system_name == Province::SITE_SYSTEM

    row
  end

  def build
    row = [
      appointment_detail.np_x_order_code,
      appointment_detail.phone_activation.main_phone_number,
      appointment_detail.full_location_address,
      appointment_date,
      appointment_time
    ]
    row.unshift(appointment_detail.phone_activation.customer.uniq_name) if appointment_detail.system_name == Province::SITE_SYSTEM

    row
  end

  private

  def appointment_date
    return 'Da definire' if appointment_detail.unable_to_proceed? || (appointment_detail.draft? && appointment_detail.dealer != appointment_detail.phone_activation.dealer)
    return 'Da definire' if appointment_detail.waiting_appointment? && appointment_detail.appointment_date.nil?

    appointment_detail.appointment_date.strftime('%d/%m/%Y')
  end

  def appointment_time
    return if appointment_detail.unable_to_proceed?
    return if appointment_detail.waiting_appointment? && appointment_detail.appointment_date.nil?

    "#{appointment_detail.time_slot_from}:00"
  end

  def time_slot
    return unless appointment_detail.time_slot_from && appointment_detail.time_slot_to

    "#{appointment_detail.time_slot_from} - #{appointment_detail.time_slot_to}"
  end
end
