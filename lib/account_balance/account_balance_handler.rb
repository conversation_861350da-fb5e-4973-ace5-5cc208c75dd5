class AccountBalance::AccountBalanceHandler
  def create_account_balance_item(options); end

  def batch_execute(dealer_group)
    dealer_group.each do |dealer|
      AccountBalance.create(dealer_id: dealer.id, balance: dealer.recharges_credit_available, amount: dealer.recharges_credit_available, balanceable_type: "AccountBalanceInitial", balanceable_id: AccountBalanceInitial::ACCOUNT_BALANCE_INITIAL_ID)
    end
  end
end
