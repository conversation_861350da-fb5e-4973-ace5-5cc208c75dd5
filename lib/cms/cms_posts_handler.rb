class Cms::CmsPostsHandler
  attr_reader :user

  def initialize(user)
    @user = user
  end

  def provide_posts_for_admin(params = {})
    page        = params[:page]
    operator_id = params[:operator_id]

    Operator.unscoped do
      page.cms_posts.where(operator_id: operator_id).includes(:operator).order(:topic_position, :category_position, :position).group_by(&:topic)
    end
  end

  def provide_posts_for_user(params = {})
    page        = params[:page]
    operator_id = params[:operator_id]
    pagination  = params[:pagination]

    ability     = AbilityFactory.build_ability_for(user)
    user_access = UserAccess.new(user)
    page_code   = page.cms_template.code

    if %w(blog blog_social news).include? page_code
      operator_ids = filtered_operators_for_user(user_access)
    end

    posts_per_page = posts_per_page_depending_on_template(page_code)

    cms_posts = if %w(blog blog_social news).include? page_code
                  page
                    .cms_posts
                    .published
                    .joins("left join cms_post_operators on cms_post_operators.cms_post_id = cms_posts.id")
                    .where("cms_post_operators.operator_id is null or cms_post_operators.operator_id in (?) and published = 1", operator_ids).distinct
                elsif operator_id == Operator::KOLME_ID && page_code != "videos"
                  page
                    .cms_posts
                    .published
                    .joins("left join cms_post_operators on cms_post_operators.cms_post_id = cms_posts.id")
                    .where("cms_post_operators.operator_id is null or cms_post_operators.operator_id in (?) and cms_posts.operator_id = 20 and published = 1", operator_ids).distinct
                else
                  page
                    .cms_posts
                    .published
                    .where(operator_id: operator_id)
                end

    cms_posts = posts_visibility(cms_posts, user_access)

    if page_code == "videos"
      return cms_posts.where.not(topic: [nil,'']).order(:topic_position, :category_position, :position).group_by(&:topic)
    end

    cms_posts = sort_posts_depending_on_template(cms_posts, page_code)

    cms_posts = cms_posts.select { |post| post if ability.can?(:show, post) }

    if page_code == "guides"
      cms_posts.select! { |post| [post&.link.present?, post&.file.present?].any? }
    end

    pagination ? cms_posts.paginate(page: pagination_page_from(params), per_page: posts_per_page) : cms_posts
  end

  private

  def filtered_operators_for_user(user_access)
    ((Operator.unscoped.pluck(:id) & user_access.operator_ids) << Operator::KOLME_ID).uniq
  end

  def pagination_page_from(params)
    params[:active] ? params[:pagination][:page] : 1
  end

  def posts_per_page_depending_on_template(template_code)
    template_code.in?(%w(blog blog_social)) ? 4 : 15
  end

  def posts_visibility(posts, user_access)
    return posts unless user_access.dealer?

    if user.kolme_master?
      posts.where(kolme_master_visibility: [nil, :exclusive])
    else
      posts.where(kolme_master_visibility: [nil, :hidden])
    end
  end

  # todo fix sort
  def sort_posts_depending_on_template(posts, template_code)
    if template_code == "news"
      posts.order!("post_date DESC, title ASC")
    else
      posts.order!("position ASC")
    end
  end
end
