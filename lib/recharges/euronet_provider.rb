class Recharges::EuronetProvider
  attr_reader :recharge_size, :recharge_kind, :extra_recharge

  def initialize(args = {})
    @recharge_kind  = args.fetch(:recharge_kind, nil)
    @recharge_size  = args.fetch(:recharge_size, nil)
    @extra_recharge = args.fetch(:extra_recharge, false)
  end

  def execute
    quantity = recharge_size.pin? ? 1 : recharge_size.euronet_quantity
    recharge = nil

    quantity.times do |index|
      source_recharge_size = RechargeSizeService.new.provide_source_for(recharge_size)

      if recharge_kind.recharge_id
        recharge = Recharge.find(recharge_kind.recharge_id)
      else
        recharge_params = { phone_number:        recharge_kind.phone_activation.present? ? nil : recharge_kind.phone_number,
                            recharge_size_id:    source_recharge_size.id,
                            dealer_id:           recharge_kind.dealer.id,
                            provider:            RechargeSize::PROVIDER_EURONET,
                            phone_activation_id: recharge_kind.phone_activation.try(:id),
                            user_id:             recharge_kind.user_id,
                            remote_ip_address:   recharge_kind.remote_ip_address,
                            from_app:            recharge_kind.from_app }
        recharge_params[:secure_token] = "#{recharge_kind.secure_token}-#{index}" unless recharge_kind.secure_token.nil?

        recharge = Recharge.new(recharge_params)
        if !recharge.save && recharge.errors[:secure_token].present?
          KolmeLogger.error(
            "*** WARNING *** creation of a recharge with non-unique secure_token has been attempted!",
            extra: { "dealer_id" => recharge.dealer_id }
          )
          raise "Invalid token"
        end
      end

      recharge.update(recharged_at: Time.now,
                                 user_id:      recharge_kind.user_id,
                                 provider:     RechargeSize::PROVIDER_EURONET,
                                 notes:        recharge_kind.notes || recharge.notes)

      @euronet_transaction = EuronetTransaction.create(call_type: EuronetTransaction::CHECK_REQUEST, recharge: recharge)
      @euronet             = Euronet.new(@euronet_transaction)
      @euronet.check

      finalize_recharge(extra_recharge, recharge_size, source_recharge_size, recharge)
    end
    recharge
  end

  def finalize_recharge(extra_recharge, recharge_size, source_recharge_size, recharge)
    if recharge.status_ok? && !extra_recharge
      # Aggiungo un account_balance
      recharge.balanceable_amount = Recharge.recharge_amount(recharge_size, source_recharge_size)
      AccountBalance::AccountBalanceItem.new(recharge).record!
    end
    if extra_recharge
      recharge.update(dealer_id: Dealer.where(role: Dealer::ROLE_SYSTEM).first.id)
    end
    if recharge.phone_activation.present?
      if recharge.status_ok? && recharge.dealer == recharge.phone_activation.dealer
        recharge.phone_activation.update(completed_recharges_amount: (recharge.phone_activation.completed_recharges_amount + source_recharge_size.amount),
                                                    completed_recharges_count:  (recharge.phone_activation.completed_recharges_count + 1))
      end
      operation_outcome_params = { operation_id:        30,
                                   user_id:             0,
                                   phone_activation_id: recharge.phone_activation.id }
      operation_outcome        = Operations::OperationsHandler.reserve(operation_outcome_params)
      current_status           = if recharge.phone_activation.recharges.where(dealer_id: recharge.phone_activation.dealer_id).present?
                                   # se almeno una delle ricariche constestuali in carico al dealer è in corso 'ko_r' se nessuna è in corso 'ok'
                                   recharge.phone_activation.recharges.where(dealer_id: recharge.phone_activation.dealer_id).select { |r| r.can_retry? and not r.status_ok? }.present? ? "ko_r" : "ok"
                                 else
                                   recharge.phone_activation.recharges.where(dealer_id: Dealer.where(role: Dealer::ROLE_SYSTEM).first.id).select { |r| r.can_retry? and not r.status_ok? }.present? ? "ko_r" : "ok"
                                 end

      if operation_outcome
        Operations::OperationsHandler.process(operation_outcome.id, operation_outcome_params.merge!(status: current_status, busy: false))
        PrivatePub.publish_to("/phone_activations/#{recharge.phone_activation.id}", OperationService.new.refresh_batch_operations_js(operation_outcome.phone_activation))
        OperationService.new.refresh_operations_js(operation_outcome.phone_activation)
      end
    end

    recharge.increment!(:recharge_attempts)

    recharge.reload.index
    Sunspot.commit(true)
  end
end
