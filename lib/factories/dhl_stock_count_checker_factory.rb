class Factories::DhlStockCountCheckerFactory
  def self.for(product)
    if product.sim?
      Dhl::StockCountCheckers::SimStockCountChecker
    elsif product.serial_in_pigeon_house?
      Dhl::StockCountCheckers::SerialInPigeonHouseStockCountChecker
    elsif product.serial?
      Dhl::StockCountCheckers::SerialStockCountChecker
    else
      raise StandardError.new "Don't know how to check stock for product #{product.code}"
    end
  end
end
