class Importers::CreditAndFidoImporter < Importers::BaseImporter
  attr_reader :errors, :dealer_ids, :mode

  def initialize(args)
    @mode = args.fetch(:mode, :auto)
    super({ file_path: args.fetch(:file_path) })
    @errors = []
    @dealer_ids = []
  end

  def import(success_callbacks, failure_callbacks)
    ensure_file_exists

    csv = CSV.new(read_file_content_and_replace_unknown_chars,
                  col_sep:           ";",
                  skip_blanks:       true,
                  headers:           :first_row,
                  header_converters: ->(h) { h.strip.gsub(" ", "").underscore }).read

    if mode == :auto && csv.size < 5000
      raise StandardError.new("Attenzione! I fidi non sono stati aggiornati, il file contiene troppe poche anagrafiche.")
    end

    Dealer.update_all(assigned_fido:          0,
                      remaining_fido:         0,
                      available_credit:       0,
                      unpaid_invoices_amount: 0,
                      sdd_payments_amount:    0,
                      daily_operations:       0)

    csv.each do |row|
      update_dealers_from(row)
    end

    reindex_dealers if dealer_ids.any?

    success_callbacks.each do |success_callback|
      success_callback.call(filename: filename, errors: errors)
    end
  rescue StandardError => e
    failure_callbacks.each do |failure_callback|
      failure_callback.call(exception: e, filename: File.try(:basename, file_path || ""))
    end
  end

  private

  def update_dealers_from(row)
    dealer = Dealer.find_by_partita_iva(row["partita_iva"])

    if dealer
      dealer_ids << dealer.id
    else
      errors << "Dealer non trovato - #{row["rag_sociale"]} (#{row['partita_iva']})"
      return
    end

    attributes = {
      assigned_fido:          row["fido_anagrafico"].gsub(",", "."),
      remaining_fido:         row["residuo"].gsub(",", "."),
      available_credit:       row["credito_disponibile"].gsub(",", "."),
      unpaid_invoices_amount: row["fattura_da_pagare"].gsub(",", "."),
      sdd_payments_amount:    row["effetti_da_liberalizzare"].gsub(",", ".")
    }

    dealer.update_columns(attributes)
  end

  def reindex_dealers
    dealers = Dealer.where(id: dealer_ids)
    Jobs::ParallelReindexJob.new(collection: dealers).run
  end
end
