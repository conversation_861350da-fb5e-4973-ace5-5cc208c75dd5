module Importers
  class ProductListImporter < Importers::BaseImporter
    COLUMNS_TO_FIELDS_MAPPING = {
      'codice_prodotto' => 'code',
      'ordinabile' => 'can_be_requested',
      'attivabile_da_magazzino_differita_kolme' => 'activable_system_owner',
      'utilizzabile_in_lok_me' => 'enabled_for_imei_reservation',
      'quantita_massima_ordinabile' => 'max_qty_orderable',
      'sconto_percentuale' => 'default_percent_discount',
      'max_di_sconto' => 'max_agent_discount_percentage',
      'visibilita' => 'ribbon',
      'utilizzabile_in_lok_me_per_windtre_diretti' => 'enabled_for_imei_reservation_franchising',
      'visibile' => 'visibility',
      'prodotto_contingentato_lok_me_solo_attivazioni' => 'imei_reservation_curtailed',
      'prezzo_al_dealer_a_iva_esclusa' => 'dealer_price_vat_excluded_a',
      'prezzo_b_iva_esclusa_differito_canale_kolme' => 'dealer_price_vat_excluded_b',
      'prezzo_c_iva_esclusa_differito_canale_windtre_diretti' => 'dealer_price_vat_excluded_c',
      'prezzo_di_acquisto_gamma' => 'gamma_purchase_price',
      'utilizzabile_in_promo_device_very' => 'available_promo_device_very',
      'utilizzabile_in_lok_me_per_gallery_gd' => 'enabled_for_imei_reservation_gallery'
    }.freeze

    PRODUCT_RIBBONS_MAPPING = I18n.translate('values.models.ribbon').invert

    attr_reader :failed_codes, :success_updates

    def initialize(file_path: nil)
      super({
              file_path: file_path,
              columns: COLUMNS_TO_FIELDS_MAPPING.keys,
            })

      @failed_codes = []
      @success_updates = []
    end

    def import
      csv_table = validate_csv_and_return!

      update_products(csv_table)

      ProductListImporterResponse.new(
        success_updates: success_updates, failed_codes: failed_codes, other_errors: []
      )
    rescue StandardError => e
      ProductListImporterResponse.new(
        success_updates: success_updates, failed_codes: [], other_errors: [e.message]
      )
    end

    def update_products(csv_table)
      csv_table.map do |row|
        product = Product.find_by(code: row['codice_prodotto'])

        unless product
          failed_codes << "#{row['codice_prodotto']} - non trovato"
          next
        end

        row['visibilita'] = PRODUCT_RIBBONS_MAPPING[row['visibilita']] if row['visibilita']

        mapped_attributes = row.to_h.transform_keys { |key| COLUMNS_TO_FIELDS_MAPPING[key] }
        mapped_attributes = mapped_attributes.reject { |_, value| value.blank? }
        if row['max_di_sconto'] == "0"
          mapped_attributes.merge!(max_agent_discount_percentage: nil)
        end
        is_updated = product.update(mapped_attributes)

        if is_updated
          success_updates << "#{row['codice_prodotto']} - #{product.name}"
        else
          failed_codes << "#{row['codice_prodotto']} - #{product.errors.full_messages.join(', ')}"
        end
      end
    end

    private

    def validate_csv_and_return!
      csv = CSV.new(read_file_content_and_replace_unknown_chars,
                    col_sep: ';',
                    skip_blanks: true,
                    headers: :first_row,
                    header_converters: ->(h) { h.parameterize.underscore }).read

      ensure_mandatory_headers_present(csv)
      csv
    end

    def ensure_mandatory_headers_present(csv)
      raise 'Missing "Codice prodotto" column in CSV file' unless csv.headers.include?('codice_prodotto')
    end

    class ProductListImporterResponse < OpenStruct
      def mail_subject
        if success_updates.count.positive? && failed_codes.empty?
          I18n.t('mail.product_list_importer.subject.success')
        elsif success_updates.count.positive? && failed_codes.any?
          I18n.t('mail.product_list_importer.subject.success_with_errors')
        else
          I18n.t('mail.product_list_importer.subject.failure')
        end
      end
    end
  end
end
