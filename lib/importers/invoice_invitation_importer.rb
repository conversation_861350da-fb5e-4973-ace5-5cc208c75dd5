class Importers::InvoiceInvitationImporter < Importers::BaseImporter
  HEADER_COLUMNS = %w(tipo data data_riferimento descrizione compenso extracompenso codice_iva partita_iva_partner appartenenza).freeze

  class InvalidVAT < ActiveRecord::ActiveRecordError
    def initialize
      super(I18n.t('activerecord.errors.messages.record_invalid', errors: 'Partita IVA non trovata.'))
    end
  end

  attr_accessor :on_success, :on_failure

  def initialize(file_path, on_success = [], on_failure = [])
    @on_success = on_success
    @on_failure = on_failure

    super({ file_path: file_path, columns: HEADER_COLUMNS })
  end

  def import
    ensure_file_exists

    csv = CSV.new(read_file_content_and_replace_unknown_chars,
                  col_sep:           ';',
                  skip_blanks:       true,
                  headers:           :first_row,
                  header_converters: ->(h) { h.strip.parameterize(separator: '_') }).read
    ensure_headers_present(csv)

    csv.each do |row|
      dealer = Dealer.find_by(partita_iva: row['partita_iva_partner'])

      raise InvalidVAT.new unless dealer

      IiRow.create!(
        row_type:      row['tipo'].downcase,
        row_date:      row['data'],
        referred_date: row['data_riferimento'],
        description:   row['descrizione'],
        reward:        row['compenso'],
        extra_reward:  row['extracompenso'],
        vat_code:      row['codice_iva'],
        dealer:        dealer,
        operator:      row['appartenenza']
      )
    end

    on_success.each do |receiver|
      receiver.call(filename: filename)
    end
  rescue StandardError => e
    on_failure.each do |receiver|
      receiver.call(exception: e, filename: File.try(:basename, file_path || ''))
    end
  end
end
