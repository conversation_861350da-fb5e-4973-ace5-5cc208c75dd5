class Ws::Calls::SendDealerDestinationsToGamma
  CALL_NAME_CLIENT = "_new_gamma_export_dealer_destinazione_cliente".freeze
  CALL_NAME_SUPPLIER = "_new_gamma_export_dealer_destinazione_fornitore".freeze

  attr_reader :response_parser, :gamma_caller, :request_builder, :code_service_class

  def initialize(args = {})
    @response_parser    = args.fetch(:response_parser, Ws::DealerGammaResponseParser.new)
    @request_builder    = args.fetch(:request_builder,
                                     Ws::RequestBuilders::SendDealerDestinationToGammaRequestBuilder.new)
    @code_service_class = args.fetch(:destination_code_service,
                                     Ws::Services::DealerGammaDestinationCodeService)
  end

  def run(dealer, success_callback: nil, failure_callback: nil, role: :client)
    gamma_code_field = role == :client ? :gamma_code_dest_cli : :gamma_code_dest_for
    code_service = code_service_class.new(dealer)
    call_name     = "Ws::Calls::SendDealerDestinationsToGamma::CALL_NAME_#{role.to_s.upcase}".constantize

    success = true
    dealer.warehouses.each do |warehouse|
      caller_params = gamma_caller_params(warehouse, gamma_code_field).merge!(call_name: call_name)
      gamma_caller = Ws::Calls::GenericGammaCall.new(new_ws_endpoint: "destinatarioMg")

      unless warehouse.send(gamma_code_field)
        code = code_service.build(role)
        warehouse.update_column(gamma_code_field, code)
      end

      xml = request_builder.request(warehouse, role)
      gamma_call = gamma_caller.run(xml, caller_params)

      if response_parser.errors?(gamma_call.response_to_json)
        failure_callback.call(
          RuntimeError.new("Gamma service returned error"),
          caller_params[:method],
          caller_params[:call_name]
        )
        success = false
      else
        success_callback&.call(gamma_call)
      end
    rescue StandardError => e
      failure_callback.call(e, caller_params[:method], caller_params[:call_name])
      success = false
    end

    success
  end

  private

  def gamma_caller_params(warehouse, gamma_code_field)
    params = { target: warehouse }

    if warehouse.send(gamma_code_field)
      dealer_gamma_code_field_value = warehouse.dealer.send(gamma_code_field == :gamma_code_dest_cli ? :gamma_code_cli : :gamma_code_for)

      tipo_cf_value = gamma_code_field == :gamma_code_dest_cli ? "0" : "1"

      object_specific_endpoint = "#{warehouse.send(gamma_code_field)}?CliforCg44=#{dealer_gamma_code_field_value}&TipocfCg44=#{tipo_cf_value}"

      params.merge!(method: :put, object_specific_endpoint: object_specific_endpoint)
    else
      params.merge!(method: :post)
    end

    params
  end
end
