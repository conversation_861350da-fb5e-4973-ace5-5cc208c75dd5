class Ws::Calls::SendDealerGeneralAnagraphicsToGamma
  CALL_NAME = '_new_gamma_export_dealer_anagrafica'.freeze
  FORCE_ERROR_CODE = 84218

  attr_reader :response_parser, :gamma_caller, :request_builder

  def initialize(args = {})
    @response_parser = args.fetch(:response_parser, Ws::DealerGammaResponseParser.new)
    @gamma_caller    = args.fetch(:gamma_caller,
                                  Ws::Calls::GenericGammaCall.new(new_ws_endpoint: 'anagrafica'))
    @request_builder = args.fetch(:request_builder,
                                  Ws::RequestBuilders::SendDealerGeneralAnagraphicsToGammaRequestBuilder.new)
  end

  def run(dealer, success_callback: nil, failure_callback: nil)
    caller_params = gamma_caller_params(dealer)
    xml           = request_builder.request(dealer)
    gamma_call = gamma_caller.run(xml, caller_params)
    if response_parser.errors?(gamma_call.response_to_json)
      failure_callback.call(
        RuntimeError.new('Gamma service returned error'),
        caller_params[:method],
        caller_params[:call_name]
      )
      false
    else
      new_general_code = response_parser.general_code(gamma_call.response_to_json)
      dealer.update_columns(gamma_code_gen: new_general_code) if new_general_code

      success_callback&.call(gamma_call)
      true
    end
  rescue StandardError => e
    failure_callback.call(e, caller_params[:method], caller_params[:call_name])
    false
  end

  private

  def gamma_caller_params(dealer)
    params = { call_name: CALL_NAME, target: dealer }

    if dealer.gamma_code_gen
      params.merge!(
        method: :put, object_specific_endpoint: dealer.gamma_code_gen, force: FORCE_ERROR_CODE
      )
    else
      params.merge!(method: :post)
    end

    params
  end
end
