class Floa::EligibilityResponse < Floa::ClientResponse
  INELIGIBLE_ERROR = 'NationalId is invalid'.freeze

  def ineligible?
    return unless body&.eligibilities&.first
    return true if body.eligibilities.first.errors&.any? { |error| error.include? INELIGIBLE_ERROR }

    body.eligibilities.first.hasAgreement == false
  end

  def valid?
    [200, 201, 204].include?(code) && errors.empty? && !ineligible?
  end
end
