class PhoneActivations::PhoneActivationAttributesBuilder
  attr_reader :args, :phone_activation

  def initialize(args = {})
    @args             = args
    additional_params = build_additional_params
    @phone_activation = PhoneActivation.new(args).decorate_for_data_composer(additional_params)
  end

  def build_all
    PhoneActivations::PhoneActivationChainComposer.new(phone_activation: phone_activation).build
  end

  private

  def build_additional_params
    { phone_activation: { accessory_data: { portability_operator_id: portability_operator_id } } }
  end

  def portability_operator_id
    args[:portability_operator_id]&.to_i
  end
end
