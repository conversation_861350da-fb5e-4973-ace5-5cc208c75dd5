# frozen_string_literal: true

class PhoneActivations::Steps::AddFixedLineDetail < PhoneActivations::Steps::Step
  ROUTE      = "add_fixed_line_detail"
  MENU_LABEL = "Dati linea fissa"

  def initialize(args)
    super(args.merge(route: ROUTE, label: MENU_LABEL))
  end

  def available?
    @phone_activation.should_edit_fixed_line_detail?
  end

  def completed?
    @phone_activation.product_category.try(:code) == "fissa" && !@phone_activation.fixed_line_detail.blank? && !@phone_activation.operator.linkem?
  end
end
