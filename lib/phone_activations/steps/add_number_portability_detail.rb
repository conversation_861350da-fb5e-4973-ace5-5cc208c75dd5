# encoding: utf-8

class PhoneActivations::Steps::AddNumberPortabilityDetail < PhoneActivations::Steps::Step
  ROUTE      = "add_number_portability_detail".freeze
  MENU_LABEL = "Dati portabilità".freeze

  def initialize(args)
    super(args.merge(route: ROUTE, label: MENU_LABEL))
  end

  def available?
    @phone_activation.should_edit_number_portability_detail?
  end

  def completed?
    @phone_activation.number_portability_detail&.valid?
  end
end
