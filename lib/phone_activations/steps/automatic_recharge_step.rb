class PhoneActivations::Steps::AutomaticRechargeStep < PhoneActivations::Steps::Step
  MENU_LABEL = "Ricarica Automatica".freeze
  ROUTE      = "add_automatic_recharge".freeze

  def initialize(args)
    super(args.merge(route: ROUTE, label: MENU_LABEL))
  end

  def available?
    phone_activation.require_automatic_recharge_step?
  end

  def completed?
    !phone_activation.automatic_recharge.nil?
  end
end
