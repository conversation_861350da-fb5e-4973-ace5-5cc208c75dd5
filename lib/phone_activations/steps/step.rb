class PhoneActivations::Steps::Step
  attr_reader :label, :phone_activation, :route

  def initialize(args)
    route_service     = args[:route_service] || RouteService.new
    @phone_activation = args[:phone_activation]
    @route            = route_service.step_route(route_name:            args[:route],
                                                 phone_activation:      @phone_activation,
                                                 additional_parameters: additional_parameters)
    @label            = args[:label]
  end

  def available?
    raise NotImplementedError, "This #{self.class} cannot respond to:"
  end

  def completed?
    raise NotImplementedError, "This #{self.class} cannot respond to:"
  end

  def duplicating_status?
    @phone_activation.duplicating
  end

  def additional_parameters
    nil
  end

  private

  class RouteService
    include ActionView::Helpers
    include ActionDispatch::Routing
    include Rails.application.routes.url_helpers

    def step_route(args = {})
      route_name            = args[:route_name]&.to_sym
      phone_activation      = args[:phone_activation]
      additional_parameters = args[:additional_parameters]&.to_sym

      url_for([route_name, phone_activation]) << (additional_parameters || '')
    end
  end
end
