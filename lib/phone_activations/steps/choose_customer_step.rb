class PhoneActivations::Steps::ChooseCustomerStep < PhoneActivations::Steps::Step
  ROUTE = "choose_customer".freeze
  MENU_LABEL = "Dati cliente".freeze

  def initialize(args)
    super(args.merge(route: ROUTE, label: MENU_LABEL))
  end

  def available?
    !@phone_activation.plan.app_ready? && @phone_activation.customer.blank?
  end

  def completed?
    !@phone_activation.plan.app_ready? && !@phone_activation.customer.blank?
  end
end
