module PhoneActivations
  module Steps
    class TlsCustomerDetailsStep < Step
      ROUTE = 'tls_customer_detail'.freeze
      MENU_LABEL = 'Dati aggiuntivi cliente'.freeze

      def initialize(args)
        super(args.merge(route: ROUTE, label: MENU_LABEL))
      end

      def available?
        @phone_activation.customer.present? && @phone_activation.plan.tariffa_tls && !completed?
      end

      def completed?
        @phone_activation.tls_customer_detail.present?
      end
    end
  end
end
