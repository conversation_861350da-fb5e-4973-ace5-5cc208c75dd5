class Operations::ChainComposer
  class << self
    def operations_chain(phone_activation_kind_id)
      operations_chain = []

      PhoneActivationKind.find(phone_activation_kind_id).operations.order("operations.operations_group ASC, operations.id ASC").pluck(:id).each_with_index do |operation_id, index|
        operations_chain.insert(index, [operation_id])
        operations_chain[index] << next_operations(operation_id, phone_activation_kind_id)
        operations_chain[index].flatten! if operations_chain[index].last.empty?
      end
      operations_chain
    end

    private

    def next_operations(operation_id, phone_activation_kind_id)
      operation                        = Operation.find(operation_id)
      phone_activation_kind_operations = PhoneActivationKind.find(phone_activation_kind_id)
                                                            .operations
                                                            .order("operations.operations_group ASC, operations.id ASC")

      if phone_activation_kind_operations.include?(operation)
        phone_activation_kind_groups = phone_activation_kind_operations.pluck(:operations_group).uniq.sort
        groups                       = [phone_activation_kind_groups[phone_activation_kind_groups.index(operation.operations_group).next]]
        next_operations              = Operation.where(operations_group: groups).order("operations_group ASC, id ASC").pluck(:id).delete_if { |id| id == operation_id }

        next_operations = (phone_activation_kind_operations.pluck(:id) & next_operations)
        next_operations.delete(5) if operation.operations_group == 0 or operation.operations_group == 1
        next_operations.delete(32) if operation.operations_group == 0 or operation.operations_group == 1
        next_operations.delete(33) if operation.operations_group == 0 or operation.operations_group == 1

        if operation_id == Operation::UPLOAD_DOCUMENTI && phone_activation_kind_operations.ids.include?(Operation::CONTROLLO_DOCUMENTI)
          next_operations = [Operation::CONTROLLO_DOCUMENTI]
        end

        if operation_id == 1
          next_operations.insert(0, 2)
          next_operations.insert(1, 31)
        elsif operation_id == 31
          next_operations = if phone_activation_kind_id.to_i == 22
                              [33]
                            else
                              [32]
                            end
        end
      else
        next_operations = nil
      end

      next_operations
    end
  end
end
