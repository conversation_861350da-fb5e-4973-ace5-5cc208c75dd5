class Operations::OperationsStrategyFactory
  def get_operation(operation_id)
    case operation_id
    when OperationConstants::TECHNICAL_VERIFICATION
      Operations::Strategies::TechnicalVerification
    when OperationConstants::LINE_INSTALLATION
      Operations::Strategies::LineInstallation
    when OperationConstants::ORIGINAL_DOCUMENTS_RECEIVED
      Operations::Strategies::OriginalDocumentsReceived
    when OperationConstants::PHONE_ACTIVATION_CONFIRMED
      Operations::Strategies::PhoneActivationConfirmed
    end
  end
end
