class Operations::Strategies::TechnicalVerification < Operations::Strategies::OperationStrategy
  def attributes_for_operation_outcome
    {
      user_id: params[:user_id],
      status: params[:status],
      phone_activation_id: params[:phone_activation_id],
      ko_reason_id: params.fetch(:ko_reason_id, nil),
      note: params.fetch(:note, nil),
      private_note: params.fetch(:private_note, nil),
      operation_id: OperationConstants::TECHNICAL_VERIFICATION
    }
  end
end
