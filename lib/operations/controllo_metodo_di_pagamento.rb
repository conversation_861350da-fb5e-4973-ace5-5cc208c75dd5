# frozen_string_literal: true

class Operations::ControlloMetodoDiPagamento < Operations::OperationAbstract
  attr_reader :phone_activation

  ICON  = "money"
  TITLE = "Controllo Metodo di Pagamento"

  def initialize(phone_activation)
    @phone_activation = phone_activation
  end

  def icon_kind_for_progressbar
    ICON
  end

  def is_required_for_progressbar?
    phone_activation.required_operations_ids.include?(operation_id)
  end

  def operation_id
    OperationConstants::CONTROLLO_METODO_DI_PAGAMENTO
  end

  def title_for_progressbar
    TITLE
  end
end
