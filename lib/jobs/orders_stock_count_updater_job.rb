class Jobs::OrdersStockCountUpdater<PERSON>ob
  attr_reader :order_repository, :product

  def initialize(product, args = {})
    @product          = product
    @order_repository = args.fetch(:order_repository, Repositories::OrderRepository.new)
  end

  def perform
    fulfillable_orders = order_repository.find_fulfillable_orders_for({ product_name: product.name })

    Sunspot.index fulfillable_orders
    # Sunspot.commit(true)
    # fulfillable_orders.map(&:index!)
  end
end
