require 'fileutils'

class Jobs::AceaDatasImporterJob
  include Jobs::TrackImporter
  attr_reader :current_user_id, :file_path, :importer

  def initialize(args = {})
    @current_user_id = args.fetch(:current_user_id, nil)
    @file_path       = args.fetch(:file_path, nil)
    @importer        = args.fetch(:importer, Importers::AceaDatasImporter.new(file_path: file_path))
  end

  def perform
    result = importer.import

    AceaDatasImporterMailer.import_result(result.success, result.errors).deliver_now
  end
end
