class Jobs::AccountingDocumentsAttachmentDownloaderJob
  attr_reader :accounting_documents, :downloader, :force_download

  def initialize(args = {})
    repository            = args.fetch(:repository, Repositories::AccountingDocumentRepository.new)
    @accounting_documents = repository.find_by_ids(args[:accounting_document_ids])
    @downloader           = args.fetch(:downloader, Downloaders::DocumentDownloader)
    @force_download       = args.fetch(:force_download, false)
  end

  def perform
    message_results = []

    accounting_documents.each do |accounting_document|
      if force_download ||
          (accounting_document.attachment.url.nil? &&
           accounting_document.creation_date > 2.months.ago)
        message_results << downloader.new(model: accounting_document).download
      end
    end

    error_messages = message_results.reject { |message| message.error.empty? }

    if error_messages.any?
      AccountingDocumentMailer.upload_documents_report(error_messages).deliver_now
    end
  end
end
