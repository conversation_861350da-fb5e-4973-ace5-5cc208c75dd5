class Jobs::PurchaseOrdersUpdaterJob
  attr_reader :file_path, :purchase_orders_updater

  def initialize(args = {})
    @file_path = args.fetch(:file_path, nil)
    @purchase_orders_updater = PurchaseOrders::PurchaseOrdersUpdater.new(file_path)

    @dirname           = File.dirname(file_path)
    @filename          = File.basename(file_path)
    @error_file          = "#{@dirname}/import_errors.txt"
    @unmatched_rows_file = "#{@dirname}/unmatched_rows.txt"
  end

  def perform
    if File.exist?(file_path)
      success_callbacks = [
        lambda { |args| log_unmatched_rows(args[:unmatched_rows]) },
        lambda { |_| FileUtils.mv(file_path, "#{@dirname}/success") }
      ]
      failure_callbacks = [
        lambda { |args| log_errors([args[:message]]) },
        lambda { |args| KolmeLogger.error args[:message] },
        lambda { |_| FileUtils.mv(file_path, "#{@dirname}/error") if File.exist?("#{@dirname}/error") }
      ]

      purchase_orders_updater.import(success_callbacks, failure_callbacks)

      summary
    end
  end

  private

  def log_errors(errors)
    return if errors.empty?

    open(@error_file, 'a') do |f|
      errors.each { |e| f.puts e }
    end
  end

  def log_unmatched_rows(unmatched_rows)
    return if unmatched_rows.empty?

    open(@unmatched_rows_file, 'a') do |f|
      unmatched_rows.each { |r| f.puts r }
    end
  end

  def summary
    return unless Dir.glob("#{@dirname}/*.csv").empty?

    full_filepath  = "#{@dirname}.csv"
    unmatched_rows = File.exist?(@unmatched_rows_file) ? File.read(@unmatched_rows_file).split("\n") : []
    errors         = File.exist?(@error_file) ? File.read(@error_file).split("\n") : []

    if unmatched_rows.any? || errors.any?
      PurchaseOrderMailer.cma_result(unmatched_rows, errors, full_filepath).deliver_now
    else
      FileUtils.rm_rf(@dirname)
      FileUtils.rm("#{@dirname}.csv")
    end
  end
end
