# frozen_string_literal: true

class Jobs::RetryEnergyContractApiCallJob
  attr_reader :api_call, :energy_contract_state

  def initialize(apicall_id, energy_contract_state)
    @api_call              = EnergyContractApiCall.find(apicall_id)
    @energy_contract_state = energy_contract_state
  end

  def perform
    if api_call.retryable_for(energy_contract_state)
      api_call.call_all_reports
    end
  end
end
