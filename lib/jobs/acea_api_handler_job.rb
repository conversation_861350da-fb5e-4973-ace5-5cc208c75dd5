class Jobs::AceaApiHandler<PERSON>ob
  attr_reader :context, :handler, :energy_contract, :reload_current_page

  def initialize(energy_contract, context = nil, reload_current_page = true, args = {})
    @context             = context
    @energy_contract     = energy_contract
    @reload_current_page = reload_current_page
    @handler             = args.fetch(:handler, EnergyContracts::ApiHandler)
  end

  def perform
    if context.present? && energy_contract.external_failure?
      return [EnergyContractApiCall.new(energy_contract: energy_contract, status: 'success'), true]
    end

        PrivatePub.publish_to("/energy_contracts/#{@energy_contract.id}",
                          'window.dispatchEvent(new CustomEvent("openWaitingModal"))')

    api_call_result, success = handler.new(energy_contract)
                                      .perform(context)

    if success
      energy_contract.success_callback_for(api_call_result || EnergyContractApiCall.new(energy_contract: energy_contract, status: 'success'),
                                           reload_current_page)
    else
      energy_contract.failure_callback_for(api_call_result)
    end

    [api_call_result, success]
  end
end
