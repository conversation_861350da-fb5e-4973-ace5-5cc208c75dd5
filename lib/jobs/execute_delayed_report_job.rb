class Jobs::ExecuteDelayedReportJob
  attr_reader :generic_params, :parameters, :report, :report_log

  def initialize(report, report_log, parameters, generic_params)
    @generic_params = generic_params
    @parameters     = parameters
    @report         = report
    @report_log     = report_log
  end

  def perform
    begin
      start = Process.clock_gettime(Process::CLOCK_MONOTONIC)
      options = parameters.present? ? { report_parameters: parameters } : {}
      report.assign_attributes(generic_params)
      report.dispatch(options)
      finish_time          = Process.clock_gettime(Process::CLOCK_MONOTONIC)
      total_execution_time = finish_time - start
      report_log.update(execution_time: total_execution_time)
    rescue Exception => e
      KolmeLogger.error "Error executing report #{report.id}: #{e.message}"
      ReportMailer.report_error(report, report_log.user.email, e.message).deliver_now
    end
  end
end
