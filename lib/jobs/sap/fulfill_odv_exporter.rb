class Jobs::Sap::FulfillOdvExporter
  attr_reader :model

  def initialize(args = {})
    model_class = args.fetch(:model_class, nil)
    @model      = args.fetch(:model, model_class.constantize.find(args.fetch(:model_id, nil)))
  end

  def perform
    return if model.is_a?(Fulfill) && model.products.all?(&:consignment)
    return if model.is_a?(ImeiReservation) && model.is_gallery_gd?

    api_request = Sap::ApiRequest.run(
      http_method: :post,
      path:        Settings.sap.api_endpoints.fulfill_odv,
      origin:      model.sap_fulfill_model
    )

    success_result = api_request.valid? && api_request.result.success

    if success_result
      if model.is_a?(Fulfill)
        model.touch(:sap_exported_at)
      end

      auditable_model = if model.is_a?(<PERSON>lfill)
                          model.order
                        else
                          model
                        end
      auditable_changes = if model.is_a?(Fulfill)
                            { 'sap_fulfill_odv_exported_at' => [nil, Time.now], 'fulfill' => [nil, model.id] }
                          else
                            { 'sap_fulfill_odv_exported_at'=>[nil, Time.now] }
                          end
      Audited::Audit.create(auditable: auditable_model, action: 'sap_fulfill_odv_export', audited_changes: auditable_changes)
    end
  end
end
