class Jobs::Sap::FidoImporterJob
  attr_reader :file_path, :importer

  def initialize(args = {})
    @file_path = args.fetch(:file_path, Settings.sap.import_path)
    @importer  = args.fetch(:importer, Sap::Importers::SapFidoImporter)
  end

  def perform
    return unless Dir.glob("#{file_path}/*.csv").any?

    Dir.glob("#{file_path}/*.csv").each do |file|
      importer.new(file).import(callbacks)
    end
  end

  def callbacks
    [
      lambda { |args| FileUtils.mv(args[:file_path], Settings.sap.log_path) },
      lambda { |args| SapMailer.import_fido(args).deliver_now }
    ]
  end
end

