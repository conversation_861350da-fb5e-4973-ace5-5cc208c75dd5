class Jobs::RobotPhoneActivationJob
  attr_reader :params

  def initialize(params)
    @params = params
  end

  def perform
    phone_activation = PhoneActivation.find_by_hex_id(params[:id])
    if params[:is_cvp_offline] == 'true'
      PhoneActivationCvpOfflineService.new(phone_activation).handle_cvp_offline_fisso
    end
    phone_activation.update_column(:w3_customer_code, params[:w3_customer_code]) if params[:w3_customer_code].present?
    phone_activation.update_column(:main_phone_number, params[:main_phone_number]) if params[:main_phone_number].present?

    message, response_status = PhoneActivations::OfficialPdaHandler.new.add_to(phone_activation, params)

    if ApplicationSetting.autosend_to_robot?
      Sunspot.commit(true)

      PhoneActivations::PhoneActivationTwinService.new(phone_activation).send_twin_to_robot
    end

    [message, response_status]
  rescue Exceptions::OfficialPdaHandlerError => e
    PhoneActivationMailer.robot_error(params, e).deliver_now

    [e.message, :internal_server_error]
  rescue StandardError => e
    KolmeLogger.error e.message
    PhoneActivationMailer.robot_error(params, e).deliver_now

    [e.message, :internal_server_error]
  end
end
