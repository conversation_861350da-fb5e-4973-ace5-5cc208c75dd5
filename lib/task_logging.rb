module TaskLogging
  def write_log(task, heading, messages=[])
    puts %{[#{Time.now.to_fs(:db)}] #{task.try(:name)} - #{heading}}
    unless messages.blank?
      puts messages.join("\n")
      puts "-------\n"
    end
  end

  def log_errors(task, errors)
    write_log(task, "Errore", errors)
  end

  def log_exception(task, exception)
    write_log(task, exception.message, exception.backtrace)
  end

end
