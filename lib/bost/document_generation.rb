require 'tempfile'
require 'pdf-forms'
require 'prawn'
# require 'prawn-layout'

# install https://www.pdflabs.com/tools/pdftk-server/ to generate pdf templates etc..
module Bost
  module DocumentGeneration
    def convert_image_to_pdf(path)
      Prawn::Document.new(page_size: 'A4', margin: 0) do |this_pdf|
        this_pdf.image(path,
                  at:  [0, Prawn::Document::PageGeometry::SIZES['A4'][1]],
                  fit: Prawn::Document::PageGeometry::SIZES['A4'])
      end
    end

    def generate_pdf(pdf_files, output_file, attributes)
      pdf_files  = [pdf_files] unless pdf_files.is_a? Array
      join_files = []

      signature_progress_index = 0

      pdf_files.each do |pdf_file|
        pdftk  = PdfForms.new
        fields = pdftk.get_field_names pdf_file
        if fields.any?
          result, signatures_added_count = set_form_values(fields, attributes, signature_progress_index)
          signature_progress_index       += signatures_added_count

          begin
            file = Tempfile.new('foo-pdf')
            pdftk.fill_form pdf_file, file.path, result , { flatten: true }

            join_files << file
          rescue StandardError => e
            KolmeLogger.error(e)
          end
        else
          join_files << pdf_file
        end
      end

      begin
        Prawn::Document.generate(output_file, margin: 0, skip_page_creation: true, optimize_objects: true) do
          join_files.each do |pdf_file|
            file_path = (pdf_file.is_a?(String) ? pdf_file : pdf_file.path)
            (1..Prawn::Document.new(template: file_path, optimize_objects: true, skip_page_creation: false).page_count).each do |page_number|
              start_new_page(template: file_path, template_page: page_number)
            end
          end
        end
      rescue StandardError => e
        KolmeLogger.error(e)
      end
      output_file
    rescue StandardError => e
      KolmeLogger.error(e)
      false
    end

    def include_customer2_signature?(pdf_files)
      pdf_files  = [pdf_files] unless pdf_files.is_a? Array

      fields = pdf_files.map do |pdf_file|
        pdftk  = PdfForms.new
        pdftk.get_field_names pdf_file
      end.flatten

      fields.any? { |field| field.start_with?('customer2_signature_') }
    end

    def set_form_values(fields, attributes, signature_progress_index)
      signatures = []
      secondary_customer_signatures = []
      result     = {}

      fields.each do |field|
        if field.start_with?('customer2_signature_')
          secondary_customer_signatures << field
        elsif field.include?('_signature_')
          signatures << field
        else
          result[field.to_sym] = attributes.fetch(field.to_sym, '')
        end
      end

      signatures.each_with_index do |signature, index|
        result[signature.to_sym] = signature_placeholder_from(attributes, signature)
                                     .gsub('#index', (signature_progress_index + index.next).to_s)
      end

      secondary_customer_signatures.each_with_index do |signature, index|
        result[signature.to_sym] = signature_placeholder_from(attributes, signature)
                                     .gsub('#index', (signature_progress_index + signatures.count + index.next).to_s)
      end

      [result, signatures.count + secondary_customer_signatures.count]
    end

    def signature_placeholder_from(attributes, signature)
      attributes.fetch(signature.to_s[/^(.*)(_signature)/].to_sym, 'signature placeholder not found')
    end
  end
end
