# encoding: utf-8

module Bost
  module Constants
    ITALY_AREA_CODE = '+39'.freeze

    ALPHABET_WITH_SPACE                = /[^a-zA-Z\à\á\è\é\ì\í\ó\ò\ù\ú\s]+/i.freeze
    ALPHABET_WITH_SPACE_AND_APOSTROPHE = /\A[a-zA-Z\à\á\è\é\ì\í\ó\ò\ù\ú\s\']+\z/i.freeze
    ALPHANUMERIC_REGEXP                = /\A[a-zA-Z0-9]+\z/i.freeze
    ALPHANUMERIC_WITH_SLASH_REGEXP     = /\A[a-zA-Z0-9\à\á\è\é\ì\í\ó\ò\ù\ú\s\/]+\z/.freeze
    CF_REGEXP                          = /\A[a-z]{6}\d{2}[a-z][0-7]\d[a-z][a-z0-9]{3}[a-z]\z/i.freeze
    CF_OR_VAT_REGEXP                   = /\A([0-9]{11}|[a-z]{6}\d{2}[a-z][0-7]\d[a-z][a-z0-9]{3}[a-z])\z/i.freeze
    ADDRESS_REGEXP                     = /\A[a-zA-Z0-9\à\á\è\é\ì\í\ó\ò\ù\ú\s\-\/]+/i.freeze
    CIVIC_NUMBER_REGEXP                = /\A[a-zA-Z0-9]+([\/\-]*[a-zA-Z0-9])*\z/i.freeze
    CUSTOMER_MOBILE_PHONE_REGEXP       = /\A(3\d{8,9})\z/.freeze
    CUSTOMER_EMAIL_REGEXP              = /\A[a-zA-Z0-9\-!#$%&*+=?^_`{|}~-]+([a-zA-Z0-9.!#$%&*+=?^_`{|}~-]?)+[a-zA-Z0-9\-!#$%&*+=?^_`{|}~-]+@[a-zA-Z0-9\-]+\.[a-zA-Z0-9\-]{2,}(\.[a-zA-Z0-9\-]{2,})?\z/.freeze
    EMAIL_REGEXP                       = /\A[\w+\-.]+@[a-z\d\-]+(\.[a-z\d\-]+)*\.[a-z]+\z/i
    MOBILE_OR_LANDLINE_REGEXP          = /\A(3\d{8,9})\z|\A(0[\d]{4,11})\z/.freeze
    MOBILE_3_REGEXP                    = /\A(3\d{8,9})\z/.freeze
    NO_SCRIPT_REGEXP                   = /\A[^(\/|\\\<>)]*\z/.freeze
    NO_SCRIPT_NO_QUOTES_REGEXP         = /\A[^(\/|\\\<>")]*\z/.freeze
    NOTE_REGEXP                        = /\A[^|\\\<>]*\z/.freeze
    NUMERIC_SIM_SERIAL_REGEXP          = /\A[0-9]{19,}\z/.freeze
    NUMERIC_WITH_DASH_REGEXP           = /\A[0-9-]+\z/.freeze
    PHONE_NUMBER_REGEXP                = /\A3[0-9]{8,9}\z/.freeze
    PHONE_AND_COUNTRY_REGEXP           = /\A(\+39)?3[0-9]{8,9}\z/.freeze
    PINPAD_CODE_RID_REGEXP             = /\AGP([0-9]*)\z/i.freeze
    PINPAD_CODE_CC_REGEXP              = /\ACC([0-9]*)\z/i.freeze
    PRODUCT_CODE_REGEX                 = /\A[^|\\\()]*\z/.freeze
    SIM_SERIAL_REGEXP                  = /\A[a-zA-Z0-9]{19,}\z/.freeze
    SWIFT_CODE_REGEXP                  = /\A[A-Z]{6}[A-Z0-9]{2}[A-Z0-9]{3}?\z/i.freeze
    VAT_REGEXP                         = /\A[0-9]{11}\z/.freeze
    ZIP_REGEXP                         = /\A\d{5}\z/.freeze
  end
end
