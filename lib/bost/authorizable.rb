module Bost
  module Authorizable
    extend ActiveSupport::Concern

    ROLES = {
      kolme: %i[kolme dealer],
      dealer: []
    }

    included do
      # SCOPES
      scope :kolme,  -> { where(role: :kolme)  }
      scope :dealer, -> { where(role: :dealer) }
    end

    module ClassMethods
      def roles
        ROLES.keys
      end

      def child_roles_for(role)
        ROLES[role.to_sym]
      end
    end

    def is?(role)
      self.role.to_sym == role if self.role.present?
    end

    def get_child_roles
      ROLES[role.to_sym] if role.present?
    end
  end
end
