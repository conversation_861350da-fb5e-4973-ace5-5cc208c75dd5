# coding: utf-8

class Product < ApplicationRecord
  KOLME_MACRO_CATEGORIES                           = %w(wind_tre_smartphone wind_tre_other open_market accessori prodotti_attiva utilita trade_marketing ricariche fwa).freeze
  KMD_MACRO_CATEGORIES                             = %w(prodotti ricariche sim trade_marketing).freeze
  MACRO_CATEGORIES_DEFAULT_ATTIVA_IMPORT           = 'prodotti_attiva'.freeze
  MACRO_CATEGORIES_DEFAULT_TAB                     = 'prodotti'.freeze
  MACRO_CATEGORIES_FILTERED_BY_DEALER_OPERATOR_IDS = %w(sim trade_marketing).freeze
  MACRO_CATEGORIES_WITH_LOGOS                      = %w(accessori prodotti_attiva utilita trade_marketing ricariche).freeze
  MAX_BUNDLE_ITEMS                                 = 100
  IGNORED_KEYS_CHANGES_FOR_GAMMA_EXPORT            = %w(activable_system_owner agent_profit_a agent_profit_b agent_profit_c allow_sim_without_number amount_in_system_owner_warehouse archived attiva_brand_id attiva_category_id attiva_subcategory_id bundle_vat_a bundle_vat_b bundle_vat_c can_be_requested current_availability default_percent_discount description dont_show_public_price force_zero_shipping_cost future_availability loanable mandatory_mnp max_agent_discount_percentage max_qty_orderable max_qty_visible moderated origin product_type promo_device_price recharge_size_id reward ribbon serial sim_format visibility public_price_list view_in_matrix).freeze
  CONCERNED_KEYS_CHANGES_FOR_DHL_EXPORT            = %w(dhl_pigeon_house dhl_pigeon_management code name company_brand_id ean_code serial public_price_list).freeze
  ORDERABLE_BY_AGENTS_TAG                          = 'Acquistabili Agenti'.freeze
  ORIGINS                                          = %w(system attiva).freeze
  PAGINABLE_MACRO_CATEGORIES                       = %w(accessori prodotti_attiva).freeze
  PDA_PRICE_CATEGORIES                             = %w(A B C D E F G H).freeze
  PRODUCT_TYPE_BUNDLE                              = 'b'.freeze
  PRICE_RANGES                                     = %w(0-*********** ***********-700 700+).freeze
  RIBBONS                                          = %w(normal news promo incoming featured).freeze
  SORT_FIELDS                                      = %w(name_asc name_desc).freeze
  MAX_NAME_LENGTH                                  = 72

  SAP_EDIT_FIELDS = %w(
    code
    sap_name
    name
    sap_product_kind_id
    sap_product_cluster_id
    company_brand_id
    sap_distribution_chain_id
    sap_valuation_class_id
    weight
    ean_code
    vat_type
  )

  IMPORT_KINDS = %w(update_and_add delete_and_add delete)

  FACETS = %w(
    macro_category
    can_be_requested
    store_tag_list
    company_brand
    ribbon
    store_subtag_list
  ).freeze

  FACET_HIERARCHY = {
    'store_tag_list' => ['store_subtag_list']
  }.freeze

  include Bost::Uuid

  include ProductDecorator
  include CategorizedPricing
  include AttivaImportable

  include ProductAutocomplete

  include AuditUtils

  AUDIT_EXCEPT = %w(
    amount_in_system_owner_warehouse
    bookable_quantity
    origin
    ean_code
    weight
    width
    height
    depth
    current_availability
    future_availability
    gamma_exported_at
    dismissed_at
    attiva_category_id
    attiva_subcategory_id
    attiva_brand_id
    created_at
    updated_at
    uuid
  ).freeze

  SAP_INFORECORD_EDIT_FIELDS = %w(vat_type gamma_purchase_price).freeze
  TAGGABLE_AUDITS = %w(store_tag_list store_subtag_list).freeze

  audited except: AUDIT_EXCEPT + TAGGABLE_AUDITS

  attr_accessor :import_errors, :importing, :importing_availability, :add_max_agent_discount_percentage,
                :backoffice_editing,
                :skip_validations, :skip_sap_name_validation

  alias_attribute :immaterial?, :immaterial

  cattr_accessor :skip_offer_instances do
    Settings.offer_instances.delayed_rebuild
  end

  attribute :sap_data, default: -> { {} }

  belongs_to :company_brand
  belongs_to :recharge_size
  belongs_to :product_macro_category

  has_many :product_operators
  has_many :purchase_orders, foreign_key: 'product_code', primary_key: 'code'
  has_many :operators, through: :product_operators

  # Attiva product import tables
  has_many :attiva_products
  belongs_to :attiva_brand
  belongs_to :attiva_category
  belongs_to :attiva_subcategory

  # Gamma fields
  belongs_to :statistic_group1
  belongs_to :statistic_group2
  belongs_to :statistic_group3
  belongs_to :statistic_group4
  belongs_to :family_product
  belongs_to :italy_sales
  belongs_to :italy_sales_return
  belongs_to :italy_purchase
  belongs_to :italy_purchase_return

  # SAP fields
  belongs_to :sap_distribution_chain
  belongs_to :sap_product_kind
  belongs_to :sap_product_cluster
  belongs_to :sap_valuation_class
  belongs_to :sap_product_supplier
  has_one    :sap_product_inforecord, autosave: true, dependent: :destroy

  # INFO: il product è un bundle
  # items associate a questo bundle
  has_many :bundle_items, dependent: :destroy
  # restituisce i prodotti che compongono questo bundle
  has_many :bundle_components,
           -> { order 'bundle_items.id ASC' },
           through: :bundle_items,
           source:  :item

  accepts_nested_attributes_for :bundle_items,
                                allow_destroy: true,
                                reject_if:     proc { |attributes| attributes['item_id'].blank? }

  has_many :dealer_category_products
  has_many :dealer_categories, through: :dealer_category_products

  has_many :product_product_categories
  has_many :product_categories, through: :product_product_categories

  belongs_to :product_color

  has_many :items, dependent: :destroy

  has_many :instock_items_in_system_owner_warehouse, -> {
    system_owner_warehouse_ids = Warehouse.joins(:dealer).where('dealers.role = ? ', 'system').ids
    in_system_owner_warehouse(system_owner_warehouse_ids).in_state(:instock)
  }, class_name: 'Item'

  has_many :sim_offer_instances,
           class_name:  'OfferInstance',
           foreign_key: 'sim_id',
           dependent:   :destroy

  has_many :product_offer_instances,
           class_name: 'OfferInstance',
           dependent:  :destroy

  has_one :default_image, -> { where(is_default: true) }, class_name: 'ProductImage'
  has_many :gallery_product_images, -> { where(is_default: false).order('position asc') }, class_name: 'ProductImage'
  has_many :product_images, -> { order 'position asc' }
  has_many :wind_ngpos_activations
  has_many :imei_reservations

  # delegating attributes
  delegate :code, :description, to: :company_brand, prefix: true, allow_nil: true
  delegate :code, :description, to: :family_product, prefix: true, allow_nil: true
  delegate :code, to: :italy_sales, prefix: true, allow_nil: true
  delegate :code, to: :italy_sales_return, prefix: true, allow_nil: true
  delegate :code, to: :italy_purchase, prefix: true, allow_nil: true
  delegate :code, to: :italy_purchase_return, prefix: true, allow_nil: true
  delegate :code, :name, to: :product_color, prefix: true, allow_nil: true

  1.upto(4) do |i|
    delegate :code, :description, to: "statistic_group#{i}", prefix: true, allow_nil: true
  end

  validates :name, :commercial_name, :code, :public_price_list, presence: true
  validates :name, length: { maximum: MAX_NAME_LENGTH }
  validates :dealer_price_vat_excluded_a,
            :dealer_price_vat_excluded_b,
            :dealer_price_vat_excluded_c, presence: true
  validates :code, format: { with: Bost::Constants::PRODUCT_CODE_REGEX }
  validates_uniqueness_of :code, case_sensitive: true
  validates :visibility, inclusion: { in: VisibilityKind::ALL - %w(i) }
  validates :sim_format, inclusion: { in: SimFormat::ALL, allow_blank: true }
  validates :product_type, inclusion: { in: ProductType::ALL }
  validate :recharge_size_should_be_for_the_chosen_operator
  validates :sap_name, presence: true, length: { maximum: 35 }, unless: -> { skip_sap_name_validation || importing }
  validates :store_tag_list, presence: true, if: proc { |c| !c.importing }
  validates_numericality_of :max_qty_orderable,
                            only_integer: true,
                            greater_than: 0,
                            allow_nil:    true
  validates_numericality_of :max_qty_visible,
                            only_integer:             true,
                            greater_than_or_equal_to: 0,
                            allow_nil:                true

  validates :agent_profit_a, :agent_profit_b, :agent_profit_c, presence: true
  validates :origin, presence: true, inclusion: { in: ORIGINS }
  validates_numericality_of :default_percent_discount,
                            greater_than_or_equal_to: 0,
                            less_than_or_equal_to:    100
  validates_numericality_of :max_agent_discount_percentage,
                            only_integer:             true,
                            greater_than_or_equal_to: 0,
                            less_than_or_equal_to:    100,
                            if:                       proc { |p| p.add_max_agent_discount_percentage }
  validate :bundle_items_uniqueness
  validate :bundle_items_emptiness
  validate :bundle_items_consignment, if: proc { |p| p.bundle? }
  validate :max_allowed_operators
  validate :dhl_flags_consistency
  validate :code_max_length
  validate :floa_financing_price_limit

  validate :has_unique_default_color_for_commercial_name

  validates :competence, presence: true
  validates :company_brand,
            :family_product,
            :statistic_group1,
            :statistic_group2,
            :product_macro_category, presence: true, unless: :importing

  validates :italy_purchase_return, presence: true, unless: :importing
  validates :italy_purchase, presence: true, unless: :importing
  validates :italy_sales_return, presence: true, unless: :importing
  validates :italy_sales, presence: true, unless: :importing

  validates :sap_distribution_chain, presence: true, unless: :importing, if: proc { |p| p.backoffice_editing }
  validates :sap_product_kind, presence: true, unless: :importing, if: proc { |p| p.backoffice_editing }
  validates :sap_product_cluster, presence: true, unless: :importing, if: proc { |p| p.backoffice_editing }
  validates :sap_valuation_class, presence: true, unless: :importing, if: proc { |p| !p.immaterial? && p.backoffice_editing }
  validates :sap_product_supplier, presence: true, unless: :importing, if: proc { |p| p.backoffice_editing }

  acts_as_taggable_on :product_kinds,
                      :sim_kinds,
                      :store_tags,
                      :brand,
                      :store_subtags

  extend TaggablesUtils

  include Bost::Duplicatable::Model
  is_duplicatable :product_kind_list, :sim_kind_list

  # attiva products cant be duped
  def duplicatable?
    !attiva_origin?
  end

  before_validation :cleanup_unneeded
  before_validation :sanitize_fields
  before_validation :strip_no_tag
  before_validation :strip_input_strings
  before_validation :truncate_attiva_product_name
  before_validation :sync_sap_product_inforecord
  before_save :set_consignment, if: :bundle?
  after_save :make_offer_instances
  after_update :reindex_offer_instances

  after_save :audit_changes
  after_save :export_to_dhl, if: :dhl_exportable?
  after_save :export_to_sap, if: :sap_to_export?
  # after_save :render_not_requestable_if_not_visible
  # after_touch :expire_bundles_cache, unless: :bundle?

  scope :activable, -> { where(visibility: VisibilityKind.visible_kinds) }
  scope :archived, -> { where(archived: true) }
  scope :attiva, -> { where(origin: 'attiva') }
  scope :floa_top_seller, -> { where(floa_top_seller: true).order(:public_price_list) }
  scope :system_owner, -> { where(origin: 'system') }
  scope :material, -> { where(immaterial: [nil, false]) }
  scope :moderated, -> { where(moderated: true) }
  scope :not_dismissed, -> { where(dismissed_at: nil) }
  scope :not_moderated, -> { where(moderated: [false, nil]) }
  scope :not_recharges, -> { where.not(product_type: ProductType.recharge_types) }
  scope :product_dealer_categories, -> { joins(:dealer_categories) }
  scope :recharges, -> { where(product_type: ProductType.recharge_types) }
  scope :unarchived, -> { where(archived: [nil, false]) }
  scope :with_serial, -> { where(serial: true) }
  scope :without_serial, -> { where(serial: false) }
  scope :in_dhl_supply_chain, -> { where(dhl_supply_chain: true) }

  scope :with_enabled_operators, -> {
    left_outer_joins(:product_operators).where('operator_id in (?) OR products.id in (?)', Operator.enabled.ids, without_operators.ids)
  }

  scope :without_operators, -> {
    includes(:product_operators).where(product_operators: { id: nil })
  }

  scope :activable_or_current, proc { |current|
    t          = Product.arel_table
    conditions = t[:visibility].in(VisibilityKind.visible_kinds)
    conditions = conditions.or(t[:id].eq(current.id)) if current.present?
    where(conditions)
  }

  scope :for_operators, proc { |operator_ids|
    joins(:product_operators).where('product_operators.operator_id in (?)', operator_ids) if operator_ids.present?
  }

  scope :with_operator, -> { joins(:product_operators).where('product_operators.operator_id is not null') }
  scope :not_sim, -> { where('product_type not in (?)', ProductType.sim_types) }
  scope :sim, -> { where(product_type: ProductType.sim_types) }
  scope :visible_in_matrix, -> { where(view_in_matrix: true) }
  scope :in_pigeon_house, -> { where(dhl_pigeon_house: true) }
  scope :outside_pigeon_house, -> { where(dhl_pigeon_house: false) }
  scope :activable_product, -> { where(product_type: (ProductType.activable_types - ProductType.sim_types)) }

  scope :filtered_by_operator_ids, ->(operator_ids) { joins(:product_operators).where('product_operators.operator_id in (?)', operator_ids) }
  scope :with_or_without_operator, ->(operator_ids) { left_outer_joins(:product_operators).where('product_operators.operator_id in (?) or product_operators.operator_id is null', operator_ids) }

  scope :sim_for_offer, proc { |offer|
    conditions = sim
    if offer.present?
      conditions = conditions.joins(sim_offer_instances: :offer).where(offers: { id: offer.id })
    end
    conditions.select('distinct products.*')
  }

  scope :not_sim_for_offer, proc { |offer|
    conditions = not_sim
    if offer.present?
      conditions = conditions.joins(product_offer_instances: :offer).where(offers: { id: offer.id })
    end
    conditions.select('distinct products.*')
  }

  scope :activable_product_for_offer, proc { |offer|
    conditions = activable_product
    if offer.present?
      conditions = conditions.joins(product_offer_instances: :offer).where(offers: { id: offer.id })
    end
    conditions.select('distinct products.*')
  }

  scope :sim_tagged, proc { |sim_tags|
    tagged_with(sim_tags, on: :sim_kinds, any: true)
  }
  ransacker :sim_tagged

  scope :product_tagged, proc { |product_tags|
    tagged_with(product_tags, on: :product_kinds, any: true)
  }
  ransacker :product_tagged

  scope :store_tagged, proc { |store_tags|
    tagged_with(store_tags, on: :store_tags, any: true)
  }
  ransacker :store_tagged

  scope :store_subtagged, proc { |store_subtags|
    tagged_with(store_subtags, on: :store_subtags, any: true)
  }
  ransacker :store_subtagged

  scope :visible, -> { where(visibility: VisibilityKind.visible_kinds) }

  scope :sim_compatible_with, proc { |product|
    where(['sim_format in (?) or sim_format is NULL', SimFormat.formats_compatible_with(product.sim_format)]) if product.present? && product.sim_format.present?
  }

  scope :sim_format_any_with_nil, proc { |sim_formats|
    sim_formats = [sim_formats] unless sim_formats.is_a?(Array)
    formats     = sim_formats.map { |format| format.to_s == '-' ? nil : format }
    where(sim_format: formats)
  }
  ransacker :sim_format_any_with_nil

  scope :loanable, -> { where(loanable: true) }
  scope :bundles, -> { where(product_type: PRODUCT_TYPE_BUNDLE) }

  scope :for_imei_reservations, ->(franchising_enabled = [true, false, nil], gallery_enabled = [true, false, nil]) do
    where(enabled_for_imei_reservation:             true,
          enabled_for_imei_reservation_franchising: franchising_enabled,
          enabled_for_imei_reservation_gallery:     gallery_enabled,
          dhl_supply_chain:                         true,
          dhl_pigeon_house:                         true)
      .order(name: :asc)
  end

  def self.bundles_including(code)
    joins(bundle_items: [:item]).where('items_bundle_items.code = ?', code)
  end
  def self.ransackable_associations(_auth_object = nil, exclude = [])
    authorizable_ransackable_associations.dup.excluding(exclude)
  end

  def self.ransackable_attributes(_auth_object = nil, exclude = [])
    authorizable_ransackable_attributes.dup.excluding(exclude)
  end

  # Solr search specification
  searchable(include: [:product_color, :operators], if: :indexable?) do
    integer(:id)

    integer :id_int, stored: true do
      try(:id).try(:to_s)
    end
    integer :company_brand_id

    text :name, as: :name_itext do
      name.gsub('+', '%2D')
    end
    text :commercial_name
    text :code
    text :description do
      Rails::Html::FullSanitizer.new.sanitize(description)
    end

    string :name
    string :origin
    string :name_downcase do
      name.downcase
    end
    string :commercial_name
    string :code
    integer :system_owner_stocks_count
    string :product_type
    string :sim_kind_list, multiple: true
    string :product_kind_list, multiple: true
    string :store_tag_list, multiple: true
    string :store_subtag_list, multiple: true
    integer :operator_ids, multiple: true, references: Operator
    boolean(:floa_financeable) { floa_financeable? }
    boolean :activable_system_owner
    integer(:can_be_requested) { can_be_requested? ? 1 : 0 }
    boolean :not_available
    boolean :enabled_for_imei_reservation
    double :dealer_price_vat_excluded_a
    double :dealer_price_vat_excluded_b
    double :dealer_price_vat_excluded_c
    double :public_price_list
    string :visibility
    string(:company_brand) { company_brand&.description }
    text :company_brand_description, as: :company_brand_description_itext do
      company_brand&.description
    end
    integer :dealer_category_ids, multiple: true, references: DealerCategory
    string :macro_category do
      product_macro_category.code
    end
    boolean :archived
    boolean :moderated
    boolean :imei_reservation_curtailed
    boolean(:visible) { exportable_product? }
    boolean :attiva_brand_visible do
      if attiva_origin?
        attiva_brand&.visible?
      else
        true
      end
    end
    string :ribbon
    integer :ribbon_sort do
      {
        'featured' => 0,
        'news'     => 1,
        'promo'    => 2,
        'normal'   => 3,
        'incoming' => 4
      }.fetch(ribbon)
    end
    float :dealer_price_vat_excluded_a_sort do
      dealer_price_vat_excluded_a - (dealer_price_vat_excluded_a * default_percent_discount / 100.00)
    end
    float :dealer_price_vat_excluded_b_sort do
      dealer_price_vat_excluded_b - (dealer_price_vat_excluded_b * default_percent_discount / 100.00)
    end
    float :dealer_price_vat_excluded_c_sort do
      dealer_price_vat_excluded_c - (dealer_price_vat_excluded_c * default_percent_discount / 100.00)
    end
    double :credit_note_amount

    boolean :can_be_used_in_promo_device do
      product_type == 'pa' &&
        promo_device_price.present? &&
        credit_note_amount.present?
    end

    integer(:available_instock) { stock_count > 0 ? 1 : 0 }

    string :fwa_areas, multiple: true do
      all_fwa_areas
    end
  end

  FACET_MAPPING = {
    'can_be_requested' => :to_i
  }.freeze

  def self.macro_categories_default_tab
    ProductMacroCategory.order(:position).first&.code
  end

  def all_fwa_areas
    areas = [fwa_areas, bundle_items.map(&:fwa_areas)].flatten.compact
    areas.empty? ? ['not_fwa'] : areas
  end

  def attiva_origin?
    origin == 'attiva'
  end

  def system_owner_origin?
    origin == 'system'
  end

  def import_promo_device(csv_row)
    unless [csv_row.second, csv_row.last].all? { |value| value&.numeric? }
      self.import_errors = I18n.t('importers.invalid_value', row_id: code)
      return
    end

    self.promo_device_price = csv_row.second
    self.credit_note_amount = csv_row.last
  end

  def indexable?
    VisibilityKind.visible_kinds.include?(visibility || 'n')
  end

  def activable?
    ProductType.activable_types.include?(product_type)
  end

  def not_available
    can_be_requested == false &&
      (stock_count || 0).zero? &&
      (store_tag_list & [ORDERABLE_BY_AGENTS_TAG]).empty?
  end

  def sim?
    ProductType.sim_types.include?(product_type)
  end

  def non_sim?
    !sim?
  end

  def non_quantity_or_sim?
    !quantity_or_sim?
  end

  def quantity_or_sim?
    sim? || quantity?
  end

  def quantity?
    !recharge? && !serial?
  end

  def recharge?
    ProductType.recharge_types.include?(product_type)
  end

  def gamma_attributes_changed?
    (changes.keys - IGNORED_KEYS_CHANGES_FOR_GAMMA_EXPORT).any?
  end

  def exportable_product?
    return true if system_owner_origin? || (attiva_origin? && moderated)

    false
  end

  def fwa_areas
    ApplicationSetting.fwa_areas_for(self)
  end

  def gamma_exportable?
    return false if bundle?
    return false if importing_availability
    return true if attiva_origin? && moderated && moderated_changed?
    return false if attiva_origin? && !moderated

    gamma_attributes_changed?
  end

  def sap_export_callback(sap_api_call)
    attrs = {
      sap_data:        sap_api_call.response,
      sap_exported_at: Time.now,
      updated_at:      Time.now
    }
    update_columns(attrs)
  end

  def sap_exported?
    sap_exported_at.present?
  end

  def sap_exportable?
    return false if consignment?
    return false if bundle?
    return false if attiva_origin? && !moderated

    true
  end

  def sap_operation
    return nil if !sap_exportable?
    return :update if sap_exported?

    :create
  end

  def sap_to_export?
    sap_operation && saved_changes.keys.any? { |property| SAP_EDIT_FIELDS.include?(property.to_s) }
  end

  def export_to_sap
    # Execs async task to export to SAP
    Delayed::Job.enqueue(Jobs::Sap::ProductExporter.new(product_id: id), queue: "sap_export_products_#{id}", priority: 5)
  end

  def sap_inforecord_to_export_on_edit?
    sap_operation == :update && saved_changes.keys.any? { |property| SAP_INFORECORD_EDIT_FIELDS.include?(property) }
  end

  # Maps the vat type to the SAP tax classification code (valid only for TTX1)
  def sap_tax_classification
    {
      '22'   => '1', # IVA al 22%
      '17RC' => 'V', # Inversione contabile IVA, art.17, comma 6, lett. B del DPR 633/72
      '17RT' => 'W', # Inversione contabile IVA, art. 17, comma 6, lett. C del DPR 633/72
      'A02'  => 'X', # Escluso art. 2
      'E74'  => 'Y', # IVA assolta alla fonte ex art. 74/D
      '7403' => 'Z' # IVA assolta ex art. 74/E da Wind Tre P.IVA 13378520152
    }[vat_type]
  end

  def sap_json
    case sap_operation
    when :create then sap_json_create
    when :update then sap_json_update
    else {}
    end
  end

  def sap_json_create
    prod_type = if immaterial?
                  'NLAG'
                else
                  (code =~ /^[0-9]+$/ ? 'ZFER' : 'FERT')
                end
    item_categ = immaterial? ? 'NLAG' : 'NORM'
    product_group = immaterial? ? 'YBMM01' : 'L004'
    sj = {
      Product:             code,
      ProductType:         prod_type,
      IsMarkedForDeletion: false,
      ProductOldID:        id.to_s,
      GrossWeight:         ((weight&.to_f.presence || 0.0) / 1000).to_s,
      WeightUnit:          'KG',
      ProductGroup:        product_group,
      BaseUnit:            'PC',
      ItemCategoryGroup:   item_categ,
      IndustrySector:      'M',
      to_Description:      {
        results: [
          {
            Product:            code,
            Language:           'IT',
            ProductDescription: sap_name
          }
        ]
      },
      to_ProductBasicText: {
        results: [
          {
            Product:  code,
            Language: 'IT',
            LongText: name
          }
        ]
      },
      to_ProductSalesTax:  {
        results: [
          {
            Product:           code,
            Country:           'IT',
            TaxCategory:       'TTX1',
            TaxClassification: sap_tax_classification
          },
          {
            Product:           code,
            Country:           'IT',
            TaxCategory:       'LCIT',
            TaxClassification: '1'
          },
          {
            Product:           code,
            Country:           'IT',
            TaxCategory:       'MTX1',
            TaxClassification: '1'
          }
        ]
      },
      to_SalesDelivery:    {
        results: [
          {
            Product:                     code,
            ProductSalesOrg:             '2310',
            ProductDistributionChnl:     '10',
            SupplyingPlant:              '2310',
            AccountDetnProductGroup:     sap_distribution_chain&.code,
            ItemCategoryGroup:           item_categ,
            FirstSalesSpecProductGroup:  sap_product_kind&.code,
            SecondSalesSpecProductGroup: sap_product_cluster&.code,
            ThirdSalesSpecProductGroup:  company_brand&.sap_code,
            to_SalesTax:                 {
              results: [
                {
                  Product:           code,
                  Country:           'IT',
                  TaxCategory:       'TTX1',
                  TaxClassification: sap_tax_classification
                },
                {
                  Product:           code,
                  Country:           'IT',
                  TaxCategory:       'LCIT',
                  TaxClassification: '1'
                },
                {
                  Product:           code,
                  Country:           'IT',
                  TaxCategory:       'MTX1',
                  TaxClassification: '1'
                }
              ]
            }
          }
        ]
      }
    }
    if immaterial?
      sj[:to_Plant] = {
        results: [
          {
            Product:      code,
            Plant:        '2310',
            ProfitCenter: '2310RIC01'
          }
        ]
      }
    else
      sj[:to_Plant] = {
        results: [
          {
            Product:                    code,
            Plant:                      '2310',
            AvailabilityCheckType:      'SR',
            PeriodType:                 'M',
            ProfitCenter:               '2310RIC01',
            PurchasingGroup:            'COR',
            IsNegativeStockAllowed:     true,
            to_ProductPlantProcurement: {
              Product:                     code,
              Plant:                       '2310',
              IsAutoPurOrdCreationAllowed: true
            },
            to_StorageLocation:         {
              results: [
                {
                  Product:         code,
                  Plant:           '2310',
                  StorageLocation: '2310' # Magazzino P.F. DHL
                },
                {
                  Product:         code,
                  Plant:           '2310',
                  StorageLocation: '2320' # Magazzino P.F. Famagosta
                },
                {
                  Product:         code,
                  Plant:           '2310',
                  StorageLocation: '2330' # Magazzino P.F. Resi Famagosta
                }
              ]
            }
          }
        ]
      }
      sj[:to_Valuation] = {
        results: [
          {
            Product:                     code,
            ValuationArea:               '2310',
            ValuationClass:              sap_valuation_class&.code,
            PriceDeterminationControl:   '2',
            PriceUnitQty:                '1',
            InventoryValuationProcedure: 'V',
            Currency:                    'EUR'
          }
        ]
      }
    end
    if ean_code.present?
      sj[:ProductStandardID] = ean_code
      sj[:InternationalArticleNumberCat] = 'HE'
    end
    sj
  end

  def sap_json_update
    sj = {
      d: {
        Product:                     code,
        Language:                    'IT',
        ProductDescription:          sap_name,
        LongText:                    name,
        FirstSalesSpecProductGroup:  sap_product_kind&.code,
        SecondSalesSpecProductGroup: sap_product_cluster&.code,
        ThirdSalesSpecProductGroup:  company_brand&.sap_code,
        AccountDetnProductGroup:     sap_distribution_chain&.code,
        ValuationClass:              sap_valuation_class&.code,
        GrossWeight:                 ((weight&.to_f.presence || 0.0) / 1000).to_s,
        ProductStandardID:           ean_code.presence || '',
        TaxClassification:           sap_tax_classification,
        TaxCategory:                 'TTX1'

      }
    }
    if ean_code.present?
      sj[:d][:InternationalArticleNumberCat] = 'HE'
    end
    sj
  end

  def dhl_exportable?
    return false unless dhl_supply_chain
    return true if saved_change_to_attribute?('dhl_supply_chain')

    return true if company_brand&.changed?

    CONCERNED_KEYS_CHANGES_FOR_DHL_EXPORT.map do |key|
      saved_change_to_attribute?(key)
    end.any?
  end

  def to_s
    name
  end

  def vat
    ProductVatType.multiplier_of(vat_type)
  end

  def legacy_object
    Bost::Legacy::Prodotto.where(IdProd: uuid).first
  end

  def sim_compatible?(other)
    other.blank? ||
      other.sim_format.blank? ||
      sim_format.blank? ||
      SimFormat.formats_compatible_with(other.sim_format).include?(sim_format)
  end

  def operator_compatible?(plan)
    plan.blank? || plan.operator.blank? || operators.empty? || operators.include?(plan.operator)
  end

  def booked_available_at
    item = items.booked.first

    expiry_minutes       = item.requested_imei_reservation.expiration_offset_minutes.minutes
    dequarantine_minutes = Settings.imei_reservations.minutes_to_dequarantine.minutes

    calculated_time = item.requested_imei_reservation.created_at + expiry_minutes + dequarantine_minutes

    available_at(calculated_time.hour, calculated_time.min)
  end

  def quarantined_available_at
    quarantine_ends_at = items.quarantined.first.quarantine_ends_at

    available_at(quarantine_ends_at.hour, quarantine_ends_at.min)
  end

  # returns subtotal of bundle items vat excluded
  # based on +price_list_category+
  # raise an error if this isn't bundle
  def bundle_item_subtotal(price_list_category)
    raise "#{inspect} is not a bundle item!" unless bundle?

    start_value = BigDecimal '0'
    bundle_items.reduce(start_value) do |sum, b_item|
      sum += b_item.dealer_price_vat_excluded(price_list_category) * b_item.quantity
    end
  end

  def bundle?
    product_type == PRODUCT_TYPE_BUNDLE
  end

  def stocked_in_system_owner?
    system_owner_stocks_count > 0
  end

  def system_owner_stocks_count
    return bookable_quantity if dhl_supply_chain?
    return amount_in_system_owner_warehouse unless serial?

    Repositories::ItemRepository.in_system_owner_warehouse(id)
  end

  def agent_profit(price_list_category)
    case price_list_category
    when 'a'
      agent_profit_a
    when 'b'
      agent_profit_b
    when 'c'
      agent_profit_c
    end
  end

  def bundle_vat(price_list_category)
    case price_list_category
    when 'a'
      bundle_vat_a
    when 'b'
      bundle_vat_b
    when 'c'
      bundle_vat_c
    end
  end

  def dealer_price_vat_excluded(price_list_category)
    case price_list_category
    when 'a'
      dealer_price_vat_excluded_a
    when 'b'
      dealer_price_vat_excluded_b
    when 'c'
      dealer_price_vat_excluded_c
    end
  end

  def discounted_dealer_price_vat_excluded(price_list_category)
    case price_list_category
    when 'a'
      dealer_price_vat_excluded_a - (dealer_price_vat_excluded_a * default_percent_discount / 100.00)
    when 'b'
      dealer_price_vat_excluded_b - (dealer_price_vat_excluded_b * default_percent_discount / 100.00)
    when 'c'
      dealer_price_vat_excluded_c - (dealer_price_vat_excluded_c * default_percent_discount / 100.00)
    end
  end

  def creator
    create_action = audits.detect { |audit| audit.action == 'create' }
    create_action ? create_action.user : nil
  end

  def last_updater
    last_update_action = audits.select { |audit| audit.action == 'update' }.last
    last_update_action ? last_update_action.user : nil
  end

  def first_thumb
    product_images.first if product_images.any? && product_images.first.image.present? && product_images.first.image.small.present?
  end

  def require_promo_device_price?
    product_type == 'pa' && (Operator::WIND_TRE_OPERATORS & operators.pluck(:code)).any?
  end

  def reservable_by(dealer)
    dhl_pigeon_house? && instock_in_dhl? &&
      dealer.available_fido >= dealer_price_vat_excluded_b
  end

  def can_be_wished_by(dealer)
    dhl_pigeon_house? && !instock_in_dhl? &&
      dealer.available_fido >= dealer_price_vat_excluded_b
  end

  def instock_in_dhl?
    bookable_quantity && bookable_quantity > 0
  end

  def sim_compatible_with?(offer_id)
    offer = Offer.find(offer_id)

    (offer.sim_kind_list & sim_kind_list).any?
  end

  def ribbon_sort
    case ribbon
    when 'featured'
      0
    when 'news'
      1
    when 'promo'
      2
    when 'normal'
      3
    when 'incoming'
      4
    end
  end

  def name_downcase
    name.downcase
  end

  def dealer_price_vat_excluded_a_sort
    dealer_price_vat_excluded_a - (dealer_price_vat_excluded_a * default_percent_discount / 100.00)
  end

  def dealer_price_vat_excluded_b_sort
    dealer_price_vat_excluded_b - (dealer_price_vat_excluded_b * default_percent_discount / 100.00)
  end

  def dealer_price_vat_excluded_c_sort
    dealer_price_vat_excluded_c - (dealer_price_vat_excluded_c * default_percent_discount / 100.00)
  end

  def update_amounts_in_warehouses
    system_owner_items_count              = Repositories::ItemRepository.in_system_owner_warehouse(id)
    self.amount_in_system_owner_warehouse = system_owner_items_count

    if dhl_supply_chain
      dhl_items_count        = Repositories::ItemRepository.in_dhl_warehouse(id)
      self.bookable_quantity = dhl_items_count

      if sim? || !dhl_pigeon_house
        self.bookable_quantity -= ItemOrder.still_to_be_shipped(id).count(:id)
      end
    end

    save
  end

  private

  def available_at(hours, minutes)
    rounded_minutes = case minutes
                      when 0..19 then '20'
                      when 20..39 then '40'
                      when 40..59 then '00'
                      end

    hours += 1 if rounded_minutes == '00'

    "#{hours}:#{rounded_minutes}"
  end

  def sync_sap_product_inforecord
    return if consignment

    if (sap_exportable? || sap_inforecord_to_export_on_edit?) && (gamma_purchase_price.presence || 0) > 0
      # build if still not created
      build_sap_product_inforecord if sap_product_inforecord.blank?
      sap_product_inforecord.gamma_purchase_price = gamma_purchase_price
      sap_product_inforecord.vat_type = vat_type
    end
  end

  def cleanup_unneeded
    return unless product_type == ProductType::PRODUCT

    self.product_kind_list            = []
    self.activable_system_owner       = false
    self.enabled_for_imei_reservation = false
    self.product_categories           = []
    self.promo_device_price           = nil
    self.credit_note_amount           = nil
    self.loanable                     = false
    self.reward                       = nil
    self.view_in_matrix               = false
    self.mandatory_mnp                = false
    self.dhl_pigeon_house             = false
  end

  def reindex_offer_instances
    if (previous_changes.keys.map(&:to_sym) & [:full_name,
                                               :mandatory_mnp,
                                               :name,
                                               :sim_format,
                                               :activable_system_owner]).any?

      Delayed::Job.enqueue(Bost::Job::OfferInstanceReindexJob.new(product_id: id), queue: 'offer_instances', priority: 0)
    end
  end

  def make_offer_instances
    return true unless should_regenerate_instances?

    begin
      Offer.transaction do
        offers = []
        if product_kind_list_changed? || other_instances_attributes_changed?
          products = product_kind_list.dup
          products += product_kind_list_was.split(',').map(&:strip).compact if product_kind_list_changed?
          offers   += Offer.tagged_with(products.uniq, on: :product_kinds, any: true).all
        end
        if sim_kind_list_changed? || other_instances_attributes_changed?
          sims = sim_kind_list.dup
          sims   += sim_kind_list_was.split(',').map(&:strip).compact if sim_kind_list_changed?
          offers += Offer.tagged_with(sims.uniq, on: :sim_kinds, any: true).all
        end
        offers.uniq.each do |offer|
          if skip_offer_instances
            offer.update_column(:to_generate, true)
          else
            offer.generate_instances!
          end
        end
      end
    rescue Exception => e
      # log exception
      KolmeLogger.error e
      Rails.logger.error %{
        !!!! ERROR saving plan #{id}: make_offer_instances
        #{ex.message}
                         #{ex.backtrace.join("\n")}
                         }
      errors.add(:base, I18n.t('errors.messages.kolme_offer_instances_not_created'))
      raise Bost::OfferInstanceError
    end
    true
  end

  def set_consignment
    self.consignment = bundle_items.first.item.consignment
  end

  def should_regenerate_instances?
    (product_kind_list_changed? ||
      sim_kind_list_changed? ||
      other_instances_attributes_changed?)
  end

  def other_instances_attributes_changed?
    product_type_changed? ||
      visibility_changed? ||
      sim_format_changed?
  end

  def sanitize_and_strip_tags(tag_list)
    tags = tag_list.to_s.split(',').map { |tag| Rails::Html::FullSanitizer.new.sanitize(tag) }
    tl   = ActsAsTaggableOn::TagList.new(tags.reject(&:blank?))
    tl.delete(Settings.tagging.no_tag)

    tl.to_s
  end

  # aids recording the tag "NO"
  def strip_no_tag
    if store_tag_list_changed?
      self.store_tag_list = sanitize_and_strip_tags(store_tag_list)
    end

    if store_subtag_list_changed?
      self.store_subtag_list = sanitize_and_strip_tags(store_subtag_list)
    end

    if product_kind_list_changed?
      self.product_kind_list = sanitize_and_strip_tags(product_kind_list)
    end

    if sim_kind_list_changed?
      self.sim_kind_list = sanitize_and_strip_tags(sim_kind_list)
    end
  end

  def recharge_size_should_be_for_the_chosen_operator
    if recharge_size.present? && !operators.include?(recharge_size.operator)
      errors.add(:recharge_size_id, I18n.t('errors.messages.kolme_recharge_size_invalid_for_operator'))
    end
  end

  def bundle_items_uniqueness
    unless bundle_items.map(&:item_id).count == bundle_items.map(&:item_id).uniq.count
      errors.add(:base, 'Non possono esserci componenti uguali')
    end
  end

  def bundle_items_emptiness
    if bundle? && bundle_items.empty?
      errors.add(:base, 'Prodotti di tipo bundle devono avere almeno un componente')
      false
    end
  end

  def bundle_items_consignment
    unless bundle_items.map(&:consignment).uniq.count == 1
      errors.add(:base, 'Non è possibile inserire nello stesso bundle prodotti in Conto Vendita e prodotti Standard')
    end
  end

  def max_allowed_operators
    if ProductType.sim_types.include?(product_type) && operators.length > 1
      errors.add(:operators, 'La Tipologia prodotto SIM prevede un solo operatore')
    end
  end

  def dhl_flags_consistency
    return unless dhl_pigeon_house?

    unless dhl_supply_chain? && product_type == 'pa' && serial?
      errors.add(:base, "'Piccionaia' DHL applicabile solo per prodotti attivabili, a seriale e in supply chain DHL")
    end
  end

  def code_max_length
    return unless dhl_supply_chain?

    if code.length > 18
      errors.add(:base, 'Un prodotto DHL Supply Chain non può avere un codice prodotto superiore a 18 caratteri')
    end
  end

  def audit_changes
    merge_audits_changes(
      [],
      [
        {
          field: :store_tag_list,
          value: store_tag_list.join(', ')
        },
        {
          field: :store_subtag_list,
          value: store_subtag_list.join(', ')
        }
      ]
    )
  end

  def requestable_or_activable?
    can_be_requested || activable_system_owner
  end

  def expire_bundles_cache
    bundle_ids = BundleItem.from_bundle_component(id).pluck(:product_id)

    Product.where(id: bundle_ids).each(&:touch)
  end

  def export_to_dhl
    job = Jobs::DhlProductsCsvExporterJob.new(products: [self])

    unless Rails.env.test? || Rails.env.development?
      Delayed::Job.enqueue(job, queue: 'dhl', priority: 5)
    end
  end

  def floa_financing_price_limit
    return unless floa_top_seller

    errors.add(:public_price_list, :invalid) unless floa_financeable_price?
  end

  def has_unique_default_color_for_commercial_name
    return if skip_validations

    product_with_default_color = Product.find_by(has_default_color: true,
                                                 commercial_name:   commercial_name)
    return unless product_with_default_color

    if has_default_color && product_with_default_color != self
      errors.add(:has_default_color,
                 'Il Colore per immagine di default per i prodotti con questo Nome aggregato ' \
                 "è già impostato (#{product_with_default_color.color})")
    end
  end

  def render_not_requestable_if_not_visible
    if !visibility.in?(VisibilityKind.visible_kinds) && (can_be_requested || activable_system_owner || view_in_matrix)
      update_columns(can_be_requested:       false,
                     activable_system_owner: false,
                     view_in_matrix:         false)
    end
  end

  def strip_input_strings
    attributes.each do |key, value|
      self[key] = value.strip if value.respond_to?('strip')
    end
  end

  def sanitize_fields
    self.max_qty_visible   ||= 0
    self.final_installment ||= 0
    self.name = name.gsub('"', "''")
    self.sap_name = sap_name&.gsub('"', "''")&.truncate(35, omission: '')
    self.commercial_name = commercial_name&.gsub('"', "''")
  end

  def truncate_attiva_product_name
    return unless attiva_origin?

    self.name = name.truncate(MAX_NAME_LENGTH, omission: '').strip.gsub('"', "''")
    self.sap_name = sap_name&.strip&.gsub('"', "''")&.truncate(35, omission: '')
    self.commercial_name = commercial_name&.strip&.gsub('"', "''")
  end
end

