module Abilities::Rules::EnergyContract
  def self.apply(base)
    base.can :autocomplete_energy_contract_pdc_code, EnergyContract
    base.cannot :show, EnergyContract do |energy_contract|
      %w(waiting_for_acea waiting_for_customer_signature signed to_recover add_submit_api_call cancelled cancelled_draft activated multiple_states).include?(energy_contract.state)
    end
    base.cannot :destroy, EnergyContract do |energy_contract|
      energy_contract.send_request_call.present?
    end
  end
end
