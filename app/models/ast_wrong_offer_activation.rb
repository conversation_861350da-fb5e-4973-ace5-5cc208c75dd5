class AstWrongOfferActivation < ApplicationRecord
  include GenericAst
  mount_uploader :attachment, AstWrongOfferActivationUploader

  PA_PERSONA_FISICA_REJECTED = false

  has_one :after_sale_thread, as: :threadable, dependent: :destroy

  validates_presence_of :plan_name,
                        :phone_imei,
                        :notes

  validates :phone_imei, numericality: true

  def require_energy_contract?
    false
  end

  def require_phone_activation?
    false
  end
end
