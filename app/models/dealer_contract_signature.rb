class DealerContractSignature < ApplicationRecord
  belongs_to :dealer
  has_many :alerts, as: :alertable, dependent: :destroy

  SIGNATURE_NEEDED_MESSAGE = 29.freeze

  scope :imported_before, -> (session_timestamp) { where('session_timestamp < ?', session_timestamp) }

  accepts_nested_attributes_for :alerts, allow_destroy: true

  searchable do
    integer :id
    integer :dealer_id

    string :envelope_id
    string :subject
    string :url
    string :cod8mld
    string :dealer_vat
    string :dealer_contact_email
    string :dealer_contact_name
    string :dealer_company_name
    string :dealer_string_name do 
      dealer.name.upcase
    end
    integer :session_timestamp

    join(:name, prefix: 'dealer', target: Dealer, type: :text, join: { from: :id, to: :dealer_id }, as: :name_itext)
  end

  def alert_params
    return unless dealer.main_warehouse
    
    {
      alertable_id:     id,
      alertable_type:   'DealerContractSignature',
      dealer_id:        dealer.id,
      warehouse_id:     dealer.main_warehouse.id,
      alert_message_id: SIGNATURE_NEEDED_MESSAGE,
      todo:             true
    }
  end
end
