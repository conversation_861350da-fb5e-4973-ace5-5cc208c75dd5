class GammaPaymentMethod < ApplicationRecord
  AUDIT_EXCEPT = %w().freeze

  audited except: AUDIT_EXCEPT

  validates :code, :description, :position, presence: true
  validates_uniqueness_of :code, :description, :position, case_sensitive: true

  default_scope { order("position ASC") }

  def self.for_select
    all.map { |gamma_payment_method| [gamma_payment_method.description, gamma_payment_method.id] }
  end
end
