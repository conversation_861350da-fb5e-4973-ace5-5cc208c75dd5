class OfficialPda < ApplicationRecord
  belongs_to :phone_activation

  has_many :documents, as: 'documentable', after_add: :set_code

  delegate :dealer,
           :warehouse,
           to: :phone_activation, prefix: false, allow_nil: false

  def code_available?
    code && code != 'unavailable_code'
  end

  def document_kind
    'documents'
  end

  def document_kinds_for(context)
    Settings.document_uploader.send(self.class.name.underscore.pluralize).contexts.send(context).fields
  end

  def documentable_type
    self.class.to_s.underscore.pluralize
  end

  def get_code
    code_available? ? code : 'Impossibile recuperare il codice contratto dal documento'
  end

  def set_code(document)
    update_columns(code: Util::OfficialPdaCodeUtil.new(document.related_file.url).get_code)
  end
end
