class RenewSms < RenewNotification
  FAILED_SMS_REFERENCE   = "-1".freeze
  MAX_BODY_CHARS    = 160
  MAX_SMS_PER_WEEK  = 2
  SENDER_ALIAS      = '3311846119'

  belongs_to :user
  belongs_to :customer

  before_validation :set_sender_alias, on: :create

  scope :successfully_sent, -> { where.not(aimon_id: FAILED_SMS_REFERENCE) }

  validate :max_sent_per_week
  validates :phone_number, presence: true
  validates :sender_alias, presence: true
  validates :body, presence: true,
                   length:   { maximum: MAX_BODY_CHARS }

  def max_sent_per_week
    if max_sent_reached?
      errors.add(:base, "Numero massimo (#{MAX_SMS_PER_WEEK}) di sms per attivazione a settimana raggiunto.")
    end
  end

  def max_sent_reached?
    phone_activation && phone_activation.latest_smses_for_week.count >= MAX_SMS_PER_WEEK
  end

  def failed!
    update_attribute :aimon_id, FAILED_SMS_REFERENCE
  end

  private

  def set_sender_alias
    self.sender_alias = Bost::Constants::ITALY_AREA_CODE + SENDER_ALIAS
  end
end
