class AstEditPaymentType < ApplicationRecord
  include GenericAst

  FIELDS_TO_LOCK_WHEN_BUILDING_FROM_PHONE_ACTIVATION = %w(phone_number).freeze
  CREDIT_CARD_TYPES                                  = %w(visa mastercard amex diners).freeze
  PAYMENT_METHODS                                    = %w(credit_card sdd).freeze
  INVALID_IBAN                                       = %w(07601 03062 03058 03169 03475).freeze
  GRAPHQL_FIELDS                                     = %w(dealer_name phone_activation_hex_id customer_uniq_name
                      phone_number operator_name payment_method iban parsed_iban credit_card_number
                      credit_card_type credit_card_expire_month credit_card_expire_year
                      credit_card_cvv notes).freeze
  GRAPHQL_TRANSLATED_FIELDS                          = %w(payment_method).freeze
  IBAN_REGEXP                                        = /\AIT\d\d[a-z]\d{10}[a-z0-9]{12}\z/i.freeze

  attr_accessor :customer_cf

  attr_accessible :after_sale_thread_id, :credit_card_cvv, :credit_card_expire_month,
                  :credit_card_expire_year, :credit_card_number, :credit_card_type,
                  :iban, :notes, :operator_id, :payment_method, :phone_number, :customer_id
  attr_encrypted :credit_card_number, key: Rails.application.credentials.attr_encrypted_key
  attr_encrypted :credit_card_expire_month, key: Rails.application.credentials.attr_encrypted_key
  attr_encrypted :credit_card_expire_year, key: Rails.application.credentials.attr_encrypted_key
  attr_encrypted :credit_card_type, key: Rails.application.credentials.attr_encrypted_key
  attr_encrypted :iban, key: Rails.application.credentials.attr_encrypted_key
  attr_encrypted :phone_number, key: Rails.application.credentials.attr_encrypted_key

  belongs_to :operator
  belongs_to :customer
  has_one :after_sale_thread, as: :threadable, dependent: :destroy

  validates :payment_method, presence: true, inclusion: PAYMENT_METHODS
  validates :operator_id, presence: true
  validates :customer_id, presence: true

  delegate :name,
           to:        :operator,
           prefix:    true,
           allow_nil: true

  delegate :dealer_name,
           :phone_activation_hex_id,
           to:        :after_sale_thread,
           prefix:    false,
           allow_nil: true

  delegate :uniq_name,
           to:        :customer,
           prefix:    true,
           allow_nil: true

  with_options if: :credit_card? do
    validate :cc_expired?
    validate :reject_postepay_unless_eolo
    validate :reject_ingdirect_if_tre
    validates :credit_card_number, presence: true, numericality: { only_integer: true }
    validates :credit_card_number, length: { is: 16 }, unless: :card_amex_or_diners?
    validates :credit_card_number, length: { is: 15 }, if: :card_amex?
    validates :credit_card_number, length: { within: 14..16 }, if: :card_diners?
    validates :credit_card_cvv, presence: true, numericality: { only_integer: true }
    validates :credit_card_cvv, length: { is: 3 }, unless: :card_amex?
    validates :credit_card_cvv, length: { is: 4 }, if: :card_amex?
    validates :credit_card_expire_month, presence: true, numericality: { only_integer: true }
    validates :credit_card_expire_year, presence: true, numericality: { only_integer: true }
    validates :credit_card_type, presence: true, inclusion: CREDIT_CARD_TYPES
  end

  with_options if: :sdd? do
    validate :reject_invalid_iban_if_tre_mobile
    validates :iban, presence: true, length: { is: 27 }, format: { with: IBAN_REGEXP }
  end

  def self.phone_number_from(phone_activation)
    phone_activation.number_portability_detail_phone_number || phone_activation.sim_item_phone_number
  end

  def credit_card?
    payment_method == 'credit_card'
  end

  def customer_required?
    true
  end

  def sdd?
    payment_method == 'sdd'
  end

  %w(visa mastercard amex diners).each do |type|
    define_method "card_#{type}?" do
      credit_card_type == type
    end
    define_method "card_not_#{type}?" do
      credit_card_type != type
    end
  end

  def card_amex_or_diners?
    card_amex? || card_diners?
  end

  def reject_invalid_iban_if_tre_mobile
    return unless iban
    return unless after_sale_thread
    return if after_sale_thread.phone_activation.blank?

    if operator.tre? && INVALID_IBAN.include?(iban.to_s[5..9]) && after_sale_thread.phone_activation.plan_service_kind.mobile?
      errors.add(:iban, I18n.t('errors.messages.kolme_payment_exclude_rid'))
    end
  end

  def cc_expired?
    return unless credit_card_expire_year && credit_card_expire_month

    if Date.new(credit_card_expire_year, credit_card_expire_month).at_end_of_month < Date.today
      errors.add(:credit_card_expire_year, I18n.t('errors.messages.kolme_payment_cc_expiry_should_be_future'))
    end
  end

  def reject_postepay_unless_eolo
    return unless credit_card_number

    if credit_card_number.starts_with?('4023') && !operator.eolo?
      errors.add(:credit_card_number, I18n.t('errors.messages.kolme_payment_exclude_postepay_cc'))
    end
  end

  def reject_ingdirect_if_tre
    return unless credit_card_number

    if credit_card_number.starts_with?('419704') && operator.tre?
      errors.add(:credit_card_number, I18n.t('errors.messages.kolme_payment_exclude_ingdirect_cc'))
    end
  end

  def readonly_field?(field, parent_object)
    parent_object.phone_activation_id.present? &&
      FIELDS_TO_LOCK_WHEN_BUILDING_FROM_PHONE_ACTIVATION.include?(field.to_s)
  end

  def parsed_iban
    iban.scan(/(..)(..)(.)(.....)(.....)(............)/).join(' ')
  end

  def graphql_printable_fields
    Graphql::GraphqlFieldsBuilder.new(self).build
  end
end