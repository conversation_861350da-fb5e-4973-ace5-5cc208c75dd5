class PhoneCallReport < ApplicationRecord
  AGENT_CALL = "Chiamata agente".freeze

  INCOMING_CALL_MOTIVATIONS_ALTRO = [
    "Chiamata interrotta",
    "Altro"
  ].freeze

  INCOMING_CALL_MOTIVATIONS_AMMINISTRAZIONE = [
    "Info Note Di Credito / Cessione Credito",
    "Info Sul Fido / Compensazioni",
    "Scadenze Fine Mese",
    "Sollecito Approvazione Bonifico Per Ordine",
    "Info Sul Mandato Sdd",
    "Info Fatture Elettroniche (Codice Sdi)",
    "Info amministrative Windtre"
  ].freeze

  INCOMING_CALL_MOTIVATIONS_ANOMALIE = [
    "Anomalia Pos Ng",
    "Anomalia Spazio Kolme",
    "Altri sistemi Windtre",
    "Luce & Gas"
  ].freeze

  INCOMING_CALL_MOTIVATIONS_FORMAZIONE = [ 
    "Operatività Pos Ng",
    "Operatività Spazio Kolme",
    "<PERSON>ced<PERSON> Kolme",
    "Welcome Call",
    "Info Offerta Consumer",
    "Chiesto Ricontatto Agente",
    "Operatività altri applicativi Windtre",
    "Luce & Gas"
  ].freeze

  INCOMING_CALL_MOTIVATIONS_PRODOTTI = [ 
    "Disponibilità Restock Prodotti",
    "Disponibilità Nuovi Prodotti"
  ].freeze

  INCOMING_CALL_MOTIVATIONS_TRASFERIMENTO = [
    "Verso Amministrazione",
    "Verso Logistica",
    "Verso Operation"
  ].freeze


  INCOMING_CALL_MOTIVATIONS_VERIFICHE = [ 
    "Avanzamento Attivazione",
    "Errata Attivazione",
    "Verifica Stato Doa",
    "Verifica Stato Ordine",
    "Avanzamento Gestione",
    "Verifica Stato Mnp",
    "Avanzamento Luce Gas",
    "FWA - Info affiliazione installatore",
    "FWA - Richiesta Kit",
    "FWA - Gestione appuntamento"
  ].freeze

  OUTGOING_CALL_MOTIVATIONS_AMMINISTRAZIONE = [
    "Recupero Crediti",
    "Mandato Sdd",
    "Fatture Errate",
    "Verifica Dati Anagrafici"
  ].freeze

  OUTGOING_CALL_MOTIVATIONS_GENERALE = [
    "Recall gestione in corso",
    "Chiamata Non Effettuata",
    "Altro"
  ].freeze

  OUTGOING_CALL_MOTIVATIONS_OPERATION = [
    "Extragestione",
    "Formazione",
    "Welcome Call"
  ].freeze

  NON_DEALER_MOTIVATIONS = [
    "Chiamata supporto interna",
    "Chiamata agente",
    "Chiamata interrotta",
    "Dealer non rilascia generalità",
    "Dealer nuova affiliazione",
    "Chiamata fornitore"
  ].freeze

  IGNORABLE_CALL_MOTIVATIONS = ["Chiamata Non Effettuata"].freeze

  INCOMING_CALL_MOTIVATION_MACRO_CATEGORIES = %w(amministrazione 
                                                 prodotti 
                                                 verifiche 
                                                 anomalie 
                                                 formazione 
                                                 trasferimento 
                                                 altro).freeze
  OUTGOING_CALL_MOTIVATION_MACRO_CATEGORIES = %w(generale operation amministrazione).freeze

  belongs_to :user
  belongs_to :dealer
  belongs_to :phone_call
  belongs_to :agent, class_name: "User", foreign_key: "agent_id"

  validates :user, presence: true
  validates :dealer, presence: true, unless: -> { dealer_id.nil? }
  validates :initiated_at, presence: true
  validates :phone_number, presence: true, format: {
    with:    /\A\d+\z/,
    message: "Solo caratteri numerici"
  }
  validates :motivation, presence: true
  validates :notes, presence: true
  validates :motivation_macro_category, presence: true
  validates :agent_id, presence: true, if: -> { motivation == AGENT_CALL }

  before_validation :set_motivation_macro_category_for_non_dealer

  delegate :full_name, to: :user, prefix: true, allow_nil: true

  scope :dealer, -> { where("dealer_id is not null") }
  scope :non_dealer, -> { where("dealer_id is null") }

  def motivation_macro_categories_options
    (incoming? ? INCOMING_CALL_MOTIVATION_MACRO_CATEGORIES : OUTGOING_CALL_MOTIVATION_MACRO_CATEGORIES).map(&:capitalize)
  end

  private

  def set_motivation_macro_category_for_non_dealer
    self.motivation_macro_category = "Generale" if dealer_id.nil? || motivation.in?(NON_DEALER_MOTIVATIONS)
  end
end