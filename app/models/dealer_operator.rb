class DealerOperator < ApplicationRecord
  # == Constants ============================================================

  # == Attributes ===========================================================

  attr_accessor :first_checkbox, :second_checkbox, :third_checkbox

  # == Extensions ===========================================================

  # == Relationships ========================================================

  belongs_to :dealer
  belongs_to :operator

  # == Validations ==========================================================

  # == Delegate =============================================================

  delegate :code, :name, to: :operator, prefix: true, allow_nil: true
  delegate :full_name, to: :signer, prefix: true, allow_nil: true

  # == Scopes ===============================================================

  scope :sort_by_operator_name, -> { joins(:operator).order('operators.name ASC') }

  # == Operations::Callbacks ============================================================

  after_save :reindex_dealer

  # == Searchable ===========================================================

  # == AASM =================================================================

  # == Class Methods ========================================================

  # == Instance Methods =====================================================

  def reindex_dealer
    Sunspot.index dealer
  end
end
