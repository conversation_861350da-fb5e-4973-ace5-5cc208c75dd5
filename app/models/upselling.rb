class Upselling < ApplicationRecord
  IMPORT_KINDS = %w(update_and_add delete_and_add).freeze

  belongs_to :product
  belongs_to :plan

  validates_presence_of :cluster,
                        :installment,
                        :plan_id,
                        :product_code,
                        :product_id

  def image_url
    return product&.default_image&.image&.url if product&.default_image&.image&.url

    product&.product_images&.first&.image&.url
  end

  def new_phone_activation_params(phone_activation)
    {
      phone_activation: {
                          plan_id:             plan_id,
                          product_id:          product_id,
                          offer_category_id:   OfferCategory::RATEALE_ID,
                          product_category_id: phone_activation.product_category_id,
                          payment_method_id:   phone_activation.payment_method_id,
                          dealer_id:           phone_activation.dealer_id,
                          user_id:             phone_activation.user_id,
                        }.merge(phone_activation.duplicate_upselling_params)
    }
  end
end