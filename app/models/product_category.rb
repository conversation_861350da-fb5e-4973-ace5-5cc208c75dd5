class ProductCategory < ApplicationRecord
  include Bost::LovBehavior

  D<PERSON>A_CATEGORY     = 'dati'.freeze
  VOICE_CATEGORY    = 'voce'.freeze
  LANDLINE_CATEGORY = 'fissa'.freeze
  SERVICES_CATEGORY = 'servizi'.freeze

  has_many :plans

  has_many :product_product_categories
  has_many :products, through: :product_product_categories

  def self.ransackable_associations(auth_object = nil, exclude = [])
    authorizable_ransackable_associations.dup.excluding(exclude)
  end

  def self.ransackable_attributes(auth_object = nil, exclude = [])
    authorizable_ransackable_attributes.dup.excluding(exclude)
  end

  def is_dati?
    code == DATA_CATEGORY
  end

  def is_voce?
    code == VOICE_CATEGORY
  end

  def is_fissa?
    code == LANDLINE_CATEGORY
  end

  def is_servizi?
    code == SERVICES_CATEGORY
  end

  def is_not_sim_compatible?
    is_fissa? || is_servizi?
  end
end