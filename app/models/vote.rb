class Vote < ApplicationRecord
  belongs_to :votable, polymorphic: true
  belongs_to :user

  has_one :dealer_contact, through: :user

  validates_presence_of :value
  validates_numericality_of :value

  def self.ransackable_associations(auth_object = nil, exclude = [])
    authorizable_ransackable_associations.dup.excluding(exclude)
  end

  def self.ransackable_attributes(auth_object = nil, exclude = [])
    authorizable_ransackable_attributes.dup.excluding(exclude)
  end
end
