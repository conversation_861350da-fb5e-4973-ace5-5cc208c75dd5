# frozen_string_literal: true

class Recharge < ApplicationRecord
  AUDIT_EXCEPT = %w(
    uuid
    phone_number
    recharged_at
    recharge_size_id
    deleted
    feedback_paymat
    created_at
    updated_at
    dealer_id
    user_id
    recharge_attempts
    phone_activation_id
    mail_sent
    no_credit_mail_sent
    provider
    token
    euronet_status
  ).freeze
  audited except: AUDIT_EXCEPT

  self.per_page = 10

  MAX_AMOUNT_NOT_TELEFONIA_24_HOURS = 100

  FAILED = 1
  SUCCESS = 0
  TO_WORK = 2

  PRESENT_STATUS_KO = 'ko'
  PRESENT_STATUS_OK = 'ok'
  PRESENT_STATUS_PENDING = 'pending'

  NOTHING_DONE = 0
  CHECK_REQUEST_SENT = 1
  CHECK_REQUEST_DONE = 5
  CONFIRM_REQUEST_SENT = 10
  CONFIRM_REQUEST_DONE = 15

  FROM_APP_PREFIX = 'B'
  SYSTEM_PREFIX = 'S'

  include Bost::Uuid
  include RechargeDecorator

  attr_reader :balanceable_amount
  attr_accessor :token

  store :feedback_paymat, accessors: %i[transaction_code
                                        progressive
                                        receipt
                                        amount
                                        date
                                        tim_id
                                        auth_id
                                        status_code
                                        recharge_id
                                        chksum
                                        validita
                                        status_code
                                        error_code
                                        error_message
                                        tech_error_message]

  belongs_to :recharge_size
  belongs_to :dealer
  belongs_to :user
  belongs_to :phone_activation
  has_many :euronet_transactions
  has_one :aggregated_operator, through: :recharge_size
  has_one :aggregated_operator_category, through: :aggregated_operator

  validates :recharge_size, presence: true
  validates :phone_number, presence: true, unless: -> { 'contextual? or pin?' }
  validates_format_of :phone_number, with: /\A^\d+\z/, unless: -> { 'contextual? or pin?' }
  validates :phone_number, length: { maximum: 10 }, unless: -> { 'contextual? or pin?' }
  validates :phone_number,
            format: { with: /\A\d{1,19}\z/ }, if: :bypass_phone_number_check?

  validates_uniqueness_of :secure_token, case_sensitive: true

  after_save :check_recharge_status_and_alert

  delegate :telefonia?, to: :aggregated_operator
  delegate :amazon?, :bypass_phone_number_check?, :cif?, :dazn?, to: :recharge_size

  scope :paymat, -> { where(provider: RechargeSize::PROVIDER_PAYMAT) }
  scope :euronet, -> { where(provider: RechargeSize::PROVIDER_EURONET) }
  scope :non_manual, -> { where('recharges.provider in (?)', [RechargeSize::PROVIDER_EURONET, RechargeSize::PROVIDER_PAYMAT]) }
  scope :failed, -> { non_manual.where(recharge_status: Recharge::FAILED, mail_sent: false).where('phone_activation_id IS NOT NULL') }
  scope :retriable, -> { joins(:recharge_size).where(recharge_status: Recharge::FAILED, mail_sent: false).where('recharge_sizes.retry_on_ko = 1') }
  scope :successful, -> { where('recharge_status = ?', Recharge::SUCCESS) }
  scope :euronet_unconfirmed, -> { euronet.successful.where(euronet_status: Recharge::CONFIRM_REQUEST_SENT) }
  scope :with_includes, -> { includes(:recharge_size, phone_activation: [:sim_item], dealer: [:warehouses]) }
  scope :not_deleted, -> { where(deleted: false) }
  scope :successful_automatic_in_last_week, -> do
    where('recharged_at BETWEEN ? AND ?', Time.now - 7.days, Time.now)
      .joins(:recharge_size)
      .where(['recharge_status = ? and recharge_sizes.provider = ?', Recharge::SUCCESS, RechargeSize::PROVIDER_AUTOMATIC])
  end

  def initialize(attributes = {})
    super
    self.secure_token ||= generate_token
  end

  searchable(include: [:recharge_size, { phone_activation: [:sim_item], dealer: [:warehouses] }]) do
    text(:phone_number)
    text(:dealer_name) { dealer.name }
    text(:dealer_ragione_sociale) { dealer.ragione_sociale }
    text(:dealer_insegna) { dealer.warehouses.map(&:ensign) }
    time(:recharged_at)
    time(:created_at)
    integer(:recharge_size_id)
    integer(:dealer_id)
    integer(:user_id)
    integer(:recharge_status) do
      if failed? && still_retriable?
        Recharge::TO_WORK
      else
        recharge_status
      end
    end
    integer(:recharge_attempts)
    integer(:id)
    boolean(:mail_sent)
    boolean(:no_credit_mail_sent)
    boolean(:deleted)
    boolean(:has_phone_activation) { !phone_activation_id.nil? }
    boolean(:recharge_size_manual) { recharge_size.manual? }
    integer(:recharge_operator) { recharge_size.paymat_operator_id }
    integer(:recharge_amount) { amount }
    boolean(:retriable) { recharge_size.retry_on_ko }
    integer(:aggregated_operator_id) { recharge_size.aggregated_operator_id }
  end

  def self.last_seven_days_amount
    successful_automatic_in_last_week.map { |recharge| recharge.recharge_size.amount.to_i }.compact.sum
  end

  def self.recharge_amount(recharge_size, source_recharge_size)
    return 0 unless recharge_size
    return source_recharge_size.amount_charged if recharge_size.paymat? && recharge_size.paymat_quantity > 1
    return recharge_size.amount_charged if recharge_size.paymat?
    return recharge_size.amount_charged if recharge_size.pin? || recharge_size.manual?
    return source_recharge_size.amount_charged if recharge_size.euronet_quantity > 1

    recharge_size.amount_charged
  end

  def amount
    recharge_size.amount
  end

  def asterisk_cif
    "#{phone_number[0, 3]}**********#{phone_number[13, 15]}"
  end

  def balanceable_amount=(val)
    @balanceable_amount = -val
  end

  def before_postemobile_change?
    created_at <= DateTime.parse('30/09/2018')
  end

  def can_execute_recharge?
    dealer_have_enough_credit?
  end

  def can_be_confirmed?
    can_be_dismissed? && dealer_have_enough_credit?
  end

  def can_be_dismissed?
    !recharge_size.automatic? && !manually_executed? && recharge_status != Recharge::FAILED
  end

  def can_retry?
    last_attempt_date >= Time.now
  end

  def check_recharge_status_and_alert
    if saved_change_to_recharge_status? && (recharge_status == Recharge::SUCCESS)
      Alert.where(alertable_id: id, alertable_type: self.class).delete_all
    end
  end

  def confirmed_or_manually_executed?
    euronet_confirmed? || manually_executed?
  end

  def contextual?
    phone_activation_id.present?
  end

  def dealer_have_enough_credit?
    return true if dealer.system_owner?

    dealer.recharges_credit_available >= recharge_size.amount
  end

  def euronet?
    provider == RechargeSize::PROVIDER_EURONET
  end

  def euronet_confirmed?
    euronet_status == Recharge::CONFIRM_REQUEST_DONE
  end

  def euronet_transaction
    euronet_transactions.where(call_type: [EuronetTransaction::CHECK_REQUEST, EuronetTransaction::OUT_OF_CREDIT]).last
  end

  def failed?
    recharge_status == Recharge::FAILED
  end

  def has_confirmed_euronet_transaction?
    euronet_transactions.where(call_type: EuronetTransaction::CONFIRM_REQUEST, status: EuronetTransaction::SUCCESS).any?
  end

  def last_attempt_date
    (created_at + 1.day).end_of_day - 2.hours
  end

  def last_euronet_confirm_transaction
    euronet_transactions.where(call_type: EuronetTransaction::CONFIRM_REQUEST).last
  end

  def legacy_object
    Bost::Legacy::Ricarica.where(IdRicarica: uuid).first
  end

  def manually_executed_success?
    manually_executed? &&
      recharge_status == Recharge::SUCCESS &&
      !recharge_size.automatic?
  end

  def manual?
    provider == RechargeSize::PROVIDER_MANUAL
  end

  def paymat?
    provider == RechargeSize::PROVIDER_PAYMAT
  end

  def phone_number
    if contextual?
      phone_activation.phone_number
    elsif pin? && !cif?
      nil
    else
      read_attribute(:phone_number)
    end
  end

  def pin?
    recharge_size.present? ? recharge_size.pin? : false
  end

  def present_status
    return Recharge::PRESENT_STATUS_PENDING if recharge_attempts.zero?

    case recharge_status
    when Recharge::SUCCESS, 90
      confirmed_or_manually_executed? ? Recharge::PRESENT_STATUS_OK : Recharge::PRESENT_STATUS_PENDING
    when Recharge::FAILED, nil
      if still_retriable?
        Recharge::PRESENT_STATUS_PENDING
      else
        Recharge::PRESENT_STATUS_KO
      end
    when Recharge::TO_WORK
      Recharge::PRESENT_STATUS_PENDING
    end
  end

  def status_ok?
    (recharge_status == Recharge::SUCCESS) || (recharge_status == 90)
  end

  def still_retriable?
    can_retry? && (contextual? || (!manual? && recharge_size.retry_on_ko)) && !mail_sent
  end

  protected

  def generate_token
    loop do
      random_token = Digest::SHA1.hexdigest([Time.now, rand].join)
      break random_token unless Recharge.exists?(secure_token: random_token)
    end
  end
end