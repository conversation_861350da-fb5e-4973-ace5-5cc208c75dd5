class AccountingDocumentDownload < ApplicationRecord
  belongs_to :user

  scope :by_legacy_document_id, ->(legacy_document_id) { where(legacy_document_id: legacy_document_id) }

  def self.already_downloaded?(legacy_document_id)
    by_legacy_document_id(legacy_document_id).any?
  end

  def self.log_download(legacy_document_id, user)
    create(legacy_document_id: legacy_document_id, user: user) unless already_downloaded?(legacy_document_id)
  end
end
