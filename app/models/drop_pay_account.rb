class DropPayAccount < ApplicationRecord
  SENSITIVE_ATTRIBUTES = %w(rid_iban mobile_phone)
  TRANSACTION_STATES   = [DropPayStatus::IN_ACTIVATION,
                          DropPayStatus::ACTIVATION_ERROR,
                          DropPayStatus::ACTIVE,
                          DropPayStatus::DEACTIVATED]

  has_many :alerts, as: :alertable, dependent: :destroy
  belongs_to :dealer, optional: true
  has_many :drop_pay_account_rows, dependent: :destroy

  audited associated_with: :dealer

  has_enumeration_for :state,
                      with:           DropPayStatus,
                      create_helpers: { prefix: true },
                      create_scopes:  { prefix: true }

  has_enumeration_for :recursive_charges_authorization,
                      with:           RecursiveChargesAuthorization,
                      create_helpers: { prefix: true },
                      create_scopes:  { prefix: true }

  validates :rid_iban, presence: true, length: { is: 27 }, iban: true
  validates :mobile_phone,
            length: { in: 9..10 },
            format: { with: /\A3[0-9]*\z/, message: 'Deve iniziare con 3' }

  with_options if: :require_date_state? do
    validates_presence_of :opened_at
  end
  validates_presence_of :closed_at, if: :state_deactivated?

  before_validation :set_dates_on_status_change, if: :state_changed?
  after_save :initialize_connection_data, if: :require_intialization_data?
  after_save :archive_alert, if: lambda { |drop_pay_account| drop_pay_account.saved_change_to_enabled? && !drop_pay_account.enabled? }

  attr_encrypted :bank_code, key: Rails.application.credentials.attr_encrypted_key

  default_scope { where.not(dealer_id: nil) }
  scope :enabled, -> { where(enabled: true) }
  scope :for_transactions_import, -> { enabled.where(state: TRANSACTION_STATES) }

  def self.system
    unscoped.where(dealer_id: nil).first
  end

  def active?
    enabled? && state_active? && recursive_charges_authorization_granted?
  end

  def can_require_connect_authorization?
    !enabled? || state_in_activation? || state_active?
  end

  def can_require_recursive_charge_authorization?
    !enabled? || !state_active? || recursive_charges_authorization_required? || recursive_charges_authorization_granted?
  end

  def check_connection_status
    if connection_id.present?
      connection_response = JSON.parse(Atono::Client.new.read_connect(self))

      update_connection_status(connection_response)
    end

    send_recursive_charges_authorization_request if require_recursive_charges_authorization?
  end

  def check_recursive_charges_authorization_status
    return unless pull_id

    connection_response = JSON.parse(Atono::Client.new.read_pull(DropPayAccount.system, pull_id))

    update_recursive_charges_authorization_status(connection_response)
  end

  def connection_id
    connection_response.fetch('id')
  end

  def connection_sharing_url
    connection_response.fetch('sharing')
  end

  def dealer_name
    dealer ? dealer.name : 'Kolme'
  end

  def jwt_token
    JwtService.new.encode(id)
  end

  def require_connect_authorization?
    return false unless enabled?

    !state_active? && !state_in_activation?
  end

  def require_recursive_charges_authorization?
    return false if system?
    return false unless enabled?
    return false unless bank_code

    !recursive_charges_authorization_granted? && !recursive_charges_authorization_required?
  end

  def send_authorizations_request
    send_connect_authorization_request if require_connect_authorization?
    send_recursive_charges_authorization_request if require_recursive_charges_authorization?
  end

  def send_connect_authorization_request
    Delayed::Job.enqueue(Jobs::Atono::DropPayAccountActivationJob.new(self),
                         queue: "drop_pay_account_activation", priority: 0)

    update(state: DropPayStatus::IN_ACTIVATION)
  end

  def send_recursive_charges_authorization_request
    Delayed::Job.enqueue(Jobs::Atono::DropPayAccountRecursiveChargeActivationJob.new(self),
                         queue: "drop_pay_account_activation", priority: 0)

    update(recursive_charges_authorization: RecursiveChargesAuthorization::REQUIRED)
  end

  def update_connection_status(connection_response)
    unless connection_response['id'] == connection_id
      KolmeLogger.error("connection response id #{connection_response['id']} does not match drop pay account #{id} connection request, dealer: #{dealer_name}")
      return
    end

    target_status = case connection_response['status']
                    when 'GRANTED'
                      DropPayStatus::ACTIVE
                    when 'WAITING'
                      DropPayStatus::IN_ACTIVATION
                    when 'REFUSED', 'REVOKED', 'CANCELLED', 'EXPIRED'
                      DropPayStatus::DEACTIVATED
                    else
                      KolmeLogger.error("unknown status for drop pay account #{id}, dealer: #{dealer.name}")
                      nil
                    end
    return unless target_status
    return if state == target_status

    update(
      state: target_status
    )

    archive_alert
  end

  def update_recursive_charges_authorization_status(pull_response)
    unless pull_response['id'] == pull_id
      KolmeLogger.error("pull response id #{pull_response['id']} does not match drop pay account #{id} pull request, dealer: #{dealer_name}")
      return
    end

    target_status = case pull_response['status']
                    when 'DRAFT', 'READY'
                      RecursiveChargesAuthorization::REQUIRED
                    when 'SCHEDULED', 'RUNNING'
                      RecursiveChargesAuthorization::GRANTED
                    when 'REFUSED'
                      RecursiveChargesAuthorization::DENIED
                    when 'CANCELLED', 'REVOKED'
                      RecursiveChargesAuthorization::REVOKED
                    else
                      RecursiveChargesAuthorization::UNKNOWN
                    end

    return if recursive_charges_authorization == target_status

    update(
      recursive_charges_authorization: target_status,
      authorization_updated_at:        Time.now
    )
  end

  private

  def archive_alert
    alerts.visible
          .joins(:alert_message)
          .where(alert_messages: { message_type: Alerts::DropPayAccountAlertMessage::DROP_PAY_ACCOUNT_CONFIRM_CONNECTION[:type] })
          .each(&:archive!)
  end

  def have_connection_data?
    bank_code.present? && connection_response.present?
  end

  def initialize_connection_data
    return unless rid_iban.present?

    update(
      state:                           DropPayStatus::NOT_ENABLED,
      opened_at:                       nil,
      closed_at:                       nil,
      authorization_updated_at:        nil,
      recursive_charges_authorization: RecursiveChargesAuthorization::NOT_REQUIRED,
      connection_response:             nil,
      bank_code:                       nil,
      pull_id:                         nil
    )

    archive_alert
    send_authorizations_request if enabled?
  end

  def require_date_state?
    state_active?
  end

  def require_intialization_data?
    saved_change_to_rid_iban? || saved_change_to_mobile_phone? ||
      (saved_change_to_enabled? && enabled? && state_not_enabled?)
  end

  def require_valid_iban_state?
    enabled?
  end

  def set_dates_on_status_change
    if state == DropPayStatus::ACTIVE
      self.opened_at = Time.now
    end

    if state == DropPayStatus::DEACTIVATED
      self.closed_at = Time.now
    end
  end

  def system?
    return false unless DropPayAccount.system.present?

    id == DropPayAccount.system.id
  end
end
