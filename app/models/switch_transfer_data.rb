class SwitchTransferData < ApplicationRecord
  belongs_to :energy_supply_detail
  has_one :dealer, through: :energy_supply_detail

  # validates :acquisition_act_number, presence: true
  validates :act_date, presence: true
  # validates :administrative_municipality, presence: true
  # validates :cadastral_plot_number, presence: true
  # validates :cadastral_registration_method, presence: true
  # validates :cadastral_sheet, presence: true
  # validates :cadastral_sub_identifier, presence: true
  # validates :cadastral_unit_type, presence: true
  # validates :certificate_number, presence: true
  # validates :contract_registry_number, presence: true

  # validates :gas_self_reading_date, presence: true, if: :gas_contract_present?
  # validates :gas_self_reading_value, presence: true, numericality: { precision: 10, scale: 2 }, if: :gas_contract_present?

  validates :ownership_title, presence: true
  # validates :plot_type, presence: true
  validates :property_possession_date, presence: true
  # validates :registration_number, presence: true
  validates :request_start_date, presence: true
  # validates :tabular_system_plot_identifier, presence: true
  # validates :tax_agency_registration_date, presence: true
  # validates :tax_agency_registration_place, presence: true
  # validates :urban_section, presence: true

  def gas_contract_present?
    energy_supply_detail.energy_contract.energy_plans.where(plan_kind: 'gas').any?
  end
end
