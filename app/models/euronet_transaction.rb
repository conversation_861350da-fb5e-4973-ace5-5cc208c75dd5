class EuronetTransaction < ApplicationRecord
  OUT_OF_CREDIT = "out_of_credit".freeze
  CHECK_REQUEST = "check_request".freeze
  CONFIRM_REQUEST = "confirm_request".freeze

  CREATED = 0
  PROCESSING = 10
  SUCCESS = 20
  ERROR = 30

  # VALIDATIONS
  # RELATIONS
  belongs_to :recharge
  # SCOPES
  scope :check_request, -> { where(call_type: EuronetTransaction::CHECK_REQUEST) }
  scope :success,       -> { where(status: EuronetTransaction::SUCCESS) }
  # TRIGGERS
  before_create :generate_orig_id
  # OTHER

  delegate :cif?, to: :recharge, allow_nil: true

  def cif
    recharge.phone_number
  end

  def pin?
    recharge.present? ? recharge.pin? : false
  end

  def check_trid
    recharge.euronet_transactions.where(call_type: EuronetTransaction::CHECK_REQUEST).last.trid
  end

  def generate_orig_id
    if origid.nil?
      self.origid = Time.now.to_i + Random.rand(Time.now.to_i)
    end
  end

  def amount
    if recharge.recharge_size.offer_code == "INF1M"
      999
    elsif recharge.recharge_size.offer_code == "INF3M"
      2499
    else
      recharge.recharge_size.amount * 100
    end
  end

  def time
    timestamp = created_at.to_i.to_s
    if timestamp.size < 12
      timestamp.to_i.to_s << "00"
    else
      timestamp
    end
  end

  def srv
    if recharge.pin?
      recharge.recharge_size.pin_srv
    else
      recharge.recharge_size.paymat_operator.euronet_srv
    end
  end

  def usercode
    EuronetEncryptor.new(recharge.phone_number, time).encrypt
  end

  def sec
    EuronetEncryptor.new(recharge.phone_number, time).encrypt_sec
  end

  def cab
    "X#{recharge.dealer.id.to_s(16).upcase.rjust(4, "0")}"
  end

  def offer_code
    if recharge.pin?
      recharge.recharge_size.offer_code
    end
  end

  def expiration_date
    Date.parse(expiration)
  end
end
