class SapPaymentKind < ApplicationRecord
  enum :kind, { customer: 0, supplier: 1 }

  validates :position, presence: true
  validates_uniqueness_of :position, if: Proc.new { SapPaymentKind.send(kind).where.not(id: id).where(position: position).any? }

  audited

  def self.for_select(kind)
    where(kind: kind).pluck(:code, :description, :id).map { |code, description, id| ["#{code} - #{description}", id] }
  end

  def self.for_affiliation_customer
    customer.find_by(code: Dealer::DEFAULT_SAP_CUSTOMER_PAYMENT_KIND)
  end

  def kind_label
    SapPaymentKind.human_attribute_name("kind/#{kind}")
  end

  def to_s
    "#{code} - #{description}"
  end
end
