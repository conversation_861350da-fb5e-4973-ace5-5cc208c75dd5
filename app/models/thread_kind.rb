class ThreadKind < ApplicationRecord
  attr_accessible :class_name, :description,
                  :enabled, :enabled_for_pa_in_progress, :enabled_collection,
                  :internal_users_only, :name, :operator_ids, :operators, :pdf_required

  DEALER_KIND_MAPPING = {
    'md_very_enabled'             => 'md_very',
    'visible_tls'                 => 'operator_tls',
    'franchising_enabled'         => 'franchising',
    'kolme_master_enabled'        => 'kolme_master',
    'generic_partner_enabled'     => 'generic',
    'pure_installer_fwa_enabled'  => 'fwa_installer_only',
    'kolme_agent_partner_enabled' => 'kolme_agent',
    'gallery_gd_partner_enabled'  => 'gallery_gd'
  }.freeze

  PA_FISSO_REJECTED = %w(AstSimReplacement).freeze
  PA_COMPLETE_REJECTED = %w(AstSimReplacement).freeze
  ONLY_PA_IN_PROGRESS = %w(AstEditPhoneActivation AstAdditionalDocumentsUpload).freeze
  REQUIRE_PHONE_ACTIVATION = %w(AstUnsolvedPaymentProof).freeze
  RETURNABLE_REJECTED = %w(AstReturnRequest).freeze
  SENSIBLE_ASSOCIATION_FIELDS = %w(franchising_enabled gallery_gd_partner_enabled generic_partner_enabled kolme_agent_partner_enabled kolme_master_enabled md_very_enabled pure_installer_fwa_enabled visible_tls).freeze
  COMMON_SENSIBLE_FIELDS = %w(enabled enabled_collection internal_users_only).freeze

  has_many :after_sale_threads
  has_and_belongs_to_many :operators

  has_many :dealer_thread_kinds, dependent: :destroy

  validates :sort_index, presence: true
  validates_uniqueness_of :sort_index, case_sensitive: true
  validates_numericality_of :sort_index

  default_scope { order(sort_index: :asc) }

  after_save :update_dealer_associations

  scope :enabled, -> { where(enabled: true) }
  scope :enabled_for_pa_in_progress, -> { where(enabled_for_pa_in_progress: true) }
  scope :enabled_collection, -> { where(enabled_collection: true) }
  scope :for_dealer, -> { where(internal_users_only: false) }
  scope :for_user, Proc.new { |role| role == 'dealer' ? for_dealer : all }
  scope :filter_by_operators, ->(operator_ids) { left_outer_joins(:operators).where(operators: { id: [operator_ids, nil].flatten }).distinct }
  scope :filter_by_dealer_kind, ->(dealer_kind) do
                                  case dealer_kind
                                  when 'md_very'
                                    md_very
                                  when 'operator_tls'
                                    where(visible_tls: true)
                                  when 'franchising'
                                    where(franchising_enabled: true)
                                  when 'kolme_master'
                                    where(kolme_master_enabled: true)
                                  when 'generic'
                                    where(generic_partner_enabled: true)
                                  when 'fwa_installer_only'
                                    where(pure_installer_fwa_enabled: true)
                                  when 'kolme_agent'
                                    where(kolme_agent_partner_enabled: true)
                                  when 'gallery_gd'
                                    where(gallery_gd_partner_enabled: true)
                                  else
                                    where(md_very_enabled: false, visible_tls: false, franchising_enabled: false,
                                          generic_partner_enabled: false, pure_installer_fwa_enabled: false,
                                          kolme_agent_partner_enabled: false, gallery_gd_partner_enabled: false)
                                  end
                                end
  scope :md_very, -> { where(md_very_enabled: true) }

  def partial_name
    if class_name.in? %w(AstProvisioning AstTlsProvisioning AstTlsDoa AstTlsRecess AstTlsAnomalyDelivery AstAdditionalInformation)
      'new_ast_provisioning'
    else
      "new_#{class_name.underscore}"
    end
  end

  def pdf_partial_name
    class_name.underscore
  end

  def new_threadable
    class_name.constantize.new
  end

  def pdf_factory
    "PdfFactories::#{class_name}PdfFactory".constantize.new
  rescue NameError
    raise "No PDF factory available for #{class_name}, make sure you defined a #{class_name}PdfFactory class"
  end

  def self.update_thread_kinds_associations(dealer_id, dealer_kind)
    dealer = Dealer.find(dealer_id)

    dealer.dealer_thread_kinds.destroy_all

    key = DEALER_KIND_MAPPING.key(dealer_kind)

    return unless key

    thread_kinds = ThreadKind.where(key => true)
    associations = thread_kinds.map do |thread_kind|
      {
        thread_kind_id: thread_kind.id,
        dealer_id:      dealer_id,
        dealer_kind:    dealer_kind
      }
    end

    DealerThreadKind.import(associations)
  end

  private

  def create_dealer_associations(dealer_kind)
    dealer_ids = Dealer.where(kind: dealer_kind).ids
    associations = dealer_ids.map do |dealer_id|
      {
        thread_kind_id: id,
        dealer_id:      dealer_id,
        dealer_kind:    dealer_kind
      }
    end

    DealerThreadKind.import(associations)
  end

  def update_dealer_associations
    clear_all_dealer_cache_keys if (saved_changes.keys & COMMON_SENSIBLE_FIELDS).any?

    changed_association_fields = saved_changes.keys & SENSIBLE_ASSOCIATION_FIELDS
    return if changed_association_fields.empty?

    changed_association_fields.each do |field|
      update_dealer_kind_associations(field)
    end
  end

  def update_dealer_kind_associations(field)
    dealer_kind = DEALER_KIND_MAPPING[field]
    dealer_thread_kinds.where(dealer_kind: dealer_kind).destroy_all

    create_dealer_associations(dealer_kind) if send(field)

    CacheKey.joins("INNER JOIN dealers ON dealers.id = cache_keys.cacheable_id AND cache_keys.cacheable_type = 'Dealer'")
      .where(dealers: { kind: dealer_kind })
      .update_all(new_ast: nil)
  end

  def clear_all_dealer_cache_keys
    CacheKey.joins("INNER JOIN dealers ON dealers.id = cache_keys.cacheable_id AND cache_keys.cacheable_type = 'Dealer'")
      .update_all(new_ast: nil)
  end
end
