class SapApiCall < ApplicationRecord
  audited on: :update

  belongs_to :origin, polymorphic: true
  belongs_to :relaunching_user, class_name: 'User', optional: true

  ransacker :origin_order_id do
    Arel.sql(
      <<~SQL.squish
        CASE
          WHEN origin_type = 'Order' THEN origin_id
          WHEN origin_type = 'Fulfill' THEN (
            SELECT order_id FROM fulfills WHERE fulfills.id = sap_api_calls.origin_id
          )
          ELSE NULL
        END
      SQL
    )
  end

  scope :date_is, ->(date) do
    where('DATE(created_at) = ?', Date.parse(date)) if date.present?
  end

  scope :contains, ->(what) do
    where('((request like :what) or (response like :what))', what: "%#{what}%") if what.present?
  end

  scope :success, -> { where(success: true) }

  ransacker :path_and_kind do
    Arel.sql('CONCAT(path, COALESCE(kind, ""))')
  end

  def failure_subject
    if path == Settings.sap.api_endpoints.fulfill_odv
      model_name = origin.class.model_name == 'Fulfill' ? 'Ordine' : origin.class.model_name.human

      "Errore Evasione #{model_name} su SAP"
    else
      "Errore #{I18n.t("values.models.sap_api_call.http_method.#{http_method}")} #{origin.class.model_name.human} su SAP"
    end
  end

  def is_create?
    http_method == 'post'
  end

  def is_update?
    http_method == 'patch'
  end

  def origin_obj
    path == Settings.sap.api_endpoints.fulfill_odv ? MockFulfill.new(model: origin) : origin
  end

  def runnable?
    origin.present? && path.present? && http_method.present?
  end

  def relaunchable?
    runnable? && !success? && relaunched_at.nil?
  end

  class << self
    def paths_for_select
      distinct.pluck(:path).collect { |p| [p] }
        .append(["#{Settings.sap.api_endpoints.dealer} #{SapApiCall.human_attribute_name("kind/#{DealerSap::ADD_ES_ROLE_API_CALL}", default: '')}",
                 "#{Settings.sap.api_endpoints.dealer}add_es_role"])
        .sort
    end

    def origin_types
      distinct.where('origin_type is not null').pluck(:origin_type)
    end

    def ransackable_attributes(_auth_object = nil)
      %w[id origin_type origin_id origin_order_id path success request response path_and_kind created_at]
    end

    def ransackable_associations(_auth_object = nil)
      []
    end

    def ransackable_scopes(_auth_object = nil)
      %w[date_is contains]
    end
  end
end
