class AstEasyPayReplacement < ApplicationRecord
  include GenericAst
  mount_uploaders :attachments, AstEasyPayReplacementUploader

  serialize :attachments, Array

  PA_PERSONA_FISICA_REJECTED = false

  has_one :after_sale_thread, as: :threadable, dependent: :destroy

  validates_presence_of :replacement_number,
                        :originator_name,
                        :assignee_name,
                        :notes
  validates_presence_of :attachments, message: 'È necessario allegare un documento'

  validates :replacement_number, numericality: true

  def require_energy_contract?
    false
  end

  def require_phone_activation?
    false
  end
end
