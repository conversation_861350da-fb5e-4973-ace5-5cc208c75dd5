# frozen_string_literal: true

# NOTE: this is strictly related to PhoneActivations, it could be
# renamed to PhoneActivationPaymentMethod
class PaymentMethod < ApplicationRecord
  include Bost::LovBehavior
  include Bost::Uuid
  include PaymentMethodSapPayloadBuilder

  BANK_TRANSFER         = 'BT'
  CREDIT                = 'C'
  CREDIT_CARD           = 'CC'
  DROP_PAY              = 'DP'
  FIDO                  = 'F'
  SDD_PAYMENT_METHOD_ID = 5
  THIRTY_DAYS           = '30'

  has_many :offer_payment_methods
  has_many :offers, through: :offer_payment_methods
  has_many :dealer_payment_methods
  has_many :dealers, through: :dealer_payment_methods
  has_many :offer_instances,
           dependent: :destroy

  has_many :blocked_payment_methods

  scope :default, -> { where(default: true) }
  scope :for_offer, proc { |offer|
    if offer.present?
      joins(offer_instances: :offer).where(offers: { id: offer.id }).select('distinct payment_methods.*')
    end
  }

  scope :only_cc_and_sdd, -> { PaymentMethod.where(id: 2).or(PaymentMethod.where(id: 5)) }

  scope :enabled, -> { where(enabled: true) }
  scope :not_default, -> { where(default: false) }

  scope :enabled_or_current, proc { |current|
    t = PaymentMethod.arel_table
    conditions = t[:enabled].eq(true)
    conditions = conditions.or(t[:id].eq(current.id)) if current.present?
    where(conditions)
  }

  alias_attribute :activable, :enabled

  def visibility
    enabled? ? 'y' : 'n'
  end

  def credit_card?
    code == 'credit_card'
  end

  def rid?
    code == 'rid'
  end

  def postal?
    code == 'postal_slip'
  end

  def pinpad?
    !!(/\Apinpad/ =~ code)
  end

  def esistente?
    code == 'esistente'
  end

  def none?
    code == 'nessuno'
  end

  def none_or_postal?
    none? || postal?
  end

  def code_for_ngpos
    return 1 if credit_card?
    return 2 if rid?
    return 3 if postal?

    0
  end

  def description_for_ngpos
    return 'Carta di Credito' if credit_card?
    return 'Conto Corrente Bancario' if rid?

    ''
  end

  class << self
    def blank_valid?(offer = nil)
      offer.blank? || OfferInstance.where(offer_id: offer.id, payment_method_id: nil).exists?
    end

    def associate_all_default_payment_methods_to_all_dealers
      PaymentMethod.default.find_each do |payment_method|
        missing_dealer_ids = Dealer.pluck(:id) - payment_method.dealer_ids
        payment_method.dealers << Dealer.find(missing_dealer_ids)
      end
    end
  end
end
