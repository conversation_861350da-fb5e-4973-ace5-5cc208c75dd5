# coding: utf-8

class FinancingStructure < ApplicationRecord
  ACTIVATION_STATES = {
    yes: I18n.t("activerecord.attributes.financing_structure.active_states.yes"),
    no: I18n.t("activerecord.attributes.financing_structure.active_states.no"),
    from_product: I18n.t("activerecord.attributes.financing_structure.active_states.from_product")
  }

  has_many :financings

  default_scope { order("position asc") }
  scope :for_select, -> {}
end
