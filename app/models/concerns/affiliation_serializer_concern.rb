module AffiliationSerializerConcern
  extend ActiveSupport::Concern

  AFFILIATION_EXCLUDED_SERIALIZED_ATTRIBUTES                = %w(created_at updated_at id dealer_id creator_user_id agent_id sub_agent_id stored_serialized).freeze
  DEALER_EXCLUDED_SERIALIZED_ATTRIBUTES                     = %w(created_at updated_at id).freeze
  DEALER_CONTACT_EXCLUDED_SERIALIZED_ATTRIBUTES             = %w(created_at updated_at dealer_id id).freeze
  WAREHOUSE_EXCLUDED_SERIALIZED_ATTRIBUTES                  = %w(created_at updated_at dealer_id id).freeze
  WAREHOUSE_BRANDS_WAREHOUSE_EXCLUDED_SERIALIZED_ATTRIBUTES = %w(created_at updated_at warehouse_id id).freeze

  PRE_EXISTING_DEALER_FIELD                                 = 'pre_existing_dealer_id'.freeze
  DEALER_CONTACT_FIELD                                      = 'dealer_contact_id'.freeze
  WAREHOUSE_FIELD                                           = 'warehouse_id'.freeze

  def serialized_dealer
    dealer_obj                         = Dealer.new(stored_serialized['dealer_attributes']
                                               .merge(affiliation_attributes)
                                               .merge({ trustpro_user_id:          additional_data.dig('dealer_contact', 'email'),
                                                        trustpro_recovery_email:   additional_data.dig('dealer_contact', 'email'),
                                                        sap_customer_payment_kind: SapPaymentKind.for_affiliation_customer,
                                                        sap_customer_payment_term: SapPaymentTerm.for_affiliation_customer }))

    dealer_obj.warehouses.first.dealer = dealer_obj

    fill_nested_model_attributes(dealer_obj)

    dealer_obj
  end

  def to_nested_attributes
    send("#{self.class.name.underscore}_nested_attributes").reject { |_k, v| v.blank? }
  end

  private

  def affiliation_attributes
    source == 'channel_change' ? { fonte_del_lead: Dealer::CHANNEL_CHANGE_LEAD_SOURCE } : {}
  end

  def affiliation_nested_attributes
    attributes.except!(*Affiliation::AFFILIATION_EXCLUDED_SERIALIZED_ATTRIBUTES)
      .merge!(dealer.to_nested_attributes)
  end

  def dealer_nested_attributes
    normalize_swift_code

    { 'dealer_attributes' => attributes.reject { |_k, v| v.blank? }
      .except!(*DEALER_EXCLUDED_SERIALIZED_ATTRIBUTES)
      .merge!('dealer_contacts_attributes' => dealer_contacts.map(&:to_nested_attributes))
      .merge!('warehouses_attributes' => warehouses.map(&:to_nested_attributes)) }
  end

  def dealer_contact_nested_attributes
    attributes.except!(*DealerContact::DEALER_CONTACT_EXCLUDED_SERIALIZED_ATTRIBUTES)
  end

  def fill_nested_model_attributes(dealer_obj)
    dealer_obj.warehouses.first.agent_wind_tre_id     = attributes['agent_id']
    dealer_obj.warehouses.first.agent_products_id     = attributes['agent_id']
    dealer_obj.warehouses.first.sub_agent_wind_tre_id = attributes['sub_agent_id']
  end

  def warehouse_nested_attributes
    attributes.except!(*Warehouse::WAREHOUSE_EXCLUDED_SERIALIZED_ATTRIBUTES)
      .merge!('opening_hours' => serialized_opening_hours)
      .merge!('warehouse_brands_warehouses_attributes' => warehouse_brands_warehouses.map(&:to_nested_attributes))
  end

  def warehouse_brands_warehouse_nested_attributes
    attributes.except!(*Warehouse::WAREHOUSE_BRANDS_WAREHOUSE_EXCLUDED_SERIALIZED_ATTRIBUTES)
  end

  def serialized_opening_hours
    before_noon = affiliation_opening_hours['before_noon']
    after_noon = affiliation_opening_hours['after_noon']
    morning_hours = [before_noon['from'], before_noon['to']].join('-')
    afternoon_hours = [after_noon['from'], after_noon['to']].join('-')

    opening_hours = [morning_hours, afternoon_hours].join(' | ')
    opening_hours += ". Chiusura: #{weekly_closing_day}" unless weekly_closing_day == 'nessuno'

    "#{opening_hours}"
  end
end
