module DealerSap
  extend ActiveSupport::Concern

  include DealerSapPayload

  ADD_ES_ROLE_API_CALL              = 'add_es_role'.freeze
  DEFAULT_SAP_CUSTOMER_PAYMENT_KIND = 'B'.freeze
  DEFAULT_SAP_CUSTOMER_PAYMENT_TERM = 'NT00'.freeze
  DEFAULT_SAP_SUPPLIER_PAYMENT_KIND = 'T'.freeze
  DEFAULT_SAP_SUPPLIER_PAYMENT_TERM = 'FM00'.freeze
  MANDATORY_ATTRIBUTES_FOR_SAP      = %w(disabled billing_address_city_id billing_address_number billing_address_postalcode billing_address_via codice_fiscale cognome_di country_id email_amministrativa kind iban name nome_di partita_iva pec prejudicial ragione_sociale reverse_charge_enabled sap_customer_payment_kind_id sap_customer_payment_term_id sap_internal_bank_institute_id sap_supplier_payment_kind_id sap_supplier_payment_term_id sap_withholding_tax_id sdi_forwarder_code telefono_amministrativo).freeze
  SAP_API_PATH                      = 'IFI206_BusinessPartner'.freeze

  def export_to_sap
    Delayed::Job.enqueue(Jobs::Sap::DealerExporter.new(id), queue: 'sap_export')
  end

  def sap_export_callback(sap_api_call)
    store_sap_ids(sap_api_call)

    if reload.sap_request_kind == ADD_ES_ROLE_API_CALL
      Jobs::Sap::DealerExporter.new(id).perform
    end
  end

  def sap_failure_recipients
    [MailAddressProvider.it, MailAddressProvider.amministrazione, MailAddressProvider.affiliazioni]
  end

  def sap_json
    send("sap_#{sap_request_kind}_payload")
  end

  def sap_request_kind
    return 'create' if sap_ids.nil?

    if kolme_agent? && !sap_ids['to_PartnerFunction_PartnerFunctions']&.include?('ES')
      return ADD_ES_ROLE_API_CALL
    end

    'update'
  end

  def sap_to_export?
    return if disabled?
    return if operator_tls?

    any_mandatory_attributes_for_sap_changed?
  end

  private

  def any_mandatory_attributes_for_sap_changed?
    (saved_changes.keys & MANDATORY_ATTRIBUTES_FOR_SAP).any?
  end

  def store_sap_ids(sap_api_call)
    ids = {
      to_BusinessPartnerAddress_AddressID:       sap_api_call.response&.dig('d', 'to_BusinessPartnerAddress', 'results', 0, 'AddressID'),
      to_EmailAddress_1_AddressID:               sap_api_call.response&.dig('d', 'to_BusinessPartnerAddress', 'results', 0, 'to_EmailAddress', 'results')&.find { |d| d['OrdinalNumber'] == '1' }&.dig('AddressID'),
      to_EmailAddress_1_Person:                  sap_api_call.response&.dig('d', 'to_BusinessPartnerAddress', 'results', 0, 'to_EmailAddress', 'results')&.find { |d| d['OrdinalNumber'] == '1' }&.dig('Person'),
      to_EmailAddress_2_AddressID:               sap_api_call.response&.dig('d', 'to_BusinessPartnerAddress', 'results', 0, 'to_EmailAddress', 'results')&.find { |d| d['OrdinalNumber'] == '2' }&.dig('AddressID'),
      to_EmailAddress_2_Person:                  sap_api_call.response&.dig('d', 'to_BusinessPartnerAddress', 'results', 0, 'to_EmailAddress', 'results')&.find { |d| d['OrdinalNumber'] == '2' }&.dig('Person'),
      to_PhoneNumber_AddressID:                  sap_api_call.response&.dig('d', 'to_BusinessPartnerAddress', 'results', 0, 'to_PhoneNumber', 'results')&.find { |d| d['OrdinalNumber'] == '1' }&.dig('AddressID'),
      to_PhoneNumber_Person:                     sap_api_call.response&.dig('d', 'to_BusinessPartnerAddress', 'results', 0, 'to_PhoneNumber', 'results')&.find { |d| d['OrdinalNumber'] == '1' }&.dig('Person'),
      to_BusinessPartnerBank_BankIdentification: sap_api_call.response&.dig('d', 'to_BusinessPartnerBank', 'results', 0, 'BankIdentification'),
      to_PartnerFunction_PartnerFunctions:       sap_api_call.response&.dig('d', 'to_Customer', 'to_CustomerSalesArea', 'results', 0, 'to_PartnerFunction', 'results')&.map { |f| f['PartnerFunction'] }
    }.compact

    prev_sap_ids = sap_ids
    update_columns(sap_ids: (sap_ids || {}).merge(ids))

    Audited::Audit.create!(
      auditable:       self,
      action:          'update',
      audited_changes: { 'sap_ids' => [prev_sap_ids, sap_ids] }
    )
  end
end
