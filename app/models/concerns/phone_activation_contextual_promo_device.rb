module PhoneActivationContextualPromoDevice
  extend ActiveSupport::Concern

  def promo_device_params
    {
      dealer_id:    dealer_id,
      warehouse_id: warehouse_id,
      item_id:      Item.where(serial: promo_device_serial).last&.id,
      serial:       promo_device_serial,
      phone_number: sim_item.phone_number,
      state:        :activated,
      activated_at: Time.now
    }
  end
end