require 'uuidtools'

module AffiliatedModels
  extend ActiveSupport::Concern

  def activate_w3sp_commercial_state
    return if pre_existing_dealer?

    warehouse.update(w3sp_commercial_state: W3spCommercialState.find_by_code(W3spCommercialState::ACTIVE_CODE))
  end

  def create_dealer_documents
    required_affiliation_documents.each do |dealer_document|
      return unless dealer_document.document.attached?

      document = Document.find_or_create_by!(documentable_type: 'Dealer',
                                             kind:              'documents',
                                             documentable_id:   dealer.id,
                                             name:              "#{dealer.name}_#{Affiliation.human_attribute_name("form_kinds/#{dealer_document.document_kind}_filename", default: dealer_document.document_kind)}.pdf")

      dealer_document.document.download do |file|
        document.related_file = FileIO.new(file, document.name)
      end
      document.save
    end
  end

  def create_models
    update!(dealer: serialized_dealer)

    update_column(:stored_serialized, stored_serialized.merge({
                                                                "#{AffiliationSerializerConcern::DEALER_CONTACT_FIELD}": dealer_contacts.first.id,
                                                                "#{AffiliationSerializerConcern::WAREHOUSE_FIELD}":      warehouses.first.id
                                                              }))
  end

  def disable_models
    return if in_progress? || pre_existing_dealer?

    warehouse.update(w3sp_commercial_state: W3spCommercialState.find_by_code('Non W3SP'))
    dealer.update(disabled: true)
  end

  def enable_lok_me
    return if pre_existing_dealer?
    return unless additional_data_obj.dealer_operators == 'windtre_very_mobile'

    dealer_contact.access_flags << AccessFlag.find_by_name(AccessFlag::IMEI_RESERVATIONS)
  end

  def set_encoding_date
    update(encoding_date: Date.today)
  end

  def update_and_index_models
    dealer.update(disabled: false)

    if kolme_master_nexus?
      dealer.update_column(:kind, :kolme_master)
      warehouse.update(w3sp_commercial_state: W3spCommercialState.find_by_code(W3spCommercialState::KOLME_MASTER_ACTIVE_CODE))
    else
      warehouse.update(w3sp_commercial_state: W3spCommercialState.find_by_code('In Codifica W3SP'))
    end

    create_and_enable_dealer_contact
  end

  private

  def create_and_enable_dealer_contact
    user = User.new(role:     'dealer',
                    email:    dealer_contact_email,
                    password: Devise.friendly_token,
                    uuid:     UUIDTools::UUID.random_create.to_s)
    user.save(validate: false)

    dealer_contact.update_columns(user_id:              user.id,
                                  primary_contact:      true,
                                  legal_representative: true,
                                  portal_enabled:       true,
                                  cellphone_otp:        dealer_contact_cellphone_1)

    dealer_contact.access_flags << AccessFlag.default_flags_from_affiliation

    raw, enc = Devise.token_generator.generate(User, :reset_password_token)

    user.reset_password_token   = enc
    user.reset_password_sent_at = Time.now.utc
    user.save(validate: false)

    UserMailer.dealer_user_welcome(user.reload, raw, agent_emails: dealer.retrieve_wind_tre_agents_emails).deliver_now

    user.index
    dealer.index
    # dealer_contact.index
  end

  class FileIO < StringIO
    def initialize(stream, filename)
      super(stream)
      @original_filename = filename
    end

    attr_reader :original_filename
  end
end
