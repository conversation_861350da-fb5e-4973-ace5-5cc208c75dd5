module AccountingDocumentPaymentRequest
  extend ActiveSupport::Concern

  included do
    has_many :payment_requests, as: :payment_requestable
  end

  def customer_full_name
    ''
  end

  def payment_request_reference_id
    id
  end

  # def payment_request_kind
  #   "phone_activation"
  # end

  # def payment_requestable_type
  #   "PhoneActivation"
  # end

  # def can_have_payment_requests?
  #   return false if stored_plan.tariffa_tls?

  #   activated_at.blank? && ko_at.blank? && upfront&.positive? && payment_requests.none?
  # end
end
