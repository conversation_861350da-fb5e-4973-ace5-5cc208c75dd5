module PaymentMethodSapPayloadBuilder
  extend ActiveSupport::Concern
  included do
    def self.sap_code_for(payment_recap)
      return if payment_recap.include?(PaymentMethod::FIDO)

      return 'M' if payment_recap.join == PaymentMethod::CREDIT_CARD
      return 'B' if [PaymentMethod::DROP_PAY, PaymentMethod::THIRTY_DAYS, PaymentMethod::BANK_TRANSFER].include? payment_recap.join

      if payment_recap.include?(PaymentMethod::CREDIT)
        other_payment_method = payment_recap - [PaymentMethod::CREDIT]

        return 'M' if other_payment_method.join == PaymentMethod::CREDIT_CARD
        return 'B' if [PaymentMethod::DROP_PAY, PaymentMethod::THIRTY_DAYS, PaymentMethod::BANK_TRANSFER].include? other_payment_method.join
      end
    end
  end
end
