module PaymentRequestHipay
  def hipay_request_data(hipay_payment)
    return unless persisted?
    uuid = hipay_payment.uuid

    thx_id = hipay_payment.thx_id
    user   = hipay_payment.payment.user

    decline_url = Rails.application.routes.url_helpers.store_payments_hipay_other_url(action: :decline, uuid: uuid)
    accept_url  = Rails.application.routes.url_helpers.store_payments_hipay_accept_url(uuid: uuid)
    notify_url  = Rails.application.routes.url_helpers.store_payments_hipay_notify_url(uuid: uuid, protocol: 'https')
    {
      orderid:               thx_id,
      operation:             "Sale",
      description:           payment_request_hipay_description,
      currency:              "EUR",
      merchant_display_name: Settings.store.hipay.merchant_display_name,
      multi_use:             1,
      language:              "it_IT",
      template:              "basic-js",
      theme_code:            Settings.store.hipay.theme_code,
      payment_product_list:  selected_payment_method,
      amount:                amount,
      cid:                   dealer_id,
      email:                 user.email,
      firstname:             user.dealer_contact.first_name,
      lastname:              user.dealer_contact.last_name,
      streetaddress:         dealer.ws_address,
      city:                  dealer.billing_address_city_description,
      zipcode:               dealer.billing_address_postalcode,
      country:               dealer.country.code,
      shipto_firstname:      dealer.primary_contact&.first_name,
      shipto_lastname:       dealer.primary_contact&.last_name, 
      shipto_streetaddress:  dealer.ws_address,
      shipto_city:           dealer.billing_address_city_description,
      shipto_zipcode:        dealer.billing_address_postalcode,
      shipto_country:        dealer.country.code,
      accept_url:            accept_url,
      decline_url:           decline_url,
      notify_url:            notify_url,
      pending_url:           decline_url,
      exception_url:         decline_url,
      cancel_url:            decline_url,
      device_channel:        2
    }
  end

  def hipay_attributes
    {
      thx_id:             "P-#{id}-#{SecureRandom.hex(6)}",
      amount:             amount,
      transaction_fees:   0,
      credit_card_method: selected_payment_method
    }
  end

  def payment_request_hipay_description
    desc = "Pagamento ##{id}"
    desc += " (#{payment_requestable.model_name.human} ##{payment_requestable.payment_request_reference_id})" if payment_requestable.present?
    desc + " - #{dealer.name}"
  end

  def valid_payment_response?(payment_response)
    true
  end
end
