module FulfillSapPayloadBuilder
  extend ActiveSupport::Concern
  included do
    alias_method :sap_fulfill_json, :sap_json
  end

  def sap_json
    payload = { "d": { "DeliveryDocumentBySupplier": shipment&.padded_id || '' } }
    if shipment
      shipment_data = {}
      shipment_data[:BillOfLading] = shipment&.ddt_number if shipment&.ddt_number.present?
      payload[:d].merge!(shipment_data)
    end

    payload[:d].merge!({
                         "to_DeliveryDocumentItem": {
                           "results": items_json + other_rows_json
                         }
                       })
    payload
  end

  private

  def items_json
    items_to_export = if dhl?
                        item_orders.where.not(sent_at: nil)
                      else
                        item_orders
                      end
    items_to_export.group_by(&:shopping_cart_item_id).map do |shopping_cart_item_id, item_orders|
      next unless shopping_cart_item_id.present?
      next unless item_orders.present?

      el = {
        "UnderlyingPurchaseOrderItem": shopping_cart_item_id.to_s[-6..-1],
        "PurchaseOrderByCustomer":     "O#{order.id}",
        "ActualDeliveryQuantity":      "#{item_orders.size}",
        "DeliveryQuantityUnit":        'PC'
      }
      # get serial from the item_orders
      item_text = item_orders.map(&:serial).compact.join(', ')
      el[:ItemText] = item_text if item_text.present?
      el
    end
  end

  def other_rows_json
    other_rows = []
    if with_shipping_costs?
      other_rows << {
        "UnderlyingPurchaseOrderItem": '10',
        "PurchaseOrderByCustomer":     "O#{order.id}",
        "ActualDeliveryQuantity":      '1',
        "DeliveryQuantityUnit":        'PC'
      }
    end
    other_rows
  end
end
