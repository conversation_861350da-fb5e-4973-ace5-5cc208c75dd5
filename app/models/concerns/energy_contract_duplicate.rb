module EnergyContractDuplicate
  extend ActiveSupport::Concern

  def attributes_for_duplication
    {
      customer_kind_group:       customer.business? ? CustomerKind::BUSINESS_LABEL : CustomerKind::CONSUMER_LABEL,
      dealer_id:                 dealer_id,
      parent_energy_contract_id: id,
      user_id:                   user_id
    }
  end

  def attributes_for_payment_method_detail_duplication
    {
      rid_iban:                            energy_contract_payment.iban,
      different_accountholder:             energy_contract_payment.different_owner,
      different_accountholder_birth_date:  energy_contract_payment.different_owner_birth_date,
      different_accountholder_birth_place: energy_contract_payment.different_owner_birth_place,
      different_accountholder_cf_or_vat:   energy_contract_payment.different_owner_cf,
      different_accountholder_first_name:  energy_contract_payment.different_owner_first_name,
      different_accountholder_last_name:   energy_contract_payment.different_owner_last_name
    }
  end
end
