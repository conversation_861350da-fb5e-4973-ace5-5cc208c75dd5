module NumberPortabilityDetailNgposPayload
  extend ActiveSupport::Concern

  def ngpos_payload
    data = {
             "mobilePhone":       "#{phone_number}",
             "dataNumber":        nil,
             "prepaid":           true,
             "fax":               nil,
             "fiscalcode":        cf_for_ngpos,
             "vatCode":           vat_for_ngpos,
             "portabilityDate":   nil,
             "serialNumberSim":   "#{sim_serial&.truncate(19, omission: '')}",
             "creditTransfer":    credit_transfer,
             "theftLoss":         sim_lost_or_stolen,
             "operator":          "#{source_operator.operator_system_name}, #{source_operator.very_system_name}",
             "otpCode":           "#{otp}",
             "virtualSim":        phone_activation.e_sim?,
             "hasCustomerHolder": different_owner
           }

    data.merge!({"customerHolder": customer_holder}) if different_owner

    data
  end

  private

  def company_data
    {
      "companyName": owner_company_name,
      "legalStatus": company_kynd_for_ngpos,
      "vat":         owner_vat
    }
  end

  def customer_holder
    data = {
             "customerType":         customer_kind_for_ngpos,
             "#{customer_info_key}": customer_info,
             "documentInfo":         document_info
           }

    data.merge!({"companyData": company_data}) if different_owner_persona_giuridica?

    data
  end

  def customer_info
    {
      "name":                    owner_first_name,
      "surname":                 owner_last_name,
      "fiscalCode":              owner_cf,
      "vat":                     owner_vat,
      "gender":                  gender_for_ngpos,
      "birthDate":               owner_birth_date.strftime('%FT%T.%LZ'),
      "birthPlace":              owner_birth_place,
      "birthProvince":           owner_birth_province&.code,
      "addressUrbanDesignation": location_address_street_type&.value,
      "addressName":             location_address,
      "addressNumber":           location_number,
      "addressZipCode":          location_zip,
      "addressCity":             location_city,
      "addressProvince":         location_province&.code,
    }
  end

  def customer_info_key
    different_owner_persona_giuridica? ? 'legalRepresentative' : 'personalDataInfo'
  end

  def document_info
    {
      "type":       "#{owner_identity_document_kind&.ngpos_type}",
      "number":     owner_document_number,
      "released":   owner_document_date.strftime('%FT%T.%LZ'),
      "releasedBy": owner_document_issued_by
    }
  end
end
