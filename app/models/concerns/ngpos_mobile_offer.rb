module NgposMobileOffer
  REJECTED_SYSTEM_OPERATOR_NAMES = 'Reload Plus'

  def mobile_offer
    { offer:
             {
               "portableNumberPhone":          "#{number_portability_detail&.phone_number}",
               "phoneNumberAutomaticRecharge": phone_number_automatic_recharge,
               "codeIMEI":                     offer_code_imei,
               "paymentMethod":                payment_method.code_for_ngpos,
               "mobileOffers":                 mobile_offers,
               "additionalServices":           additional_services,
               "imeiVerifyParam":              imei_verify_params,
               "category":                     plan.product_category_description
             } }
  end

  private

  def phone_number_automatic_recharge
    return unless automatic_recharge?

    if paper_signature?
      customer.mobile_phone
    elsif digital_signature?
      signature_obj.customer&.number
    end
  end

  def additional_services
    return [] if operator.very_mobile?

    options.additional_services.pluck(:system_name_operator)
  end

  def determine_discount_value
    return "€ #{number_with_precision(monthly_discount)}" if payment_method.credit_card? || monthly_discount_sdd.blank?

    "€ #{number_with_precision(monthly_discount_sdd)}" if payment_method.rid? && monthly_discount_sdd.present?
  end

  def cost_matrix_service
    @cost_matrix_service ||= CostMatrixService.new(phone_activation: self)
  end

  def monthly_discount_sdd
    cost_matrix_service.cost_matrix.monthly_discount_sdd
  end

  def monthly_discount
    cost_matrix_service.cost_matrix.monthly_discount
  end

  def imei_verify_params
    return {} if operator.very_mobile?
    return {} unless product.present?

    discount_value = determine_discount_value

    {
      "MDP/Fin":       "#{payment_method.description_for_ngpos}",
      "Anticipo":      upfront_for_ngpos,
      "Durata":        length.to_s,
      "Costo Totale":  public_price_for_ngpos,
      "Reload Plus":   reload_plus? ? 'Si' : 'No',
      "Importo Rata":  importo_rata_ngpos,
      "Sconto":        discount_value,
      "Sconto totale": discount_value
    }
  end

  def mobile_offers
    data = [
      {
        "name":        plan.system_name_operator.split('|').map(&:strip),
        "options":     plan_gb_for_ngpos,
        "subTitle":    plan.subtitle_system_name_operator,
        "useSubTitle": plan.check_subtitle_system_name_operator
      }
    ]

    options.for_robot.where.not(system_name_operator: [REJECTED_SYSTEM_OPERATOR_NAMES]).each do |option|
      data << { "name": option.system_name_operator.split('|').map(&:strip) }
    end

    data
  end
end
