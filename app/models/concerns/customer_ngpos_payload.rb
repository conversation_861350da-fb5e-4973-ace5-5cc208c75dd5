module CustomerNgposPayload
  extend ActiveSupport::Concern

  def fiscal_code_for_ngpos(include_vat = false)
    return id_doc_number if turista?
    return cf_or_vat if include_vat

    cf
  end

  def ngpos_payload
    data = {
      "customerType":  customer_kind_for_ngpos,
      data_info.to_sym => personal_info,
      "contactInfo":   {
        "phoneNumber":       "#{mobile_phone}",
        "secondPhoneNumber": '',
        "email":             "#{email}",
      },
      "documentInfo":  document_info
    }

    data.merge!(company_info) if persona_giuridica?

    data
  end

  def minor_ngpos_payload
    {
      "personalDataInfoUnderAge": personal_info,
      "documentInfoUnderAge":     document_info
    }
  end

  private

  def city_for_ngpos
    return headquarters_city&.description if persona_giuridica?

    city&.description
  end

  def company_info
    {
      "companyData": {
        "companyName": company_name,
        "legalStatus": I18n.t("ngpos.fields.customer.#{company_kind.parameterize.underscore}"),
        "vat":         vat
      }
    }
  end

  def data_info
    return 'legalRepresentative' if persona_giuridica?

    'personalDataInfo'
  end

  def document_info
    {
      "type":         (identity_document_kind&.ngpos_type || ''),
      "releasedBy":   "",
      "nationality":  "#{id_doc_country.posng_value}",
      "released":     document_release_date,
      "number":       "#{id_doc_number}",
      "municipality": "",
      "province":     ""
    }
  end

  def document_release_date
    return unless id_doc_date.present?

    "#{id_doc_date.strftime('%FT%T.%LZ')}"
  end

  def province_for_ngpos
    return headquarters_province&.code if persona_giuridica?

    province&.code
  end

  def street_type_for_ngpos
    return headquarters_address_street_type&.ps if persona_giuridica?

    address_street_type&.ps
  end

  def nation_for_ngpos
    'ITALIA'
  end

  def personal_info
    { "surname":                 "#{last_name}",
      "name":                    "#{first_name}",
      "birthDate":               "#{birth_date.strftime('%FT%T.%LZ')}",
      "fiscalCode":              fiscal_code_for_ngpos,
      "vat":                     vat,
      "birthPlace":              birth_place_for_ngpos,
      "birthProvince":           "#{birth_province.code}",
      "birthNation":             "#{birth_country.posng_value}",
      "gender":                  gender_for_ngpos,
      "addressUrbanDesignation": street_type_for_ngpos,
      "addressName":             address_street_name.presence || headquarters_address_street_name,
      "addressNumber":           number.presence || headquarters_number,
      "addressPlace":            "",
      "addressCity":             city_for_ngpos,
      "addressZipCode":          zip.presence || headquarters_zip,
      "addressProvince":         province_for_ngpos,
      "addressNation":           nation_for_ngpos,
      "tourist":                 turista?
    }
  end
end