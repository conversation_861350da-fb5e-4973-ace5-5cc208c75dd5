module DealerSunspotSearch
  extend ActiveSupport::Concern

  def add_md_very_filters_to(sunspot_search)
    return sunspot_search unless md_very?

    if mandatory_mnp?
      sunspot_search.build do
        with :mandatory_mnp, true
      end
    end

    sunspot_search.build do
      with :automatic_recharge_mandatory_very, false
    end
  end

  def add_dealer_kind_visiblity_filters_to(sunspot_search)
    sunspot_search.build do
      with :dealer_kind_visibility, kind
    end
  end
end
