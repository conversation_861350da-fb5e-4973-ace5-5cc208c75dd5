module Transformer
  extend ActiveSupport::Concern

  included do
    cattr_accessor :transformations

    before_validation :perform_transformations
  end

  def perform_transformations
    self.class.transformations.each do |operation, fields|
      send("do_transform_#{operation}", fields)
    end
  end

  def do_transform_uppercase(fields)
    fields.each do |field|
      current_value = send(field)
      send("#{field}=", current_value.upcase) if current_value
    end
  end

  def do_transform_titleize(fields)
    fields.each do |field|
      current_value = send(field)
      send("#{field}=", current_value.titleize) if current_value
    end
  end

  module ClassMethods
    def transform(operation, *fields)
      self.transformations ||= {}
      self.transformations[operation.to_sym] ||= []
      self.transformations[operation.to_sym] << fields.to_a
      self.transformations[operation.to_sym].flatten!
    end
  end
end
