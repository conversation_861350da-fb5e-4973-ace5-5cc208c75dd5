module PhoneActivationSapPayloadBuilder
  extend ActiveSupport::Concern

  def sap_json
    payload = {
      "d": {
        "SalesOrderType":                 'TA',
        "SoldToParty":                    "K#{dealer_id}",
        "SalesOrganization":              '2310',
        "DistributionChannel":            '10',
        "OrganizationDivision":           '01',
        "AccountingDocExternalReference": "A#{id}",
        "PurchaseOrderByCustomer":        "A#{id}",
        "to_Text":                        {
          "results": [
                       {
                         "Language":   'IT',
                         "LongTextID": 'TX06',
                         "LongText":   sap_long_text
                       }
                     ]

        },
        "to_Item":                        {
          "results": [item_sap_json]
        }
      }
    }

    if warehouse.agent_wind_tre.present? && warehouse.agent_wind_tre.sap_code.present?
      payload[:d].merge!({ "to_Partner": {
                           "results": [to_partner_json]
                         } })
    end

    payload
  end

  def sap_fulfill_json
    shipment_operation = operation_outcomes.where(operation_id: OperationConstants::SPEDIZIONE_EFFETTUATA_TRACKING, status: 'ok').last&.shipment
    payload = { "d": { "DeliveryDocumentBySupplier": shipment_operation&.padded_id || '' } }
    if shipment_operation.present?
      shipment_data = {}
      shipment_data[:BillOfLading] = shipment_operation.ddt_number if shipment_operation.ddt_number.present?
      payload[:d].merge!(shipment_data)
    end

    payload[:d].merge!({
                         "to_DeliveryDocumentItem": {
                           "results": [
                                        {
                                          "UnderlyingPurchaseOrderItem": '10',
                                          "PurchaseOrderByCustomer":     "A#{id}",
                                          "ActualDeliveryQuantity":      '1',
                                          "DeliveryQuantityUnit":        'PC',
                                          "ItemText":                    [model_long_text, product_item.serial].join(' - ')
                                        }
                                      ]
                         }
                       })
    payload
  end

  def sap_sendable?
    !product&.consignment && product_item&.system_owner_warehouse?
  end

  def sap_fulfill_model
    MockFulfill.new(model: self)
  end

  private

  def agent_w3_json
    {
      "PartnerFunction": 'ES',
      "Customer":        "#{warehouse.agent_wind_tre&.sap_code}"
    }
  end

  def item_sap_json
    {
      "Material":                    product.code,
      "RequestedQuantity":           '1',
      "ProductionPlant":             '2310',
      "StorageLocation":             '2310',
      "UnderlyingPurchaseOrderItem": '10',
      "RequestedQuantityISOUnit":    'PCE',
      "SalesOrderItemCategory":      'TAN',
      "to_PricingElement":           {
        "results": [
                     {
                       "ConditionType":            'PPR0',
                       "ConditionRateValue":       format('%.2f', product.dealer_price_vat_excluded_b).to_s,
                       "ConditionCurrency":        'EUR',
                       "ConditionQuantity":        '1',
                       "ConditionQuantityISOUnit": 'PCE'
                     }
                   ]
      },
      "to_Text":                     {
        "results": [
                     {
                       "SalesOrderItem": '10',
                       "LongTextID":     'TX16',
                       "Language":       'IT',
                       "LongText":       "#{model_long_text}"
                     }
                   ]
      }
    }
  end

  def model_long_text
    "Attivazione #{hex_id} - #{customer.full_name}"
  end

  def to_partner_json
    agent_w3_json
  end

  def sap_long_text
    warehouse.sap_address.gsub('\\n', "\n")
  end
end
