module EncryptedUtils
  extend ActiveSupport::Concern

  def decrypt_field(field, value, iv)
    self.class.attr_encrypted_decrypt(field, value, iv: Base64.decode64(iv))
  end

  def real_encrypted_changes(model = self)
    attr_encrypted = Object.const_get(model.class.name).attr_encrypted_encrypted_attributes.with_indifferent_access

    unchanged = attr_encrypted.map do |attr, _change|
      if !attr.in?(model.changes.keys) ||
          marshaled_value(model, attr, attr_encrypted) == model.changes[attr].first
        attr
      end
    end

    model.changes
      .except(*unchanged.map { |field| "encrypted_#{field}" })
      .except(*unchanged.map { |field| "encrypted_#{field}_iv" })
  end

  private

  def marshaled_value(model, attr, attr_encrypted)
    marshal = attr_encrypted[attr]

    if marshal[:marshaler] == Marshal || !model.changes[attr].second.is_a?(String)
      return model.changes[attr].second
    end

    marshal[:marshaler].send(marshal[:load_method], model.changes[attr].second)
  end
end
