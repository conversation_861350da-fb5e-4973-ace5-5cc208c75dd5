module WindNgposUploadDocuments
  extend ActiveSupport::Concern

  def add_documents(app_upload_context, gql_context, uploaded_documents)
    uploaded_documents.each do |document|
      document_name = Document.name_for(document.document_type, self, app_upload_context)

      file_contents   = document.files.map { |file| Base64.decode64(file) }.reject(&:blank?)
      file_extensions = [document.file_extension || 'png']

      handler_params = {
        context:           app_upload_context,
        documentable_id:   id,
        documentable_type: 'wind_ngpos_activations',
      }

      documents_handler = Documents::DocumentsHandler.new(handler_params)

      create_tmp_response, uploaded_file_names = Document.create_tmp_files(document_name,
                                                                           document.document_type,
                                                                           file_contents,
                                                                           self,
                                                                           app_upload_context,
                                                                           file_extensions)

      if create_tmp_response
        Util::PdfHandler.new.check_pdf_format(files: create_tmp_response)
      else
        raise StandardError.new 'Hai caricato dei file non supportati'
      end

      documents_handler.prepare_documents

      app_version       = gql_context[:app_version] || ''
      documents_handler.create_documents(
        current_user_id:                 gql_context[:current_user].id,
        skip_operation_outcome_creation: true,
        app_version:                     app_version,
        virtual:                         document.virtual,
        kind:                            document.document_type,
        content:                         document.content_kind.map(&:underscore).join('|')
      )
    end
  end
end
