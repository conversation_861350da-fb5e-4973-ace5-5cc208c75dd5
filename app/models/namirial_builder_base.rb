module NamirialBuilderBase
  extend self

  def authorization_headers(user_login_name, organization_key)
    raise NotImplementedError, "This #{self.class} cannot respond to:"
  end

  def build_namirial_delete_envelope_payload(envelope_id)
    raise NotImplementedError, "This #{self.class} cannot respond to:"
  end

  def build_namirial_prepare_payload(ssp_file_id)
    raise NotImplementedError, "This #{self.class} cannot respond to:"
  end

  def build_namirial_send_payload(ssp_file_id, prepare_body_json)
    raise NotImplementedError, "This #{self.class} cannot respond to:"
  end

  def document_user_id
    raise NotImplementedError, "This #{self.class} cannot respond to:"
  end

  def namirial_document
    raise NotImplementedError, "This #{self.class} cannot respond to:"
  end

  def namirial_file_id_field
    raise NotImplementedError, "This #{self.class} cannot respond to:"
  end

  def namirial_document_path
    raise NotImplementedError, "This #{self.class} cannot respond to:"
  end

  def namirial_id
    raise NotImplementedError, "This #{self.class} cannot respond to:"
  end

  def parse_next_step_response(response)
    raise NotImplementedError, "This #{self.class} cannot respond to:"
  end

  def private_pub_publish_url
    raise NotImplementedError, "This #{self.class} cannot respond to:"
  end

  def process_complete_sign
    raise NotImplementedError, "This #{self.class} cannot respond to:"
  end

  def process_next_step_sign
    raise NotImplementedError, "This #{self.class} cannot respond to:"
  end
end
