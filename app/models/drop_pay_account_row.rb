class DropPayAccountRow < ApplicationRecord
  belongs_to :drop_pay_account
  has_one :dealer, through: :drop_pay_account

  belongs_to :payable, polymorphic: true
  has_one :payment, as: :payment_implementation

  scope :processable, -> do
    left_joins(:payment)
      .where(payments: { id: nil })
      .where(state: PROCESSABLE_STATES)
      .where('(kind = ? AND document_issued_at <= ?) OR (kind = ? AND document_expires_at <= ?)',
             DropPayAccountRow.kinds[:credit],
             Date.today,
             DropPayAccountRow.kinds[:debit],
             Date.today)
      .where('attempts_count < ?', MAX_ATTEMPTS)
      .order(created_at: :desc)
  end

  delegate :name, to: :dealer, prefix: true

  has_enumeration_for :state,
                      with:           DropPayRowStatus,
                      create_helpers: { prefix: true },
                      create_scopes:  { prefix: true }

  validates_numericality_of :billing_amount, less_than_or_equal_to: :total_amount
  validates :drop_pay_account, presence: true
  validates :document_issued_at, presence: true
  validates :document_expires_at, presence: true, if: :debit?
  validate :enabled_drop_pay_account, on: :create

  before_validation :set_drop_pay_account, :set_kind
  before_create :set_default_description

  attr_accessor :dealer_vat

  enum kind: { credit: 0, debit: 1 }

  AUDIT_EXCEPT = %w(attempts_count transactions_log).freeze

  CREDIT_STATE_KEYS            = %w(completed in_progress).freeze
  DATE_FILTER_CREDIT_KEYS_FROM = [:document_issued_at_from, :created_at_from].freeze
  DATE_FILTER_CREDIT_KEYS_TO   = [:document_issued_at_to, :created_at_to].freeze

  DATE_FILTER_DEBIT_KEYS_FROM = [:document_expires_at_from, :document_issued_at_from].freeze
  DATE_FILTER_DEBIT_KEYS_TO   = [:document_expires_at_to, :document_issued_at_to].freeze

  RECURSIVE_CHARGE_AUTHORIZATION_DESCRIPTION = 'Autorizzazione addebito ricorsivo'

  MAX_ATTEMPTS = 7

  PRESENT_STATUS_KO      = 'ko'.freeze
  PRESENT_STATUS_OK      = 'ok'.freeze
  PRESENT_STATUS_NEW     = 'new'.freeze
  PRESENT_STATUS_PARTIAL = 'partial'.freeze
  PRESENT_STATUS_PENDING = 'pending'.freeze
  PROCESSABLE_STATES     = %w(new partial unchargeable).freeze

  audited associated_with: :dealer, except: AUDIT_EXCEPT, on: %i[create], if: :manually_added

  searchable(include: [:dealer, :drop_pay_account]) do
    integer :id
    string :kind

    integer :dealer_id do
      dealer.id
    end
    integer :drop_pay_account_id do
      drop_pay_account.id
    end
    integer :payment_id do
      payment&.id
    end
    string :payable_type
    integer :payable_id

    text(:dealer_name) { dealer.name.strip.downcase }
    text :document_number

    date :created_at
    date :document_issued_at
    date :document_expires_at

    string :state

#    join(:id, prefix: 'order', target: Order, type: :integer, join: { from: :id, to: :payable_id })
  end

  def add_transaction_row(params)
    increment!(:attempts_count) unless params[:increment_attempts] == false

    amount = params[:outcome] == DropPayAccountRow::PRESENT_STATUS_OK ? params[:amount].to_f : 0
    transaction = params.merge(attempt: attempts_count, residual_amount: residual_amount - amount)

    update(transactions_log: (transactions_log || []) << transaction.to_json,
           residual_amount:  transaction[:residual_amount]
    )

    set_outcome_state
    send_alert
  end

  def any_amount_charged?
    residual_amount < billing_amount
  end

  def commit_sunspot_index
    Sunspot.index self
    Sunspot.commit(true)
  end

  def gateway
    DropPayService.new
  end

  def payment_kind_description
    I18n.t('order.payment_methods.drop_pay')
  end

  def processable?
    return false if payment.present?
    return document_expires_at <= Date.today if debit?

    document_issued_at <= Date.today
  end

  def human_errors
    errors.attribute_names.map { |error_key| present(error_key) }
  end

  def max_attempts_reached?
    attempts_count == MAX_ATTEMPTS
  end

  def nothing_charged?
    !any_amount_charged?
  end

  def present_status
    case state
    when 'new'
      DropPayAccountRow::PRESENT_STATUS_NEW
    when 'partial'
      DropPayAccountRow::PRESENT_STATUS_PARTIAL
    when 'scheduled', 'in_progress'
      DropPayAccountRow::PRESENT_STATUS_PENDING
    when 'completed'
      DropPayAccountRow::PRESENT_STATUS_OK
    else
      DropPayAccountRow::PRESENT_STATUS_KO
    end
  end

  def refresh_view_row
    PrivatePub.publish_to('/trigger_drop_pay_events', "window.App.dropPayAccountRowListRefresher.refresh(#{id})")
  end

  def set_outcome_state
    update(state: outcome_state)

    refresh_view_row
  end

  def to_partial_path
    "drop_pay_account_rows/row_#{kind}"
  end

  def transaction_rows
    return [] unless transactions_log

    transactions_log.map { |row| JSON.parse(row, object_class: OpenStruct) }
  end

  def trigger_balance_job
    return unless processable?
    return if state_scheduled? || state_in_progress?

    update(session_timestamp: nil)
    Delayed::Job.enqueue(Jobs::Atono::BalanceHandlerJob.new(self),
                         queue:    'drop_pay_account_balance',
                         priority: 0)
  end

  private

  def enabled_drop_pay_account
    return unless drop_pay_account

    self.errors.add(:enabled_drop_pay_account, I18n.t('drop_pay_account.validation_errors.enabled_drop_pay_account', dealer_vat: dealer_vat)) unless drop_pay_account.enabled?
  end

  def present(error_key)
    case error_key
    when :drop_pay_account, :enabled_drop_pay_account
      I18n.t("drop_pay_account.validation_errors.#{error_key}", dealer_vat: dealer_vat)
    when :billing_amount
      I18n.t("drop_pay_account.validation_errors.#{error_key}_#{kind}", dealer_vat: dealer_vat, document_number: document_number)
    end
  end

  def outcome_state
    return :completed if residual_amount.zero?
    return :partial if any_amount_charged?
    return :unchargeable if nothing_charged?
  end

  def send_alert
    return if payment.present?

    if debit?
      current_alert = dealer.alerts.visible.where(alertable_type: 'DropPayAccountRow', alertable_id: id).first
      residual_amount.zero? ? current_alert.archive! : current_alert.touch if current_alert
      Alerts::DropPayAccountRowAlertMessage.new(self).insert_message(Alerts::DropPayAccountRowAlertMessage::DEBIT_FAILURE) unless current_alert || residual_amount.zero? || max_attempts_reached?
    end

    if credit? && residual_amount.zero?
      Alerts::DropPayAccountRowAlertMessage.new(self).insert_message(Alerts::DropPayAccountRowAlertMessage::CREDIT_SUCCESS)
    end
  end

  def set_default_description
    return if description.present?

    self.description = I18n.t('drop_pay_account_row.description',
                              kind: I18n.t("atono.helpers.#{kind}"),
                              document_number: document_number,
                              document_issued_at: I18n.l(document_issued_at))
  end

  def set_drop_pay_account
    self.drop_pay_account ||= DropPayAccount.joins(:dealer).find_by(dealers: { partita_iva: self.dealer_vat })
  end

  def set_kind
    self.kind ||= filename.downcase =~ /debit/ ? :debit : :credit
  end

  def set_residual_amount
    self.residual_amount = billing_amount
  end
end
