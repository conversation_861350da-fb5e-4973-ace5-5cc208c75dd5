class FixedLineDetail < ApplicationRecord
  COVERAGE_STATUSES = {
    unchecked: 0,
    false:     1,
    true:      2
  }
  attr_encrypted :location_address_street_name, key: Rails.application.credentials.attr_encrypted_key

  belongs_to :phone_activation
  belongs_to :location_address_street_type, class_name: "StreetType", foreign_key: :location_address_street_type_id
  belongs_to :city
  belongs_to :location_province, class_name: 'Province', foreign_key: :location_province_id

  has_one :plan, through: :phone_activation
  has_one :appointment_detail, through: :phone_activation

  delegate :product_category, to: :plan, allow_nil: true
  delegate :code, :description, to: :product_category, allow_nil: true
  delegate :code, :fwa_assurance_products, :fwa_installation_products, :matching_area_codes, :posng_value, to: :location_province, allow_nil: true
  delegate :ps, to: :location_address_street_type, prefix: true, allow_nil: true

  validates :location_address_street_name, presence: true
  validates :location_number, presence: true
  validates :location_cap, presence: true
  validates :location_city, presence: true
  validates :coverage_status, presence: true, inclusion: { in: COVERAGE_STATUSES.values }

  before_save :handle_number_format
  before_save :set_location_province

  after_update :update_appointment_data, if: :appointment_detail

  def appointment_attributes
    {
      location_number:                 location_number,
      location_city_id:                city_id,
      location_cap:                    location_cap,
      location_address_street_type_id: location_address_street_type_id,
      location_address_street_name:    location_address_street_name,
      system_name:                     location_province.system_name
    }
  end

  def as_payload
    {
      address: full_location_address,
      brand: '',
      particella: '',
      country: 'ITALIA',
      city: location_city,
      toponymy: location_address_street_type&.value,
      postalCode: location_cap,
      streetNumber: location_number,
      province: location_province&.code
    }
  end

  def full_location_address
    [location_address_street_type&.value, location_address_street_name].join(" ").strip
  end

  private

  def handle_number_format
    return unless location_number
    return if location_number.numeric?

    self.location_number = self.location_number.scan(/\d+|\D+/).map { |bit| bit.gsub(/\s+/, '') }.join.strip
  end

  def set_location_province
    return unless city

    self.location_province = city.province
  end

  def update_appointment_data
    if (previous_changes.keys && appointment_attributes.keys).any?
      appointment_detail.assign_attributes(appointment_attributes)
      appointment_detail.save
    end
  end
end
