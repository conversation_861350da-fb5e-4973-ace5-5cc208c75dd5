class SapValuationClass < ApplicationRecord
  has_many :products

  validates_uniqueness_of :code, :description, case_sensitive: true
  validates :code, presence: true
  validates :description, presence: true
  delegate :description_for_dropdown, to: :dropdown_presenter
  alias :code_and_description :description_for_dropdown

  ATTIVA_DEFAULT_CODE = 'Z007'
  def dropdown_presenter
    CodeAndDescriptionPresenter.new(self)
  end
end
