class AuditPresenter
  attr_accessor :audit

  DEFAULT_SEPARATOR = "&quot; => &quot;".freeze

  def initialize(audit)
    @audit = audit
  end

  def present
    return present_dealer_operator_removal(audit) if dealer_operator_removal?(audit)

    audited_changes = ""
    audited_object  = audit.auditable

    audit.audited_changes.each_pair do |key, values|
      next if key.to_s.start_with?('encrypted_') && key.to_s.end_with?('_iv')

      cleaned_key = key.to_s.gsub('encrypted_', '').gsub('_id', '')

      if cleaned_key == "areamanager"
        audited_changes << present_area_manager_for(values)
        next
      end

      original_values_object =
        if audited_object.instance_of?(PhoneActivation) && ["product_item"].include?(cleaned_key)
          values.map { |value| Item.unscoped.find_by_id(value) }.compact.first
        else
          begin
            audited_object.send(cleaned_key)
          rescue StandardError
            nil
          end
        end

      if original_values_object.present? && original_values_object.is_a?(ActiveRecord::Base)
        values = [values] unless values.is_a? Array
        values.each_with_index do |value, index|
          next unless value.present?

          values[index] = original_object_description(original_values_object, value)
        end
      end

      change_label = if audited_object&.class&.respond_to?(:human_attribute_name)
                       if audit.associated_type
                         "#{audited_object.class} #{audited_object.class.human_attribute_name(cleaned_key)}"
                       else
                         audited_object.class.human_attribute_name(cleaned_key)
                       end
                     else
                       cleaned_key.to_s.humanize
                     end
      change_values = change_values_for(audited_object, key, values)

      audited_changes << "#{change_label}: #{change_values}" if change_values
    end
    audited_changes.html_safe
  end

  private

  def original_object_description(original_values_object, value)
    original_values_object = original_values_object.class.where(id: value).first
    return value unless original_values_object

    better_description = value
    better_description = original_values_object.description if original_values_object.respond_to?(:description)
    better_description = original_values_object.full_name if original_values_object.respond_to?(:full_name)
    better_description = original_values_object.name if original_values_object.respond_to?(:name)
    better_description = original_values_object.serial if original_values_object.is_a?(Item)
    better_description
  end

  def change_values_for(audited_object, key, values)
    return unless values.is_a?(Array)

    return present_encrypted(key, values) if key.to_s.start_with?('encrypted_')
    return "&quot;#{[values].join(DEFAULT_SEPARATOR)}&quot; <br>" unless values.first.is_a?(Array)
    return "#{Operator.where(id: values.last).pluck(:name)} <br>" if audited_object.is_a?(Dealer) && key.to_s == "dealer_operator_ids"
    return "#{PaymentMethod.where(id: values.last).pluck(:description)} <br>" if audited_object.is_a?(Dealer) && key.to_s == "dealer_payment_method_ids"
    return present_array_for(values) if audited_object.is_a?(Plan) && key.to_s == "option_ids"
    return present_array_for(values) if audited_object.is_a?(Warehouse) && key.to_s == "warehouse_brand_ids"
    return "#{values.first} => #{values.last} <br>" if audited_object.is_a?(Plan) && ["customer_kind_ids", "dealer_category_ids"].include?(key.to_s)
    return "#{Operator.where(id: values.last).pluck(:name)} <br>" if key.to_s.include? "operator_ids"

    "&quot;#{values.first}&quot; => &quot;#{values.last}&quot; <br>"
  end

  def present_encrypted(key, values)
    return if values.all? { |v| v.nil? }

    iv_values = audit.audited_changes.fetch("#{key}_iv", nil)
    return unless iv_values

    decrypted_values = values.map do |value|
      next if value.nil?
      audit.auditable.class.attr_encrypted_decrypt(key.gsub('encrypted_', '').to_sym, value, iv: Base64.decode64(iv_values[values.index(value)]))
    end

    decrypted_values.uniq.count > 1 ? "&quot;#{[decrypted_values].join(DEFAULT_SEPARATOR)}&quot; <br>" : nil
  end

  def present_area_manager_for(values)
    "Areamanager : &quot;#{[User.where(id: values.first)&.first&.full_name, User.where(id: values.second)&.first&.full_name].join(DEFAULT_SEPARATOR)}&quot; <br>"
  end

  def present_array_for(changes)
    return "rimosse => #{changes.first - changes.last}" if changes.first.size > changes.last.size

    "aggiunte => #{changes.last - changes.first}"
  end

  def present_dealer_operator_removal(audit)
    username = audit.user.full_name
    operator = audit.audited_changes[:operator_name]
    "L'utente #{username} ha rimosso l'operatore #{operator}"
  end

  def dealer_operator_removal?(audit)
    audit.action == "update" && audit.comment == "dealer_operator"
  end
end
