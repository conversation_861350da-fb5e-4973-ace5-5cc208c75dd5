module WindNgposActivations
  module ImeiReservationStrategies
    class AutoConfirmStrategy < BaseStrategy
      attr_accessor :imei_reservation

      def applicable?
        associated_reservation
      end

      def execute
        return unless activation.dealer

        service = ImeiReservationItemService.new(imei_reservation: associated_reservation)
        service.autoconfirm_reservation(dealer_notes_from_activation, activation_sale_amount, activation)

        if service.response.error
          WindTreDatasImporterMailer
            .autoconfirm_error(activation, associated_reservation, service.response.error)
            .deliver_now
        else
          PrivatePub.publish_to(
            "/notify_imei_reservation_autoconfirmation/#{associated_reservation.id}",
            "window.App.ImeiReservationAutoconfirmHandler.notifyAutoconfirm();"
          )
          PrivatePub.publish_to(
            "/notify_imei_reservation_autoconfirmation_to_app/#{associated_reservation.id}",
            firstLine:  I18n.t("imei_reservations.autoconfirm_notification"),
            secondLine: I18n.t("imei_reservations.autoconfirm_suggestion")
          )
          PrivatePub.publish_to(
            "/trigger_countdown_stop",
            "window.App.ImeiReservationRowCoundownStopper.stopCountdown(#{associated_reservation.id});"
          )
        end
      end

      private

      def associated_reservation
        self.imei_reservation ||= Repositories::ImeiReservationRepository.find_pending_by_item(activation.item_id)
      end
    end
  end
end
