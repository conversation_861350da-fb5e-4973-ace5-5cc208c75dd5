class CustomerService
  attr_reader :cf_or_vat, :customer

  def initialize(args)
    @customer = args.fetch(:customer, nil)
    @cf_or_vat = args.fetch(:cf_or_vat, nil)
  end

  def marketing_communications_agreed_changed!
    if customer.marketing_communications_agreed?
      customer.update_column(:marketing_communications_agreed_at, Time.now)
      customer.update_column(:marketing_communications_expire_at, expire_date)
    end
    Jobs::PhoneActivationsReindexJob.new(phone_activation_ids: customer.phone_activation_ids).delay.perform(false)
    AlertMessageService.archive_customer_expired_notifications(customer)
  end

  def new_customer
    customer = Customer.new
    customer.customer_kind = find_customer_kind_by_cf_or_vat
    customer.cf = cf_or_vat if customer.customer_kind_code == CustomerKind::CLIENTE_PRIVATO
    customer.vat = cf_or_vat if customer.customer_kind_code == CustomerKind::PERSONA_GIURIDICA
    customer
  end

  private

  def expire_date
    customer.marketing_communications_agreed_at + 36.month
  end

  def find_customer_kind_by_cf_or_vat
    return CustomerKind.find_by_code(CustomerKind::CLIENTE_PRIVATO) if cf_or_vat =~ Bost::Constants::CF_REGEXP
    return CustomerKind.find_by_code(CustomerKind::PERSONA_GIURIDICA) if cf_or_vat =~ Bost::Constants::VAT_REGEXP

    nil
  end
end
