class CleanDeviceService
  def clean
    Device.all.group_by(&:user_id).each do |id, devices|
      devices.group_by(&:firebase_token).each do |token, duplicated_devices|
        if duplicated_devices.count > 1
          duplicated_devices.sort_by(&:last_sign_in_at).each_with_index do |device, index|
            device.destroy! unless index == duplicated_devices.count - 1
          end
        end
      end
    end
  end
end
