class SapApiCallService
  def initialize(sap_api_call, current_user)
    @original_sap_api_call = sap_api_call
    @sap_api_call = sap_api_call.dup
    @current_user = current_user
  end

  def execute
    return false unless @sap_api_call.runnable?

    begin
      make_api_request
    rescue StandardError => e
      handle_error(e)
      false
    end
  end

  private

  def make_api_request
    api_request = Sap::ApiRequest.run(
      http_method:  @sap_api_call.http_method,
      kind:         @sap_api_call.kind,
      path:         @sap_api_call.path,
      origin:       @sap_api_call.origin_obj,
      sap_api_call: @sap_api_call
    )

    @original_sap_api_call.update(relaunched_at: Time.now, relaunching_user_id: @current_user&.id)

    if api_request.valid? && api_request.result.success
      if @sap_api_call.origin.respond_to?(:sap_export_callback)
        @sap_api_call.origin.sap_export_callback(@sap_api_call)
      end

      if @sap_api_call.path == Settings.sap.api_endpoints.odv
        if [ImeiReservation, Insurance, PhoneActivation].include?(@sap_api_call.origin_obj.class)
          Delayed::Job.enqueue(Jobs::Sap::FulfillOdvExporter.new(model_id: @sap_api_call.origin_obj.id, model_class: @sap_api_call.origin_obj.class.name),
                               queue:    "sap_#{@sap_api_call.origin_obj.class.name.snakecase}_#{@sap_api_call.origin_obj.id}",
                               priority: 5)
        end

        if @sap_api_call.origin_obj.is_a?(Order)
          @sap_api_call.origin_obj.fulfills.where(sap_exported_at: nil).each do |fulfill|
            Delayed::Job.enqueue(Jobs::Sap::FulfillOdvExporter.new(model_id: fulfill.id, model_class: 'Fulfill'),
                                 queue:    "sap_fulfill_#{fulfill.id}",
                                 priority: 5)
          end
        end
      end
      true
    else
      false
    end
  end

  def handle_error(error)
    @sap_api_call.update!(
      response: { error: error.message },
      success:  false
    )
  end
end