module PartialPrinters
  class EnergyContractPrinter < BasePrinter
    PARTIAL_NAME_MAPPING = {
      customer_data: 'energy_contracts/details/_customer_data',
      summary:       'energy_contracts/summary'
    }.freeze

    def partial_name
      PARTIAL_NAME_MAPPING[partial]
    end

    def printing_options
      if partial == :summary
        summary_options
      else
        { template: partial_name, layout: 'layouts/printing_layout', locals: locals }
      end
    end

    private

    attr_reader :energy_contract

    def locals
      { energy_contract: energy_contract, gas_plan: energy_contract.gas_plan, energy_plan: energy_contract.energy_plan }
    end

    def summary_options
      {
        pdf:         'document',
        template:    partial_name,
        layout:      'energy_contracts_pdf',
        disposition: 'inline',
        margin:      {
          bottom: 15,
          top:    15,
          left:   26,
          right:  26
        },
        locals:      locals
      }
    end

    def initialize_args
      @energy_contract = args[:energy_contract]
    end
  end
end
