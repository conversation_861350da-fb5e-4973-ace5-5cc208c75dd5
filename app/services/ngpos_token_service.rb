class NgposTokenService
  attr_reader :ngpos_robot_credentials

  def initialize(args = {})
    @ngpos_robot_credentials = args.fetch(:ngpos_robot_credentials, Settings.ngpos_robot)
  end

  def get
    payload = { "login": ngpos_robot_credentials.login, "password": ngpos_robot_credentials.password }

    response = RestClient::Request.execute(method:  :post,
                                           url:     ngpos_robot_credentials.token_url,
                                           payload: payload.to_json,
                                           headers: { accept: :json, content_type: :json })
    response.body
  end

  def get_bearer
    ['Bearer', get.gsub('"', '')].join(' ')
  end
end
