class OptionCompatibilityMatrixFilter
  attr_reader :options, :row_id, :column_id

  @@Table = Struct.new(:columns, :rows)

  def initialize(options, row_id, column_id)
    @options = options
    @row_id = row_id
    @column_id = column_id

    @result = @@Table.new
  end

  def call
    if only_row?
      @result.columns = options
      @result.rows    = options.select { |option| option.id.to_s == row_id }
    elsif row_and_column?
      @result.columns = options.select { |option| option.id.to_s == column_id }
      @result.rows    = options.select { |option| option.id.to_s == row_id }
    elsif only_column?
      @result.columns = options.select { |option| option.id.to_s == column_id }
      @result.rows    = options
    else
      @result.columns = options
      @result.rows    = options
    end

    @result
  end

  def only_operator?
    row_id.blank? && column_id.blank?
  end
  def only_row_or_column?
    row_id.present? || column_id.present?
  end

  def only_column?
    row_id.blank? && column_id.present?
  end

  def only_row?
    row_id.present? && column_id.blank?
  end

  def row_and_column?
    row_id.present? && column_id.present?
  end

  def current_column_index_in_rows
    @result.rows.index { |row| row.id == @result.columns.first.id }
  end
end
