class PhoneActivationSearchableService
  attr_reader :phone_activation, :last_completed_operations, :operation_ids

  def initialize(phone_activation)
    @phone_activation          = phone_activation
    @last_completed_operations = phone_activation.last_completed_operations
    @operation_ids             = phone_activation.operation_ids
  end

  def next_operation_is_pdc_check
    return false unless operation_ids.include?(OperationConstants::PDC_CONFERMATA)
    return false if phone_activation.status_ko?

    phone_activation.next_operation_is(Operation::PDC_CONFERMATA) || last_operation_is_ko_r_for(Operation::PDC_CONFERMATA)
  end

  private

  def last_operation_is_ko_r_for(operation_id)
    last_completed_operations.where(operation_id: operation_id, status: "ko_r").any?
  end
end
