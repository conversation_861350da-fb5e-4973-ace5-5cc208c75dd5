class OfficialPdaService
  attr_reader :phone_activation

  def initialize(phone_activation)
    @phone_activation = phone_activation
  end

  def create_from(official_pda_file, user_id = User::ROBOT_USER_ID)

    official_pda = @phone_activation.official_pda || OfficialPda.create(phone_activation_id: @phone_activation.id)

    file_name = "#{Document.name_for(official_pda.document_kind, official_pda, nil)}.pdf"
    file      = Tempfile.new([file_name, ".pdf"])
    file.binmode
    file.write(official_pda_file.read)
    file.close

    document = Document.new(related_file: file,
                                               name:         file_name,
                                               user_id:      user_id,
                               kind:              official_pda.document_kind,
                               status:            nil)
    # documentable_type: official_pda.class.name.to_s,
    #   documentable_id:   official_pda.id,
    #
    #   )

    official_pda.documents << document

    # begin
    #   official_pda.set_code
    # rescue StandardError => e
    #   KolmeLogger.error(e)
    # end

    file.delete
  end
end
