module AttivaService
  class Article
    include AttivaService::Concerns::FieldMethods

    VAT_MAP = {
      '0'     => '17RT',
      '17RC'  => '17RC',
      '22'    => '22',
      '22,00' => '22',
      'A74'   => '7403',
      'RLB'   => '17RC',
      'RLC'   => '17RC',
      'A02'   => 'A02'
    }.freeze

    DEFAULT_ATTRS = {
      origin:                 'attiva',
      activable_system_owner: false,
      dont_show_public_price: false,
      loanable:               false,
      mandatory_mnp:          false,
      max_qty_orderable:      nil,
      operator_id:            nil,
      product_type:           'p',
      reward:                 0,
      serial:                 false,
      sim_format:             nil,
      view_in_matrix:         false,
      visibility:             'y'
    }.freeze

    ATTIVA_PRODUCT_ATTRS = %i[
      dealer_price_vat_excluded_a
      dealer_price_vat_excluded_b
      dealer_price_vat_excluded_c
    ].freeze

    attr_reader :errors

    def initialize(noko_node)
      @noko_node = noko_node
      @errors = []
    end

    def product_attributes
      # FIXME: remove duplicated keys
      attributes.slice :code, :name, :commercial_name, :description, :dealer_price_vat_excluded_a, :dealer_price_vat_excluded_b, :dealer_price_vat_excluded_c, :gamma_purchase_price, :product_type, :public_price_list, :vat_type, :ean_code, :weight, :width, :height, :depth, :amount_in_system_owner_warehouse, :current_availability, :future_availability, *DEFAULT_ATTRS.keys
    end

    def non_sensible_product_attributes
      product_attributes.stringify_keys.except(*(AttivaProduct::SENSIBLE_ATTRS + AttivaProduct::READONLY_ATTRS))
    end

    def attiva_product_attributes
      attributes.slice *ATTIVA_PRODUCT_ATTRS
    end

    def category_attributes
      attributes[:category]
    end

    def subcategory_attributes
      attributes[:subcategory]
    end

    def brand_attributes
      attributes[:brand]
    end

    def code
      attributes[:code]
    end

    def name
      attributes[:name]
    end

    def vat_type
      attributes[:vat_type]
    end

    def attributes
      @attributes ||= {
        code:                             text('CodAtv'),
        name:                             description_text,
        commercial_name:                  description_text,
        description:                      full_description_text,
        dealer_price_vat_excluded_a:      price_for_product,
        dealer_price_vat_excluded_b:      price_for_product,
        dealer_price_vat_excluded_c:      price_for_product,
        gamma_purchase_price:             price_for_product,
        public_price_list:                price_for(text('PrezzoEndUser')),
        vat_type:                         vat_type_from_field,
        ean_code:                         text('CodiceEAN'),
        weight:                           weight_text,
        width:                            text('Larghezza', true),
        height:                           text('Altezza', true),
        depth:                            text('Profondita', true),
        current_availability:             text('DisponibilitaPresente'),
        future_availability:              text('DisponibilitaFutura'),
        amount_in_system_owner_warehouse: text('DisponibilitaPresente'),
        image_url:                        image_url,
        category:                         {
          code: attr_value('Categoria', 'id'),
          name: text('Categoria')
        },
        subcategory:                      {
          code: attr_value('SottoCategoria', 'id'),
          name: text('SottoCategoria')
        },
        brand:                            {
          name: text('Marchio')
        }
      }.merge(DEFAULT_ATTRS).reject { |_k, v| v.nil? }.with_indifferent_access
    end

    def image_url
      image_url_from_file = text('ImmagineGrande')
      return nil if image_url_from_file.in? ['images/', 'http://aws.attiva.com/images/']

      if image_url_from_file.starts_with? 'http'
        image_url_from_file
      else
        "http://aws.attiva.com/#{image_url_from_file}"
      end
    end

    def valid?
      errors << :unknown_vat_type unless attributes.keys.include? :vat_type.to_s

      errors.empty?
    end

    private

    def price_for(end_user_price)
      return unless end_user_price

      end_user_price.gsub!(',', '.')
      return 0 unless end_user_price&.numeric?

      BigDecimal(end_user_price)
    end

    def vat_type_from_field
      VAT_MAP.fetch(attr_value('DescrizioneIVA', 'codice'), nil)
    end
  end
end
