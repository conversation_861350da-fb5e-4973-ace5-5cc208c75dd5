module SmsHosting
  class Response
    SUCCESS_STATUS = 200

    attr_reader :response

    def initialize(response)
      @response = response
    end

    def inspect
      "<SmsHosting::Response @response=#{response}>"
    end

    def sms_reference
      message.dig("transactionId") if success?
    end

    def message
      @message ||= JSON.parse(response.body)
    end

    def success?
      response.code == SUCCESS_STATUS
    end

    def failed?
      !success?
    end
  end
end
