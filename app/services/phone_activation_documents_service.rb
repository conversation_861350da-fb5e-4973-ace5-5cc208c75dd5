class PhoneActivationDocumentsService
  attr_reader :phone_activation

  def initialize(phone_activation)
    @phone_activation = phone_activation
  end

  def complete_operation!
    return if phone_activation.upload_documents_completed?

    create_digital_signature_operation_outcome

    OperationOutcome.create(
      operation_id:        OperationConstants::UPLOAD_DOCUMENTI,
      user_id:             1,
      note:                '',
      phone_activation_id: phone_activation.id,
      private_note:        'Victor sta scaricando i documenti firmati',
      status:              'visited',
      busy:                true
    )
  end

  private

  def create_digital_signature_operation_outcome
    operation_outcome_params = {
      operation_id:        OperationConstants::DIGITAL_SIGNATURE,
      user_id:             phone_activation.user_id,
      note:                '',
      phone_activation_id: phone_activation.id
    }

    operation_outcome = Operations::OperationsHandler.reserve(operation_outcome_params)

    # evaluate whether to remove the delay after the operation is processed
    Operations::OperationsHandler.process(
      operation_outcome.id,
      operation_outcome_params.merge!(status: 'ok', busy: false),
      complete_todo: true,
      remove_delay:  :evaluate
    )
  end
end
