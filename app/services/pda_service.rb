# encoding: utf-8

class PdaService
  attr_reader :pda_attributes_builder, :phone_activation

  def initialize(phone_activation, args = {})
    @phone_activation = phone_activation
    @pda_attributes_builder = args.fetch(:pda_attributes_builder, PhoneActivations::Pda::PdaAttributesBuilder)
  end

  def provide_attributes(preview = false)
    raise "Cliente non impostato" if phone_activation.customer.blank?

    phone_activation_on_db = PhoneActivation.eager_load(
      :fixed_line_detail,
      :number_rebinding_detail,
      :offer,
      :payment_method_detail,
      :product,
      :product_category,
      :dealer,
      :recharge_size,
      :stored_plan,
      customer:                  %i[birth_province birth_country country customer_kind address_street_type headquarters_address_street_type],
      number_portability_detail: %i[contract_kind],
      phone_activation_options:  %i[option],
      plan:                      %i[contract_kind product_category],
      user:                      %i[dealer_contact],
      warehouse:                 %i[country]
    ).where(id: phone_activation.id).first

    pda_attributes_builder.new(phone_activation_on_db, preview).build
  end

  def provide_templates
    phone_activation.plan.pda_kind.build_pdf_list(
      prefix:                    phone_activation.customer.customer_kind.pda_code,
      beyond_pda_kind_threshold: phone_activation.beyond_pda_kind_threshold?,
      solo_sim_cdc:              phone_activation.require_solo_sim_cdc_template?,
      solo_sim_sdd:              phone_activation.require_solo_sim_sdd_template?,
      solo_sim_none:             phone_activation.require_solo_sim_none_template?, 
      sdd:                       phone_activation.require_rid_pda_template?,
      cdc:                       phone_activation.require_credit_card_pda_template?,
      mnp:                       (phone_activation.do_number_portability == "Y"),
      none:                      phone_activation.require_none_pda_template?,
      different_accountholder:   phone_activation&.different_accountholder,
      generic_transfer:          phone_activation.require_generic_transfer_template?,
      kolme_master_transfer:     phone_activation.require_kolme_master_transfer_template?,
      options_filenames:         phone_activation.options.where.not(pda_filename: ['', nil]).pluck(:pda_filename).uniq
    )
  end
end
