module PdfFactories
  class PdfFactory
    include Bost::DocumentGeneration

    def build_pdf(resource)
      attributes = attributes_for(resource)
      templates  = templates_for(resource)
      tempfile   = Tempfile.new(resource.class.name.underscore + "-" + resource.id.to_s + ".pdf")
      generate_pdf(templates, tempfile, attributes)
    end

    def attributes_for(_resource)
      raise "Not implemented"
    end

    def templates_for(_resource)
      raise "Not implemented"
    end

    def flag(value)
      value ? "1" : ""
    end

    def document_complete_path(path, file_name)
      [path, file_name].join("/")
    end
  end
end
