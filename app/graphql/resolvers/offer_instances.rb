module Resolvers
  class OfferInstances < Resolvers::Base
    argument :search_params, Types::OfferInstanceSearchType, required: false

    type [Types::OfferInstanceType], null: true

    def resolve(args = {})
      search_params = args[:search_params] || {}

      search = Sunspot.new_search(OfferInstance)

      search.build do
        with :operator_id, search_params[:operator_ids] if search_params[:operator_ids].present?
        with :portability_operator_ids, search_params[:portability_operator_ids] if search_params[:portability_operator_ids].present?
        with :request_mnp, search_params[:portability_required] unless search_params[:portability_required].nil?
        with :contains_not_zero_threshold, true
        with :product_activable_system_owner, true
        with :tariffa_cb, false
        with :has_options_with_thresholds, true
      end

      dealer = context[:current_user].dealer
      if dealer
        search = dealer.add_md_very_filters_to(search)
      end


      total = search.execute.total

      search.build do
        paginate(page: 1, per_page: total)
      end

      search.results
    end
  end
end
