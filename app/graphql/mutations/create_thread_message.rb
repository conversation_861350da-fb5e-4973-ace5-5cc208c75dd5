require 'apollo_upload_server'

module Mutations
  class CreateThreadMessage < Mutations::BaseMutation
    argument :after_sale_thread_id, ID, required: true
    argument :message, String, required: true
    argument :attachment, Types::DocumentInputType, required: false

    field :thread_message, Types::ThreadMessageType, null: true

    def resolve(args)
      user = authorize_and_return_user

      if args[:attachment].present?
        document = OpenStruct.new(args[:attachment].to_h)

        file_contents   = Base64.decode64(document.files.first)
        file_extensions = [document.file_extension || 'png']

        base_tmp_path = "#{Settings.document_uploader.tmp_dir}/thread_messages"
        FileUtils.rm_rf(base_tmp_path)
        FileUtils.mkdir_p(base_tmp_path)
        filepath =  [base_tmp_path, "attachment.#{file_extensions.first}"].join('/')
        File.open(filepath, 'wb') do |f|
          f.write file_contents
        end

        args.merge!(attachment: File.open(filepath))
      end

      thread_message = ThreadMessage.create!(args.merge(user_id: user.id))

      if thread_message.valid?
        AfterSaleThreads::ThreadMessagesGraphqlCallbacks.new(thread_message).execute_create_callbacks
        if args[:attachment].present?
          FileUtils.rm_rf(base_tmp_path)
        end

        MutationResult.call(obj: { thread_message: thread_message }, success: true, message: "Success!")
      else
        MutationResult.call(obj: { thread_message: nil }, success: false, errors: thread_message.errors.full_messages)
      end
    end
  end
end
