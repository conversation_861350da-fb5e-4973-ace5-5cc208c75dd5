module Mutations
  class UpdateImeiReservation < Mutations::BaseMutation
    argument :id, ID, required: true
    argument :state, String, required: true
    argument :dealer_notes, String, required: false

    field :imei_reservation, Types::ImeiReservationType, null: true

    def resolve(args)
      imei_reservation = ImeiReservation.find(args[:id])
      service = ImeiReservationItemService.new(imei_reservation: imei_reservation)

      case args[:state]
      when "confirmed"
        service.confirm_reservation
        if service.response.imei_reservation.confirmed?
          MutationResult.call(obj: { imei_reservation: service.response.imei_reservation }, success: true)
        else
          error_message = service.response.error.is_a?(String) ? service.response.error : service.response.error.message
          MutationResult.call(obj: { imei_reservation: nil }, success: false, errors: [error_message])
        end
      when "canceled"
        service.terminate_reservation(:cancel!)
        if service.response.imei_reservation.canceled?
          MutationResult.call(obj: { imei_reservation: service.response.imei_reservation }, success: true)
        else
          error_message = service.response.error.is_a?(String) ? service.response.error : service.response.error.message
          MutationResult.call(obj: { imei_reservation: nil }, success: false, errors: [error_message])
        end
      when "expired"
        service.terminate_reservation(:expire!)
        if service.response.imei_reservation.expired?
          MutationResult.call(obj: { imei_reservation: service.response.imei_reservation }, success: true)
        else
          error_message = service.response.error.is_a?(String) ? service.response.error : service.response.error.message
          MutationResult.call(obj: { imei_reservation: nil }, success: false, errors: [error_message])
        end
      when "sent_to_dhl"
        service.send_reservation_to_dhl(args[:dealer_notes])
        if service.response.error.nil?
          MutationResult.call(obj: { imei_reservation: service.response.imei_reservation }, success: true)
        else
          error_message = service.response.error.is_a?(String) ? service.response.error : service.response.error.message
          MutationResult.call(obj: { imei_reservation: nil }, success: false, errors: [error_message])
        end
      end
    end
  end
end