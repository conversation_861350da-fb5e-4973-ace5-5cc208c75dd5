module Types
  class NumberPortabilityDetailType < Types::BaseObject
    field :phone_number, String, null: true
    field :source_operator_id, Integer, null: true
    field :sim_serial, String, null: true
    field :contract_kind_id, Integer, null: true
    field :credit_transfer, <PERSON><PERSON>an, null: true
    field :different_owner, <PERSON><PERSON><PERSON>, null: true
    field :location_address, String, null: true
    field :location_address_street_type_id, Int, null: true
    field :location_city, String, null: true
    field :location_number, String, null: true
    field :location_province_id, Int, null: true
    field :location_zip, String, null: true
    field :otp, String, null: true
    field :owner_first_name, String, null: true
    field :owner_kind, String, null: true
    field :owner_last_name, String, null: true
    field :owner_cf, String, null: true
    field :owner_birth_date, String, null: true
    field :owner_birth_place, String, null: true
    field :owner_document_date, String, null: true
    field :owner_document_expiry, String, null: true
    field :owner_document_number, String, null: true
    field :owner_birth_country_id, Integer, null: true
    field :owner_birth_province_id, Integer, null: true
    field :owner_company_kind, String, null: true
    field :owner_company_name, String, null: true
    field :owner_document_issued_by, String, null: true
    field :owner_document_kind, String, null: true
    field :owner_gender, String, null: true
    field :owner_identity_document_kind_id, Integer, null: true
    field :owner_vat, String, null: true
    field :voice_migration_code, String, null: true
    field :prefix, String, null: true
  end
end
