module Types
  class AlertMessageType < Types::BaseObject
    field :action_icon, String, null: true
    field :action_label, String, null: false
    field :info_icon, String, null: true
    field :info_class, String, null: true

    def action_label
      object.todo? ? object.alert_message&.action_label : 'Archivia'
    end

    def action_icon
      object.todo? ? object.alert_message&.icon : 'check'
    end

    def info_icon
      object.alert_message.label
    end

    def info_class
      object.alert_message.style_class
    end
  end
end