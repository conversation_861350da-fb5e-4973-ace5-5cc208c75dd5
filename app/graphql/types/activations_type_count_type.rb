module Types
  class ActivationsTypeCountType < Types::BaseObject
    field :activation_type, String, null: false
    field :count, Int, null: false
    field :with_mnp_count, Int, null: false
    field :without_mnp_count, Int, null: false

    def count
      case object.activation_type
      when "fixed"
        Repositories::WindTreDataRepository.new.fixed_counts(object.user.current_user_warehouse_ids)
      when "mobile"
        Repositories::WindTreDataRepository.new.mobile_counts(object.user.current_user_warehouse_ids)
      when "very"
        if object.user.dealer&.md_very?
          object.user.very_count
        else
          Repositories::WindTreDataRepository.new.very_counts(object.user.current_user_warehouse_ids)
        end
      else
        0
      end
    end

    def with_mnp_count
      return 0 if object.user.dealer.blank?
      return 0 unless object.user.dealer.md_very?
      return 0 if object.activation_type != 'very'

      object.user.dealer.very_phone_activations_count(:mnp_current_month)
    end

    def without_mnp_count
      return 0 if object.activation_type != 'very'
      return 0 unless object.user&.dealer&.md_very?

      count - with_mnp_count
    end
  end
end
