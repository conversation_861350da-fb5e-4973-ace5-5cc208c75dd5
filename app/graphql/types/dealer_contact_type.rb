module Types
  class DealerContactType < Types::BaseObject
    field :first_name, String, null: false
    field :last_name, String, null: false
    field :date_of_birth, String, null: true
    field :portal_enabled, Bo<PERSON>an, null: true
    field :cellphone_1, String, null: true
    field :cellphone_2, String, null: true
    field :cellphone_otp, String, null: true
    field :cf, String, null: true
    field :skype, String, null: true

    field :primary_contact, <PERSON><PERSON><PERSON>, null: true
    field :consel_affiliation_code, String, null: true
    field :legal_representative, <PERSON><PERSON><PERSON>, null: true
    field :note, String, null: true
    field :can_activate_simulation_plans, <PERSON><PERSON><PERSON>, null: true

    def date_of_birth
      object.date_of_birth.to_s
    end

    def skype
      ''
    end
  end
end
