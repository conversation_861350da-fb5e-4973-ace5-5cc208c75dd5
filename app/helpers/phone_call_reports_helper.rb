module PhoneCallReports<PERSON>elper
  def motivation_macro_catgories_for(phone_call_report)
    phone_call_report.motivation_macro_categories_options
  end

  def phone_call_type_report_icon(phone_call_report)
    if phone_call_report.incoming?
      tag.i class: ["call-type-icon", "fa", "fa-arrow-right", "green"], alt: "incoming"
    else
      tag.i class: ["call-type-icon", "fa", "fa-arrow-left", "red"], alt: "outgoing"
    end
  end
end
