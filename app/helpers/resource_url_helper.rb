module ResourceUrl<PERSON>elper
  def resource_link_to(resource)
    target_resource = target_resource_for resource
    link_to target_resource.id, resource_url_for(target_resource)
  end

  def resource_url_for(resource)
    resource = resource.order if resource.is_a?(AttivaOrder)
    
    parts = []
    parts << prefix_for(resource)
    parts << resource
    url_for parts.compact
  end

  def target_resource_for(resource)
    return resource.order if resource.is_a?(Fulfill)
    return resource.phone_activation if resource.is_a?(OperationOutcome)

    resource
  end

  # Returns a model name prefixed with a translated article
  #   e.g. given a phone_activation instance, it returns "l'attivazione" or "un'attivazione"
  #   requires setting the following translation keys:
  #     - activerecord.models.#{resource.model_name.i18n_key}.article_def
  #     - activerecord.models.#{resource.model_name.i18n_key}.article_indef
  #
  # @param resource [Object] The model you're presenting
  # @param use_indefinite [Boolean] Whether to use indefinite form
  def model_name_with_article(resource, use_indefinite: false)
    model_name = resource.model_name.human.downcase
    article_suffix = use_indefinite ? "article_indef" : "article_def"
    article = t("activerecord.models.#{resource.model_name.i18n_key}.#{article_suffix}")
    "#{article}#{model_name}"
  end

  def prefix_for(resource)
    :store if resource.is_a?(Order)
  end
end
