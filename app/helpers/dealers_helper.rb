module DealersHelper
  include Util::WithHoldingPayment

  def contract_terms_for
    current_user.is?(User::INTERNAL_USERS) ? ContractTerm.visible : @dealer.contract_terms
  end

  def contract_terms_visible?
    return false if @dealer.operator_tls?
    return true if @dealer.affiliated_from_contract_terms? && current_user.internal_user?

    !@dealer.md_very?
  end

  def count_down(warehouse)
    seconds_to_go = (warehouse.free_shipping_ends_at - Time.now).floor

    countdown_tag(:div, seconds_to_go, 'HH:MM:SS', class: 'text-center inline-countdown', extra_data: { autostart: true })
  end

  def dealer_kinds_select
    Dealer.kinds.collect { |k, v| [ Dealer.human_attribute_name(k), v ] }
                .delete_if { |k, v| v == 'operator_tls' && @dealer.drop_pay_account&.persisted? }
                .delete_if { |k, v| v == 'windtre_ecommerce' }
  end

  def enable_payment_methods
    PaymentMethod.enabled.not_default.order('description')
  end

  def free_shipping_warehouses_grouped(_dealer, free_shipping_warehouses)
    free_shipping_until_groups_sorted_by_date = free_shipping_warehouses
                                                  .group_by(&:free_shipping_ends_at)
                                                  .sort_by { |k, _v| k }

    free_shipping_until_groups_sorted_by_date
  end

  def fwa_document_links_for(documentable, context)
    render partial: 'dealers/fwa_installation/download_links', locals: { documentable: documentable, context: context }
  end

  def fwa_document_partial_for(context)
    context == 'archived_fwa_kolme_document' ? 'dealers/fwa_installation/archived_document' : 'dealers/fwa_installation/document'
  end

  def fwa_installation_documents_list(documentable, context)
    documents = documentable.fwa_document_scope_for(context)

    if context == 'archived_fwa_kolme_document'
      documents.sort_by do |document|
        name = document_content_name(document.documentable, context, document.content)
        [name, -document.created_at.to_i]
      end
    else
      document_contents = documentable.fwa_document_contents_for(context)
      existing_contents = documents.pluck(:content)

      build_missing_documents(documentable, context, document_contents - existing_contents)

      sort_documents(documents, document_contents)
    end
  end

  def have_sezionali?
    !(@dealer.sezionale_rk.zero? || @dealer.sezionale_ak.zero? || @dealer.sezionale_k.zero? || @dealer.sezionale_m.zero? || @dealer.sezionale_sk.zero? || @dealer.sezionale_oc.zero? || @dealer.sezionale_fc.zero? || @dealer.sezionale_tq.zero? || @dealer.sezionale_sg.zero? || @dealer.sezionale_hq)
  end

  def link_to_numbers
    return unless @dealer.operators.any? { |operator| operator.w3_or_very? }

    if current_user.is?(:admin, :super_user) || ApplicationSetting.my_numbers_enabled
      content_tag :div, class: 'pt-2 pb-2' do
        button_link_to 'Mostra avanzamento mese', numbers_dealer_path(@dealer), target: '_blank'
      end
    end
  end

  def link_to_tender_letter(dealer)
    if dealer.show_tender_letter_link?
      link_to dealer.tipo_gara, lettere_di_gara_dealers_url(dealer_id: dealer.id)
    else
      dealer.tipo_gara
    end
  end

  def mdv_document_links_for(documentable_object)
    render partial: 'dealers/mdv_affiliation/download_links', locals: { documentable: documentable_object, context: 'upload_mdv_document' }
  end

  def recharges_credit_available_visibile?
    SpazioEnv.kolme? && (current_user.can_access_insert_recharges? || @dealer.agents.include?(current_user))
  end

  def render_dealer_icons(dealer)
    dealer_segmentation = fa_icon(dealer.dealer_segmentation.icon, type: dealer.dealer_segmentation.icon_type) if dealer.dealer_segmentation.present?
    dealer_star         = icon_for_new_affiliate(dealer)
    tags_to_return      = (dealer_segmentation || '') + (dealer_star || '')
    tags_to_return.html_safe
  end

  def select_lead_sources
    Dealer::LEAD_SOURCES.map do |lead_source|
      [lead_source, lead_source]
    end
  end

  def select_tender_kinds
    Dealer::TENDER_KINDS.map do |tender_kind|
      [tender_kind, tender_kind]
    end
  end

  def translate_ritenuta_acconto(ritenuta_acconto)
    ritenuta_acconto == -1 ? 'Nessuna' : ritenuta_acconto
  end

  def summary_link(phone_number)
    link_to fa_icon('phone-square', class: 'green'),
            summary_dealers_path(
              search:        phone_number,
              outgoing_call: 1
            ),
            target: '_blank'
  end

  def phone_tag(phone)
    content_tag(:span, class: 'phone-number-container') do
      can?(:summary, Dealer) ? "#{phone} #{summary_link(phone)}".html_safe : phone
    end
  end

  def third_party_installations_until_at_label(dealer)
    "Fino al #{l dealer.third_party_installations_disabled_until_at}" unless dealer.third_party_installations_enabled?
  end

  private

  def any_dealer_thread_kinds
    ThreadKindService.new(dealer: current_user.dealer, user: current_user).find.any?
  end

  def build_missing_documents(documentable, context, missing_contents)
    missing_contents.each do |content|
      documentable.fwa_document_scope_for(context).build(
        kind: documentable.fwa_document_kind_for(context),
        content: content
      )
    end
  end

  def sort_documents(documents, document_contents)
    documents.sort_by { |doc| document_contents.index(doc.content) }
  end
end
