module <PERSON><PERSON><PERSON><PERSON><PERSON>
  def unwanted_gritter_messages
    [
      I18n.t('devise.failure.unauthenticated'),
      I18n.t('devise.failure.already_authenticated')
    ]
  end

  def recaptcha_tags_for(resource)
    user = User.find_by_username(resource.username)

    return unless user
    return if user.failed_attempts < Settings.recaptcha.attempts_before_captcha ||
      user.access_locked?

    content_tag(:div, id: 'recaptcha-flex-box', class: 'center-flex-aligner pre-otp-controls') do
      recaptcha_tags
    end
  end
end