module NgposOperationOutcomesHelper
  # FIELDS_WITH_DATEPICKER = %w(operation_outcome_activation_date_ok operation_outcome_operator_created_at_ok operation_outcome_receiving_date_ok operation_outcome_report_confirmated_at_ok operation_outcome_pdc_confirmed_at_ok).freeze

  def fields_for_ngpos_operation_outcome(ngpos_operation_outcome, options = {})
    return unless ngpos_operation_outcome&.ngpos_operation

    operation = ngpos_operation_outcome.ngpos_operation
    @fields = operation.fields

    if operation.id.in?(NgposOperation.ko_r_ids)
      @ko_reasons = KoReason.where(ngpos_operation_id: operation.id)
    end

    render partial: 'fields', locals: { f: options[:form_builder] } unless @fields.empty?
  end

  def actions_for_ngpos_operation_outcome(ngpos_operation_outcome, form_builder)
    @actions = ngpos_operation_outcome&.ngpos_operation&.actions

    render partial: 'ngpos_operation_outcomes/actions', locals: { f: form_builder } unless @actions.empty?
  end

  def name_for_ngpos_recovered_dealer(operation_outcome)
    operation_outcome.recovery_note.split('<br>').first.split('-').last.strip unless operation_outcome.recovery_note.nil?
  end

  def fields_for_ngpos_recovered_by_dealer(operation_outcome)
    if operation_outcome.ko_reason.action.include?('upload')
      'Upload effettuato'
    else
      "Note: #{operation_outcome.recovery_note.partition('<br>').last.strip}"
    end
  end

  def timeline_fields_for_ngpos_operation_outcome(operation_outcome, is_dealer = false)
    NgposOperationOutcomes::NgposOperationOutcomeRow.new(operation_outcome).render_timeline(is_dealer)
  end

  def recovery_ngpos_fields_for(operation_outcome)
    result = []
    if is_ngpos_recover_for_dealer?(operation_outcome)
      if is_a_ngpos_recovery_note?(operation_outcome)
        result << ngpos_recovery_note(operation_outcome)
      elsif is_a_ngpos_recovery_upload?(operation_outcome)
        if allows_pdc_download(operation_outcome)
          result << download_associated_pdc(operation_outcome)
        end
        if recover_ngpos_id_card_and_contract?(operation_outcome)
          result << upload_ngpos_id_card_and_contract(operation_outcome)
        end
        if recover_ngpos_id_card?(operation_outcome)
          result << upload_ngpos_id_card(operation_outcome)
        end
        if recover_ngpos_contract?(operation_outcome)
          result << upload_ngpos_contract(operation_outcome)
        end
      end
    end

    result.join(' ').html_safe
  end

  def is_ngpos_recover_for_dealer?(operation_outcome)
    operation_outcome.status == 'ko_r' &&
      operation_outcome.ko_reason &&
      operation_outcome.ko_reason.responsible == 'dealer' &&
      (operation_outcome.id == operation_outcome.wind_ngpos_activation.last_completed_operations.where(ngpos_operation_id: operation_outcome.ngpos_operation_id).pluck(:id).last) && !operation_outcome.recovered_by_dealer
  end

  def is_a_ngpos_recovery_note?(operation_outcome)
    operation_outcome.ko_reason.action == 'recovery_note'
  end

  def is_a_ngpos_recovery_upload?(operation_outcome)
    operation_outcome.ko_reason.action.split(':')[0] == 'upload'
  end

  def ngpos_recovery_note(operation_outcome)
    form_tag recover_wind_ngpos_activation_ngpos_operation_outcome_path(wind_ngpos_activation_id: operation_outcome.wind_ngpos_activation_id, id: operation_outcome.id), remote: true do
      concat text_area(:ngpos_operation_outcome, :recovery_note, class: 'recovery_note', id: "recovery_note_#{operation_outcome.wind_ngpos_activation_id}_#{operation_outcome.ngpos_operation_id}", value: '')
      concat positive_button_submit_tag(NgposOperationOutcome.human_attribute_name('recover'), id: "recovery_button_#{operation_outcome.wind_ngpos_activation_id}_#{operation_outcome.ngpos_operation_id}", disabled: true, class: 'disabled recover_button', disable_with: 'Attendere prego...')
    end
  end

  def allows_pdc_download(operation_outcome)
    operation_outcome.ko_reason_id.in? KoReason::ALLOWING_PDC_CONTEXTUAL_DOWNLOAD
  end

  def recover_ngpos_id_card?(operation_outcome)
    operation_outcome.ko_reason.action.split(':')[1] == 'id_card'
  end

  def recover_ngpos_contract?(operation_outcome)
    operation_outcome.ko_reason.action.split(':')[1] == 'contract'
  end

  def recover_ngpos_id_card_and_contract?(operation_outcome)
    operation_outcome.ko_reason.action.split(':')[1] == '[id_card,contract]'
  end

  def upload_ngpos_id_card(operation_outcome)
    @filter = 'id_card'
    document_uploader_for(operation_outcome.wind_ngpos_activation)
  end

  def upload_ngpos_contract(operation_outcome)
    @filter = 'contract'
    document_uploader_for(operation_outcome.wind_ngpos_activation)
  end

  def upload_ngpos_id_card_and_contract(operation_outcome)
    @filter = 'id_card_and_contract'
    document_uploader_for(operation_outcome.wind_ngpos_activation)
  end

  def download_associated_pdc(operation_outcome)
    associated_phone_activation = operation_outcome.wind_ngpos_activation.associated_phone_activation
    return unless associated_phone_activation

    content_tag(:div, class: 'form-aligner') do
      button_link_to 'Stampa PDC aggiornata',
                      download_pda_path_for(associated_phone_activation),
                      target: '_blank',
                      class:  'btn btn-primary'
    end
  end
end