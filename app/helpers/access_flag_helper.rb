module AccessFlagHelper
  def collection_for_access_flag_checkboxes
    visible_access_flags.map do |flag|
      popover = popover_tag(
        content: render(partial: 'users/operations_limitation_tooltip_content'),
        html:    true
      ) if flag.name == AccessFlag::DISABLE_DOWNLOAD_OPERATIONS

      label = DealerContact.human_attribute_name(flag.name)

      [
        "#{DealerContact.human_attribute_name(flag.name)} #{popover.presence}".strip.html_safe,
        flag.id,
        { id: "#{flag.name.parameterize.underscore}_checkbox", class: access_flags_class(label) }
      ]
    end
  end

  private

  def access_flags_class(label)
    classes = []
    classes << 'internal-user-only' if current_user.is?(:dealer) && label.in?(AccessFlag::INTERNAL_USER_ONLY_ACCESS_FLAGS)
    classes << 'md-very-toggle-field' if label.in?(AccessFlag::UNAVAILABLE_FOR_MDV_AFFILIATION_ACCESS_FLAGS)
    classes << 'md-very-affiliated-toggle-field' if label.in?(AccessFlag::DISABLED_FOR_MDV_AFFILIATION_ACCESS_FLAGS)
    classes << 'operator-tls-flags-toggle-field' if label.in?(AccessFlag::UNAVAILABLE_FOR_TLS_OPERATOR_ACCESS_FLAGS)
    classes << 'fwa-installer-only-flags-toggle-field' if label.in?(AccessFlag::UNAVAILABLE_FOR_FWA_INSTALLER_ONLY_ACCESS_FLAGS)
    classes << 'kolme-master-flags-toggle-field' if label.in?(AccessFlag::UNAVAILABLE_FOR_KOLME_MASTER_ACCESS_FLAGS)

    classes.join(' ')
  end

  def visible_access_flags

    access_flags = if current_user.is?(:dealer)
                     primary_contact_flags
                   else
                     AccessFlag.for_user(User.new(role: 'dealer')).to_a
                   end

    exclude_flags = []
    if !can?(:show_access_floa_financings, DealerContact) || @user.is?(:dealer) && !@user.new_record? && @user.operator_tls?
      exclude_flags << AccessFlag::FLOA_FINANCINGS
    end
    unless can?(:can_access_insert_recharges, DealerContact)
      exclude_flags << AccessFlag::RECHARGES
    end

    if (current_user.is?(:dealer) && @user.new_record?) || (current_user.is?(:dealer) && !current_user.dealer_contact.access_flags.pluck(:name).include?(AccessFlag::FWA_INSTALLER))
      exclude_flags << AccessFlag::FWA_INSTALLER
    end

    exclude_flags.any? ? access_flags.reject { |flag| exclude_flags.include?(flag.name) } : access_flags
  end

  def primary_contact_flags
    flags = current_user.dealer_contact.access_flags.to_a
    if flags.pluck(:name).include?(AccessFlag::ACTIVATIONS) &&
      !flags.pluck(:name).include?(AccessFlag::DISABLE_DOWNLOAD_OPERATIONS)
      flags << AccessFlag.disable_download_operations_flag
    end
    flags
  end
end
