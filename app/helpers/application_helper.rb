# encoding: utf-8

module ApplicationHelper
  include Bost::FormattingHelpers
  include Bost::Listing::Helpers
  include Bost::Duplicatable::Helpers

  prepend Gritter::Helpers

  NUMBER_STEP = 'any'.freeze

  def bootstrap_col_size_for(size)
    if size == 4
      'col-md-6'
    else
      "col-md-#{12 / size}"
    end
  end

  def count_clickable_link_to(target, *args, options)
    classes = "#{options[:class]} #{'count-clickable' if can?(:register_click, target)}"
    data    = (options[:data] || {}).merge(target_id: target.id)

    link_to(*args, options.except!(:class, :data).merge(class: classes, data: data))
  end

  def date_is_in_the_past?(date_string)
    return if date_string.blank?

    Date.parse(date_string) < Date.today
  end

  def embed_image_to_pdf(image)
    base64 = Base64.encode64(image.read).gsub(/\s+/, '')
    "data:#{image.content_type};base64,#{Rack::Utils.escape(base64)}"
  end

  def fido_enabled?
    can?(:show, IiRow) && !current_user.has_limited_operations_flag?
  end

  def fido_rechages_colspan
    [fido_enabled?, recharges_enabled?].all? ? 6 : 12
  end

  def font_awesome_icon_types
    %w(fas fab).collect { |t| [I18n.t("font_awesome_icon_type/#{t}"), t] }
  end

  def inputmask_currency_data_attributes
    { inputmask: "'alias': 'currency', 'groupSeparator': ''" }
  end

  def inputmask_date_data_attributes
    { inputmask: "'alias': 'datetime', 'inputFormat': 'dd/mm/yyyy', 'placeholder': 'gg/mm/aaaa'" }
  end

  def inputmask_negativecurrency_data_attributes
    { inputmask: "'mask': '(0)|(-9{1,10}[.9{1,2})]', 'nullable': true, 'greedy': false, 'placeholder': ' ',
                  'showMaskOnHover': false, 'showMaskOnFocus': false, 'rightAlign': true" }
  end

  def model_cache_key(model, additional_fields = [])
    Digest::MD5.hexdigest(([current_user.cache_keys, model.id, model.updated_at.to_i] + additional_fields).join)
  end

  def recharges_enabled?
    can?(:index, Recharge) && !current_user.has_limited_operations_flag?
  end

  def search_value_for(key)
    return unless params[:search].present?

    params[:search][key]
  end

  def status_bar(status, options = { theme: 'percentbar' })
    case options[:theme]
    when 'percentbar'
      content = content_tag(:div, class: 'status_bar percentbar') do
        tag.div(class: "status_#{status}")
      end
    when 'blocks'
      content = ''
      content << content_tag(:div, class: 'status_bar blocks') do
        10.times.each { |_t| content << (content_tag(:div, class: 'status_block total') {}) }
        status.times.each { |_t| content << (content_tag(:div, class: 'status_block partial') {}) }
      end
    end
    content.html_safe
  end

  def counter(priority)
    conditions = (priority.zero? ? ['status != 10'] : ['status != 10 and priority in (?)', PhoneActivation.priority_from_level(priority)])
    unless (counter = PhoneActivation.where(conditions).count) == 0
      "<span id='counter_#{priority}' class='counter'>#{counter}</span>".html_safe
    end
  end

  def h2_accordion_tag(name, options = {})
    if options[:show_visited]
      %(<h2 class="accordion open title"><span class="accordion_anchor">#{link_to name, '#'}</span> <span id="show_visited">#{link_to fa_icon('user'), '#', title: 'Mostra tutto'}</span></h2>).html_safe
    else
      %(<h2 class="accordion open title"><span class="accordion_anchor">#{link_to name, '#'}</span><img style='display:none' id="reloading_#{name.parameterize.underscore}" src='/assets/ajax-loader.gif'/></h2>).html_safe
    end
  end

  def h2_accordion_tag_small(name, id = '')
    %(<h2 id="#{id}" class="accordion accordion_small close">#{link_to name, '#'}</h2>).html_safe
  end

  def legacy_attributes_block(&block)
    content_tag(:div, class: 'legacy-attributes') do
      content_for(:javascript) do
        %{
          $(".legacy-attributes-title").live('click', function(){
              $(this).next('.legacy-attributes-fields').toggle();
          });
          }.html_safe
      end
      content_tag(:div, t('labels.legacy_attributes'), class: 'legacy-attributes-title') +
        content_tag(:div, class: 'legacy-attributes-fields', &block)
    end
  end

  def form_id(object)
    action = object.persisted? ? :edit : :new
    dom_id(object, action)
  end

  def current_controller?(controller, action = false)
    page             = Rails.application.routes.recognize_path request.fullpath
    controller_found = controller == page[:controller]
    action ? action == page[:action] && controller_found : controller_found
  end

  def document_uploader_for(documentable_object, context = 'upload')
    render partial: 'documents/form', locals: { documentable: documentable_object, context: context }
  end

  def document_links_for(documentable_object, context = 'upload')
    render partial: 'documents/download_links', locals: { documentable: documentable_object, context: context }
  end

  def icon_for_new_affiliate(dealer)
    return unless dealer.persisted?
    return fa_icon('ban', title: 'Dealer disattivo') if dealer.disabled?
    return if dealer.operator_tls?

    fa_icon('plug', title: 'Dealer affiliato da meno di 3 mesi') if dealer.created_at.to_date >= 3.months.ago
  end

  # wrapper for `link_to` that adds "btn btn-default" to the list of classes inside html_options
  def button_link_to(name = nil, options = nil, html_options = nil, &block)
    html_options         ||= {}
    css_classes          = html_options.fetch(:class, '').concat(' btn btn-default')
    html_options[:class] = css_classes
    link_to name, options, html_options, &block
  end

  def page_title(section_name, page_name = nil, icon = nil, title_class = nil)
    separator = ' / '
    if page_name
      html_title = %(<h1 #{"class=#{title_class}" if title_class}>#{fa_icon(icon) if icon} #{section_name}#{separator}<span class='page_name'>#{page_name}</span></h1>).html_safe
      text_title = "#{section_name.to_s.capitalize}#{separator}#{page_name.to_s.capitalize}"
    else
      html_title = %(<h1 #{"class=#{title_class}" if title_class}>#{fa_icon(icon) if icon} #{section_name}</h1>).html_safe
      text_title = "#{section_name.to_s.capitalize}"
    end
    @title_tag = "#{page_title_prefix}#{text_title}"

    html_title
  end

  def html_title_value(section_name, page_name = nil, icon = nil, title_class = nil)
    title = if page_name
              %(<h1 #{"class=#{title_class.presence}"}>#{fa_icon(icon) if icon} #{section_name} / <span class='page_name'>#{page_name}</span></h1>)
            else
              %(<h1 #{"class=#{title_class.presence}"}>#{fa_icon(icon) if icon} #{section_name}</h1>)
            end

    title.html_safe
  end

  def text_title_value(section_name, page_name = nil)
    text_title =
      if page_name
        "#{section_name.to_s.capitalize} / #{page_name.to_s.capitalize}"
      else
        "#{section_name.to_s.capitalize}"
      end

    "#{page_title_prefix}#{text_title}"
  end

  def page_title_prefix
    "#{'Staging ' if ['staging'].include?(Rails.env)} #{Settings.application.name} - "
  end

  def fa_number(number)
    %(<i class='fa number'>#{number}</i>).html_safe
  end

  def button_link_for_show(url, options = {})
    css_class       = (options[:class] ? (options[:class] + ' default_blu') : 'default_blu')
    options[:title] ||= 'Visualizza'
    options[:class] = css_class
    button_link_to fa_icon('eye'), url, options
  end

  def button_link_for_edit(url, options = {})
    css_class       = (options[:class] ? (options[:class] + ' grey') : 'grey')
    options[:title] = options[:title] || 'Modifica'
    options[:class] = css_class
    button_link_to fa_icon('gears'), url, options
  end

  def button_link_for_refresh(url, options = {})
    css_class       = (options[:class] ? (options[:class] + ' green') : 'green')
    options[:title] = 'Aggiorna'
    options[:class] = css_class
    button_link_to fa_icon('refresh'), url, options
  end

  def button_link_for_destroy(url, options = {})
    css_class       = (options[:class] ? (options[:class] + ' red') : 'red')
    options[:title] = options[:title] || 'Elimina'
    options[:class] = css_class
    button_link_to fa_icon('times-circle'), url, options
  end

  def button_link_for_duplicate(url, options = {})
    css_class       = (options[:class] ? (options[:class] + ' grey') : 'grey')
    options[:title] = 'Duplica'
    options[:class] = css_class
    button_link_to fa_icon('files-o'), url, options
  end

  def button_link_for_download(url, options = {})
    css_class       = (options[:class] ? (options[:class] + ' grey') : 'grey')
    options[:title] = 'Download'
    options[:class] = css_class
    button_link_to fa_icon('cloud-download-alt'), url, options
  end

  def button_link_for_rebuild(url, options = {})
    css_class       = (options[:class] ? (options[:class] + ' grey') : 'grey')
    options[:title] = 'Ricostruisci'
    options[:class] = css_class
    button_link_to fa_icon('wrench'), url, options
  end

  def link_for_export(url, options = {})
    css_class       = (options[:class] ? (options[:class] + ' grey') : 'grey')
    options[:title] ||= 'Esporta vista attuale'
    options[:class] = css_class
    text = options[:text] || ''
    link_to fa_icon('file-excel-o', text: text), url, options
  end

  def section_block(title = '', options = {}, &block)
    content_tag(:div, class: options[:class], id: options[:id], 'data-bind' => options['data-bind']) do
      content_tag(:p, title, class: (!title.empty? ? "block_title #{options[:class]}" : '')) +
        capture(&block)
    end.html_safe
  end

  def empty_titled_section_block(options = {}, &block)
    content_tag(:div, class: options[:class], id: options[:id], 'data-bind' => options['data-bind']) do
      content_tag(:p, '', class: "block_title #{options[:class]}") +
        capture(&block)
    end.html_safe
  end

  def devise_error_messages_custom!
    return '' if resource.errors.empty?

    messages = resource.errors.full_messages.map { |msg| content_tag(:li, msg) }.join
    sentence = I18n.t('errors.messages.not_saved',
                      count:    resource.errors.count,
                      resource: resource.class.model_name.human.downcase)

    html = <<-HTML
    <div id="error_explanation" class='panel panel-danger'>
      <div class="panel-heading">#{sentence}</div>
      <div class="panel-body"><ul>#{messages}</ul></div>
    </div>
    HTML

    html.html_safe
  end

  def devise_error_messages_custom_password!
    return '' if resource.errors.empty?

    messages = resource.errors.full_messages.map { |msg| content_tag(:li, msg) }.join
    sentence = I18n.t('errors.messages.not_saved',
                      count:    resource.errors.count,
                      resource: resource.class.model_name.human.downcase)

    messages.sub! 'Current password non può essere lasciato in bianco', 'La password corrente non può essere lasciata in bianco'
    messages.sub! 'Current password non è valido', 'La password corrente non è valida'
    messages.sub! 'Password non coincide', 'La nuova password non coincide'
    messages.sub! 'Password deve essere di almeno', 'La nuova password deve essere di almeno'
    messages.sub! 'Password deve contenere almeno', 'La nuova password deve contenere almeno'
    messages.sub! 'Reset password token non è valido', 'Richiesta non più valida, effettua un nuovo reset della password'
    messages.sub! 'Reset password token è scaduto, si prega di richiederne uno nuovo', 'Il link è scaduto, si prega di richiedere un nuovo reset della password.'

    html = <<-HTML
      <div id="error_explanation" class='panel panel-danger'>
        <div class="panel-heading">#{sentence}</div>
        <div class="panel-body"><ul>#{messages}</ul></div>
      </div>
    HTML

    html.html_safe
  end

  def nice_select(form_builder, name, collection, options = {})
    tag = if form_builder
            if options[:collection]
              form_builder.collection_select name, collection, options[:collection][0], options[:collection][1], options
            else
              form_builder.select name, collection, options
            end
          else
            if options[:collection]
              collection_select name, nil, collection, options[:collection][0], options[:collection][1], options
            else
              select name, nil, collection, options
            end
          end
    unless options.fetch(:hide_button, false)
      button = %(<span class="input-group-btn">
                  <button id="" class="btn btn-default" type="button">#{fa_icon 'angle-down'}</button>
                 </span>).html_safe
    end
    (tag + button)
  end

  def format_date_if_field(obj, field, format = :default)
    klass = obj.class.name
    res   = case klass
            when 'Hash'
              obj[field]
            when 'Ransack::Search'
              obj.send(field)
            else
              obj.try(field)
            end
    I18n.l(res, format: format) if res.present?
  end

  def sort_caret_icon(sort_field_name)
    if params[:sort] && params[:sort][:field] == sort_field_name
      fa_icon params[:sort] && params[:sort][:field] == sort_field_name && params[:sort][:type] == 'desc' ? 'caret-down' : 'caret-up'
    end
  end

  def ga_snippet(onlydealer: true)
    return if onlydealer && !current_user&.is?(:dealer)

    Settings.analytics&.snippet&.html_safe
  end

  def render_progressbar_for_index(obj, _color = 'progress-bar-info')
    current_user.is?(:dealer) ? render_progressbar(obj, color = 'progress-bar-info') : render_progressbox(obj, color = 'progress-bar-info')
  end

  def render_progressbar(obj, color = 'progress-bar-info')
    render_progressbar_for(obj, color, true)
  end

  def render_progressbox(obj, color = 'progress-bar-info')
    render_progressbar_for(obj, color, false)
  end

  def render_progressbar_for(obj, color = 'progress-bar-info', for_dealer = true, reload_obj = true)
    obj.reload if reload_obj

    if for_dealer
      render partial: 'shared/dealer_progress_bar', locals: { obj: obj, color: color }
    else
      if obj.is_a?(PhoneActivation)
        @operation_operation_outcomes = PhoneActivations::PhoneActivationProgressBar.new(obj).visible_operations

        render partial: 'shared/non_dealer_progress_bar', locals: { obj: obj, color: color }
      end
    end
  end

  def true_or_false(bool)
    bool ? 'Sì' : 'No'
  end

  def email_logo
    "#{Settings.application.full_url}/assets/email/logo_kolme_clean.png"
  end

  def error_image
    'kolme_error.png'
  end

  def favicon_for_environment
    favicon_link_tag Bost::Application.config.favicon
  end

  def logo_for_environment
    'logo_kolme.png'
  end

  def title_image_for_cms
    'spazio.jpg'
  end

  def login_logo_for_environment
    "spazio_kolme_logo.png?#{Time.now.to_i}"
  end

  def back_to_system_for_environment
    'back_to_system_kolme.png'
  end

  # input: a string made of up to ten characters     (3291234567)
  # output: the string with a space after the prefix (329 1234567)
  def formatted_phone_number(string)
    string.scan(/(\d{3})(\d{6,7})/).flatten.join(' ')
  end

  def facet_translation(facet, scope = nil)
    key = [scope, :facets, facet.underscore].compact.join('.')
    I18n.t key, default: ActiveRecord::Base.human_attribute_name(facet)
  end

  # model
  # field_name (e.g. :note)
  # url
  # title
  def in_place_edit_for(model, field_name, url, title, custom_class = nil, mode = :best_in_place)
    if mode == :best_in_place
      best_in_place(
        model,
        field_name,
        url:          url,
        place_holder: title,
        class:        custom_class
      )
    end
  end

  def badge_for(counter, custom_class = 'badge')
    "<span class='#{custom_class}'>#{counter}</span>".html_safe if counter && counter > 0
  end

  # Presents a model field, using the following structure (to be used in a <dl> tag):
  # <dt>label</dt>
  # <dd>value</dd>
  #
  # @param [Object] model The model you're presenting
  # @param [Symbol] field_name The field you're presenting.
  # @param [Hash] opts Options.
  #
  # @option opts [Symbol] :as present the field value using a special strategy.
  #                           Accepted values are :check_box, :text_area, :html, :link, :translation, :image
  # @option opts [Boolean] :unless_blank only present the field if value.present?
  # @option opts [Symbol]  :version image version to show (only when as: :image)
  def model_show_field(model, field_name, options = {}, &block)
    field_label = content_tag(:dt) { options.fetch(:label, model.class.human_attribute_name(field_name)) }
    field_value = if block_given?
                    content_tag(:dd, &block)
                  else
                    content_tag(:dd) { present_field_value(model, field_name, options.fetch(:as, nil), options) }
                  end
    return if options.fetch(:unless_blank, false) && field_value == content_tag(:dd)

    field_label + field_value
  end

  def model_show_field_with_link(model, field_name, options = {})
    return unless model.present?

    show_field_options = {
      as:           :link,
      unless_blank: true,
      link:         options
    }
    show_field_options[:label] = options[:label] if options[:label].present?
    model_show_field(model, field_name, show_field_options)
  end

  def present_field_value(model, field_name, present_as, options = {})
    scope       = options.fetch(:scope, nil)
    field_value = model.send(field_name)
    case present_as.to_s
    when 'check_box'
      check_box_tag field_name, '1', field_value, disabled: true
    when 'boolean_text'
      true_or_false(field_value)
    when 'text_area'
      text_area_tag field_name, field_value, disabled: true
    when 'html'
      (content_tag('div') { field_value.to_s.html_safe }) if field_value.present?
    when 'link'
      link_to field_value.to_s, options[:link][:custom_link] || model, options[:link]
    when 'translation'
      I18n.t([scope, field_value.to_s].join('.'))
    when 'image'
      version = options.fetch(:version, :self)
      image_tag model.send(field_name).send(version).url
    when 'code'
      content_tag :pre do
        model.send(field_name)
      end
    when 'short_date'
      l(model.send(field_name), format: :short)
    when 'formatted_date'
      l model.send(field_name)
    else
      field_value.to_s.html_safe
    end
  end

  def resources_info(resources)
    content_tag(:div, page_entries_info(resources), class: 'page_info')
  end

  def pagination_box(resources, options = {})
    if resources.total_pages > 1
      content_tag(:div, will_paginate(resources, options))
    end
  end

  def present(model, presenter_class = nil)
    klass     = presenter_class || "#{model.class}Presenter".constantize
    presenter = klass.new(model, self)
    if block_given?
      yield(presenter)
    else
      presenter
    end
  end

  def warehouses_prompt_for(user)
    user.dealer.warehouses.count > 1 ? t('simple_form.labels.warehouse.warehouse_id') : nil
  end

  def spazio_routes
    Rails.application.routes.url_helpers
  end

  def forum_routes
    Thredded::Engine.routes.url_helpers
  end

  def value_for_tags(tags_array)
    tags_array.join(',') if tags_array.present?
  end

  def array_to_ul(array_of_strings)
    string = '<ul>'
    array_of_strings.each { |each_string| string += "<li>#{each_string}</li>" }
    string += '</ul>'
  end

  def open_dealer_sdi_update_modal?
    return unless current_user&.is?(:dealer)

    !current_user.dealer.has_valid_sdi_forwarder_code?
  end

  def open_dealer_signed_contract_modal?
    return unless current_user && current_user.is?(:dealer) && current_user.dealer_contact.primary_contact?
    return if controller.controller_path == 'dealers' && controller.action_name == 'sign_contract'
    return unless current_user.dealer.with_signed_contracts_to_sign

    current_user.dealer.unsigned_contract.created_at <= ApplicationSetting.days_to_sign_contract.days.ago # .beginning_of_day
  end

  def opening_hours_friendly_format(opening_hours)
    return unless opening_hours

    before_noon = opening_hours['before_noon']
    after_noon  = opening_hours['after_noon']

    "Mattina: #{before_noon['from']} alle #{before_noon['to']}, Pomeriggio: #{after_noon['from']} alle #{after_noon['to']}"
  end

  def popover_tag(options = { placement: 'right' })
    link_to fa_icon('info-circle', text: options[:text]), 'javascript:void(0)',
            data: { toggle:    'popover',
                    html:      options[:html],
                    placement: options[:placement],
                    container: 'body',
                    trigger:   'hover',
                    content:   options[:content] }
  end

  def month_name(date, offset = 0)
    I18n.t('date.month_names')[1..-1][date.month - 1 + offset]
  end

  def even_odd_class(index)
    return 'even' if index.zero? || index.even?

    'odd'
  end
end
