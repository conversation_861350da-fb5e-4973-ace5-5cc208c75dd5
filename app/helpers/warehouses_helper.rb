module WarehousesHelper
  def compass_branch_tooltip(compass_branch)
    content_tag :div, ["Indirizzo: #{compass_branch.address}",
                       "Comune: #{compass_branch.city.name}",
                       "Recapito: #{compass_branch.phone_number}"].join('<br/>'), class: 'pl-1 pr-1'
  end

  def map_for(warehouse)
    render partial: "map", locals: { lat: warehouse.latitude, lng: warehouse.longitude, name: warehouse.name.titleize }
  end

  def findomestic_referent_info(findomestic_referent)
    ["Email: #{findomestic_referent.email}",
     "Numero: #{findomestic_referent.number}"].join('<br/>')
  end

  def shopping_mall_select_options
    [['No', false], ['Si', true]]
  end

  def weekly_closing_day_options
    ['nessuno'] + I18n.t('date.day_names')
  end

  def options_for_partnership_acceptance(warehouse, warehouse_brand)
    state_options = WarehouseBrandsWarehouse::PARTNERSHIP_ACCEPTED_STATES.map do |state|
      [WarehouseBrandsWarehouse.human_attribute_name("partnership_accepted/#{state}"), state]
    end

    selected_state = warehouse.partnership_acceptance_state(warehouse_brand)
    selected_state ||= ""

    options_for_select([["", ""]] + state_options, selected: selected_state)
  end

  def select_clusters
    Warehouse::CLUSTERS.map { |cluster| [Warehouse.human_attribute_name(cluster), cluster] }
  end

  def select_dealer_kinds
    Warehouse::DEALER_KINDS.map do |dealer_kind|
      [dealer_kind, dealer_kind]
    end
  end

  def pos_codes_for_override
    ApplicationSetting.pos_override_values.map do |pos_code_setting|
      ["#{pos_code_setting.description}: #{pos_code_setting.value}", pos_code_setting.id]
    end
  end

  def select_fwa_installer_search
    [
      ['Installatori abilitati', 'fwa_installer_enabled'],
      ['Installatori Conto Terzi', 'third_party_installations'],
      ['Installatori a tetto', 'enabled_to_work_at_height'],
      ['Installatori in attesa documentazione', 'awaiting_documents'],
      ['Installatori con documentazione da verificare', 'awaiting_documents_validation'],
      ['Installatori in attesa abilitazione', 'fwa_installer_awaiting_activation']
    ]
  end
end
