module ItemsHelper
  def link_field_for_booked_item
    return link_to @item.order.id, store_order_path(@item.order) if @item.order.present?

    "Prenotazione in fase di conferma"
  end

  def dealer_can_set_item_as_sold?(item)
    item.instock?
  end

  def dealer_can_change_item_warehouse?(item)
    current_user.dealer.warehouses.enabled.count >= 2 && item.instock?
  end

  def selected_product_type(search_params = {})
    search_params[:product_product_type_eq] || (current_user.is?(:agent) || current_user.is?(:sub_agent)) && filters_state_clean? && ProductType::ACTIVABLE_PRODUCT
  end

  def selected_state(search)
    search.state_eq || (current_user.is?(:agent) || current_user.is?(:sub_agent)) && filters_state_clean? && :instock
  end

  def default_items_params_for(dealer)
    {
      search: {
        warehouse_name_eq:       ((dealer.warehouses.first&.name).to_s if dealer.warehouses.count == 1),
        state_eq:                :instock,
        product_product_type_eq: :non_sim
      }.compact
    }
  end

  def items_massive_select_options
    return [["Pagina Corrente", "1"], ["Nessun articolo", "3"]] if @total_entries > 5000

    [["Pagina Corrente", "1"], ["Lista completa (#{@total_entries})", "2"], ["Nessun articolo", "3"]]
  end

  def toggle_item_events_modal_button(item)
    button_link_to fa_icon("camera-retro"),
                   item_refresh_item_events_modal_path(item),
                   title:  "Visualizza i cambiamenti",
                   class:  "dark_yellow btn btn-default",
                   remote: true,
                   data:   { "item_id" => item.id }
  end

  def product_name_width_for(current_user)
    current_user.is?(:dealer) ? "col-md-4 pl-0" : "col-md-2 pl-0"
  end

  def get_title
    @title
  end

  def state_label_for_role(item)
    if current_user.is?(:dealer) && item.booked?
      "Allocato - In partenza"
    else
      item.aasm.human_state
    end
  end

  def editable_states_for(current_user, state)
    return Item::STATES_FOR_NON_ADMIN.map(&:to_s) if @item.present_in_a_related_object? && !current_user.is?(:admin)
    return Item::STATES.map(&:to_s) unless current_user.is?(:user)

    if state.to_sym.in?(Item::STATES_UNLEAVABLE_BY_USER)
      []
    elsif state.to_sym.in? Item::STATES_UNREACHABLE_BY_USER
      Item::STATES_FOR_USER.map(&:to_s)
    else
      (Item::STATES_FOR_USER - Item::STATES_UNREACHABLE_BY_USER).map(&:to_s)
    end
  end

  private

  def filters_state_clean?
    request.parameters[:commit] == I18n.t("search.clean") && params[:clean_agent_role_filters].blank?
  end
end
