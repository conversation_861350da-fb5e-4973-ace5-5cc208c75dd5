<% present(@after_sale_thread) do |after_sale_thread| %>
  <% present(after_sale_thread.threadable) do |threadable| %>
    <div class="row">
      <div class="col-md-6">
        <dl class="dl-horizontal model-fields mb-0">
          <%= model_show_field after_sale_thread, :dealer_name %>
          <%= model_show_field_with_link after_sale_thread.phone_activation,
                                         :hex_id,
                                         label:  t('activerecord.attributes.after_sale_thread.phone_activation_hex_id'),
                                         target: '_blank' unless current_user.is?(:dealer) %>
          <%= model_show_field after_sale_thread, :phone_activation_hex_id if current_user.is?(:dealer) %>
        </dl>
      </div>
      <div class="col-md-6">
        <dl class="dl-horizontal model-fields extra mb-0">
          <%= model_show_field threadable, :convergence_fixed_number, { unless_blank: true } %>
          <%= model_show_field threadable, :convergence_mobile_number, unless_blank: true %>
          <%= model_show_field threadable, :traffic_share_mobile_numbers, unless_blank: true %>
          <%= model_show_field threadable, :request_kind, unless_blank: true do %>
            <%= AstConvergenceRebuild.human_attribute_name("request_kind/#{threadable.request_kind}") %>
          <% end %>
        </dl>
      </div>
    </div>
    <%= render partial: 'after_sale_threads/details/ast_notes_full_width', locals: { threadable: threadable } %>
  <% end %>
<% end %>
