<% present(@after_sale_thread) do |after_sale_thread| %>
  <% present(after_sale_thread.threadable) do |threadable| %>
    <div class="row">
      <div class="col-md-6">
        <dl class="dl-horizontal model-fields mb-0">
          <%= model_show_field after_sale_thread, :dealer_name %>
          <%= model_show_field_with_link after_sale_thread.phone_activation,
                                         :hex_id,
                                         label:  t('activerecord.attributes.after_sale_thread.phone_activation_hex_id'),
                                         target: '_blank' unless current_user.is?(:dealer) %>
          <%= model_show_field after_sale_thread, :phone_activation_hex_id if current_user.is?(:dealer) %>
        </dl>
      </div>
    </div>
    <%= render partial: 'after_sale_threads/details/ast_notes_full_width', locals: { threadable: threadable } %>
  <% end %>
<% end %>
