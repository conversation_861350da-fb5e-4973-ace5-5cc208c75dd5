<% present(@after_sale_thread) do |after_sale_thread| %>
  <% present(after_sale_thread.threadable) do |threadable| %>
  <div class="row">
    <div class="col-md-6">
      <dl class="dl-horizontal model-fields mb-0">
        <%= model_show_field after_sale_thread, :dealer_name %>
        <%= model_show_field_with_link after_sale_thread.phone_activation,
                                       :hex_id,
                                       label:  t('activerecord.attributes.after_sale_thread.phone_activation_hex_id'),
                                       target: '_blank' unless current_user.is?(:dealer) %>
        <%= model_show_field after_sale_thread, :phone_activation_hex_id if current_user.is?(:dealer) %>
        <%= model_show_field after_sale_thread, :customer_full_name, unless_blank: true %>
        <%= model_show_field threadable, :unsolved_amount, unless_blank: true %>
      </dl>
    </div>
    <div class="col-md-6">
      <dl class="dl-horizontal model-fields mb-0">
        <%= model_show_field threadable,
                             :payment_method,
                             as:           :translation,
                             scope:        'values.models.ast_unsolved_payment_proof.payment_method',
                             unless_blank: true %>
        <%= model_show_field threadable, :vcy, unless_blank: true, label: 'VCY' %>
        <%= model_show_field threadable, :cro, unless_blank: true %>
      </dl>
    </div>
  </div>
  <%= render partial: 'after_sale_threads/details/ast_notes_full_width', locals: { threadable: threadable } %>
  <% end %>
<% end %>
