<%= simple_form_for @after_sale_thread, html: { id: 'basic-data-section' } do |f| %>
  <div class='row' id='basic_data_section_container' data-coming-from-phone-activation-page="true<%= coming_from_phone_activation_page %>">
      <% if current_user.is? :dealer %>
          <div class='col-md-4'>
            <%= f.input :dealer_id, as: :hidden, input_html: { value: current_user.dealer.id } %>
            <%= f.input :warehouse_id,
                        as:         :select,
                        collection: current_user.dealer.warehouses,
                        label:      false,
                        prompt:     warehouses_prompt_for(current_user),
                        input_html:       {
                            data: {
                                url: warehouses_dealer_url(id: ':id'),
                                value: f.object.warehouse_id || nil
                            }
                        }
            %>
          </div>
      <% else %>
          <div class='col-md-4'>
            <div class="field">
              <div class="input select">
                <%= f.autocomplete_field :dealer,
                                         autocomplete_dealer_with_thread_kind_filters_dealers_path,
                                         id_element:  '#after_sale_thread_dealer_id',
                                         placeholder: t('simple_form.labels.after_sale_thread.dealer'),
                                         readonly:    (f.object.readonly_field?('dealer'))
                %>
                <%= f.input :dealer_id, as: :hidden %>
                <span class="hide"
                      data-dealer-operator-ids="<%= @after_sale_thread.try(:dealer).try(:operator_ids) %>"
                      id="dealer_operator_ids">
              </span>
              </div>
            </div>
          </div>
          <div class='col-md-4'>
            <div class="field">
              <div class="input select">
                <%= f.input :warehouse_id,
                            as:         :select,
                            collection: (f.object.dealer_warehouses || []),
                            readonly:   (f.object.readonly_field?('warehouse')),
                            input_html:       {
                                data: {
                                    url: warehouses_dealer_url(id: ':id'),
                                    value: f.object.warehouse_id || nil
                                }
                            },
                            label:      false,
                            prompt:     t('simple_form.labels.after_sale_thread.warehouse_id')

                %>
              </div>
            </div>
          </div>
      <% end %>
      <div class='col-md-4'>
        <div class="field">
          <div class="input select">
            <%= f.input :thread_kind_id,
                        as:         :select,
                        collection: [],
                        input_html:       {
                            data: {
                                url: find_for_dealer_thread_kinds_path,
                                value: f.object.thread_kind_id || nil
                            }
                        },
                        label:      false,
                        prompt:     t('simple_form.labels.after_sale_thread.thread_kind')

            %>
          </div>
        </div>
      </div>
    </div>
<% end %>
