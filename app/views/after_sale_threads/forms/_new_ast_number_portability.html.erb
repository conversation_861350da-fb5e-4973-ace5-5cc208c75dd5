<div class="row" id='ast-number-portability-form'>
  <%= f.fields_for :threadable do |t| %>
    <div class="col-md-12">
    <%= render partial: 'after_sale_threads/sections/thread_kind_details', locals: { f: f } %>
    </div>
    <div class="col-md-8">
    <%= render partial: 'after_sale_threads/sections/phone_activation_or_customer_search', locals: { t: t, f: f } %>
    </div>
    <div class="ast-container">
      <div class="col-md-8">
        <%= t.association :operator, wrapper_class: 'field mt-2', collection: operators_for(f.object, current_user) %>
        <%= t.input :target_number, wrapper_class: 'field' %>
        <%= t.input :target_sim_serial, wrapper_class: 'field' %>

        <%= t.input :source_portability_operator,
                    as:            :select,
                    wrapper_class: 'field',
                    label_method:  :label,
                    value_method:  :group_value,
                    collection:    portability_operators,
                    input_html:    { 'data-value' => t.object.source_portability_operator } %>
        <%= t.input :custom_source_operator,
                    wrapper_class: 'field',
                    wrapper_html:  { id: 'number_portability_custom_source_operator_wrap' } %>
        <%= t.input :source_number, wrapper_class: 'field' %>
        <%= t.input :source_sim_serial, wrapper_class: 'field' %>
        <%= t.input :source_contract_kind,
                    wrapper_class: 'field',
                    as:            :select,
                    collection:    AstNumberPortability::CONTRACT_KINDS.map { |k|
                      [I18n.t("activerecord.attributes.ast_number_portability.contract_kinds.#{k}"), k]
                    } %>
        <%= t.input :credit_transfer, wrapper_class: 'field' %>
        <%= t.input :different_accountholder,
                    wrapper_class: 'field',
                    input_html:    { 'data-toggle' => 'collapse', 'data-target' => '#accountholder_data' } %>
        <div id="accountholder_data" class="<%= t.object.different_accountholder ? 'in' : 'collapse' %>">
          <%= t.input :accountholder_first_name, wrapper_class: 'field' %>
          <%= t.input :accountholder_last_name, wrapper_class: 'field' %>
          <%= t.input :accountholder_cf_or_iva, wrapper_class: 'field' %>
          <%= t.input :accountholder_birth_date,
                      wrapper_class: 'field',
                      input_html:    {
                          class: 'datepicker'
                      } %>
          <!-- TODO autocomplete città provincia -->
          <%= t.input :accountholder_birth_place, wrapper_class: 'field' %>
          <div class="field">
            <%= t.select :accountholder_birth_country_id, Country.for_select, prompt: 'Seleziona lo stato...' %>
          </div>
          <%= t.input :accountholder_id_doc_kind,
                      wrapper_class: 'field',
                      as:            :select,
                      collection:    Customer::DocKind %>
          <%= t.input :accountholder_id_doc_date,
                      wrapper_class: 'field',
                      input_html:    {
                          class: 'datepicker'
                      } %>
          <%= t.input :accountholder_id_doc_number, wrapper_class: 'field' %>
        </div>
        <%= t.input :notes, label: 'Note (facoltative)' %>
      </div>
    </div>
  <% end %>
</div>
