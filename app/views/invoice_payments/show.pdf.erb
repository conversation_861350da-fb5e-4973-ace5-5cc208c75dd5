<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
  <style>
    @font-face {
      font-family: 'SF Pro Text';
      src: url("<%= font_url('SF-Pro-Text-Light.otf', host: Rails.application.routes.url_helpers.root_url) %>") format('opentype');
      font-weight: 300;
      font-style: normal;
    }

    @font-face {
      font-family: 'SF Pro Text';
      src: url("<%= font_url('SF-Pro-Text-Semibold.otf', host: Rails.application.routes.url_helpers.root_url) %>") format('opentype');
      font-weight: bold;
      font-style: normal;
    }

    body {
      font-size: 11px;
      background-color: #FFFFFF;
      font-family: 'SF Pro Text', sans-serif;
    }

    /*#fattura {*/
    /*  z-index:1;*/
    /*  background-color: #FFFFFF;*/
    /*}*/

    /*#fattura table {*/
    /*  background-color: #FFFFFF;*/
    /*  border-collapse: collapse;*/
    /*  width: 100%;*/
    /*}*/

    /*#fattura table tr {*/
    /*  background-color: #FFFFFF;*/
    /*  border-top: medium none;*/
    /*}*/

    /*#fattura table#external tr td {*/
    /*  padding: 10px;*/
    /*}*/

    /*#fattura table tr:first-child {*/
    /*  background-color: #FFFFFF;*/
    /*}*/

    /*#fattura table tr td {*/
    /*  background-color: #FFFFFF;*/
    /*  text-align: left;*/
    /*}*/

    /*#fattura table td {*/
    /*  border: 1px solid #000000;*/
    /*  height: 20px !important;*/
    /*}*/

    /*#fattura table#rows {*/
    /*  border-bottom: 1px solid #000000;*/
    /*}*/

    /*#fattura table#rows tr {*/
    /*  border-left: 1px solid #000000;*/
    /*  border-right: 1px solid #000000;*/
    /*}*/

    /*#fattura table#rows tr td {*/
    /*  border-style: none solid;*/
    /*  border-width: medium 1px;*/
    /*}*/

    /*#fattura table#rows tr.top-bottom {*/
    /*  border-bottom: 1px solid #000000;*/
    /*  border-top: 1px solid #000000;*/
    /*}*/

    /*#fattura table#rows tr.center td {*/
    /*  text-align: center;*/
    /*}*/

    /*#fattura table#rows tr.odd td {*/
    /*  background-color: #E6E6E6;*/
    /*}*/

    /*#fattura table#rows td#grey {*/
    /*  background-color: #BDBDBD;*/
    /*}*/

    /*#fattura table#totals {*/
    /*  border-color: #000000;*/
    /*  border-style: solid;*/
    /*  border-width: 1px;*/
    /*}*/

    /*#fattura table#totals td {*/
    /*  border: medium none;*/
    /*}*/

    /*#fattura table#totals tr {*/
    /*  border-left: 1px solid #000000;*/
    /*  border-right: 1px solid #000000;*/
    /*}*/

    .page-break {
      display: block;
      clear: both;
      page-break-after: always;
    }

    #background {
      position: absolute;
      display: block;
      min-width: 100%;
      opacity: 0.5;
      text-align: center;
      background-color: transparent;
      padding-top:50%;
      z-index: 9999;
    }
    #bg-text {
      color: lightgrey;
      font-size: 120px;
      transform: rotate(300deg);
      -webkit-transform: rotate(300deg);
      opacity: 0.9;
      filter: alpha(opacity=50);
      background-color: transparent;
    }

    /* new layout styles */

    .wrapper-el {
      height: 1300px;
      position: relative;
    }
    .invoice-header-table {
      width: 100%;
      margin-bottom: 20px;
      border-collapse: separate;
      border-spacing: 0 20px
    }
    .invoice-header-table td {
      vertical-align: top;
    }
    .invoice-header-address, .document-details {
      padding-left: 10px;
    }
    .invoice-header-address {
      border-left: 1px solid #005297;
    }
    .invoice-table {
      width: 100%;
    }
    .invoice-table-header {
      color: #005297;
      font-size: 1.5em;
      padding-bottom: 5px;
      border-bottom: 1px solid #005297;
      margin-bottom: 10px;
      text-align: center;
    }
    .invoice-table {
      border-spacing: 4px 4px;
      border-collapse: separate;
    }
    .invoice-table th {
      color: #005297;
      background-color: transparent;
      font-weight: normal;
    }
    .invoice-table td,
    .invoice-table th {
      padding: 8px; text-align: left;
    }
    .invoice-table tr:nth-child(odd) td {
      background-color: #E5E3E3;
    }
    .invoice-table tr:nth-child(even) td {
      background-color: #f2f2f2;
    }
    .invoice-table th.total-currency,
    .invoice-table td.total-currency {
      font-weight: bold;
    }
    hr {
      margin: 20px 0;
      background-color: #005297;
      border: none;
      height: 1px;
    }
    .totals-table {
      margin: 0 20px 20px 0;
      width: fit-content;
      border-collapse: separate;
      border-spacing: 45px 0;
      float: right;
      padding: 20px 20px;
    }
    .totals-table th, .totals-table td {
      white-space: nowrap;
      text-align: left;
    }
    .reminder-block {
      clear: both;
      margin-top: 20px;
      padding-top: 10px;
      border-top: 1px solid #005297;
      text-align: center;
      color: #005297;
      font-size: 1em;
    }
    .footer {
      position: absolute;
      bottom: 10px;
      right: 10px;
      width: 100%;
    }
    .footer .center { text-align: center; }
    .footer .right {
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
    }
    .kolme-logo {
      display: block;
      margin-left: auto;
      margin-right: auto;
      width: 22.8mm;
      height: 7.2mm;
    }
  </style>
</head>
<body class="wicked">
<div id="content">
  <% pages.each_with_index do |group, group_index| %>
    <%= render partial: 'invoice_payments/detail_new', locals: { group: group,
                                                                 group_index: group_index,
                                                                 invoice_payment: invoice_payment,
                                                                 pages: pages } %>
  <% end %>
</div>
</body>
</html>
