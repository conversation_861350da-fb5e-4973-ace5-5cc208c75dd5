<%= page_title("affiliazioni", affiliation_page_title) %>
<div class='affiliation'>

  <%= subscribe_to "/affiliation_timeline/#{@affiliation.id}" %>
  <div id="outcome-form-modal-container"></div>
  <div id="list-forms-modal-container"></div>
  <div class="affiliation_timeline">
    <%= render partial: 'affiliations/affiliation_timeline' %>
  </div>
  <hr/>
  <div class="content sections row">
    <div class="col-md-6">
      <%= section_block 'Dati Affiliazione' do %>
        <p>
          <b><%= Affiliation.human_attribute_name(:source) %></b>
          <span><%= Affiliation.human_attribute_name("sources/#{@affiliation.source}") %></span>
        </p>
        <p>
          <b><%= Affiliation.human_attribute_name(:agent_id) %></b>
          <span><%= @affiliation.agent&.full_name %></span>
        </p>
        <p>
          <b><%= Affiliation.human_attribute_name(:sub_agent_id) %></b>
          <span><%= @affiliation.sub_agent&.full_name %></span>
        </p>
        <p>
          <b><%= Affiliation.human_attribute_name(:dealer_operators) %></b>
          <span><%= Affiliation.human_attribute_name("dealer_operators/#{@affiliation.additional_data_obj.dealer_operators}") %></span>
        </p>
        <p>
          <b><%= Affiliation.human_attribute_name(:dealer_type) %></b>
          <span><%= Affiliation.human_attribute_name("dealer_types/#{@affiliation.additional_data_obj.dealer_type}") %></span>
        </p>
      <% end %>

      <%= render partial: 'affiliations/dealer_data_show' %>
      <%= render partial: 'affiliations/warehouse_data_show' %>
    </div>

    <div class="col-md-6">
      <%= render partial: 'affiliations/bank_details_show' %>
      <%= render partial: 'affiliations/dealer_contacts_data_show' %>

      <%= section_block 'Contatti aggiuntivi' do %>
        <p>
          <b><%= Dealer.human_attribute_name(:pec) %></b>
          <span><%= @affiliation.dealer.pec %></span>
        </p>
        <p>
          <b><%= Dealer.human_attribute_name(:telefono_amministrativo) %></b>
          <span><%= @affiliation.dealer.telefono_amministrativo %></span>
        </p>
        <p>
          <b><%= Dealer.human_attribute_name(:email_amministrativa) %></b>
          <span><%= @affiliation.dealer.email_amministrativa %></span>
        </p>
        <% unless @affiliation.kolme_master_nexus? %>
          <p>
            <b><%= Affiliation.human_attribute_name(:dealer_email) %></b>
            <span><%= @affiliation.dealer_email %></span>
          </p>
        <% end %>
      <% end %>
    </div>
  </div>

  <div class="row">
    <div class="col-md-6">
      <% if can?(:edit, @affiliation) %>
        <%= button_link_to 'Modifica', edit_affiliation_path(@affiliation) %>
      <% end %>
      <%= button_link_to 'Torna alla lista', affiliations_path %>
    </div>
  </div>
</div>