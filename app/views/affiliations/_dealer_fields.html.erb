<%= section_block 'Anagrafica Partner' do %>
  <%= dealer_form.hidden_field :origin, value: 'affiliation' %>

  <%= additional_data_form.fields_for :dealer do |dealer_additional_data| %>
    <div class="field">
      <%= dealer_form.input :ragione_sociale, input_html: { maxlength: 40, class: 'title' }, required: false %>
    </div>
    <div class="field">
      <%= dealer_form.input :ditta_individuale,
                            as: :select,
                            collection: [['Sì', true], ['No', false]],
                            include_blank: true,
                            input_html: { disabled: f.object.affiliation_outcome_documents_check&.status_ok? }
      %>
      <%= dealer_form.hidden_field :ditta_individuale if f.object.affiliation_outcome_documents_check&.status_ok? %>
    </div>
    <div class="field <%= 'field_with_errors' if f.object&.errors.attribute_names&.include?(:dealer_company_kind) %>">
      <%= label_tag Affiliation.human_attribute_name('company_kind') %>
      <%= dealer_additional_data.select :company_kind,
                                        options_for_select(
                                          Affiliation::COMPANY_KINDS.collect { |cc|
                                            [
                                              Affiliation.human_attribute_name("company_kinds/#{cc}"),
                                              cc
                                            ]
                                          }, selected: f.object.additional_data_obj.dealer.company_kind),
                                        include_blank: true
      %>
    </div>
    <div class="field spinnered">
      <%= dealer_form.input :partita_iva, required: false %>
    </div>
    <div class="field spinnered">
      <%= dealer_form.input :codice_fiscale, input_html: { class: 'upper' }, required: false %>
    </div>
    <div class="field">
      <%= dealer_form.label :tax_regime %>
      <%= dealer_form.input :tax_regime,
                            as:            :select,
                            label:         false,
                            collection:    Dealer::TAX_REGIME.map { |tr|
                              [
                                Dealer.human_attribute_name("tax_regimes/#{tr}"),
                                tr,
                                { 'data-di': Dealer::TAX_REGIME_ONLY_DITTA_INDIVIDUALE.include?(tr) }
                              ]
                            },
                            include_blank: true,
                            input_html: { disabled: f.object.affiliation_outcome_documents_check&.status_ok? }
      %>
      <%= dealer_form.hidden_field :tax_regime if f.object.affiliation_outcome_documents_check&.status_ok? %>
    </div>

    <div class="field">
      <%= additional_data_form.input :company_name_change,
                                    as: :boolean,
                                    wrapper: :inline_checkbox,
                                    label: 'Cambio ragione sociale',
                                    input_html: { checked: f.object.additional_data_obj&.company_name_change.to_s.to_bool } %>
    </div>

    <% unless @affiliation.kolme_master_nexus? %>
      <div class="field">
        <%= dealer_form.input :not_interested_in_energy_and_gas, as: :boolean, wrapper: :inline_checkbox %>
      </div>
    <% end %>

    <%= section_block 'Indirizzo Sede Legale' do %>
      <div class="row">
        <div class="col-sm-3">
          <div class="<%= f.object&.errors.attribute_names&.include?(:dealer_billing_street_type) ? 'field_with_errors' : 'field' %>">
            <%= dealer_form.label :billing_address_via, required: false %>
            <%= dealer_additional_data.select :billing_street_type,
                                              options_from_collection_for_select(
                                                StreetType.all,
                                                :value,
                                                :value,
                                                f.object.dealer_billing_street_type
                                              ),
                                              include_blank: true %>
          </div>
        </div>
        <div class="col-sm-9 field">
          <%= label_tag "" %>
          <%= dealer_additional_data.input :billing_address_via,
                                           input_html: { class: 'title',
                                                         value: f.object.additional_data_obj&.dealer&.billing_address_via },
                                           label:      false %>
        </div>
        <%= dealer_form.hidden_field :billing_address_via, id: 'dealer_billing_address_via' %>
      </div>
      <div class="field">
        <%= dealer_form.input :billing_address_number %>
      </div>
      <div class="field">
        <%= dealer_form.input :billing_address_postalcode, required: false %>
      </div>
      <div class="<%= dealer_form.object&.errors.attribute_names&.include?(:billing_address_city) ? 'field_with_errors' : 'field' %>">
        <%= dealer_form.label :billing_address_city %>
        <%= autocomplete_field_tag :billing_address_city_ui, dealer_form.object.city_ui, autocomplete_city_description_lov_index_path, :update_elements => { :id => '#affiliation_dealer_attributes_billing_address_city_id' } %>
        <%= dealer_form.hidden_field :billing_address_city_id %>
      </div>
    <% end %>

  <% end %>
<% end %>