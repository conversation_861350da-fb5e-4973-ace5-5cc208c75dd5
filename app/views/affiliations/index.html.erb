<%= page_title('affiliazioni', 'lista') %>

<%= render partial: 'affiliations/search', locals: { search: @search, search_form: @search_form } %>
<div class="page_info">
  <%= page_entries_info @affiliations %>
</div>
<div id="affiliations">
  <table class="index">
    <tr>
      <th>ID</th>
      <th class="wide"><%= Affiliation.human_attribute_name(:created_at) %></th>
      <th><%= Affiliation.human_attribute_name(:status) %></th>
      <th class="wide">Ragione sociale</th>
      <% if current_user.can?(:see_agent_column, Affiliation) %>
        <th>Agente/Sub-Agente</th>
      <% end %>
      <th><%= Affiliation.human_attribute_name(:dealer_operators) %></th>
      <th><%= Affiliation.human_attribute_name(:source) %></th>
      <th class="tools narrow"></th>
    </tr>

    <% @affiliations.each do |affiliation| %>
      <tr id="affiliation_row_<%= affiliation.id %>" class="<%= affiliation_row_class_name(affiliation) %>">
        <%= render partial: 'affiliations/affiliation_row', locals: { affiliation: affiliation } %>
      </tr>
    <% end %>
  </table>

  <%= pagination_box @affiliations %>

  <div id="print-forms-modal-container"></div>
  <div id="upload-forms-modal-container"></div>
</div>