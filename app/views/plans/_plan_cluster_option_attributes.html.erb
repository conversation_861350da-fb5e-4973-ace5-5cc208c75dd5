<div class="field cluster_option_form">
  <%= f.fields_for :plan_cluster_option_attributes, @plan_cluster_option do |plan_cluster_option| %>
    <%= label_tag :cluster_option_id, "Cluster cliente" %>
    <br/>
    <%= select_tag :cluster_option_id,
                   options_for_select(cluster_options_for_select.map { |o| [o.name, o.id, data: { children: o.has_children?, value: o.additional_info?, url: input_fields_cluster_option_path(o, plan_cluster_option_id: @plan_cluster_option) }] },
                                      selected: selected_cluster_option_for(plan_cluster_option.object)),
                   id: "plan_parent_cluster_option_id" %>

    <div class="child_cluster_option"></div>
    <div class="cluster_option_fields"></div>

    <%= plan_cluster_option.hidden_field :cluster_option_id, value: plan_cluster_option.object&.cluster_option_id, id: "cluster_option_id" %>
    <%= plan_cluster_option.hidden_field :cluster_option_parent_id, value: plan_cluster_option.object&.cluster_option_parent_id, id: "cluster_option_parent_id" %>
    <%= plan_cluster_option.hidden_field :plan_id, value: plan_cluster_option.object&.plan_id %>

    <%= hidden_field nil, :plan_cluster_option_value, value: @plan_cluster_option.value.to_json %>
  <% end %>
</div>
