<div class="mt-1" id="<%= @cluster_option.code %>_cluster_option">
  <div class="row">
    <div class="col-md-3">
      <%= label_tag :plan_plan_cluster_option_attributes_value_min_age, "Età inferiore a" %>
      <%= text_field_tag "plan[plan_cluster_option_attributes][value][min_age]", nil, required: true, maxlength: 2, class: 'numeric integer' %>
    </div>
    <div class="col-md-3">
      <%= label_tag :plan_plan_cluster_option_attributes_value_max_age, "Età superiore a" %>
      <%= text_field_tag "plan[plan_cluster_option_attributes][value][max_age]", nil, required: true, maxlength: 2, class: 'numeric integer' %>
    </div>
  </div>
  <span class="error">L'età minima deve essere minore dell'età massima</span>
</div>
<%= hidden_field_tag "plan[plan_cluster_option_attributes][value][outward_range]", '1' %>
