<%= page_title('<PERSON>ssioni Credito', 'Lista') %>

<%= render partial: 'wind_ngpos_activations/search' %>
<%= render partial: 'wind_ngpos_activations/import_wind_ngpos_modal' %>

<%= resources_info @wind_ngpos_activations %>

<%= subscribe_to '/wind_ngpos_activations' %>

<% if current_user.is?(:dealer)%>
  <%= subscribe_to '/wind_ngpos_activations_dealer_users' %>
<% elsif current_user.is?(:agent)%>
  <%= subscribe_to '/wind_ngpos_activations_agents' %>
<% else %>
  <%= subscribe_to '/wind_ngpos_activations_internal_users' %>
<% end%>

<table id="wind-ngpos-activations-list">
  <tr>
    <% unless current_user.is?(:dealer) %>
      <th>ID</th>
    <% end %>
    <th style="width: 12%">Data/Ora</th>
    <th><%= WindNgposActivation.human_attribute_name("customer_name") %></th>
    <th><%= WindNgposActivation.human_attribute_name("imei_or_serial") %></th>
    <% unless current_user.is?(:dealer) %>
      <th>Offerta</th>
    <% end%>
    <th class="col-md-2"><%= WindNgposActivation.human_attribute_name("product_name") %></th>
    <% if current_user.is?(:dealer) %>
      <th><%= WindNgposActivation.human_attribute_name("warehouse") %></th>
    <% else%>
      <th><%= WindNgposActivation.human_attribute_name("dealer") %></th>
    <% end %>
    <th>Documentazione</th>
    <th>Origine</th>
    <th class="col-md-2 tools"></th>
  </tr>

  <%= render partial: 'wind_ngpos_activations/wind_ngpos_activation_row', collection: @wind_ngpos_activations, as: :activation %>
</table>

<%= pagination_box @wind_ngpos_activations %>

<div id="debug-modal-container"></div>
