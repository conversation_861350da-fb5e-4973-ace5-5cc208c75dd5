<%= page_title('attivazioni', 'inserimento nuova attivazione') %>
<div id='new-phone-activation-modal-container'></div>
<%= render partial: 'phone_activations/phone_activation_header', locals: { phone_activation: @phone_activation } %>

<%= subscribe_to "/phone_activations/#{@phone_activation.id}/add_customer_from_app_callback" %>
<%= content_tag(
            :div,
            id: "add-customer-from-app-container",
            data: {
              phone_activation_id: @phone_activation.id,
              unload_action_url:   cancel_app_acquisition_phone_activation_path(@phone_activation),
              unload_guard_active: true
            }
          ) do %>
  <div class='row'>
    <%= form_tag "#", id: "new_customer" do %>
      <%= content_tag(
        :div,
        class: "col-md-8 text-center",
        id: "send-push-notification-container",
        data: {
          associated_form_id:    "new_customer",
          associated_form_model: "customer"
        }
      ) do %>
        <% if @phone_activation.plan.business? || @phone_activation.plan.for_tourists? %>
          <div class="panel panel-primary mt-3" >
            <div class="panel-heading"><strong><%= Customer.human_attribute_name(:customer_kind) %></strong></div>
            <div class="panel-body">
              <% customer_kinds = CustomerKind.where(id: @phone_activation.plan.customer_kind_ids)%>
              <% customer_kinds.each do |ck| %>
                <div style="display: inline-block; margin-right: 10%">
                  <%= radio_button_tag "customer[customer_kind_id]", ck.id, ck.id == CustomerKind.cliente_privato.id %>
                  <%= label_tag :customer_kind_id, ck.description %>
                </div>
              <% end %>
            </div>
          </div>
        <% end%>
        <%= render partial: 'phone_activations/push_notification_formlet', locals: { person_role: "cliente", title: 'cliente' } %>
      <% end%>
    <% end %>
  </div>
<% end%>