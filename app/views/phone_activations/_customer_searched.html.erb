<% if @phone_activation.customer.present? %>
  <%= form_for @phone_activation do |f| %>
    <%= f.hidden_field :customer_id %>
    <h2><%= @minor_customer.present? ? "Intestatario" : "Cliente"  %></h2>
    <%= render partial: 'phone_activations/customer_searched_show', locals: { customer: @phone_activation.customer } %>
    
    <% if @minor_customer.present? %>
      <h2>Cliente (minorenne)</h2>
      <%= f.hidden_field :minor_customer_id %>
      <%= render partial: 'phone_activations/customer_searched_show', locals: { customer: @phone_activation.minor_customer } %>
    <% end %>  
    
    <% @phone_activation.options.each do |option| %>
      <%= check_box_tag 'phone_activation[option_ids][]', option.id, true, style: 'display:none' %>
    <% end %>
    <% if @phone_activation.customer_valid_for_phone_activation_kind?(@customer) &&
(@minor_customer.blank? || @phone_activation.customer_valid_for_phone_activation_kind?(@minor_customer))
    %>
      <%= f.submit(I18n.t('buttons.save_and_continue'), :class => 'button') %>
    <% end %>
  <% end %>
<% else %>
  <h4>Cliente non trovato</h4>
  <div>
    <%= link_to 'Crea nuovo cliente', new_customer_url(:phone_activation_id => @phone_activation.id, :customer_cf => params[:cf_or_vat]), :class => 'button' %>
  </div>
<% end %>
