<div id="phone_activation_heading" class="phone_activation_heading col-md-9">
  <%= section_column do %>
    <%= section_block do %>
      <p>
        <span><%= @phone_activation.hex_id %></span>
        <span class="orange"><%= " - #{fa_icon('level-down-alt')} Disattiva".html_safe if @phone_activation.disabled %></span>
      </p>
      <p>
        <span><%= l @phone_activation.created_at, format: :long %></span>
      </p>
      <p>
        <span><%= @phone_activation.customer_uniq_name || @phone_activation.customer_name %></span>
      </p>

      <% if @phone_activation.customer %>
        <% if @phone_activation.customer.customer_kind.code == 'privato' %>
          <p>
            <span><%= @phone_activation.customer.name %></span>
          </p>
        <% elsif @phone_activation.customer.customer_kind.code == 'ditta individuale' %>
          <p>
            <span><%= @phone_activation.customer.company_name %></span>
          </p>
        <% end %>
      <% end %>
    <% end %>
  <% end %>
  <%= section_column do %>
    <% if @phone_activation.dealer %>
      <%= section_block do %>
        <p>
          <%= render partial: 'phone_activations/warehouse_accessory_data' %>
        </p>
        <p>
          <span><%= @phone_activation.warehouse.area_code %><%= " - #{@phone_activation.warehouse.region.description}" %><%= " -  #{@phone_activation.warehouse.h3g_area_code}" %><%= " - #{@phone_activation.warehouse.sub_agent_wind_tre.info.last_name}" if @phone_activation.warehouse.sub_agent_wind_tre %></span>
        </p>
      <% end %>
    <% end %>
    <p id="phone_activation_kind_<%= @phone_activation.id %>">
      <span><%= @phone_activation.phone_activation_kind.description %></span>
    </p>
  <% end %>
  <%= section_column do %>
    <%= section_block do %>
      <p class="orange">
        <span class="best_in_place_note" title="Clicca per aggiungere una nota"><%= best_in_place @phone_activation, :private_note, url: update_private_note_phone_activation_path(@phone_activation) %></span>
      </p>
    <% end %>
    <% if @phone_activation.operator.tre? %>
      <%= section_block do %>
        <% if @phone_activation.needs_documents_upload? && can?(:upload_ftp_documents, PhoneActivation) %>
          <%= button_link_to "Rilancia caricamento documenti", enqueue_ftp_documents_upload_phone_activation_path(@phone_activation), class: "btn btn-danger" %>
        <% end %>
      <% end %>
    <% end %>
  <% end %>
</div>