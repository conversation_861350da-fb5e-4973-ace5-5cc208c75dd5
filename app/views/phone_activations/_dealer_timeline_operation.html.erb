<div class="timeline_entry dealer_timeline">
  <div class="details light-border">
    <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
      <div class="panel panel-default">
        <div class="dealer_timeline_operation">
          <% if operation_outcomes.any? %>
            <%= render partial: 'phone_activations/dealer_timeline/completed_operations', locals: { phone_activation: phone_activation, operation: operation, operation_outcomes: operation_outcomes } %>
          <% elsif !operation.id.in? OperationConstants::EXCLUDED_FROM_DEALER_TIMELINE %>
            <%= render partial: 'phone_activations/dealer_timeline/todo_operations', locals: { phone_activation: phone_activation, operation: operation, operation_outcomes: operation_outcomes } %>
          <% end %>

          <% if print_operation_outcomes(operation_outcomes) %>
            <%= render partial: 'phone_activations/dealer_timeline/recover_operations',
                       locals: { phone_activation: phone_activation,
                                 operation: operation,
                                 operation_outcomes: operation_outcomes } %>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>



<script type="text/javascript">
    $(document).ready(function () {
        $('.recovery_note').keyup(function () {
            recovery_note_id = $(this).attr('id');
            ids = recovery_note_id.split('recovery_note_')[1].split('_');
            phone_activation_id = ids[0];
            operation_id = ids[1];
            var button = $('#recovery_button_' + ids[0] + '_' + ids[1]);
            if ($(this).val() == '') {
                button.attr('disabled', 'disabled');
                button.addClass('disabled');
            } else {
                button.attr('disabled', false);
                button.removeClass('disabled');
            }
        });
    });
</script>
