<%= fields_for(phone_activation) do |f| %>
  <%= f.label :operator, Plan.human_attribute_name(:operator), class: 'selector_title' %><br>
  <div class="selector">
    <div id="operators_container">
      <% operators = DataComposer::PhoneActivationDataComposer.new(phone_activation: phone_activation, tariffa_cb: phone_activation.tariffa_cb, current_user: current_user).operators.models %>
      <% operators.each do |operator| %>
        <% operator_class = "operator" %>
        <% operator_class += ' selected' if params[:phone_activation][:operator_id] == operator.id.to_s %>
        <% operator_class += ' single_icon' if operators.size == 1 %>
        <%= image_tag(operator.logo_highlight.small.url,
            class: operator_class,
            title: operator.name,
            data: { value: operator.id }
        ) %>
      <% end %>
      <%= f.hidden_field :operator_id, value: params[:phone_activation][:operator_id] %>
    </div>
  </div>
<% end %>

<script type="text/javascript">
  $(document).ready(function () {
    $('.operator').click(function () {
      id = $(this).attr('data-value');
      $('#phone_activation_operator_id').val(id).trigger('change');
      $('.operator').removeClass('selected');
      $(this).addClass('selected');
    })
  })

  if($('.operator').length == 1 && $('#dealer_id').val() != ''){
    window.App.phoneActivationDataComposer.lockReload('operators');

    $('.operator').addClass('selected');
    selected = $('.operator').attr('data-value');
    $('#phone_activation_operator_id').val(selected);
    $('#phone_activation_operator_id').trigger('change');
  }
</script>