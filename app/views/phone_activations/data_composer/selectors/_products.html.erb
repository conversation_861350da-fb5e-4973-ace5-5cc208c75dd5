<%= fields_for(phone_activation) do |f| %>
  <% products = DataComposer::PhoneActivationDataComposer.new(phone_activation: phone_activation, tariffa_cb: tariffa_cb, current_user: current_user).products.data %>
  <% if products && !products.empty? %>
    <div class='selector_container_no_margin'>
      <div class='selector_container'>
        <%= f.label :product, class: 'selector_title' %><br/>
        <div class="selector product-box-selector">
          <div id="product-selector">
            <%= select_tag :aggregated_product_id,
                           options_for_select(products.map { |p|
                             [
                               p.name,
                               p.id,
                               {
                                 'data-product_colors':           p.product_colors.to_json,
                                 'data-default_image_product_id': p.default_image_product_id,
                                 'data-product-rid-guaranteed': p.rid_guaranteed
                               }
                             ] }),
                           prompt: '' %>
          </div>
          <div id='device-details-container' class="text-center" style="display:none">
            <%= link_to '#', id: 'device-details', data: { url: store_product_path(id: ':product_id') } do %>
              <%= fa_icon('wrench') %><br>
              <span>SCHEDA</span><br>
              <span>PRODOTTO</span>
            <% end %>
          </div>
        </div>
      </div>
      <div class='selector_container'>
        <%= f.label :product_color, class: 'selector_title' %><br/>
        <div class="selector">
          <div id="product_color-selector">
            <%= f.select :product_id, [], include_blank: true %>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <script type="text/javascript">
      var js_data_composer = new PhoneActivationDataComposerProductColor("<%= form_id(@phone_activation) %>")

      <% if f.object.product %>
      js_data_composer.select_product_color(<%= f.object.product.id %>);
      <% end %>
  </script>
<% end %>
