<div class="row">
  <div class="col-md-6">
    <p class='block_title'>Dettagli</p>
    <div class="field">
      <%= f.input :name %>
    </div>
    <div class="field">
      <%= f.input :client_support %>
    </div>
    <div class="field">
      <%= f.input :url %>
    </div>
    <div class="field">
      <%= f.input :access_level, collection: translate_access_levels_for_operator, selected: @operator.access_level || AccessLevel::ONLY_MD, include_blank: false %>
    </div>
    <div class="field">
      <%= f.input :position %>
    </div>
    <div class="field">
      <%= f.input :needs_pdf %>
    </div>
    <div class="field">
      <%= f.input :pdf_name,
                  input_html: { onblur: "checkPdf(this.value, '#{operators_check_pdf_url}')" },
                  label:      'Nome PDF - Lettera di gara <i id="pdf_name_check_results" title="-" data-toggle="tooltip" class="fa"></i>'.html_safe %>
    </div>
    <div class="field">
      <%= f.input :tender_letter_position %>
    </div>
    <div class="field">
      <%= f.input :cms_position %>
    </div>
    <div class="field">
      <%= f.input :cms_column, collection: Operator::CMS_COLUMNS.map { |column| [Operator.human_attribute_name(column), column] }, selected: @operator.cms_column %>
    </div>
    <div class="field">
      <%= f.input :disabled, wrapper: :vertical_boolean %>
    </div>
    <div class='alert alert-danger disableOperatorWarning'>
      <b>Attenzione!</b>&nbsp;Disabilitando l'operatore lo stesso verrà contestualmente nascosto dalla lista degli
      operatori abilitabili su Partner.
    </div>
  </div>
  <div class='col-md-6'>
    <p class='block_title'>Logo</p>
    <div class="field">
      <label>Logo</label>
      <%= image_tag(f.object.logo.small.url) if f.object.logo? %>
      <%= f.file_field :logo %>
      <%= f.hidden_field :logo_cache %>
    </div>
    <div class="field">
      <label>Logo (highlight)</label>
      <%= image_tag(f.object.logo_highlight.small.url) if f.object.logo_highlight? %>
      <%= f.file_field :logo_highlight %>
      <%= f.hidden_field :logo_highlight_cache %>
    </div>
    <div class="field">
      <%= f.input :hide_logo_in_store_card %>
    </div>
  </div>
</div>

<script>
    $(function () {
        if ($('input#operator_needs_pdf').attr('checked')) {
            checkPdf($('input#operator_pdf_name').val(), '<%=operators_check_pdf_url%>');
        }
    });
</script>
