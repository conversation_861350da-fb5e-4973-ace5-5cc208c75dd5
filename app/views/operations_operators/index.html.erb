<div class="row m-2">
  <div class="col-md-8">
    <%= check_box_tag :show_disabled %>
    <%= label_tag :show_disabled, t('operator.show_disabled') %>
  </div>
  <div class="col-md-4">
    <%= link_to 'Indicizza attivazioni con Operazioni da fare', reindex_workable_phone_activations_operations_operators_path, method: :post, class: 'btn btn-default', data: { confirm: 'Avviare l\'indicizzazione?' } %>
  </div>
</div>

<%= form_tag operations_operators_path, method: :post do %>
  <table class='table table-striped'>
    <tr>
      <th>Operazione</th>
      <th>Ordinamento</th>
      <% @operators.each do |operator| %>
        <th data-operator-disabled="<%= operator.disabled %>"><%= operator.name %></th>
      <% end %>
      <th></th>
    </tr>
    <% @operations.each do |operation| %>
      <tr>
          <td><%= operation.description %></td>
          <td><%= text_field_tag "operations[#{operation.id}][priority_index]", operation.priority_index, maxlength: 3, class: 'numeric' %></td>
          <% @operators.each do |operator| %>
            <td data-operator-disabled="<%= operator.disabled %>">
              <%= check_box_tag "operations[#{operation.id}][operator_ids][]", operator.id, operation.operator_ids.include?(operator.id), id: dom_id(operator) %>
            </td>
          <% end %>
      </tr>
  <% end %>
  </table>
  <%= submit_tag 'Aggiorna' %>
<% end %>