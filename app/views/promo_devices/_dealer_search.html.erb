<div>
  <div class='search_form'>
    <%= form_for promo_devices_path, method: 'get' do %>
      <div class='row'>
        <div class='col-md-6'>
          <div class='input-group'>
            <span class='input-group-addon'><%= fa_icon 'barcode' %></span>
            <%= text_field_tag 'search[serial]', params.dig(:search, :serial), placeholder: PromoDevice.human_attribute_name('serial') %>
          </div>
        </div>
        <div class='col-md-6'>
          <div class='input-group'>
            <span class='input-group-addon'><%= fa_icon 'mobile' %></span>
            <%= autocomplete_field_tag 'search[product_name]', params.dig(:search, :product_name), autocomplete_product_name_products_path, placeholder: PromoDevice.human_attribute_name('product') %>
          </div>
        </div>
      </div>
      <div class='row'>
        <div class='col-md-6'>
          <div class='input-group'>
            <span class='input-group-addon'><%= fa_icon 'calendar' %></span>
            <%= text_field_tag 'search[created_at_from]', params.dig(:search, :created_at_from), class: 'datepicker', placeholder: PromoDevice.human_attribute_name('created_at_from') %>
          </div>
        </div>
        <div class='col-md-6'>
          <div class='input-group'>
            <span class='input-group-addon'><%= fa_icon 'calendar' %></span>
            <%= text_field_tag 'search[created_at_to]', params.dig(:search, :created_at_to), class: 'datepicker', placeholder: PromoDevice.human_attribute_name('created_at_to') %>
          </div>
        </div>
      </div>
      <div class='row'>
        <div class='col-md-12 search-buttons'>
          <%= button_tag type: 'submit', class: 'btn btn-primary', title: I18n.t("search.search").titleize do %>
            <%= fa_icon('search') %>
          <% end %>
          <%= link_to fa_icon('undo'), promo_devices_path(commit: I18n.t("search.clean")), title: I18n.t("search.clean").titleize, class: 'btn btn-primary' %>
        </div>
      </div>
      <div class='row mt-1'>
        <div class='col-md-12'>
          <%= link_to "Vai a #{promo_device_title(true)}", promo_devices_path(legacy: true), class: 'btn btn-default btn-block' %>
        </div>
      </div>
    <% end %>
  </div>
</div>