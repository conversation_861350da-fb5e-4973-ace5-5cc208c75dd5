<div id="receipt_<%= recharge.id %>" class='receipt'>
  <% unless render_from_app %>
    <p class='no_print'>
      <%= link_to fa_icon('print', text: '[Stampa]'), '#', id: "print_receipt_#{recharge.id}", class: 'print_receipt_button' %>
    </p>
  <% end %>
  <p>KOLME SRL</p>
  <% if recharge.recharge_status == 1 or recharge.recharge_status.nil? %>
      <%= render partial: 'recharges/receipts_euronet/error', locals: {recharge: recharge} %>
  <% else %>
      <%= render partial: partial_receipt_for(recharge), locals: {recharge: recharge} %>
      <% if not recharge.notes.blank? %>
          <p class='center'><%= recharge.notes %></p>
      <% end %>
  <% end %>
</div>

<script type="text/javascript">
    $(document).ready(function () {
        $('.print_receipt_button').bind('click', function () {
            $.fancybox.close();
            elem_to_print_id = $(this).attr('id').split('_')[2];
            $('#receipt_' + elem_to_print_id).printElement();
        });
    })
</script>