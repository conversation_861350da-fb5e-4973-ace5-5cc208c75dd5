<%= page_title '<PERSON>riche', 'Inserimento e lista' %>

<%= render partial: 'recharges/heading' %>

<%= subscribe_to "/update_recharges" %>

<% unless current_user.is?(:dealer) %>
  <div class="page_info">
    <%= page_entries_info @recharges %>
  </div>
<% end %>
<table class="recharges">
  <tr>
    <th class="status_column"><%= Recharge.human_attribute_name('status') %></th>
    <% unless current_user.is?(:dealer) %>
      <th class="id_column">#</th>
    <% end %>
    <th><%= Recharge.human_attribute_name('user') %></th>
    <th><%= Recharge.human_attribute_name('created_at') %></th>
    <% unless current_user.is?(:dealer) %>
      <th><%= Recharge.human_attribute_name('dealer') %></th>
    <% end %>
    <th><%= Recharge.human_attribute_name('phone_number') %></th>
    <th><%= Recharge.human_attribute_name('recharge_size') %></th>
    <% unless current_user.is?(:dealer) %>
      <th class="notes"><%= Recharge.human_attribute_name('visible_notes') %></th>
    <% else %>
      <th class="notes"><%= Recharge.human_attribute_name('notes') %></th>
    <% end %>
    <% unless current_user.is?(:dealer) %>
      <th><%= Recharge.human_attribute_name('notes_kolme') %></th>
      <th><%= Recharge.human_attribute_name('recharge_attempts') %></th>
    <% end %>
    <th></th>
  </tr>

  <% @recharges.each do |recharge| %>
    <tr id="recharge_<%= recharge.id %>" class="<%= (!recharge.can_execute_recharge? && recharge.recharge_status == Recharge::TO_WORK) ? 'shaded' : '' %>">
      <td class="present_status">
        <div class="<%= recharge.present_status %>"></div>
      </td>
      <% unless current_user.is?(:dealer) %>
        <td class="id_column">
          <% if current_user.is?(:admin) %>
            <%= recharge.from_app? ? Recharge::FROM_APP_PREFIX : Recharge::SYSTEM_PREFIX %>&nbsp;
          <% end %>
          <%= recharge.id %>
        </td>
      <% end %>
      <td><%= recharge.user ? recharge.user.full_name : 'System' %></td>
      <td><%= recharge.created_at.strftime("%d/%m/%Y %H:%M") if recharge.created_at %></td>
      <% unless current_user.is?(:dealer) %>
        <td><%= link_to_if (can?(:manage, Recharge) and not @dealer), recharge.dealer.name, dealer_path(recharge.dealer), target: "_blank" %></td>
      <% end %>
      <td><%= recharge.phone_number_expression %></td>
      <td><%= recharge.recharge_size.name %></td>
      <% if current_user.is?(:dealer) %>
        <td class="notes dealer_notes"><%= recharge.notes %></td>
      <% else %>
        <td class="notes"><%= best_in_place recharge, :notes, as: 'textarea', place_holder: 'Aggiungi nota' %></td>
        <td class="notes"><%= best_in_place recharge, :notes_kolme, as: 'textarea', place_holder: 'Aggiungi nota' %></td>
        <td><%= recharge.recharge_attempts if recharge.recharge_size.automatic? %></td>
      <% end %>
      <td class=" <%= 'pending' if recharge.recharge_status == Recharge::TO_WORK %>">
        <%= button_group do %>
          <% if can?(:manage, PhoneActivation) %>
            <%= button_link_to fa_icon('eye'), phone_activation_path(recharge.phone_activation), title: 'Visualizza attivazione' if recharge.phone_activation %>
          <% end %>
          <% if can?(:show_receipt, recharge) %>
            <%= button_link_to fa_icon('bookmark'), show_receipt_recharge_path(recharge), title: 'Visualizza scontrino', id: "show_receipt_#{recharge.id}_fancy", class: 'lb show_receipt_button' if recharge.recharge_size.automatic? || recharge.manually_executed? || recharge.recharge_status == Recharge::FAILED %>
          <% end %>
          <% if can?(:destroy, Recharge) or can?(:manually_execute, Recharge) %>
            <%= button_link_to fa_icon('thumbs-up'),
                               recharge_path(
                                   recharge,
                                   recharge: { manually_executed: true, recharge_status: Recharge::SUCCESS },
                                   filter:   params.to_unsafe_h
                               ),
                               method: 'put',
                               class:  'btn green',
                               title:  'Ricarica OK',
                               id:     "recharge_ok_#{recharge.id}" if recharge.can_be_confirmed? %>
            <%= button_link_to fa_icon('thumbs-down'),
                               recharge_path(
                                   recharge,
                                   recharge: { manually_executed: true, recharge_status: Recharge::FAILED },
                                   filter:   params.to_unsafe_h
                               ),
                               method: 'put',
                               class:  'btn red',
                               title:  'Ricarica KO',
                               id:     "recharge_ko_#{recharge.id}" if recharge.can_be_dismissed? %>
            <% if (current_user.is?(:super_user) and recharge.recharge_status == Recharge::FAILED) or current_user.is?(:admin) %>
              <%= button_link_for_destroy recharge, method: 'delete', data: { confirm: 'Sicuro di voler cancellare questa ricarica?' } %>
            <% end %>
          <% end %>
          <% if can?(:list, :audits) %>
            <%= button_link_to_audit(recharge) %>
          <% end %>
        <% end %>
      </td>
    </tr>
  <% end %>
</table>
<%= pagination_box @recharges %>

<script type="text/javascript">
    $(document).ready(function () {
        $('#recharged_at_from').datepicker({
            dateFormat: "dd/mm/yy",
            changeMonth: true,
            numberOfMonths: 1,
            onClose: function (selectedDate) {
                $("#recharged_at_to").datepicker("option", "minDate", selectedDate);
            }
        });
        $('#recharged_at_to').datepicker({
            dateFormat: "dd/mm/yy",
            changeMonth: true,
            numberOfMonths: 1,
            onClose: function (selectedDate) {
                $("#recharged_at_from").datepicker("option", "maxDate", selectedDate);
            }
        });
    })
</script>