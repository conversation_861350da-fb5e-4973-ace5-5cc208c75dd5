<p>
  Data: <%= I18n.l(recharge.euronet_transaction.created_at) %><br/>
  Merc: <%= Settings.recharges.merc %><br/>
  TermId: <%= Settings.recharges.termid %>
</p>

<p class="<%= recharge.receipt_class('add_bold') %>">
  <b>NUMERO DI TELEFONO</b><br/>
  <%= recharge.phone_number_for_receipt %><br/>
</p>

<p class="<%= recharge.receipt_class('add_bold') %>">
  Importo:<br/> <%= number_to_currency recharge.amount, :unit => "euro", :format => "%n %u" %>
</p>

<p>
  Non rimborsabile <br>
  IVA assolta ex art. 74 co. 1 lett. d) DPR 633/72 <br>
  da Compagnia Italia Mobile S.r.l. PI 05370200965
</p>