<p>
  Data: <%= I18n.l(recharge.euronet_transaction.created_at) %><br/>
  Merc: <%= Settings.recharges.merc %><br/>
  TermId: <%= Settings.recharges.termid %>
</p>

<p>
  COOP RICARICHE<br/>
  Il presente scontrino<br/>
  costituisce ricevuta<br/>
  dell’avvenuto pagamento.<br/>
  Conservare fino a
  ricarica avvenuta
</p>
<p>Scontrino non fiscale.</p>

<p class="<%= recharge.receipt_class('add_bold') %>">
  <b>NUMERO DI TELEFONO</b><br/>
  <%= recharge.phone_number_for_receipt %><br/>
</p>

<p class="<%= recharge.receipt_class('add_bold') %>">
  Importo:<br/> <%= number_to_currency recharge.amount, :unit => "euro", :format => "%n %u" %>
</p>

<p class="<%= recharge.receipt_class('remove_bold') %>">
  TID: <%= recharge.euronet_transaction.euronet_recharge %><br/>
  AUTH: </b><%= recharge.euronet_transaction.auth %>
</p>

<p>
  <span class="<%= recharge.receipt_class('remove_bold') %>"> NON RIMBORSABILE</span><br/><br/>
  IVA inclusa, assolta ai sensi<br/>
  dell'art. 74 DPR 633/72 comma 1, lettera d<br/>
  da COOP ITALIA SOCIETA' COOPERATIVA<br/>
  P.I. 01515921201<br/>
  COOP Voce accreditera' la<br/>
  ricarica entro 24 ore
</p>