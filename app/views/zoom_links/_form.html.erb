<% if zoom_link.errors.any? %>
  <div id="error_explanation" class='panel panel-danger'>
    <div class="panel-heading"><%= pluralize(zoom_link.errors.count, "errore ha", "errori hanno") %>
      impedito al link Zoom di essere salvato:
    </div>
    <div class="panel-body">
      <ul>
        <% zoom_link.errors.full_messages.each do |msg| %>
          <li><%= msg %></li>
        <% end %>
      </ul>
    </div>
  </div>
<% end %>
<div class="row search_form">
  <%= simple_form_for zoom_link, remote: true do |f| %>
    <div class="field">
      <%= f.label :url_alias, required: true %>
      <div style="display: flex;align-items: stretch">
        <div style="width:25%">
          <input type="url" value="<%= [Settings.application.full_secure_url, 'zoom'].join('/') %>/" readonly/>
        </div>
        <%= f.input :url_alias, label: false, wrapper_html: { style: 'flex:1' } %>
      </div>
    </div>
    <div class="field">
      <%= f.input :meeting_url %>
    </div>
    <div class="field">
      <%= f.input :restricted, wrapper: :vertical_boolean, label: ZoomLink.human_attribute_name('restricted') %>
    </div>
    <div class="field">
      <%= f.input :active, wrapper: :vertical_boolean, label: ZoomLink.human_attribute_name('active') %>
    </div>
    <div style="width:20%">
      <%= f.submit t("buttons.save"), class: "btn btn-default col-md-12" %>
    </div>
  <% end %>
</div>