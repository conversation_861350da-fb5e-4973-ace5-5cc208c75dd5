<%= section_block 'Indirizzo di Fatturazione' do %>
    <p>
      <b><%= Dealer.human_attribute_name(:billing_address_via) %></b>
      <span><%= dealer.billing_address_via %></span>
    </p>

    <p>
      <b><%= Dealer.human_attribute_name(:billing_address_number) %></b>
      <span><%= dealer.billing_address_number %></span>
    </p>

    <p>
      <b><%= Dealer.human_attribute_name(:billing_address_postalcode) %></b>
      <span><%= dealer.billing_address_postalcode %></span>
    </p>
    <p>
      <b><%= Dealer.human_attribute_name(:country_id) %></b>
      <span><%= @dealer.country %></span>
    </p>
    <% if dealer.country.italy? %>
        <p>
          <b><%= Dealer.human_attribute_name(:billing_address_city) %></b>
          <span><%= @dealer.billing_address_city.description.titleize %> [<%= @dealer.billing_address_province %>]</span>
        </p>
    <% else %>
        <p>
          <b><%= Dealer.human_attribute_name(:foreign_city) %></b>
          <span><%= @dealer.foreign_city %></span>
        </p>

        <%= render partial: 'dealers/sap_business_partner_taxes', locals: { dealer: dealer } %>
    <% end %>
<% end %>

