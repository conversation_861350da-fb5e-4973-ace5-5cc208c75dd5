<div id="<%= documentable.documentable_type.singularize %>_<%= documentable.id %>_<%= context %>_uploaded_files" class="<%= documentable.documentable_type.singularize %>_mdv_affiliation_uploaded_files">
  <% documentable.mdv_affiliation_documents_list(current_user).each do |document| %>
    <div id="<%= documentable.id %>_<%= document.kind %>_files" class="<%= documentable.documentable_type.singularize %>_files">
      <div class='file'>
        <div>
          <%= image_tag "icon_pdf.png" if document.related_file.present? %>
          <%= document_content_name(documentable, context, "#{'upload_' unless document.related_file.present?}#{document.content}") %>
          <span style="padding: 10px"><%= display_status(document) %></span>
          <% unless document.notes.blank? %>
            <p>Note: <font class="red"><%= document.notes %></font></p>
          <% end %>
        </div>
        <%= button_group do %>
          <% if document.related_file.present? %>
            <%= button_link_for_download download_document_path(documentable.documentable_type, documentable.id, document.id, context: context), target: '_blank' %>
            <% if can?(:update_mdv_affiliation_document, documentable) && document.can_update_status? %>
              <%= link_to fa_icon('thumbs-up'), accept_mdv_affiliation_document_dealer_path(id: documentable.id, document_id: document.id), title: 'Valida documento', class: 'green btn btn-default', remote: true %>
              <%= link_to fa_icon('thumbs-down'), '#', title: 'Rifiuta documento', class: 'red btn btn-default reject_button' %>
            <% end %>
          <% else %>
            <% if can?(:upload_mdv_affiliation_document_form, documentable) %>
              <%= link_to 'Carica documenti', upload_mdv_affiliation_document_form_dealer_path(documentable, { content: document.content }), class: 'btn btn-default', remote: true %>
            <% end %>
          <% end %>
        <% end %>
      </div>
      <%= render partial: 'dealers/mdv_affiliation/reject_document', locals: { documentable: documentable, document: document } if document.related_file.present? %>
    </div>
  <% end %>
</div>
