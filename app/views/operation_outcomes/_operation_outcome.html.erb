<div id="operation_outcome">
  <div id="content">
    <% prevent_send_to_robot = @operation_outcome.phone_activation.official_pda.present? && @operation_outcome.phone_activation.official_pda.documents.any? %>
    <%= render partial: 'operation_outcomes/box_upload',
               locals:  { operation_outcome: @operation_outcome,
                          phone_activation:  @phone_activation } if box_upload_available_for(@operation_outcome) %>

    <%= render partial: 'phone_activations/details/add_number_rebinding', locals: { phone_activation: @phone_activation } if @operation_outcome.operation_id == OperationConstants::SEGNALAZIONE_NUMERO_ATTIVO %>

    <div class="box recovery_note"><%= recovery_note_for_operation_outcome(@operation_outcome) %></div>
    <div class="box sections" id="sections_for_operation_outcome"><%= sections_for_operation_outcome(@operation_outcome) %></div>
    <%= form_for [@operation_outcome.phone_activation, @operation_outcome], html: { id: 'operation_outcome_form' }, remote: true do |f| %>
      <div class="box actions"><%= actions_for_operation_outcome(@operation_outcome, f) %></div>
      <div class="box fields"><%= fields_for_operation_outcome(@operation_outcome, form_builder: f) %></div>
      <div class="action_buttons">
        <% if proceed_button_is_required_for(@operation_outcome) %>
          <%= f.submit t('buttons.save_operation'), class: 'button save_button', data: { disable_with: 'Attendere prego...' } %>
        <% end %>
        <%= danger_button_link_to t('buttons.release_operation'),
                                  cancel_phone_activation_operation_outcome_path(@operation_outcome.phone_activation, @operation_outcome),
                                  method:       'post',

                                  data: {
                                    remote: true,
                                    disable_with: 'Attendere prego...',
                                  },
                                  class:        'cancel_button' %>
      </div>
    <% end %>
  </div>
</div>

<script type="text/javascript">
    $(document).ready(function () {
        $.validator.addMethod("check_minimum_date", function (value, element) {
            var test_date = value.split("/");
            return new Date(Date.parse(test_date[2] + "-" + test_date[1] + "-" + test_date[0])) < new Date();
        }, "La data non è valida, deve essere precedente o uguale alla data odierna");

        $('#operation_outcome_form').validate({
            rules: {
                "operation_outcome[activation_date]": {check_minimum_date: true},
                "operation_outcome[operator_created_at]": {check_minimum_date: true}
            },
            errorPlacement: function (error, element) {
                error.insertAfter(element);
            },
            wrapper: "span"
        });

        $("form").on("keypress", function (e) {
            if (e.keyCode == 13) {
                return false;
            }
        });

        <% if @operation_outcome.operation_id == 2 %>
        $('span.button.ko').click(function () {
            if ($('#submit').length == 0) {
                $('a.danger.button').before("<input id=\"submit\" class=\"button\" data-disable-with=\"Attendere prego...\" name=\"commit\" type=\"submit\" value=\"Procedi\">")
            }
        });
        <% end %>

        $(".save_button").click(function (e){
            if (<%= prevent_send_to_robot %> && $('#operation_outcome_status_send_to_robot')[0].checked) {
                e.preventDefault()
                alert("PDC ufficiale già uploadata. Per inviare l’attivazione al robot, elimina prima il file.")
            }
        });
    })
</script>

<% if OperationConstants::WITH_DATES_CHECKER.include?(@operation_outcome.operation_id) %>
  <%= render partial: 'operation_outcomes/dates_checker', locals: { operation_outcome: @operation_outcome } %>
<% end %>
