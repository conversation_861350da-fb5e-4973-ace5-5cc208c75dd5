<%= hint_for_sections %>
<div class="col-md-12">
  <% if should_show_delay_button?(@phone_activation) && !current_user.agent? %>
    <%= link_to far_icon('clock-o'), new_phone_activation_delay_path(phone_activation_id: @phone_activation.id,
                                                                     complete_url:        request.original_url,
                                                                     from_show:           true),
                title: 'Ricordamelo fra...',
                class: 'lb btn-xs btn-danger float-right' %>
  <% end %>
  <p>
    <b>Tariffa:</b>
    <%= @phone_activation.plan.name %>
    <% if @phone_activation.plan.note.present? %>
      <button type="button" class="btn note-info-btn" data-toggle="popover" title="" data-content="<%= @phone_activation.plan.note %>">
        <%= fa_icon('info-circle') %>
      </button>
    <% end %>
  </p>
  <p>
    <b>Offerta:</b>
    <%= @phone_activation.offer_category.description %> <%= offer_length_for_plan_details(@phone_activation) %>
  </p>
  <% if not @phone_activation.options.empty? %>
    <p class="red">
      <b>Opzioni:</b>
      <% @phone_activation.options.each do |option| %>
        <%= option.name %>
        <% if option.notes.present? %>
          <button type="button" class="btn note-info-btn" data-toggle="popover" title="" data-content="<%= option.notes %>">
            <%= fa_icon('info-circle') %>
          </button>
        <% end %>
        <%= '-' unless option.id == @phone_activation.options.last.id %>
      <% end %>
    </p>
  <% end %>
  <% if not @phone_activation.note.blank? %>
    <p class="red">
      <b><%= PhoneActivation.human_attribute_name("note") %>:</b>
      <%= @phone_activation.note %>
    </p>
  <% end %>
  <p>
    <b>Modalità di firma:</b>
    <%= @phone_activation.translate_signature_kind %>
  </p>
  <% if @phone_activation.warehouse.present? && @phone_activation.system_owner_shipping? %>
    <p>
      <b>Corriere:</b>
      <%= @phone_activation.warehouse.forwarder_name %>
    </p>
  <% end %>
  <% if @phone_activation.recharge_size.present? %>
    <p>
      <b>Ricarica contestuale:</b>
      <%= @phone_activation.recharge_size.name %>
    </p>
  <% end %>
  <p>
    <b><%= PhoneActivation.human_attribute_name("monthly") %>:</b>
    <%= number_to_currency(@phone_activation.monthly, precision: ((@phone_activation.monthly.to_f % 1) != 0) ? 2 : 0) %>
  </p>
  <% if @phone_activation.offer_due_date %>
    <p>
      <b>Scadenza vincolo:</b>
      <%= @phone_activation.offer_due_date.strftime("%d-%m-%Y") %>
    </p>
  <% end %>
  <% if @phone_activation.financing_code.present? %>
    <p>
      <b><%= PhoneActivation.human_attribute_name("financing_code") %>:</b>
      <%= @phone_activation.financing_code %>
    </p>
  <% end %>
  <% if @phone_activation.number_rebinding_detail.present? %>
    <p>
      <b><%= PhoneActivation.human_attribute_name("number_rebinding") %>:</b>
      <%= @phone_activation.number_rebinding_detail.phone_number %>
    </p>
    <% if @phone_activation.number_rebinding_detail.product_change? %>
      <p>
        <b><%= NumberRebindingDetail.human_attribute_name("imei_serial") %>:</b>
        <%= @phone_activation.number_rebinding_detail.imei_serial %>
      </p>
    <% end %>
  <% end %>
  <% if @phone_activation.contract_rebinding_detail.present? %>
    <p>
      <b><%= PhoneActivation.human_attribute_name("contract_rebinding") %>:</b>
      <%= @phone_activation.contract_rebinding_detail.code %>
    </p>
  <% end %>
  <% if @phone_activation.phone_activation_kind.code == 'FDA' %>
    <div class='upfront-details'>
      <b>Anticipo</b>
      <span><%= number_to_currency(@phone_activation.upfront) %></span>
      <% if can?(:create, PaymentRequest) %>
        <%= render 'shared/payment_requestable_payment_request', payment_requestable: @phone_activation %>
      <% end %>
    </div>
  <% end %>

  <%= render partial: 'operation_outcomes/sections/linked_selection_section' %>

  <% if @phone_activation.easycare_insurance %>
    <p class="easycare_info">
      <b>Richiesta EasyCare+ </b>
      <%= link_to fa_icon('times-circle'), remove_easycare_phone_activation_path(@phone_activation), class: 'btn-default red', title: 'Elimina copertura EasyCare+', data: { remote: true, confirm: "Sei sicuro di voler eliminare EasyCare+ dall’attivazione? Vuoi procedere?" } if is_completed_step?(@operation_outcome) %>
    </p>
  <% end %>

  <% if @phone_activation.has_valid_appointment_detail? %>
    <p class="valid-appointment-detail-link">
      <b>Installazione gestita da dealer</b>
      &nbsp;
      <%= link_to fa_icon('broadcast-tower'),
                  appointment_details_path(search: { phone_activation_id_eq: @phone_activation.id, statuses: [''] }),
                  target: '_blank',
                  class:  'red' %>
      <% if @phone_activation.appointment_detail.expired? %>
      <p class="valid-appointment-detail-link">
        <b>Attenzione data di appuntamento scaduta, invita il dealer a rifissare
          l'appuntamento prima di completare l'attivazione</b>
      </p>
    <% end %>
    </p>
  <% end %>

  <% if @phone_activation.has_open_after_sale_threads? %>
    <p class="open-after-sale-threads-link">
      <b>Attenzione gestioni in corso</b>
      &nbsp;
      <%= link_to fa_icon('puzzle-piece'),
                  after_sale_threads_path(phone_activation_hex_id: @phone_activation.hex_id, filter_search_state: ['open']),
                  target: '_blank',
                  class:  'red' %>
    </p>
  <% end %>

  <% if @phone_activation.cvp_offline %>
    <p>
      <b><%= PhoneActivation.human_attribute_name("cvp_offline") %>:</b>
      <%= true_or_false(@phone_activation.cvp_offline) %>
    </p>
  <% end %>

  <% if @phone_activation.promo_device_serial.present? %>
    <p class="red">
      <b><%= PhoneActivation.human_attribute_name("has_contextual_promo_device") %>
        :</b>
      <%= @phone_activation.promo_device_serial %>
    </p>
  <% end %>

  <% if @phone_activation.origin == 'app' %>
    <p>
      <b>Inserimento:</b>
      full app
    </p>
  <% end %>
  <% if @phone_activation.automatic_recharge? %>
    <p class="red">
      <b><%= PhoneActivation.human_attribute_name("automatic_recharge") %>:</b>
      <% if @phone_activation.plan.automatic_recharge_optional? %>
        <%= best_in_place @phone_activation,
                          :automatic_recharge,
                          class:      'text-red',
                          as:         :select,
                          collection: [[true, "Sì"], [false, "No"]]
        %>
      <% else %>
        <%= true_or_false(@phone_activation.automatic_recharge) %>
      <% end %>
    </p>
  <% end %>
  <% if @phone_activation.operator.very_mobile? && !(@phone_activation.status_ko? || @phone_activation.status_complete?) && same_activations(@phone_activation).present? %>
    <p class="red-alert">Attenzione, ci sono più attivazioni a nome di questo
      cliente, verifica prima di attivare.</p>
    <p><%= same_activations(@phone_activation) %></p>
  <% end %>
</div>
