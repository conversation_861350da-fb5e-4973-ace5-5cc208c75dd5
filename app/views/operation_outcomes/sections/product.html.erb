<%= hint_for_sections %>
<% @phone_activation = @phone_activation.reload %>
<% if @phone_activation.product %>
  <%= section_column do %>
    <p>
      <b>Prodotto:</b>
      <span><%= product_full_name(@phone_activation) %></span>
    </p>
    <p>
      <b><%= Item.human_attribute_name("product_code") %>:</b>
      <span><%= "#{@phone_activation.product.code} (#{@phone_activation.product.operators.map(&:name).join(", ")})" %></span>
    </p>    
    <p>
      <b><%= Item.human_attribute_name("warehouse") %>:</b>
      <% if @phone_activation.product_item %>
        <span><%= @phone_activation.product_item.warehouse.name %></span>
      <% end %>
    </p>
    <p>
      <b><%= Item.human_attribute_name("serial") %>:</b>
      <% if @phone_activation.product_item %>
        <%= @phone_activation.product_item.serial %> <%= link_to fa_icon('external-link-alt'), items_path(search: { serial_eq: @phone_activation.product_item.serial }), target: :blank, title: 'Visualizza dettaglio' %>
      <% else %>
        <span>Non è stato selezionato un seriale per il prodotto specificato</span>
      <% end %>
    </p>
    <p>
      <b><%= product_price_label_for(@phone_activation.phone_activation_kind) %>:</b>
      <span><%= best_in_place @phone_activation, :public_price %></span>
    </p>
    <div class='upfront-details'>
      <b>Anticipo:</b>
      <span>
        <%= number_to_currency(@phone_activation.upfront) %>
        <% if @phone_activation.tls_upfront.present? %>
          <%= link_to fa_icon('external-link-alt'), tls_upfronts_path(search: { phone_activation_hex_id_eq: @phone_activation.hex_id }), target: :blank, title: 'Visualizza dettaglio anticipo' %>
        <% end %>
      </span>
      <% if can?(:create, PaymentRequest) %>
        <%= render 'shared/payment_requestable_payment_request', payment_requestable: @phone_activation %>
      <% end %>
    </div>
    <% if @phone_activation.include_final_installment? %>
      <div class='upfront-details'>
        <b>Rata finale:</b>
        <span><%= product_final_installment_price_for(@phone_activation) %></span>
      </div>
    <% end %>
    <% if @phone_activation.product_item_additional_serial.present? %>
      <p>
        <b><%= Item.human_attribute_name(:additional_serial) %>:</b>
        <span><%= @phone_activation.product_item_additional_serial %></span>
      </p>
    <% end %>
  <% end %>
<% else %>
  <%= hint_for_sections 'No product' %>
<% end %>
<% if @phone_activation.sim %>
  <%= section_column do %>
    <p>
      <b>Sim:</b>
      <span><%= @phone_activation.sim.name %></span>
    </p>
    <p>
      <b><%= Item.human_attribute_name("warehouse") %>:</b>
      <% if @phone_activation.sim_item %>
        <span><%= @phone_activation.sim_item.warehouse.name %></span>
      <% end %>
    </p>
    <% if @phone_activation.sim_item %>
      <p>
        <b><%= Item.human_attribute_name("serial") %>:</b>
        <%= @phone_activation.sim_item.serial %> <%= link_to fa_icon('external-link-alt'), items_path(search: { serial_eq: @phone_activation.sim_item.serial }), target: :blank, title: 'Visualizza dettaglio' %>
      </p>
      <p>
        <b><%= Item.human_attribute_name("phone_number") %>:</b>
        <%= @phone_activation.sim_item.phone_number %>
      </p>
      <% if @phone_activation.sim_item_additional_serial.present? %>
        <p>
          <b><%= Item.human_attribute_name(:additional_serial) %>:</b>
          <span><%= @phone_activation.sim_item_additional_serial %></span>
        </p>
      <% end %>
    <% else %>
      <span>Non è stato selezionato un seriale per la sim specificata</span>
    <% end %>
  <% end %>
<% else %>
  <%= hint_for_sections 'No sim' %>
<% end %>