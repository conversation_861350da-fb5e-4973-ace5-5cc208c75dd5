<div class="row" id="items_operations">
  <div class="col-md-12 mt-2">
    <% if can? :create, Item %>
      <div class="col-md-12">
        <div class="panel panel-primary">
          <div class="panel-heading">Operazioni su articoli (per massiva max 5000 articoli)</div>
          <div class="panel-body item-panels">
            <div class="row col-md-12">
              <% unless current_user.is?(:dealer) %>
                <div class="row col-md-2">
                  <%= select_tag 'massive_action_select', options_for_select(items_massive_select_options), class: "form-control massive_select", include_blank: "Selezione massiva" %>
                </div>
              <% end %>
              <%= button_link_to fa_icon("cogs") , '#', class: "btn btn-sm massive", id: "massive-edit-button" %>
              <% unless @warehouse.present? %>
                <%= button_link_to fa_icon("times-circle"), '#', class: "btn btn-sm red", id: "massive_destroy" %>
              <% end %>
              <% if @warehouse.present? %>
                <%= button_link_to "Creazione massiva", massive_new_item_warehouses_path(warehouse_id: @warehouse.id), class: "btn btn-sm not_massive" %>
              <% else %>
                <%= button_link_to "Creazione massiva", massive_new_item_warehouses_path, class: "btn btn-sm not_massive" %>
              <% end %>
              <% if @warehouse.present? %>
                <%= button_link_to "Creazione singola", new_item_warehouses_path(warehouse_id: @warehouse.id), class: "btn btn-sm" %>
              <% else %>
                <%= button_link_to "Creazione singola", new_item_warehouses_path, class: "btn btn-sm" %>
              <% end %>
              <% if can? :massive_tranfer, Item %>
                <%= button_link_to "Trasferimento", massive_transfer_items_warehouses_path, class: "btn btn-sm" %>
              <% end %>
              <%= button_link_to "Aggiorna prodotti a quantità", new_quantity_warehouses_path, class: "btn btn-sm" %>
              <%= render 'warehouses/items/items_update'%>
              <%= button_link_to "Esporta XLS", @xls_url, class: "btn btn-sm" %>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>
