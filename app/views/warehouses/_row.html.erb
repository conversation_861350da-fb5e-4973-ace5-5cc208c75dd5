<% cache warehouse, key: "#{warehouse.updated_at.to_i}_#{warehouse.id}_#{current_user.id}" do %>
<tr class="<%= 'disabled' if warehouse.disabled? %>">
  <td class="id_column"><%= warehouse.id %></td>
  <td><%= warehouse.name %></td>
  <td><%= [warehouse.address, warehouse.number].join(", ") %></td>
  <td><%= warehouse.zip %></td>
  <td><%= warehouse.city ? warehouse.city.description.titleize : warehouse.foreign_city %></td>
  <td><%= warehouse.province&.code %></td>
  <td><%= warehouse.country %></td>
  <td><%= render_dealer_icons(warehouse.dealer) unless current_user.is?(:dealer) %> <%= link_to warehouse.dealer, dealer_path(warehouse.dealer) %></td>
  <% if can? :index, Item %>
    <td>
      <%= button_group do %>
        <%= button_link_for_show warehouse_path(warehouse) %>
        <% if can? :update, warehouse %>
          <%= button_link_for_edit edit_warehouse_path(warehouse) %>
        <% end %>
        <% if can?(:list, :audits) %>
          <%= button_link_to_audit(warehouse) %>
        <% end %>
      <% end %>
    </td>
  <% end %>
</tr>
<% end %>