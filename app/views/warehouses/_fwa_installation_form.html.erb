<%= section_block 'Dettagli Installatore', class: 'operator-tls-hide-fields' do %>
  <div class="field">
    <%= f.input :fwa_installer, wrapper: :vertical_boolean %>
  </div>

  <div id="fwa_installer_container">
    <div class="field">
      <%= f.input :enabled_to_work_at_height, collection: [false, true],
                  label_method:  -> (state) { Warehouse.human_attribute_name("enabled_to_work_at_height/#{state}") },
                  include_blank: false %>
    </div>
    <div class="field">
      <%= f.input :third_party_installations, collection: [false, true],
                  label_method:  -> (state) { Warehouse.human_attribute_name("third_party_installations/#{state}") },
                  include_blank: false %>
    </div>
    <div class="row">
      <div class="col-md-6 field">
        <%= f.input :fwa_phone_number %>
      </div>
      <div class="col-md-6 mt-2 field">
        <%= f.input :fwa_whatsapp_enabled, as: :boolean %>
      </div>
    </div>
    <div class="<%= f.object&.errors.has_key?(:installation_area_cities) ? 'field_with_errors' : 'field' %>">
      <%= f.label :installation_area_cities %>
      <br/>
      <%= f.text_field :installation_area_city_ids, class: 'tokenInput',
                       data: {
                           token_input_theme:       'facebook',
                           token_input_prepopulate: f.object.installation_area_cities.map { |c| { id: c.id, name: c.description } }.to_json
                       }
      %>
    </div>
    <% if current_user.is?(:admin, :super_user) && f.object.persisted? && f.object.third_party_installations %>
      <div class="field">
        <label>
          Salva con importa lista comuni
          <%= file_field(:csv, :attached, accept: 'text/csv') %>
        </label>
      </div>
    <% end %>
  </div>
<% end %>
