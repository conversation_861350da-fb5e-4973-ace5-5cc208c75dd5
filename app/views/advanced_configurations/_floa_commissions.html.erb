<div class="modal fade" id="floa_commissions_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <%= form_for @floa_commissions, remote: true do |f| %>
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="exampleModalLabel">Commissioni Floa</h5>
        </div>
        <div class="modal-body">
          <% FloaFinancing::INSTALLMENTS.each do |installment| %>
            <%= label_tag "Floa #{installment} rate" %>
            <%= text_field_tag "application_setting[value][#{installment}]",
                               @floa_commissions.floa_commissions_obj[installment.to_s],
                               data: { inputmask: "'alias': 'decimal', 'digits': '2', 'max': '100'" } %>
          <% end %>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
          <%= f.submit 'Salva', class: 'btn btn-primary' %>
        </div>
      </div>
    </div>
<% end %>
</div>
