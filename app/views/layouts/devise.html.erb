<!DOCTYPE html>
<html translate="no">
<head>
  <title> <%= Settings.application.name %> - Login </title>
  <% Rails.env == 'production' ? 'SpazioKolme' : 'Bost' %>
  <%= stylesheet_link_tag "application", :media => "all" %>
  <%= javascript_include_tag "application" %>
  <%= favicon_for_environment %>
  <%= csrf_meta_tags %>
  <script language="javascript">
      $(document).ready(function () {
          <%= yield :javascript %>
      });
  </script>
</head>
<body>
<div id="devise">
  <%- if controller_name.in? %w(sessions two_factor_authentication tls_upfronts) %>
    <%= render partial: 'layouts/logo' %>
  <% end %>
  <div id="devise_content">
    <%= gflash %>
    <%= render partial: 'application/flash_to_gritter' %>
    <%= yield %>
  </div>
</div>
<div id="power_user_box" style='display:none'></div>
<script type="text/javascript">
    $(document).ready(function () {
        $(function () {
            var $alert = $('#alert');
            if ($alert.length) {
                var alerttimer = window.setTimeout(function () {
                    $alert.trigger('click');
                }, 3000);
                $alert.animate({height: $alert.css('line-height') || '50px'}, 200)
                        .click(function () {
                            window.clearTimeout(alerttimer);
                            $alert.animate({height: '0'}, 200);
                        });
            }
        });

        window.App.BestInPlaceInitializer.init()

        $('#call_us').click(function(){
            $('.accordion-body').removeClass('in');
            $('.accordion-body').addClass('collapse');
            $('.call_us').slideToggle();
            $('.contact_us').hide();
        });
        $('#contact_us').click(function(){
            $('.accordion-body').removeClass('in');
            $('.accordion-body').addClass('collapse');
            $('.contact_us').slideToggle();
            $('.call_us').hide();
        });
    })
</script>
<%= ga_snippet %>
</body>
</html>
