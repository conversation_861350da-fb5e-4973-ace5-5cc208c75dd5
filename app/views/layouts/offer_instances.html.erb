<!DOCTYPE html>
<html translate="no">
<head>
  <% Rails.env == 'production' ? 'SpazioKolme' : 'Bost' %>
  <%= stylesheet_link_tag 'application', :media => 'all' %>
  <%= javascript_include_tag "application" %>
  <%= favicon_for_environment %>
  <%= csrf_meta_tags %>
  <%= render partial: 'layouts/google_tag_manager_head' %>

  <script language="javascript">
      $(document).ready(function () {
          <%= yield :javascript %>
      });
  </script>
</head>
<body>
<%= render partial: 'layouts/google_tag_manager_body' %>
<%= render partial: 'layouts/common/modals' %>
<div id="content" class="row">
  <div class="col-sm-3 col-md-2 col-lg-2 sidebar">
    <%= render partial: 'layouts/logo' %>
    <%= render partial: 'layouts/contact_links' %>
    <% if user_signed_in? %>
        <%= render :partial => 'layouts/user_info' %>
    <% end %>
    <div>
      <% if user_signed_in? %>
          <div class='row'>
            <%= render partial: "layouts/menu/#{current_user.role_label}_menu" %>
          </div>
      <% end %>
    </div>
  </div>
  <div id="main" class="col-sm-9 col-md-10 col-lg-10 col-sm-offset-3 col-md-offset-2 col-lg-offset-2">
    <%= gflash %>
    <%= render partial: 'application/flash_to_gritter' %>
    <div class="col-lg-12">
      <%= yield %>
    </div>

  </div>
</div>
<div id="power_user_box" style='display:none'></div>
<script type="text/javascript">
    $(document).ready(function () {
        $(function () {
            var $alert = $('#alert');
            if ($alert.length) {
                var alerttimer = window.setTimeout(function () {
                    $alert.trigger('click');
                }, 3000);
                $alert.animate({height: $alert.css('line-height') || '50px'}, 200)
                    .click(function () {
                        window.clearTimeout(alerttimer);
                        $alert.animate({height: '0'}, 200);
                    });
            }
        });

        window.App.BestInPlaceInitializer.init()

      /* Javascript for Nice Select */
        $('.input-group-btn button').click(function () {
            var el = $(this).parent().prev();
            el.simulate('mousedown');
        })

        $('#call_us').click(function () {
            $('.accordion-body').removeClass('in');
            $('.accordion-body').addClass('collapse');
            $('.call_us').slideToggle();
            $('.contact_us').hide();
            $('.address').hide();
        });
        $('#contact_us').click(function () {
            $('.accordion-body').removeClass('in');
            $('.accordion-body').addClass('collapse');
            $('.contact_us').slideToggle();
            $('.call_us').hide();
            $('.address').hide();
        });
        $('#address').click(function () {
            $('.accordion-body').removeClass('in');
            $('.accordion-body').addClass('collapse');
            $('.address').slideToggle();
            $('.contact_us').hide();
            $('.call_us').hide();
        });
    })
</script>
<%= ga_snippet %>
<%= render partial: 'layouts/expirable' %>
</body>
</html>
