<%= render 'shared/modal', modal_classes: 'energy_supply_details ' do %>
  <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-action="click->modal#close">&times;</button>
  <div class="modal-body" data-bs-theme="activation">
    <%= render partial: 'energy_contracts/api_errors' %>
    <%= simple_form_for @energy_supply_detail,
                        url: update_energy_data_energy_contract_energy_supply_detail_path(@energy_contract, @energy_supply_detail),
                        data: { controller: "energy-supply-details" } do |f| %>
      <div class="column">
        <%= render 'energy_data_fields', f: f %>
      </div>
      <div class="row text-center submit-button-container">
        <%= f.submit t("buttons.confirm"), class: 'btn btn-primary btn-long' %>
      </div>
    <% end %>
  </div>
<% end %>
