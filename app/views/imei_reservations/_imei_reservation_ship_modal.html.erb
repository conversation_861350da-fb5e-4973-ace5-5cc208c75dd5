<%= subscribe_to "/notify_imei_reservation_automatic_shipment/#{imei_reservation.id}" %>
<div id="imei-reservation-ship-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title" id="exampleModalLongTitle"><%= "Prenotazione Lok-me confermata" %></h3>        
      </div>
      <div class="modal-body" style="font-size: 16px">
        <p>
          Inserisci di seguito l'eventuale riferimento (ad es. il nome del cliente) che vuoi compaia sul pacchetto.
        </p>
        <div class="form-container">
          <div class="col-md-8">
            <%= simple_form_for imei_reservation, url: send_to_dhl_imei_reservation_path(imei_reservation), remote: true, html: { class: "ship-imei-reservation spinnered"} do |f| %>
              <div class="field">
                <%= f.input :dealer_notes, label: false, input_html: { maxlength: 25 } %>
              </div>
              <%= f.submit "Salva notazione", id: "ship-reservation-button", class: 'btn btn-primary' %>
            <% end%>
          </div>
          <div class="col-md-2" id="modal_countdown">
                <%= countdown_tag :div,
                            imei_reservation.auxiliary_job.run_at - Time.now,
                            nil,
                            extra_data: { 
                              autostart:             false,
                              clock_face:            "MinuteCounter",
                              nominal_minutes_to_go: imei_reservation.expiration_offset_minutes,
                              reservation_id:        imei_reservation.id
                            } 
                %>
              </div>
        </div>
      </div>      
    </div>
  </div>
</div>