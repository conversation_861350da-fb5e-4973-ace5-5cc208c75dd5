<div class=<%= "pending-reservation" if imei_reservation.requested? %>>
  <span class='mr-1'><%= "#{ImeiReservation.human_attribute_name(imei_reservation.state)}#{ "  -" if imei_reservation.requested? }" %></span>
  <% if imei_reservation.requested? && imei_reservation.expires_at.present? %>
    <% seconds_to_go = [0, (imei_reservation.expires_at - Time.now).ceil].max %>
          <%= countdown_tag :div,
                        seconds_to_go,
                        nil,
                        {
                          extra_data: {
                            expiry_callback_url: expire_imei_reservation_path(imei_reservation, context: "index")
                          }
                        },
                        countdown_time_format(seconds_to_go) %>
  <% end %>
</div>