<tr id=<%= imei_reservation.id %>>
  <td><%= imei_reservation.id %></td>
  <td><%= imei_reservation.created_at.strftime("%d/%m/%Y %H:%M") %> </td>
  <% if current_user.is?(:dealer) %>
    <td><%= imei_reservation.warehouse_name %></td>
  <% else %>
    <td><%= link_to imei_reservation.warehouse_name, warehouse_path(imei_reservation.warehouse), target: "_blank" %></td>
  <% end %>
  <% if current_user.internal_user? %>
    <td><%= imei_reservation.channel %></td>
  <% end %>
  <td><%= imei_reservation.product_name %></td>
  <% if current_user.internal_user? %>
    <td><%= imei_reservation.product.public_price_list %></td>
  <% end %>
  <td>
    <% if imei_reservation.dealer.gallery_gd? %>
      <%= imei_reservation.customer_full_name %>
    <% else %>
      <%= imei_reservation.dealer_notes %>
    <% end %>
  </td>
  <td>
    <%= render partial: 'imei_reservations/imei_reservation_state_cell', locals: { imei_reservation: imei_reservation } %>
  </td>
  <% unless current_user.is?(:dealer) %>
    <td class="auto-icon-container"><%= fa_icon("check") if imei_reservation.auto? %></td>
  <% end %>
  <td>
    <%= button_group do %>
        <% if can?(:show_pending, imei_reservation) %>
          <%= button_link_to fa_icon("spinner", class: "fa-spin"),
                            new_imei_reservation_path(pending_reservation_id: imei_reservation.id, warehouse_id: imei_reservation.warehouse_id),
                            title: "Inserimento in corso",
                            class: "btn btn-default imei_reservation_pending" %>
        <% end %>
        <% if can?(:show, imei_reservation) %>
          <%= toggle_show_modal_button(imei_reservation) %>
        <% end %>
        <% if imei_reservation.fattura_cliente_finale.present? %>
          <%= button_link_to fa_icon('file-pdf'), download_automatic_invoice_path(imei_reservation.fattura_cliente_finale), title: 'Scarica documento cliente', class: 'btn btn-default' %>
        <% end %>
        <% unless imei_reservation.requested? %>
          <%= tracking_url_button(imei_reservation) %>
        <% end %>
        <% if can?(:download_credit_transfer, imei_reservation) %>
          <%= button_link_to fa_icon('receipt'), download_credit_transfer_imei_reservation_path(imei_reservation), title: 'Genera Modulo Cessione', class: 'btn btn-default green' %>
        <% end %>
        <% if can?(:list, :audits) %>
          <%= button_link_to_audit(imei_reservation) %>
        <% end %>
        <% if can?(:manually_confirm, imei_reservation) && imei_reservation.state.in?(["expired", "canceled"]) %>
          <%= button_link_to fa_icon('thumbs-up'), pre_manually_confirm_imei_reservation_path(imei_reservation),
                             title: 'Conferma manualmente',
                             data:   { confirm: "Vuoi confermare manualmente questa Lok-me?" },
                             remote: true,
                             class: "green",
                             id: "manually_confirm_lok_me_#{imei_reservation.id}" %>
        <% end %>
        <% if can?(:read, GammaCall) && can?(:debug, GammaCall) && imei_reservation.gamma_calls.any? %>
          <%= button_link_to fa_icon("bug"), gamma_calls_path(caller_type: "ImeiReservation", caller_id: imei_reservation.id, commit: I18n.t("search.clean")), title: "DEBUG", class: "grey" %>
        <% end %>
        <% if can?(:manage, imei_reservation) && imei_reservation.suspended? && imei_reservation.findomestic_financing? %>
          <%= button_link_to fa_icon('calendar-times'), cancel_findomestic_financing_imei_reservation_path(imei_reservation),
                            method: :patch,
                            title: 'Annulla Finanziamento',
                            data:   { confirm: "Si sta per annullare il finanziamento Findomestic per la prenotazione #{imei_reservation.id}, procedere?" },
                            class: 'red' %>
        <% end %>
        <% if can?(:delete, imei_reservation) && !imei_reservation.state.in?(["expired", "canceled"]) %>
          <%= button_link_to fa_icon('thumbs-down'), delete_imei_reservation_path(imei_reservation),
                            method: :patch,
                            title: 'Annulla',
                            data:   { confirm: "Si sta per annullare la prenotazione #{imei_reservation.id}, procedere?" },
                            class: 'red' %>
        <% end %>
    <% end %>
  </td>
</tr>

