<% content_for :title, 'Findomestic API Calls' %>

<div class="row">
  <div class="col-md-12">
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">Findomestic API Calls</h3>
      </div>
      <div class="panel-body">

        <%= search_form_for @search, url: findomestic_api_calls_path, method: :get, local: true do |f| %>
          <div class="row">
            <div class="col-md-3">
              <%= f.search_field :imei_reservation_id_eq, placeholder: 'Lok-Me ID', class: 'form-control' %>
            </div>
            <div class="col-md-3">
              <%= f.select :call_type_eq, options_for_select([['All', '']] + FindomesticApiCall::CALL_TYPES.map { |t| [t.humanize, t] }), {}, { class: 'form-control' } %>
            </div>
            <div class="col-md-3">
              <%= f.select :status_eq, options_for_select([['All', '']] + FindomesticApiCall::STATUSES.map { |s| [s.humanize, s] }), {}, { class: 'form-control' } %>
            </div>
            <div class="col-md-3">
              <%= f.submit 'Search', class: 'btn btn-primary' %>
              <%= link_to 'Reset', findomestic_api_calls_path, class: 'btn btn-default' %>
            </div>
          </div>
        <% end %>

        <div class="table-responsive" style="margin-top: 20px;">
          <table class="table table-striped">
            <tbody>
              <tr>
                <th>ID</th>
                <th>Lok-Me ID</th>
                <th>Call Type</th>
                <th>Status</th>
                <th>HTTP Status</th>
                <th>Retry Count</th>
                <th>Created At</th>
                <th>Actions</th>
              </tr>
              <% @findomestic_api_calls.each do |api_call| %>
                <tr class="<%= status_row_class(api_call.status) %>">
                  <td><%= api_call.id %></td>
                  <td>
                    <%= link_to api_call.imei_reservation_id,
                        imei_reservation_path(api_call.imei_reservation),
                        target: '_blank' %>
                  </td>
                  <td><%= api_call.call_type.humanize %></td>
                  <td>
                    <span class="label label-<%= status_label_class(api_call.status) %>">
                      <%= api_call.status.humanize %>
                    </span>
                  </td>
                  <td><%= api_call.http_status %></td>
                  <td><%= api_call.retry_count %></td>
                  <td><%= api_call.created_at.strftime('%d/%m/%Y %H:%M') %></td>
                  <td>
                    <%= link_to 'View', findomestic_api_call_path(api_call),
                        class: 'btn btn-sm btn-info' %>
                    <% if api_call.failed? && ['activate_installment', 'cancel_installment'].include?(api_call.call_type) %>
                      <%= link_to 'Retry', retry_findomestic_api_call_path(api_call),
                          method: :post,
                          class: 'btn btn-sm btn-warning',
                          confirm: 'Are you sure you want to retry this API call?' %>
                    <% end %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>

        <%= paginate @findomestic_api_calls if respond_to?(:paginate) %>
      </div>
    </div>
  </div>
</div>

<% content_for :javascript do %>
  <script>
    // Auto-refresh every 30 seconds for pending/retrying calls
    <% if @findomestic_api_calls.any? { |call| ['pending', 'retrying'].include?(call.status) } %>
      setTimeout(function() {
        window.location.reload();
      }, 30000);
    <% end %>
  </script>
<% end %>
