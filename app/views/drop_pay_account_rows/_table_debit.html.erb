<table class="index drop_pay_account_rows">
  <tr>
    <% unless current_user.is?(:dealer)%>
      <th width="15%"><%= DropPayAccountRow.human_attribute_name(:dealer) %></th>
    <% end %>
    <th><%= DropPayAccountRow.human_attribute_name(:document_number) %></th>
    <th><%= DropPayAccountRow.human_attribute_name(:document_issued_at) %></th>
    <th><%= DropPayAccountRow.human_attribute_name(:document_expires_at) %></th>
    <th><%= DropPayAccountRow.human_attribute_name(:total_amount) %></th>
    <th><%= DropPayAccountRow.human_attribute_name(:billing_debit_amount) %></th>
    <th><%= DropPayAccountRow.human_attribute_name(:residual_amount) %></th>
    <th width="19%"><%= DropPayAccountRow.human_attribute_name(:description) %></th>
    <th><%= DropPayAccountRow.human_attribute_name(:state) %></th>
    <th></th>
  </tr>
  <%= render partial: 'drop_pay_account_rows/row_debit', collection: @drop_pay_account_rows, as: :drop_pay_account_row %>
</table>
