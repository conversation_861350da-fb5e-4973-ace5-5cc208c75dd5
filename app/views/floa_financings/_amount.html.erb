<div class="row">
  <div class="col-md-8 col-md-offset-2">
    <h2 class="floa__title floa__title--big floa__title--editable">
      <span class="floa-amount" id="editAmount"><%= number_to_currency(@floa_financing.amount, format: '%n', delimiter: '') %></span><span>&euro;</span>
      <span class="hidden material-symbols-outlined floa-amount__edit customer_view_toggle editor_controls" id="editAmountBtn">stylus</span>
    </h2>
    <%= hidden_field_tag :amount, @floa_financing.amount %>
    <%= f.hidden_field :installments %>
    <%= f.hidden_field :delta %>
    <%= hidden_field_tag :floa_financing_total_financing_value, @floa_financing.total_financing_value %>
  </div>
</div>
<div class="hidden customer_view_toggle editor_controls">
  <div class="row">
    <div class="col-md-12 amount_editor__container">
      <%= link_to '#', class: 'amount_editor', data: { amount: -10 } do %>
        -10€ <span class="arrow--left"></span>
      <% end %>
      <%= link_to '#', class: 'amount_editor', data: { amount: -5 } do %>
        -5€ <span class="arrow--left"></span>
      <% end %>
      <%= link_to '#', class: 'amount_editor', data: { amount: 5 } do %>
        <span class="arrow--right"></span> +5€
      <% end %>
      <%= link_to '#', class: 'amount_editor', data: { amount: 10 } do %>
        <span class="arrow--right"></span> +10€
      <% end %>
    </div>
  </div>
  <% if should_show_profit? %>
    <div class="row">
      <div class="col-md-12">
        <p class="floa-amount__margin floa-color--primary">Il tuo margine è di <span id="profit"><%= number_to_currency(@floa_financing.profit, format: '%n', delimiter: '') %></span>€</p>
        <p class="floa-amount__margin floa-color--primary <%= 'hidden' unless @floa_financing.fixed_for_zero_profit %>" id="fixed_for_zero_profit">Prezzo adeguato per non avere margine negativo</p>
      </div>
    </div>
  <% end %>
</div>
