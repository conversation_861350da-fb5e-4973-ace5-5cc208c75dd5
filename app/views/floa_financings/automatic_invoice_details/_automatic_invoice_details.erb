<div class="container">
  <%= render partial: 'floa_financings/automatic_invoice_details/invoice_customer_data', locals: { serialized_customer_data: automatic_invoice_resource.serialized_data['customer'] } %>
  <div class="row top100">
    Milano, <%= l automatic_invoice_resource.fattura_cliente_finale.created_at, :format => :date_only %>
  </div>
  <div class="row tls_bold font-big">
    Fattura Commerciale <%= automatic_invoice_resource.fattura_cliente_finale.invoice_number %> del
    <%= l automatic_invoice_resource.fattura_cliente_finale.created_at, :format => :date_only %>
  </div>

  <div class="row top30" id="fattura">
    <div class="col-sm-12">
      <table>
        <thead>
        <tr>
          <td>Prodotto</td>
          <td>Seriale</td>
          <td>Imponibile</td>
          <td>IVA</td>
          <td width="120">Totale</td>
        </tr>
        </thead>
        <tbody>
        <tr>
          <td><%= automatic_invoice_resource.floa_financing_product.name %></td>
          <td><%= automatic_invoice_resource.floa_financing_product.serial %></td>
          <td><%= number_to_currency automatic_invoice_resource.amount / 1.22 %></td>
          <td><%= number_to_currency (automatic_invoice_resource.amount - (automatic_invoice_resource.amount / 1.22)) %></td>
          <td><%= number_to_currency automatic_invoice_resource.amount %></td>
        </tr>
        <tr>
          <td colspan="100%" class="white" height="30"></td>
        </tr>
        <tr class="total">
          <td class="white"></td>
          <td colspan="3">
            <p>Prima rata pagata<p/>
            <p>Ulteriori rate da versare a Floa<p/>
            <p>Da pagare a Kolme</p>
          </td>
          <td>
            <p><%= number_to_currency (automatic_invoice_resource.paid_amount) %></p>
            <p><%= number_to_currency (automatic_invoice_resource.remaining_amount) %></p>
            <p>&euro;<span style="margin-left: 15px">-</p>
          </td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>

  <div class="row" id="valenza">
    <div class="col-sm-10 col-sm-offset-1">
      <div>La presente fattura rappresenta unicamente il documento di acquisto del bene.</div>
      <div>Il corrispettivo è riscosso mediante il piano di rateizzazione sottoscritto con FLOA.</div>
    </div>
  </div>

  <div class="row font-big" id="warehouse_data" align="center">
    <div class="fieldset">
      <h1><span>Il tuo negozio di fiducia</span></h1>
      <table cellpadding="10">
        <tr>
          <% if automatic_invoice_resource.dealer.main_cluster_warehouse&.logo.present? %>
            <td width="50%" align="center">
              <%= image_tag embed_image_to_pdf(automatic_invoice_resource.dealer.main_cluster_warehouse.logo.small), height: 100 %>
            </td>
          <% end %>
          <td>
            <strong><%= automatic_invoice_resource.serialized_data.dig('warehouse', 'name') %></strong><br/>
            <nobr><%= automatic_invoice_resource.serialized_data.dig('warehouse', 'address') %> <%= automatic_invoice_resource.serialized_data.dig('warehouse', 'number') %></nobr><br/>
            <nobr><%= automatic_invoice_resource.serialized_data.dig('warehouse', 'zip') %>, <%= automatic_invoice_resource.serialized_data.dig('warehouse', 'city_and_country') %></nobr><br/><br/>

            <% if automatic_invoice_resource.serialized_data.dig('warehouse', 'phone_number').present? %>
              <%= image_tag wicked_pdf_asset_base64(automatic_invoice_resource.dealer.main_cluster_warehouse.floa_financing_phone_number_logo), height: 15, style: 'margin-right: 10px' %><%= automatic_invoice_resource.serialized_data.dig('warehouse', 'phone_number') %>
            <% end %>
          </td>
        </tr>
      </table>
    </div>
  </div>
</div>
