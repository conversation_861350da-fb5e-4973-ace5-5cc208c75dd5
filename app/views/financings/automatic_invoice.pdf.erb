<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
  <!-- Latest compiled and minified CSS -->
  <style>
    .row {
      margin-bottom: 10px;
    }

    .row:before,
    .row:after {
      display: table;
      content: " ";
    }

    .row:after {
      clear: both;
    }

    .row:before,
    .row:after {
      display: table;
      content: " ";
    }

    .row:after {
      clear: both;
    }

    .container {
      font-size: 11px;
      width: 100%;
    }

    .col-sm-1,
    .col-sm-2,
    .col-sm-3,
    .col-sm-4,
    .col-sm-5,
    .col-sm-6,
    .col-sm-7,
    .col-sm-8,
    .col-sm-9,
    .col-sm-10,
    .col-sm-11 {
      float: left;
    }

    .col-sm-12 {
      width: 100%;
    }

    .col-sm-11 {
      width: 91.66666666666666%;
    }

    .col-sm-10 {
      width: 83.33333333333334%;
    }

    .col-sm-9 {
      width: 75%;
    }

    .col-sm-8 {
      width: 66.66666666666666%;
    }

    .col-sm-7 {
      width: 58.333333333333336%;
    }

    .col-sm-6 {
      width: 50%;
    }

    .col-sm-5 {
      width: 41.66666666666667%;
    }

    .col-sm-4 {
      width: 33.33333333333333%;
    }

    .col-sm-3 {
      width: 25%;
    }

    .col-sm-2 {
      width: 16.666666666666664%;
    }

    .col-sm-1 {
      width: 8.333333333333332%;
    }

    .col-sm-pull-12 {
      right: 100%;
    }

    .col-sm-pull-11 {
      right: 91.66666666666666%;
    }

    .col-sm-pull-10 {
      right: 83.33333333333334%;
    }

    .col-sm-pull-9 {
      right: 75%;
    }

    .col-sm-pull-8 {
      right: 66.66666666666666%;
    }

    .col-sm-pull-7 {
      right: 58.333333333333336%;
    }

    .col-sm-pull-6 {
      right: 50%;
    }

    .col-sm-pull-5 {
      right: 41.66666666666667%;
    }

    .col-sm-pull-4 {
      right: 33.33333333333333%;
    }

    .col-sm-pull-3 {
      right: 25%;
    }

    .col-sm-pull-2 {
      right: 16.666666666666664%;
    }

    .col-sm-pull-1 {
      right: 8.333333333333332%;
    }

    .col-sm-pull-0 {
      right: 0;
    }

    .col-sm-push-12 {
      left: 100%;
    }

    .col-sm-push-11 {
      left: 91.66666666666666%;
    }

    .col-sm-push-10 {
      left: 83.33333333333334%;
    }

    .col-sm-push-9 {
      left: 75%;
    }

    .col-sm-push-8 {
      left: 66.66666666666666%;
    }

    .col-sm-push-7 {
      left: 58.333333333333336%;
    }

    .col-sm-push-6 {
      left: 50%;
    }

    .col-sm-push-5 {
      left: 41.66666666666667%;
    }

    .col-sm-push-4 {
      left: 33.33333333333333%;
    }

    .col-sm-push-3 {
      left: 25%;
    }

    .col-sm-push-2 {
      left: 16.666666666666664%;
    }

    .col-sm-push-1 {
      left: 8.333333333333332%;
    }

    .col-sm-push-0 {
      left: 0;
    }

    .col-sm-offset-12 {
      margin-left: 100%;
    }

    .col-sm-offset-11 {
      margin-left: 91.66666666666666%;
    }

    .col-sm-offset-10 {
      margin-left: 83.33333333333334%;
    }

    .col-sm-offset-9 {
      margin-left: 75%;
    }

    .col-sm-offset-8 {
      margin-left: 66.66666666666666%;
    }

    .col-sm-offset-7 {
      margin-left: 58.333333333333336%;
    }

    .col-sm-offset-6 {
      margin-left: 50%;
    }

    .col-sm-offset-5 {
      margin-left: 41.66666666666667%;
    }

    .col-sm-offset-4 {
      margin-left: 33.33333333333333%;
    }

    .col-sm-offset-3 {
      margin-left: 25%;
    }

    .col-sm-offset-2 {
      margin-left: 16.666666666666664%;
    }

    .col-sm-offset-2-bis {
      margin-left: 16.000000000000007%;
    }

    .col-sm-offset-1 {
      margin-left: 8.333333333333332%;
    }

    .col-sm-offset-0 {
      margin-left: 0;
    }

    #fattura {
      background-color: #FFFFFF;
    }

    #fattura table {
      background-color: #FFFFFF;
      border-collapse: collapse;
      width: 100%;
      margin-bottom: 20px;
      border: 1px solid #000000;
    }

    #fattura table {
      border: 1px solid #000000;
    }

    #fattura table thead tr {
      border: 1px solid #000000;
    }

    #fattura table tbody tr {
      border-right: 1px solid #000000;
    }

    #fattura table thead tr td {
      text-align: center;
      font-weight: bold;
    }

    #fattura table td {
      border-right: 1px solid #000000;
      padding-left: 5px;
    }

    .height100 {
      height: 100px;
    }

    #box-totale {
      border: 1px solid #000000;
      margin-top: 20px;
      font-weight: bold;
      margin-bottom: 20px;

      /*padding: 5px;*/
    }

    #rivenditore {
      border: 1px solid #000000;
      /*margin-top: 20px;*/
      background-color: #f1f1f1;
      font-weight: bold;
      font-size: 13px;
      height: 160px;
      padding-top: 5px;
    }

    #offerta {
      border: 1px solid #000000;
      /*margin-top: 20px;*/
      background-color: #f1f1f1;
      font-weight: bold;
      font-size: 13px;
      height: 160px;
      padding-top: 5px;
    }

    #valenza {
      border: 1px solid #000000;
      margin-top: 200px;
      text-align: center;
      padding: 5px;
    }

    #kolme {
      font-size: 22px;
      font-weight: bold;
    }

    #kolme_address {
      font-size: 12px;
    }

    .bold {
      font-weight: bold;
    }

    .text-left {
      text-align: left;
    }

    .text-right {
      text-align: right;
    }

    .top30 {
      margin-top: 30px;
    }

    .top60 {
      margin-top: 60px;
    }

    .top100 {
      margin-top: 100px;
    }
  </style>
</head>
<body>
  <% if @automatic_invoice.created_at < Time.parse(Settings.financings.new_invoice_format_start_date) %>
    <%= render partial: "financings/invoice_without_upfront_page" %>
  <% else %>
    <%= render partial: "financings/invoice_with_upfront_page" %>
  <% end %>
</body>
</html>
