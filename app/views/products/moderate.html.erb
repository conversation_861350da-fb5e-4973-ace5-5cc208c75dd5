<%= page_title('Prodotti', 'Lista Moderazioni') %>

<%= render partial: 'products/moderate_search' %>

<div class="page_info">
  <%= page_entries_info @products %>
</div>
<% unless @products.empty? %>
    <table>
      <tr>
        <th><%= sort_link @search, "code", Product.human_attribute_name("code") %></th>
        <th><%= sort_link @search, "attiva_brand", Product.human_attribute_name("attiva_brand") %></th>
        <th><%= sort_link @search, "name", Product.human_attribute_name("name") %></th>
        <th><%= sort_link @search, "amount_in_system_owner_warehouse", 'Giac.' %></th>
        <th><%= sort_link @search, "dealer_price_vat_excluded_a", 'Riv. A' %></th>
        <th><%= sort_link @search, "public_price_list", 'Pub.' %></th>
        <th class="tools"></th>
      </tr>
      <% @products.each do |product| %>
          <tr class="<%= 'deleted' if product.archived? %>">
            <td><%= product.code %></td>
            <td><%= product.attiva_brand.name %></td>
            <td><%= product.name %></td>
            <td><%= product.stock_count %></td>
            <td><%= number_to_currency product.dealer_price_vat_excluded_a %></td>
            <td><%= number_to_currency product.public_price_list %></td>
            <td>
              <%= button_group do %>
                  <%= button_link_for_show show_moderate_products_path(id: product) %>
                  <% if !product.archived? %>
                      <%= button_link_to fa_icon('flask'), edit_moderate_products_path(product), title: 'Modera' %>
                  <% end %>
                  <% if can?(:list, :audits) %>
                      <%= button_link_to_audit(product) %>
                  <% end %>
                  <%= button_link_to fa_icon('archive'), archive_products_path(product), title: title_for_archived(product), class: product.archived? ? 'btn btn-default enable' : 'btn btn-default archive' %>
              <% end %>
            </td>
          </tr>
      <% end %>
    </table>
<% end %>
<%= pagination_box @products %>

<%= render partial: 'products/massive_moderate_form' %>
