<a role="button" data-toggle="collapse" href="#diff-panel-<%= attribute_name %>"
   aria-expanded="false" aria-controls="collapse">
  differenze
</a>

<div class="panel panel-info collapse diff-panel" id="diff-panel-<%= attribute_name %>">
  <div class="panel-heading">
    Differenze
  </div>
  <div class="panel-body">
    <div class="row">
      <% if old_attiva_product.present? %>
        <div class="col-md-6 vertical-line">
          <div class="diff bg-warning text-warning">
            <%= old_attiva_product.send attribute_name %>
          </div>
          <small>data: <%= old_attiva_product.updated_at %></small>
        </div>
      <% end %>
      <div class="col-md-6">
        <div class="diff bg-success"
            id="new_product_<%= attribute_name %>"
            data-value="<%= new_attiva_product.send(attribute_name) %>">
          <%= new_attiva_product.send attribute_name %>
        </div>
        <small>data: <%= new_attiva_product.updated_at %></small>
      </div>
    </div>
  </div>
  <div class="panel-footer">
    <button class="btn btn-sm btn-primary"
        data-toggle="collapse"
        href="#diff-panel-<%= attribute_name %>"
        aria-expanded="false"
        aria-controls="collapse">
      chiudi
    </button>
    <%= link_to 'aggiorna',
          "#",
          onclick: "App.Products.updateModerateValue('#product_#{attribute_name}', '#new_product_#{attribute_name}'); return false;",
          class: 'btn btn-sm btn-success'
      %>
  </div>
</div>
