<div class="gamma_section">
  <h3>Sezione Gamma</h3>
  <div class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
    <abbr title="required">*</abbr>
    <%= f.label :company_brand_id %>
    <%= f.select :company_brand_id, options_from_collection_for_select(company_brands, :id, :description_for_dropdown, f.object.company_brand_id), include_blank: t('prompts.product.company_brand') %>
  </div>

  <div class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
    <abbr title="required">*</abbr>
    <%= f.label :family_product_id %>
    <%= f.select :family_product_id, options_from_collection_for_select(family_products, :id, :description_for_dropdown, f.object.family_product_id), include_blank: t('prompts.product.family_product') %>
  </div>

  <div class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
    <abbr title="required">*</abbr>
    <%= f.label :statistic_group1_id %>
    <%= f.select :statistic_group1_id, options_from_collection_for_select(statistic_groups1, :id, :description_for_dropdown, f.object.statistic_group1_id), include_blank: t('prompts.product.statistic_group1') %>
  </div>

  <% if f.object.origin == 'system' || f.object.moderated %>
      <div class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <abbr title="required">*</abbr>
        <%= f.label :statistic_group2_id %>
        <%= f.select :statistic_group2_id, options_from_collection_for_select(statistic_groups2, :id, :description_for_dropdown, f.object.statistic_group2_id), include_blank: t('prompts.product.statistic_group2') %>
      </div>
  <% end %>

  <div class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
    <%= f.label :statistic_group3_id %>
    <%= f.select :statistic_group3_id, options_from_collection_for_select(statistic_groups3, :id, :description_for_dropdown, f.object.statistic_group3_id), include_blank: t('prompts.product.statistic_group3') %>
  </div>

  <div class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
    <%= f.label :gamma_purchase_price %>
    <%= diff_with_attiva(@product, :gamma_purchase_price) %>
    <%= f.number_field :gamma_purchase_price, class: 'text_field', step:  ApplicationHelper::NUMBER_STEP %>
  </div>

  <% if f.object.origin == 'system' || f.object.moderated%>
      <div class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <abbr title="required">*</abbr>
        <%= f.label :italy_sales_id %>
        <%= f.select :italy_sales_id, options_from_collection_for_select(italy_sales, :id, :description_for_dropdown, f.object.italy_sales_id), include_blank: t('prompts.product.italy_sales') %>
      </div>

      <div class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <abbr title="required">*</abbr>
        <%= f.label :italy_purchase_id %>
        <%= f.select :italy_purchase_id, options_from_collection_for_select(italy_purchases, :id, :description_for_dropdown, f.object.italy_purchase_id), include_blank: t('prompts.product.italy_purchase_return') %>
      </div>

      <div class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <abbr title="required">*</abbr>
        <%= f.label :italy_sales_return_id %>
        <%= f.select :italy_sales_return_id, options_from_collection_for_select(italy_sales_returns, :id, :description_for_dropdown, f.object.italy_sales_return_id), include_blank: t('prompts.product.italy_sales_return') %>
      </div>

      <div class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <abbr title="required">*</abbr>
        <%= f.label :italy_purchase_return_id %>
        <%= f.select :italy_purchase_return_id, options_from_collection_for_select(italy_purchase_returns, :id, :description_for_dropdown, f.object.italy_purchase_return_id), include_blank: t('prompts.product.italy_purchase_return') %>
      </div>
  <% end %>
</div>

<div class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
  <%= f.label :height %>
  <%= f.text_field :height %>
</div>

<div class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
  <%= f.label :width %>
  <%= f.text_field :width %>
</div>

<div class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
  <%= f.label :depth %>
  <%= f.text_field :depth %>
</div>

<div class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
  <%= f.label :weight %>
  <%= f.text_field :weight %>
</div>

<div class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
  <%= f.label :immaterial %>
  <%= f.select :immaterial, [["No", false], ["Bene immateriale", true]], selected: @product.immaterial %>&nbsp;
</div>
