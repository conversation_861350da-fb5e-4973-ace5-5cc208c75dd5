<%= page_title('Prodotti', title_for(@product)) %>
<div class="col-md-12 products-container mt-2">
  <div class="row col-md-12">
    <div class="col-md-6">
      <input type='hidden' id='product_product_type' value="<%= @product.product_type %>"/>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <b>Nome Prodotto:</b>
        <%= @product.name %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <b>Nome SAP:</b>
        <%= @product.sap_name %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <b>Nome Aggregato Per Il Prodotto:</b>
        <%= @product.commercial_name %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <b><%= Product.human_attribute_name("code") %>:</b>
        <%= @product.code %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <b><%= Product.human_attribute_name("ean_code") %>:</b>
        <%= @product.ean_code %>
      </p>
      <% if @product.color %>
        <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
          <b><%= Product.human_attribute_name('product_color') %>:</b>
          <%= "#{@product.color}#{" (default)" if @product.has_default_color? }" %>
        </p>
      <% end %>
      <% if can? :see_competence_field, Product %>
        <p class="field togglable" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
          <b><%= Product.human_attribute_name("competence") %>:</b>
          <%= @product.competence %>
        </p>
      <% end %>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <b><%= Product.human_attribute_name("product_type") %>:</b>
        <%= ProductType.description_of(@product.product_type) %>
      </p>

      <% if @product.product_type == 'pa' %>
        <p class="field togglable" data-visible-pa="true" data-visible-s="false" data-visible-p="false" data-visible-r="false" data-visible-b="false">
          <b><%= Product.human_attribute_name("product_kinds") %>:</b>
          <%= @product.product_kinds.join(", ") %>
        </p>
      <% end %>
      <% if @product.product_type == 's' %>
        <p class="field togglable" data-visible-pa="false" data-visible-s="true" data-visible-p="false" data-visible-r="false" data-visible-b="false">
          <b><%= Product.human_attribute_name("sim_kinds") %>:</b>
          <%= @product.sim_kinds.join(", ") %>
        </p>
      <% end %>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <b><%= Product.human_attribute_name("returnable") %>:</b>
        <%= true_false @product.returnable %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <b><%= Product.human_attribute_name("can_be_requested") %>:</b>
        <%= true_false @product.can_be_requested %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <b><%= Product.human_attribute_name("computer_gross_requestable") %>
          :</b>
        <%= true_false @product.computer_gross_requestable %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="false" data-visible-p="false" data-visible-r="false" data-visible-b="false">
        <b><%= Product.human_attribute_name("activable_system_owner") %>:</b>
        <%= true_false @product.activable_system_owner %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="false" data-visible-p="false" data-visible-r="false" data-visible-b="false">
        <b><%= Product.human_attribute_name("activable_for_windtre_ecommerce") %>:</b>
        <%= true_false @product.activatable_for_windtre_ecommerce %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="false" data-visible-p="false" data-visible-r="false" data-visible-b="false">
        <b><%= Product.human_attribute_name("enabled_for_imei_reservation") %>
          :</b>
        <%= true_false @product.enabled_for_imei_reservation %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="false" data-visible-p="false" data-visible-r="false" data-visible-b="false">
        <b><%= Product.human_attribute_name("enabled_for_imei_reservation_franchising") %>
          :</b>
        <%= true_false @product.enabled_for_imei_reservation_franchising %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="false" data-visible-p="false" data-visible-r="false" data-visible-b="false">
        <b><%= Product.human_attribute_name("enabled_for_imei_reservation_gallery") %>
          :</b>
        <%= true_false @product.enabled_for_imei_reservation_gallery %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="false" data-visible-p="true" data-visible-r="false" data-visible-b="false">
        <b><%= Product.human_attribute_name("imei_reservation_curtailed") %>
          :</b>
        <%= true_false @product.imei_reservation_curtailed %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="false" data-visible-p="true" data-visible-r="false" data-visible-b="false">
        <b><%= Product.human_attribute_name("floa_top_seller") %>:</b>
        <%= true_false @product.floa_top_seller %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="false" data-visible-b="false">
        <b><%= Product.human_attribute_name("serial") %>:</b>
        <%= true_false @product.serial %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="false" data-visible-b="false">
        <b><%= Product.human_attribute_name(:rid_guaranteed) %>:</b>
        <%= true_false @product.rid_guaranteed %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="false" data-visible-b="false">
        <b><%= Product.human_attribute_name(:consignment) %>:</b>
        <%= true_false @product.consignment %>
      </p>
      <p class="field togglable" data-visible-pa="false" data-visible-s="true" data-visible-p="false" data-visible-r="false" data-visible-b="false">
        <b><%= Product.human_attribute_name("allow_sim_without_number") %>:</b>
        <%= true_false @product.allow_sim_without_number %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <b><%= Product.human_attribute_name("force_zero_shipping_cost") %>:</b>
        <%= true_false @product.force_zero_shipping_cost %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <b><%= Product.human_attribute_name("floa_commissions") %>:</b>
        <%= @product.floa_commissions ? "#{@product.floa_commissions}%" : 'No' %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <b><%= Product.human_attribute_name("available_promo_device_very") %>:</b>
        <%= true_false @product.available_promo_device_very %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <b><%= Product.human_attribute_name("max_qty_orderable") %>:</b>
        <%= @product.max_qty_orderable %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <b><%= Product.human_attribute_name("max_qty_visible") %>:</b>
        <%= @product.max_qty_visible %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="false" data-visible-b="true">
        <b><%= Product.human_attribute_name("operators") %>:</b>
        <%= @product.operators.map(&:name).join(",") if @product.operators.any? %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <b><%= Product.human_attribute_name("store_tags") %>:</b>
        <%= @product.store_tags.join(", ") %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <b><%= Product.human_attribute_name("store_subtags") %>:</b>
        <%= @product.store_subtags.join(", ") %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="false" data-visible-p="false" data-visible-r="false" data-visible-b="false">
        <b><%= Product.human_attribute_name("product_categories") %>:</b>
        <%= @product.product_categories.map(&:description).join(", ") %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="false">
        <b><%= Product.human_attribute_name("vat_type") %>:</b>
        <%= ProductVatType.description_of(@product.vat_type) %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="false">
        <b>Prezzo al Dealer A IVA esclusa:</b>
        <%= @product.dealer_price_vat_excluded_a %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="false">
        <b><%= Product.human_attribute_name('dealer_price_vat_excluded_b') %></b>
        <%= @product.dealer_price_vat_excluded_b %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="false">
        <b><%= Product.human_attribute_name('dealer_price_vat_excluded_c') %></b>
        <%= @product.dealer_price_vat_excluded_c %>
      </p>
      <p>
        <b><%= Product.human_attribute_name(:final_installment) %></b>
        <%= number_to_currency @product.final_installment %>
      </p>
      <p>
        <b>Sconto percentuale:</b>
        <%= @product.default_percent_discount %>%
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <b>Prezzo al Pubblico IVA inclusa:</b>
        <%= @product.public_price_list %>
      </p>
      <% if promo_available_for(@product) %>
        <p class="field togglable" data-visible-pa="true" data-visible-s="false" data-visible-p="false" data-visible-r="false" data-visible-b="false">
          <b><%= Product.human_attribute_name(:promo_device_price) %>: </b>
          <%= @product.promo_device_price %>
        </p>
      <% end %>
      <% if SpazioEnv.kolme? %>
        <p class="field togglable" data-visible-pa="true" data-visible-s="false" data-visible-p="false" data-visible-r="false" data-visible-b="false">
          <b><%= Product.human_attribute_name(:credit_note_amount) %>: </b>
          <%= @product.credit_note_amount %>
        </p>
      <% end %>
      <p class="field togglable" data-visible-pa="true" data-visible-s="false" data-visible-p="false" data-visible-r="false" data-visible-b="false">
        <b><%= Product.human_attribute_name("loanable") %>:</b>
        <%= true_false @product.loanable %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="false" data-visible-r="false" data-visible-b="false">
        <b><%= Product.human_attribute_name("reward") %>:</b>
        <%= number_to_currency @product.reward %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="false" data-visible-b="true">
        <b><%= Product.human_attribute_name('max_agent_discount_percentage') %>
          :</b>
        <%= @product.max_agent_discount_percentage %>
      </p>
      <% unless @product.bundle? %>
        <p class="field agent_profit togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="false">
          <b><%= Product.human_attribute_name("agent_profit_a") %>:</b>
          <%= @product.agent_profit_a %>
        </p>
        <p class="field agent_profit togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="false">
          <b><%= Product.human_attribute_name("agent_profit_b") %>:</b>
          <%= @product.agent_profit_b %>
        </p>
        <p class="field agent_profit togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="false">
          <b><%= Product.human_attribute_name("agent_profit_c") %>:</b>
          <%= @product.agent_profit_c %>
        </p>
      <% end %>
      <p class="field togglable" data-visible-pa="true" data-visible-s="false" data-visible-p="false" data-visible-r="false" data-visible-b="false">
        <b><%= Product.human_attribute_name("view_in_matrix") %>:</b>
        <%= true_false @product.view_in_matrix %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="false" data-visible-r="false" data-visible-b="false">
        <b><%= Product.human_attribute_name("mandatory_mnp") %>:</b>
        <%= true_false @product.mandatory_mnp %>
      </p>
      <% if SpazioEnv.kolme? %>
        <p class="field togglable" data-visible-pa="false" data-visible-s="true" data-visible-p="false" data-visible-r="false" data-visible-b="false">
          <b>Taglio ricarica incluso:</b>
          <%= @product.recharge_size.name if @product.recharge_size %>
        </p>
      <% end %>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="false" data-visible-b="true">
        <b><%= Product.human_attribute_name("sim_format") %>:</b>
        <%= SimFormat.description_of(@product.sim_format) %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <b><%= Product.human_attribute_name("visibility") %>:</b>
        <%= VisibilityKind.description_of(@product.visibility) %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <b><%= Product.human_attribute_name(:product_macro_category) %>:</b>
        <%= @product.product_macro_category.name %>
      </p>
      <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
        <b><%= Product.human_attribute_name("ribbon") %>:</b>
        <%= localized_description_of(@product.ribbon, 'ribbon') %>
      </p>
      <% if SpazioEnv.kolme? %>
        <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
          <b>Ultimo aggiornamento gamma:</b>
          <% if @product.gamma_exported_at %>
            <%= l @product.gamma_exported_at %>
          <% end %>
        </p>
        <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
          <b><%= Product.human_attribute_name("dhl_supply_chain") %>:</b>
          <%= true_false @product.dhl_supply_chain %>
        </p>
        <% if @product.dhl_supply_chain? %>
          <p class="field togglable" data-visible-pa="true" data-visible-s="false" data-visible-p="false" data-visible-r="false" data-visible-b="false">
            <b><%= h(Product.human_attribute_name("dhl_pigeon_house").html_safe) %>
              :</b>
            <%= true_false @product.dhl_pigeon_house %>
          </p>

          <% if @product.dhl_pigeon_house %>
            <p class="field togglable" data-visible-pa="true" data-visible-s="false" data-visible-p="false" data-visible-r="false" data-visible-b="false">
              <b><%= h(Product.human_attribute_name("dhl_pigeon_management").html_safe) %>
                :</b>
              <%= true_false @product.dhl_pigeon_management %>
            </p>
          <% end %>
        <% end %>
        <p class="field togglable" data-visible-pa="true" data-visible-s="true" data-visible-p="true" data-visible-r="true" data-visible-b="true">
          <b><%= Product.human_attribute_name("agent_fulfillable") %>:</b>
          <%= true_false @product.agent_fulfillable %>
        </p>
      <% end %>
    </div>
    <div class="col-md-6">
      <%= render( 'products/sap_fields_show', product: @product ) if show_sap?(@product) %>
      <%= render 'products/gamma_fields_show', product: @product %>
    </div>
  </div>


  <% unless @product.bundle_items.empty? %>
    <div class="row">
      <div class="col-md-12">
        <table class="table table-hover">
          <tr>
            <th>Componente</th>
            <th>IVA</th>
            <th>Q.TY</th>
            <th>Prezzo (A)</th>
            <th>Margine AM (A)</th>
            <th>Prezzo (B)</th>
            <th>Margine AM (B)</th>
            <th>Prezzo (C)</th>
            <th>Margine AM (C)</th>
          </tr>
          <% @product.bundle_items.each do |bundle_item| %>
            <tr>
              <td><%= bundle_item.name %></td>
              <td><%= bundle_item.vat_type %></td>
              <td><%= bundle_item.quantity %></td>
              <td><%= bundle_item.dealer_price_vat_excluded_a %></td>
              <td><%= bundle_item.agent_profit_a %></td>
              <td><%= bundle_item.dealer_price_vat_excluded_b %></td>
              <td><%= bundle_item.agent_profit_b %></td>
              <td><%= bundle_item.dealer_price_vat_excluded_c %></td>
              <td class="text-left"><%= bundle_item.agent_profit_c %></td>
            </tr>
          <% end %>
          <tr>
            <td></td>
            <td></td>
            <td class="text-right"><b>Totale:</b></td>
            <td>
              <div>
                <%= @product.bundle_item_subtotal('a') %>
              </div>
            </td>
            <td></td>
            <td>
              <div>
                <%= @product.bundle_item_subtotal('b') %>
              </div>
            </td>
            <td></td>
            <td>
              <div>
                <%= @product.bundle_item_subtotal('c') %>
              </div>
            </td>
            <td></td>
          </tr>
          <tr class="last_row">
            <td></td>
            <td></td>
            <td class="text-right"><b>IVA:</b></td>
            <td>
              <div><%= @product.bundle_vat_a %></div>
            </td>
            <td></td>
            <td>
              <div><%= @product.bundle_vat_b %></div>
            </td>
            <td></td>
            <td>
              <div><%= @product.bundle_vat_c %></div>
            </td>
            <td></td>
          </tr>
        </table>
      </div>
    </div>
  <% end %>
  <div class="row mb-2">
    <div class="col-md-6">
      <div class="col-md-8 slider_container">
        <%= render partial: 'store/products/image_slider', locals: { product: @product, color_selected: true } %>
      </div>
    </div>
    <div class="col-md-6">
      <div class="product_description">
        <%= h(@product.description&.html_safe) %>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-md-12">
      <% if can? :see_images_button, Product %>
        <%= button_link_to 'Lista immagini', product_product_images_path(@product) %>
      <% end %>
      <% if can? :manage, Product %>
        <%= button_link_to 'Modifica', edit_product_path(@product) %>
        <% if @product.duplicatable? %>
          <%= button_link_to 'Duplica', polymorphic_path([:duplicate, @product]) %>
        <% end %>
      <% end %>
      <%= button_link_to 'Indietro', products_path %>
    </div>
  </div>
  <br>
  <br>
</div>


