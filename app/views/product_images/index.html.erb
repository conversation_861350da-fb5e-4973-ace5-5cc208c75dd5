<%= page_title('Immagini', "#{@product.name} (#{@product.code})") %>

<% if @product.has_default_color && @product.default_image %>
  <div class="page_info">Immagine di default</div>
  <%= render partial: 'product_images/list', locals: { product_images: [@product.default_image] } %><br/>
<% end %>

<%= render partial: 'product_images/list', locals: { product_images: @product.gallery_product_images } %>

<div class="col-md-12" style="padding: 10px">
  <% if can? :manage, ProductImage %>
      <%= button_link_to 'Nuova Immagine', new_product_product_image_path(@product), class: 'btn-primary' %>
  <% end %>
  <%= link_to 'Torna al prodotto', product_path(@product), class: 'btn btn-default' %>
</div>
