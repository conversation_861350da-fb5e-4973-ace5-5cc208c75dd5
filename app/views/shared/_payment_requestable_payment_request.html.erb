<% if can?(:create, PaymentRequest) && payment_requestable.can_have_payment_requests? %>
  <%= create_payment_request_button_for(payment_requestable) %>
<% elsif can?(:show, PaymentRequest) && payment_requestable.payment_requests.any? %>
  &nbsp;
  &mdash;
  &nbsp;
  <% payment_request = payment_requestable.payment_requests.last %>
  <%= link_to "Richiesta di pagamento (#{payment_request.id})", payment_requests_path(search: {
      payment_request_kind:             [payment_requestable.payment_request_kind],
      payment_requestable_reference_id: payment_requestable.payment_request_reference_id,
      show:                             ['all']
  }), class: 'payment_request_link' %>
<% end %>
