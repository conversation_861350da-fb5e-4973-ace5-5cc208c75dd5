<% error_class = (phone_call.import_errors.any? ? 'danger' : '') %>
<tr id="<%= "phone_call_row_#{phone_call.id}" %>" class="<%= error_class %>">
  <td><%= l phone_call.called_at %></td>
  <% if can?(:see_dealer_name, phone_call) %>
      <td><%= dealer_full_name(phone_call) %></td>
  <% end %>
  <td class='phone-call-type-icon-container'><%= phone_call_type_icon(phone_call) %></td>
  <td><%= internal_user_full_name(phone_call) %></td>
  <td><%= humanize(phone_call.duration_in_seconds) %></td>
  <td>
    <%= in_place_edit_for(phone_call, :note, add_note_phone_call_path(phone_call), 'Inserisci note', 'best_in_place-phone-call', :best_in_place) do %>
        <%= truncate(phone_call.note, length: 120) || 'Inserisci note' %>
    <% end %>
  </td>
  <td class="vote"><%= render 'phone_calls/vote', phone_call: phone_call %></td>
  <td class="tools phone_activation_tools">
    <%= button_group do %>
        <% if can?(:see_import_details, phone_call) && phone_call.import_errors.any? %>
            <%= import_errors_button(phone_call) %>
        <% end %>
        <% if can?(:listen, phone_call) && phone_call.file_name %>
          <audio class='phone_call_audio' preload="none" id="audio_<%= phone_call.id %>" data-phone-call-id="<%= phone_call.id %>">
            <source src="<%= listen_phone_call_url(phone_call) %>" type="audio/mpeg">
            N/A
          </audio>
          <%= link_to "javascript:void(0)", id: "audio_button_" + phone_call.id.to_s, class: 'btn btn-default play_button', title: 'Ascolta chiamata', data: { phone_call_id: phone_call.id } do %>
              <%= fa_icon('play', class: 'play_icon') %>
              <%= fa_icon('pause', class: 'pause_icon', hidden: 'hidden') %>
          <% end %>
        <% end %>
        <%= button_link_to fa_icon('eye', class: 'default_blu'), "javascript:void(0)", class: 'toggle-call-modal', title: 'Dettagli chiamata', data: { target: "#phone-call-modal-#{phone_call.id}" } %>
        <% if can? :destroy, PhoneCall %>
            <%= button_link_for_destroy phone_call_path(phone_call), method: :delete, title: 'Elimina chiamata', data: { confirm: 'Procedere con la cancellazione ?' } %>
        <% end %>
        <% if (can? :reimport, PhoneCall) && phone_call.import_errors.any? %>
            <%= button_link_to fa_icon('refresh'), reimport_phone_call_path(phone_call), title: 'Importa nuovamente' %>
        <% end %>
        <% if (can? :show, PhoneCallReport) && phone_call.report %>
            <%= button_link_to fa_icon('info-circle', class: "green"), "javascript:void(0)", class: 'toggle-call-modal', title: 'Report chiamata', data: { target: "#phone-call-report-modal-#{phone_call.report.id}" } %>
        <% end %>
    <% end %>
  </td>
  <%= render 'phone_calls/phone_call_modal', phone_call: phone_call %>
  <% if can?(:see_import_details, phone_call) && phone_call.import_errors.any? %>
      <%= render 'phone_calls/phone_call_error_modal', phone_call: phone_call %>
  <% end %>
  <% if can?(:show, PhoneCallReport) && phone_call.report %>
      <%= render 'phone_calls/phone_call_report_modal', report: phone_call.report %>
  <% end %>
</tr>
