<%= simple_form_for @ko_reason do |f| %>
  <% if @ko_reason.errors.any? %>
    <div id="error_explanation" class='panel panel-danger'>
      <div class="panel-heading"><%= pluralize(@ko_reason.errors.count, "errore", "errori") %> hanno impedito a
        questa ko reason di
        essere salvata:
      </div>
      <div class="panel-body">
        <ul>
          <% @ko_reason.errors.full_messages.each do |msg| %>
            <li><%= msg %></li>
          <% end %>
        </ul>
      </div>
    </div>
  <% end %>

  <div class="row">
    <div class="col-md-6">
      <div class="field">
        <%= f.input :action, collection: @actions, label_method: 'last', value_method: 'first' %>
      </div>
      <div class="field">
        <%= f.input :description, as: 'string' %>
      </div>
      <div class="field">
        <%= f.input :action_description, as: 'string' %>
      </div>
      <div class="field">
        <%= f.input :operation_id, collection: @operations, label_method: 'description', value_method: 'id' %>
      </div>
      <div class="field">
        <%= f.input :ngpos_operation_id, collection: @ngpos_operations, label_method: 'description', value_method: 'id' %>
      </div>
      <div class="field">
        <%= f.input :responsible, collection: @responsible %>
      </div>
      <div class="field">
        <%= f.input :recoverable, as: :boolean, wrapper: :vertical_boolean %>
      </div>
      <div class="field">
        <%= f.input :charge_operator, as: :boolean, wrapper: :vertical_boolean %>
      </div>
      <div class="field">
        <%= f.input :visible_in_activation, as: :boolean, wrapper: :vertical_boolean %>
      </div>
      <div class="field">
        <%= f.input :triggers_item_removal, as: :boolean, wrapper: :vertical_boolean %>
      </div>
      <div class="field">
        <%= f.input :robot, as: :boolean, wrapper: :vertical_boolean %>
      </div>
      <div class="actions">
        <%= f.submit(:class => "btn btn-primary") %>
      </div>
    </div>
  </div>
<% end %>
