<div class='wrapper appointment_detail'>
  <div class='row'>
    <div class='col-md-10'>
      <%= form_for @appointment_detail, url: phone_activation_appointment_detail_path(@phone_activation),
                                        html: { id: "edit_appointment_detail"},
                                        data: { check_appointments_presence_url: check_appointments_presence_dealer_path(@appointment_detail.current_dealer) } do |f| %>
        <%= f.hidden_field :installation_kind, value: 'activation' %>
        <% if @phone_activation.should_edit_appointment_detail_not_fwa? %>
          <%= render partial: 'appointment_details/appointment_form_not_fwa', locals: { f: f, phone_number_editable: true, modal: false } %>
        <% else %>
          <%= render partial: 'appointment_details/appointment_form', locals: { f: f, phone_number_editable: true, modal: false } %>
        <% end %>
      <% end %>
    </div>
  </div>
</div>
