<%= form_for @appointment_detail, remote: true do |f| %>
  <%= render partial: 'appointment_details/recap_duplicate', locals: { f: f } %>
  <%= f.hidden_field :installation_kind, value: @appointment_detail.installation_kind.to_s %>
  <%= f.hidden_field :installation_fee %>
  <%= f.hidden_field :location_address_street_name %>
  <%= f.hidden_field :location_address_street_type_id %>
  <%= f.hidden_field :location_cap %>
  <%= f.hidden_field :location_city_id %>
  <%= f.hidden_field :location_number %>
  <%= f.hidden_field :modem_serial %>
  <%= f.hidden_field :phone_activation_id %>
  <%= f.hidden_field :phone_number %>
  <%= f.hidden_field :pin %>
  <%= f.hidden_field :sim_serial %>
  <%= f.hidden_field :system_name %>
  <%= f.hidden_field :parent_np_x_order_code %>
  <%= f.hidden_field :status %>
  <div class="col-md-12 mt-2">
    <%= f.submit 'Conferma', class: 'btn btn-success' %>
    <button type="button" class="btn btn-danger" data-dismiss="modal" aria-label="Close">
      Annulla
    </button>
  </div>

<% end %>
