<%= form_for appointment_detail, remote: true, data: { check_serial_url: check_serial_for_support_items_path } do |f| %>
    <div class="row">
      <h3><%= AppointmentDetail.human_attribute_name("support_kind/materials_replace") %></h3>
      <%= f.label :support_kind_note %>
      <%= f.text_area :support_kind_note, readonly: appointment_detail.support_kind_note.present? %>

      <div class="materials_replace-fields mb-1">
        <%= f.label :modem_serial, class: 'mt-1' %>
        <%= f.text_field :modem_serial, readonly: appointment_detail.modem_serial.present? %>

        <%= f.label :antenna_serial, class: 'mt-1' %>
        <%= f.text_field :antenna_serial,  readonly: appointment_detail.antenna_serial.present? %>
      </div>

      <div class="col-md-12 mt-2">
        <%= submit_tag 'Conferma', class: 'btn btn-success' %>
        <button type="button" class="btn btn-danger" data-dismiss="modal" aria-label="Close">
          Annulla
        </button>
      </div>
    </div>
  <% end %>
