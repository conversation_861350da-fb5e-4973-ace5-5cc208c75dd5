<%= page_title('Pa<PERSON>e <PERSON>') %>

<div class="page_info">
  <%= page_entries_info @pages %>
</div>

<table id="pages">
  <tr>
    <th>ID</th>
    <th>Data creazione</th>
    <th><PERSON><PERSON></th>
    <th>Pubblicato</th>
    <th>Categoria</th>
    <th>Template</th>
    <th></th>
  </tr>

  <% @pages.each do |page| %>
    <% cache [page, current_user.role_label] do %>
      <tr id="<%= page.id %>">
        <td><%= page.id %></td>
        <td><%= l page.created_at %></td>
        <td><%= page.title %></td>
        <td><%= page.published %></td>
        <td><%= page.dealer_category.code %></td>
        <td><%= page.cms_template %></td>
        <td>
          <%= button_group do %>
            <%= button_link_for_show page_path(page) %>
            <% if can?(:update, Page) %>
              <%= button_link_for_edit edit_page_path(page) %>
            <% end %>
            <% if can?(:list, :audits) %>
              <%= button_link_to_audit(page) %>
            <% end %>
            <% if can?(:destroy, Page) %>
              <%= button_link_for_destroy page_path(page), method: :delete, data: { confirm: "Sei sicuro?" } %>
            <% end %>
          <% end %>
        </td>
      </tr>
    <% end %>
  <% end %>
</table>

<script type="text/javascript">
    $(document).ready(function () {
        addSortableToTable("#pages", "<%= sort_pages_path %>");
    });
</script>


<div class="row" style="padding: 20px;">
  <div class="col-md-12">
    <div class="col-md-12">
      <% if can?(:create, Page) %>
        <%= button_link_to 'Nuova Pagina', new_page_path, class: 'button primary' %>
        <%= button_link_to 'Immagine di listino', edit_price_list_image_path(@price_list_image), class: 'button primary' %>
        <%= button_link_to 'Link Zoom', zoom_links_path(commit: I18n.t('search.clean')), class: 'button primary' %>
        <%= button_link_to 'Edit Home MD Very', home_md_very_boxes_path, class: 'button primary' %>
      <% end %>
      <%= pagination_box @pages %>
    </div>
  </div>
</div>