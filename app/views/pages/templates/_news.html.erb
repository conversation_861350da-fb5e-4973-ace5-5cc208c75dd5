<div class="row" style="padding: 20px">
  <div class="col-md-12">
    <p class="page-subtitle"><%= page.subtitle %></p>
  </div>
  <% posts = paginated_posts({active: active,  page: page, current_user: current_user, pagination: { page: params[:page] } }) %>
  <% posts.each do |post| %>
      <div class="col-md-12" style="border-bottom: 1px solid #eeeeee">
        <div class="row">
          <div class="col-md-1 vertical-center">
            <span class="link_operator_news"><%= operator_image(post, 'img-responsive') %></span>
          </div>
          <div class="col-md-11 vertical-center">
            <span class="link_news"><%= l(post.post_date, format: '%d %B %Y') if post.post_date.present? %>
              - <%= count_clickable_link_to post, post.title, link_to_file_or_url(post), target: :blank %></span>
          </div>
        </div>
      </div>
  <% end %>
  <%= render partial: 'pages/templates/no_operators' if @no_operators %>
  <%= will_paginate paginated_posts({active: active,  page: page, current_user: current_user, pagination: { page: params[:page] } }), params: { tab_active: "#{page.id}" } %>
</div>

<style type="text/css">
  .vertical-center {
    min-height: 5%; /* Fallback for browsers do NOT support vh unit */
    min-height: 5vh; /* These two lines are counted as one :-)       */
    display: flex;
    align-items: center;
  }

  span.link_news {
    font-size: 14px;
    font-weight: 500;
  }

  span.link_operator_news {
    width: 100%;
    text-align: center;
  }

  span.link_operator_news a {
    font-size: 14px;
    font-weight: 500;
  }

  .img-responsive {
    margin: 0 auto;
  }
</style>
