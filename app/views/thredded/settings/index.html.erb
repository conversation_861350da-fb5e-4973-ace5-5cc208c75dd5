<% content_for :thredded_page_title, t('thredded.settings.title') %>
<% content_for :thredded_page_id, 'thredded--topics-index' %>
<% content_for :thredded_breadcrumbs, render('thredded/shared/breadcrumbs') %>

<%= thredded_page do %>

  <%= simple_form_for current_user, url: spazio_routes.thredded_settings_update_path do |f| %>
    <% if current_user.errors.any? %>
      <div id="error_explanation" class='panel panel-danger'>
        <div class="panel-heading"><%= pluralize(current_user.errors.count, "errore", "errori") %> hanno impedito a questo utente di
          essere salvato:
        </div>
        <div class="panel-body">
          <ul>
            <% current_user.errors.full_messages.each do |msg| %>
              <li><%= render_custom_error(msg) %></li>
            <% end %>
          </ul>
        </div>
      </div>
    <% end %>

    <% unless current_user.is?(:dealer) %>
      <%= render partial: 'thredded/settings/avatar', locals: { user_form_builder: f } %>
    <% end %>

    <br/>

    <div class="row">
      <div class="col-md-6">
        <%= f.submit 'Salva modifiche',
                      class: 'thredded--form--submit',
                      data: { disable_with: 'Attendi...' } %>
        <%= button_link_to fa_icon('arrow-left'), root_path %>
      </div>
    </div>
  <% end %>

<% end %>
