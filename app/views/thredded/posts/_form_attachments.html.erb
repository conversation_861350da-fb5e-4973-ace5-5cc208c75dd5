<%= form.hidden_field :destroy_attachments %>
<div style="margin-bottom: 16px">
  <%= form.object.post.attachments.map { |attachment|
                                          content_tag(:font, id: "thredded_attachment_#{attachment.id}", class: 'thredded_attachment_element ') do
                                            link_to(attachment.blob.filename, main_app.url_for(attachment))
                                              .concat('&nbsp;&nbsp;'.html_safe)
                                              .concat(link_to('<i class="fas fa-times-circle"></i>'.html_safe,
                                                              '#',
                                                              class: 'thredded_destroy_attachment',
                                                              data: {
                                                                attachment_id: attachment.id
                                                              }))
                                          end
                                      }.join("<font class='separator'>, </font>").html_safe %>
</div>
<%= form.file_field(:attachments, multiple: true) %>