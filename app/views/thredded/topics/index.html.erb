<% content_for :thredded_page_title, messageboard.name %>
<% content_for :thredded_page_id, 'thredded--topics-index' %>
<% content_for :thredded_breadcrumbs, render('thredded/shared/breadcrumbs') %>

<%= thredded_page do %>
  <%= content_tag :section,
                  class: 'thredded--main-section thredded--topics',
                  'data-thredded-topics' => true,
                  'data-thredded-topic-posts-per-page' => Thredded.posts_per_page do %>
    <%= render 'thredded/topics/form',
               topic: @new_topic,
               css_class: 'thredded--is-compact',
               placeholder: t('thredded.topics.form.title_placeholder_start') if @new_topic %>
    <%= render partial: 'thredded/topics/topic',
               collection: @topics,
               locals: {
                   sticky_topics_divider: true,
                   topics: @topics
               } %>
  <% end %>

  <footer class="thredded--pagination-bottom">
    <%= paginate @topics %>
  </footer>

  <%= render 'thredded/messageboards/messageboard_actions', messageboard: messageboard %>
<% end %>