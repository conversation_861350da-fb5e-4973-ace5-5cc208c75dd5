<%= page_title('Ordini', "Lista") %>
<div class="orders orders-index">
  <%= render partial: 'store/orders/search' %>
  <div class="page_info">
    <%= page_entries_info @orders %>
  </div>
  <table class="orders_table">
    <tr>
      <th>Ordine</th>
      <th>Data</th>
      <% unless current_user.is?(:dealer) %>
        <th>Partner</th>
      <% end %>
      <th>Punto Vendita</th>
      <th>Stato</th>
      <th>AVANZAMENTO</th>
      <th></th>
      <th width="20%"></th>
    </tr>

    <%= render partial: 'store/orders/row', collection: @orders, as: :order %>
  </table>
  <%= pagination_box @orders %>
</div>

<%= render partial: 'store/orders/payment_already_in_progress_modal' %>
