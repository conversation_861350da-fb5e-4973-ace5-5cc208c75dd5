<%= render partial: 'store/orders/navigation', locals: { breadcrumbs: [[1, t("labels.select_a_shipping_address")], [2, "Come vuoi pagare?"]] } %>
<div class="orders">
  <%= simple_form_for [:store, @order], remote: true, html: { class: "spinnered validate-before-submit-enabled" } do |f| %>

    <%= f.input :pay_thirty_days, as: :radio_buttons, collection: available_payment_modes_for(@order), label: 'Seleziona il metodo di pagamento', readonly: nil, required: true %>
    <br/>
    <%= f.submit t("buttons.continue"), :class => "button disable-submit-button" %>
    <%= button_link_to 'Torna al carrello', quit_checkout_store_order_path(@order), method: :post, id: 'quit-checkout-btn' %>
  <% end %>
</div>


<script type="text/javascript">
    $(function () {
        $('.spinnered').submit(function () {
            $('.spinnered').spin();
            $('#quit-checkout-btn').disable();
        });
    });
</script>
