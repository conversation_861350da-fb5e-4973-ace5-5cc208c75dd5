$("#edit_form_partials").html("<%= escape_javascript(render partial: @order.aasm_state) %>");

<% if @order.hipay_payments.pending.last %>
  <% if @forward_url.present? %>
    window.location.href = "<%= @forward_url %>";
  <% end %>
<% end %>

<% if @forward_token_url.present? %>
  window.location.href = "<%= @forward_token_url %>";
<% end %>

<% if @error_message %>
  $.gritter.add({ text: "<%= @error_message %>", title: 'Errore', image:'/assets/error.png' });
<% end %>

<% if @order.payment3? %>
	window.App.Payment3Form.init()
<% end %>

window.App.DisableSubmitButtons.init()
new ValidateBeforeSubmitEnabled().init()
