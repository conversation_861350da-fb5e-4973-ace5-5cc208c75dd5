<%= page_title('Ordini', 'Carrello') %>
<% if @shopping_cart.empty? %>
    <div class="col-md-12 empty-shopping-cart">
      <%= t 'labels.shopping_cart_empty' %>
    </div>
<% else %>
    <% if can? :order_for_others, Order %>
        <%= form_tag store_update_dealer_path, method: :post, class: 'form-inline', id: 'dealer_selector' do |f| -%>
          <div class="form-group">
            <%= label :dealer_name, 'Utilizza questa tendina per acquistare per conto di un dealer:' %>&nbsp;
            <%= autocomplete_field_tag :dealer_name, params[:dealer_name], autocomplete_dealer_with_or_without_operators_dealers_path, value: @shopping_cart.dealer.try(:name), id: 'dealer_name', id_element: '#dealer_id', style: 'width: 300px', class: 'form-control', required: true %>
            <%= hidden_field_tag :dealer_id, nil, id: 'dealer_id' %>
          </div>
          <div class="form-group">
            <%= submit_tag 'ok', data: { disable_with: '...' }, id: 'submit_button', class: 'btn btn-default btn-sm' %>
          </div>
        <% end %>
    <% end %>
    <div id="order_wrap">
      <%= render partial: 'store/shopping_carts/order', locals: {shopping_cart: @shopping_cart} %>
    </div>
<% end %>
