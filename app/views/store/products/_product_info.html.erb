<%= render 'store/products/overlay' if overlay_needed_for(product, @context) %>
<div class="row">
  <% if product.display_ribbon? %>
    <%= render partial: 'store/products/ribbon', locals: { ribbon: product.ribbon } %>
  <% end %>
  <%= render partial: 'store/products/store_product_image',
              locals:  {
                product_obj:    product_obj,
                product:        product,
                color_selected: color_selected,
                context:        @context
              }
  %>
</div>
<div class="row caption product-description">
  <%= render partial: 'store/products/store_product_description',
              locals:  {
                product_obj:    product_obj,
                product:        product,
                color_selected: color_selected,
                context:        @context
              }
  %>
  <%= render partial: "store/products/#{store_product_actions_for(@context)}",
              locals:  {
                product_obj:    product_obj,
                product:        product,
                color_selected: color_selected
              } %>
</div>
