<div class="product-details">
  <div class='product_detail_head'>
    <span class='product_name'><%= @product.name %></span>
    <br>
    <span class='product_code'><%= @product.code %></span>
  </div>

  <div class='product_detail_recap'>
    <div class='col-md-4 dealer_price'>
      <% if current_user.is?(:dealer) %>
        <span class="product_detail_recap_label ">Prezzo al dealer:</span><%= number_to_currency @product.dealer_price_vat_excluded(current_user.dealer.price_list_category) %>
      <% else %>
        <span class="product_detail_recap_label ">Prezzo al dealer:</span>
        <div class="small">
          A <%= number_to_currency @product.dealer_price_vat_excluded_a %><br />
          B <%= number_to_currency @product.dealer_price_vat_excluded_b %><br />
          C <%= number_to_currency @product.dealer_price_vat_excluded_c %>
        </div>
      <% end %>
    </div>
    <% unless @product.dont_show_public_price? %>
      <div class='col-md-4 public_price'>
        <span class="product_detail_recap_label ">Prezzo al pubblico:</span><%= number_to_currency(@product.public_price_list) %>
      </div>
    <% end %>
    <% if not @product.product_type == 'r' %>
      <div class='col-md-2'>
        <span class="product_detail_recap_label ">Giacenza:</span><%= @product.stock_count %>
      </div>
    <% end %>
    <div class='col-md-2 product_actions'>
      <% if can? :add_to_cart, @product %>
        <span id="errors"></span>
        <%= form_tag(store_add_to_cart_path(:product_id => @product.id), :remote => true) do %>
          <button type="submit" class="btn btn-primary add_to_cart" title='Aggiungi al carrello'>
            <%= fa_icon('plus') %><%= fa_icon('shopping-cart') %>
          </button>
          <%= text_field_tag "quantity", 1 %>
        <% end %>
      <% end %>
    </div>
  </div>

  <div class="row">
    <div class='col-md-8'>
      <% unless @product.description.blank? %>
        <h4>Descrizione</h4>
        <div class="product_description">
          <%= h(@product.description.html_safe) %>
        </div>
      <% end %>

      <div class="product_image ml-2 <%= 'no_image' if @product.gallery_product_images.length == 0 %>">
        <% if @product.gallery_product_images.any? %>
          <% @product.gallery_product_images.each_with_index do |product_image, index| %>
            <% if index.zero? %>
              <div class='main-product-image'>
                <%= image_tag(product_image.image.large.url, alt: product_image.title) if product_image.image? %>
              </div>
            <% end %>
          <% end %>
        <% else %>
          <div class="main-product-image"><%= fa_icon('camera') %></div>
        <% end %>
      </div>
    </div>

    <div class='col-md-4'>
      <% unless @product.dont_show_public_price? %>
      <p>
        <span class='margin'>
          <%= Product.human_attribute_name('margin') %>:
        </span>
        <span class='percentage'>
          <%= calculate_margin(@product, @order.dealer.price_list_category) %>%
        </span>
      </p>
      <% end %>
      <div class="other-data">
        <h4>Specifiche</h4>

        <% if @product.company_brand.present? %>
          <div class="clearfix">
            <b><%= Product.human_attribute_name("brand") %>:</b>
            <span><%= @product.company_brand.description %></span>
          </div>
        <% end %>

        <% if @product.store_tags.present? %>
          <div class="clearfix">
            <b><%= Product.human_attribute_name("store_tags") %>:</b>
            <span><%= @product.store_tags.join(", ") %></span>
          </div>
        <% end %>

        <% if @product.operators.any? %>
          <div class="clearfix">
            <b><%= Product.human_attribute_name("operator") %>:</b>
            <span><%= @product.operators.map(&:name).join(",") %></span>
          </div>
          <% end %>

        <% if @product.sim_format.present? %>
          <div class="clearfix">
            <b><%= Product.human_attribute_name("sim_format") %>:</b>
            <span><%= SimFormat.description_of(@product.sim_format) %></span>
          </div>
        <% end %>

        <% unless @product.bundle? %>
          <% if @product.vat_type.present? %>
            <div class="clearfix">
              <b><%= Product.human_attribute_name("vat").upcase %>:</b>
              <%= ProductVatType.description_of(@product.vat_type) %>
            </div>
          <% end %>
        <% end %>
      </div>
    </div>
  </div>
</div>
