<div class="row">
	<div class="col-md-12 choose_payment_kind orders">
		<%= page_title("Richiesta di pagamento / #{@payment_request.id}") %>
		<%= simple_form_for @payment_request, url: pay_payment_request_path(@payment_request), html: { class: "spinnered"} do |f| %>
			<div class="payment_methods_list">
				<span class="title">Seleziona il metodo di pagamento: </span>
				<%= f.input :selected_payment_method,
										as: :radio_buttons,
										collection: @payment_request.payment_method_list(only_cc: true, show_bill_charges: false),
										include_blank: false,
										label: false,
										input_html: { class: "cc_options"} %>
			</div>
			<div class="actions">
				<%= link_to "Paga subito", "#", class: "btn btn-primary", id: "pay_now" %>
				<%= link_to "Annulla", payment_requests_path, class: "btn btn-danger" %>
			</div>
			<%= render 'store/orders/mybank_modal' %>
		<% end %>
	</div>
</div>

<script type="text/javascript">
		$(function () {
        $('.cc_options').prop('checked', false);
        $('.spinnered').submit(function () {
            $("#mybank").modal('hide');
            $('.spinnered').spin();
        });


        $("#pay_now").on("click", function () {
            if ($(".cc_options").filter(":checked").val() == "mybank") {
                $("#mybank").modal();
            } else {
                $('.spinnered').submit();
            }
        });
    });    
</script>