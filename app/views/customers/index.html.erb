<%= page_title('clienti', 'lista') %>

<div class="search_form row">
  <%= search_form_for Customer.ransack(params[:q]) do |f| %>
    <div class="row">
      <div class='col-md-6'>
        <div class='input-group'>
          <span class="input-group-addon"><%= fa_icon 'tags' %></span>
          <%= f.search_field :first_name_eq, type: "text", value: params[:search].present? ? params[:search][:first_name_eq] : "", placeholder: Customer.human_attribute_name(:first_name) %>
        </div>
      </div>
      <div class='col-md-6'>
        <div class='input-group'>
          <span class="input-group-addon"><%= fa_icon 'tags' %></span>
          <%= f.search_field :last_name_eq, type: "text", value: params[:search].present? ? params[:search][:last_name_eq] : "", placeholder: Customer.human_attribute_name(:last_name) %>
        </div>
      </div>
      <div class='col-md-6'>
        <div class='input-group'>
          <span class="input-group-addon"><%= fa_icon 'tags' %></span>
          <%= f.search_field :cf_or_vat_or_company_cf_eq, type: "text", value: params[:search].present? ? params[:search][:cf_or_vat_or_company_cf_eq] : "", placeholder: 'Codice Fiscale/Partita IVA' %>
        </div>
      </div>
      <div class='col-md-6'>
        <div class='input-group'>
          <span class="input-group-addon"><%= fa_icon 'tags' %></span>
          <%= f.search_field :company_name_like, type: "text", value: params[:search].present? ? params[:search][:company_name_like] : "", placeholder: 'Denominazione/Ragione Sociale' %>
        </div>
      </div>
    </div>
    <div class="row">
      <div class='col-md-4'>
        <% if can?(:create, User) %>
          <%= button_link_to 'Crea cliente', new_customer_path %>
        <% end %>
      </div>
      <div class='col-md-6 col-md-offset-2 search-buttons'>
        <%= button_tag type: 'submit', class: 'btn btn-primary', title: I18n.t("search.search").titleize do %>
          <%= fa_icon('search') %>
        <% end %>
        <%= link_to fa_icon('undo'), customers_path(commit: I18n.t("search.clean")), title: I18n.t("search.clean").titleize, class: 'btn btn-primary' %>
      </div>
    </div>
  <% end %>
</div>

<table>
  <tr>
    <th class="id_column">#</th>
    <th>Nome</th>
    <th>Cognome</th>
    <th>Denominazione/Ragione Sociale</th>
    <th>C.F. / P.IVA</th>
    <th class="tools"></th>
  </tr>

  <% @customers.each do |customer| %>
    <tr>
      <td class="id_column"><%= customer.id %></td>
      <td><%= customer.first_name %></td>
      <td><%= customer.last_name %></td>
      <td><%= customer.company_name %></td>
      <td><%= customer.cf_or_vat %></td>
      <td>
        <%= button_group do %>
          <%= button_link_for_show customer %>
          <% if can?(:list, :audits) %>
            <%= button_link_to_audit(customer) %>
          <% end %>
          <% if can?(:update, customer) && can?(:update_from_list, customer) %>
            <%= button_link_for_edit edit_customer_path(customer) %>
          <% end %>
          <% if can?(:destroy, Customer) %>
            <%= button_link_for_destroy customer, method: :delete, data: { confirm: 'Sei sicuro?' } %>
          <% end %>
        <% end %>
      </td>
    </tr>
  <% end %>
</table>

<%= render partial: 'layouts/empty_list', locals: { message: @message_result } if @customers.empty? %>
<% if @customers.any? %>
  <%= pagination_box @customers %>
<% end %>
