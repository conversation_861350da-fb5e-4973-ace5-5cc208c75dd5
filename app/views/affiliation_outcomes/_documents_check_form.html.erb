<div class="modal-body">
  <div id="affiliation-outcome-form-errors"></div>
  <div class="row">
    <% if @affiliation.required_affiliation_documents.empty? %>
      <div class="col-md-12 text-center">
        <h4>Non è stato ancora caricato alcun documento per questa affiliazione.</h4>
      </div>
    <% else %>
      <%= f.fields_for :affiliation do |affiliation_form| %>
        <%= affiliation_form.fields_for :required_affiliation_documents do |affiliation_document| %>
          <div class="col-md-12 affiliation_document_row">
            <div class="col-md-5">
              <h5><%= Affiliation.human_attribute_name("form_kinds/#{affiliation_document.object.document_kind}") %></h5>
            </div>
            <div class="col-md-1">
              <% if affiliation_document.object.document.attached? %>
                <%= button_link_to fa_icon('file-pdf-o'), url_for(affiliation_document.object.document), target: '_tab', class: 'grey' %>
              <% end %>
            </div>
            <div class="col-md-3">
              <div class="field">
                <%= affiliation_document.select :status,
                                                affiliation_document.object.status_for_select.collect { |s| [AffiliationDocument.human_attribute_name(s), s] },
                                                { include_blank: affiliation_document.object.status.blank? },
                                                {
                                                  disabled: affiliation_document.object.status.present?,
                                                  class: 'document_status'
                                                } %>
              </div>
            </div>
            <div class="col-md-3">
              <%= affiliation_document.input :ko_reason,
                                             label: false,
                                             input_html: {
                                                           disabled: affiliation_document.object.status.present?,
                                                           class: 'document_ko_reason'
                                                         } %>
            </div>
          </div>
        <% end %>
      <% end %>
    <% end %>
  </div>
</div>
<div class="modal-footer">
  <%= f.hidden_field :status %>
  <%= hidden_field_tag :all_required_documents_approved, @affiliation.all_required_documents_approved? %>

  <% if @affiliation.required_affiliation_documents.empty? %>
    <button type="button" class="btn btn-default" data-dismiss="modal">Chiudi</button>
  <% else %>
    <div class="row">
      <div class="col-md-7 pt-1">
        <div id="send_agent_sms_container">
          <%= f.check_box :send_agent_sms %>
          <%= f.label :send_agent_sms %>
        </div>
      </div>
      <div class="col-md-5">
        <%= f.submit 'Salva Approvazioni', id: 'save_documents_check' %>
        <%= button_tag 'Affiliazione KO', type: 'button', class: 'btn btn-danger', onclick: 'confirmKo()' %>
      </div>
    </div>
  <% end %>
</div>

<script type="text/javascript">
    function confirmKo() {
        if (confirm('Sei sicuro di voler chiudere definitivamente in KO la richiesta di affiliazione?')) {
            $("#affiliation_outcome_status").val('ko');
            $("#affiliation-outcome-form").submit();
        }
    }
</script>