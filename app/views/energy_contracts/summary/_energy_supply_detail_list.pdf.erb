<ul class="energy-supply-detail-summary-list-<%= plan.plan_kind %>">
  <% if plan.energy? %>
    <li>
      Codice POD: <span class="bold shrink-text"><%= energy_contract.pod_code %></span>
    </li>
    <% unless %w(CP_CB CP).exclude?(energy_contract.check_punto_cliente_call&.check_punto_cliente_code).eql?(false) %>
        <li>
          Tensione di alimentazione: <span class="bold shrink-text"><%= suply_voltage_display(energy_contract.energy_supply_detail&.supply_voltage) %></span>
        </li>
        <li>
          Potenza impegnata: <span class="bold shrink-text"><%= energy_contract.energy_supply_detail&.power_used %> kW</span>
        </li>
    <% end %>
    <li>
      Tipologia: <span class="bold shrink-text"><%= EnergyPlan.human_attribute_name("destination/#{plan.destination}") %></span>
    </li>
    </ul>
  <% else %>
    <li>
      Codice PDR: <span class="bold shrink-text"><%= energy_contract.pdr_code %></span>
    </li>
    <% unless %w(CP_CB CP).exclude?(energy_contract.check_punto_cliente_call&.check_punto_cliente_code).eql?(false) %>
        <li>
          Uso: <span class="bold shrink-text"><%= energy_contract.energy_supply_detail&.destination %></span>
        </li>
        <li>
          Categoria d’uso: <span class="bold shrink-text"><%= energy_contract.energy_supply_detail&.use_category %></span>
        </li>
      <% end %>
    <li>
      Tipologia: <span class="bold shrink-text"><%= EnergyPlan.human_attribute_name("destination/#{plan.destination}") %></span>
    </li>
  <% end %>
</ul>
