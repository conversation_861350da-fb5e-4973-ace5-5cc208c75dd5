<div class="mb-80 plan-summary">
  <%= render partial: 'energy_contracts/details/plan', formats: [:html], locals: { plan: plan } %>
  <div class="plan-supply-detail">
    <% if %w(CP_CB CP).exclude?(energy_contract.check_punto_cliente_call&.check_punto_cliente_code).eql?(false) %>
      <p class="mb-20">
        Il tuo cambio prodotto è stato richiesto! Questa è la nuova offerta che andrà a sostituire l'attuale per l'utenza:
      </p>
    <% else %>
      <p class="mb-20">
        La tua fornitura <%= plan.energy? ? 'luce' : 'gas' %> si trova in<br>
        <span class="bold shrink-text"><%= energy_contract.full_address %></span>
      </p>
      <p class="mb-10">
        Ecco le informazioni che riguardano il tuo contatore <%= plan.energy? ? 'della luce' : 'del gas' %>:
      </p>
    <% end %>

    <%= render('energy_contracts/summary/energy_supply_detail_list', energy_contract: energy_contract, plan: plan) %>

    <p class="mb-20">
      <span class="bold shrink-text">Questa offerta è stata richiesta <%= full_format_date_for(energy_contract.created_at) %></span>,
      l’attivazione è prevista entro 60 giorni.
    </p>

    <p class="small-text mb-0">
      <% if plan.energy? %>
        Puoi consultare l'andamento mensile dell'indice PUN e le<br>
        relative fasce orarie sul sito windtre.it
      <% else %>
        Puoi consultare l'andamento mensile dell'indice PSV sul<br>
        sito windtre.it
      <% end %>
    </p>
  </div>
</div>
