<%
  energy_contract_plan = @energy_contract.energy_contract_energy_plans.energy.first
  gas_contract_plan = @energy_contract.energy_contract_energy_plans.gas.first
%>

<%= render 'shared/modal', modal_classes: 'details_modal' do %>
  <div data-controller="details-modal">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-action="click->modal#close">
      &times;
    </button>

    <%= render 'energy_contracts/details/header', energy_contract_plan: energy_contract_plan, gas_contract_plan: gas_contract_plan %>

    <section class="modal-body" data-bs-theme="activation">
      <section class="info-container">
        <table>
          <%= render 'energy_contracts/details/selected_offer_row' %>
          <%= render 'energy_contracts/details/submitted_data_row' %>
          <%= render 'energy_contracts/details/customer_signature_row' %>
          <%= render 'energy_contracts/details/submitted_offer_row' %>

          <% if energy_contract_plan %>
            <%= render('energy_contracts/details/energy_details_row',
                       energy_contract:      @energy_contract,
                       energy_contract_plan: energy_contract_plan) %>
          <% end %>

          <% if gas_contract_plan %>
            <%= render('energy_contracts/details/energy_details_row',
                       energy_contract:      @energy_contract,
                       energy_contract_plan: gas_contract_plan) %>
          <% end %>
        </table>
      </section>

      <section class="center-info">
        <%= button_tag 'Chiudi', class: 'btn btn-primary btn-long', data: { action: 'click->modal#close' } %>
      </section>
    </section>
  </div>
<% end %>
<script>
    $('.material-symbols-outlined').addClass('material-visible')
</script>
