module AffiliationDecorator
  REMOVED_STEPS = [:affiliation_proposal, :certification_invitation, :contract_delivery, :affiliation_form]

  def agent_name_for(user)
    return sub_agent.full_name if sub_agent
    agent&.full_name unless user.is?(:agent)
  end

  def attribute_changed?(field, value)
    formatted_attribute(field) != value
  end

  def dealer_address
    dealer.billing_address_via
  end

  def dealer_address_with_number
    [dealer_address, dealer.billing_address_number].join(' ')
  end

  def dealer_full_address
    [
      dealer_address_with_number,
      dealer.postal_code_and_city
  ].join(', ')
  end

  def duplication_allowed?
    Affiliation.of_partita_iva_or_codice_fiscale(dealer_partita_iva, dealer_codice_fiscale).all?(&:duplicable?)
  end

  def formatted_attribute(field)
    return '1' if send(field).is_a?(TrueClass)
    return '0' if send(field).is_a?(FalseClass)
    return send(field).strftime('%d/%m/%Y') if send(field).is_a?(Date)

    send(field).to_s
  end

  def operations
    if kolme_master_nexus?
      AffiliationOutcome::KOLME_MASTER_NEXUS_FIRST_GROUP_OPERATIONS + ['welcome_call']
    else
      base_operations = (AffiliationOutcome::DISPLAYED_FIRST_GROUP_OPERATIONS.map(&:to_sym) + aasm.states.map(&:name)).uniq

      base_operations.reject do |operation|
        REMOVED_STEPS.include?(operation) &&
          !affiliation_outcomes.where(operation: operation.to_s).exists?
      end
    end
  end

  def translated_attribute(value)
    return value if value.blank?
    translation = I18n.t("activerecord.attributes.affiliation.#{value}")

    translation.start_with?('Translation missing') ? value : translation
  end

  def warehouse_full_address
    dealer.warehouses.first.address
  end
end
