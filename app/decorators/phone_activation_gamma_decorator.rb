module PhoneActivationGammaDecorator
  include ActionView::Helpers::NumberHelper
  
  DOCUMENT_CODE = 'FA'.freeze
  ITEM_TYPE = 'Articolo'.freeze
  QUANTITY = 1.freeze

  def gamma_csv_row
    [
      DOCUMENT_CODE,
      created_at.strftime('%d/%m/%Y'),
      hex_id,
      dealer.gamma_code_cli,
      warehouse.gamma_code_dest_cli,
      '',
      '',
      '',
      '',
      "#{DOCUMENT_CODE}#{hex_id}",
      ITEM_TYPE,
      "#{product.code}",
      row_description,
      QUANTITY,
      number_with_precision(product.dealer_price_vat_excluded_b, separator: '.'),
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      product_item.serial
    ]
  end

  def row_description
    product.name
  end
end
