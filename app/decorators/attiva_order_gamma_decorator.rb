module AttivaOrderGammaDecorator
  include ActionView::Helpers::NumberHelper
  
  DOCUMENT_CODE = 'OA'.freeze
  FEES_TYPE = 'Spesa'.freeze
  PAYMENT_CODE = 'B3F'.freeze
  SHIPPING_COST = 9
  SHIPPING_COST_CODE = 'ECS'.freeze
  SHIPPING_ITEM_DESCRIPTION = 'Spese di spedizione'.freeze
  SHIPPING_ITEM_TYPE = 'TRASPCA'.freeze
  SUPPLIER_CODE = '95'.freeze

  def gamma_csv_rows
    rows = shopping_cart_items.map(&:gamma_csv_row_for_attiva_order)

    rows << shipping_costs_row

    rows
  end

  def shipping_costs_row
    [
      DOCUMENT_CODE, 
      order.created_at.strftime('%d/%m/%Y'),
      order_id.to_s,
      SUPPLIER_CODE,
      "#{order.dealer.gamma_code_cli} - #{order.warehouse.gamma_code_dest_cli}",
      '',
      '',
      '',
      PAYMENT_CODE,
      "#{SHIPPING_COST_CODE}#{order_id.to_s}",
      FEES_TYPE,
      SHIPPING_ITEM_TYPE,
      SHIPPING_ITEM_DESCRIPTION,
      1,
      number_with_precision(SHIPPING_COST, separator: '.'),
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
    ]
  end
end