# encoding: utf-8

module CustomerDecorator
  def birth_place_for_ngpos
    return birth_country.posng_value unless birth_province.italian?

    birth_place
  end

  def city_ui_description
    if city.present?
      "#{city.description} [#{province.code}]"
    end
  end

  def civility_for_floa
    gender == 'M' ? 'Mr' : 'Ms'
  end

  def customer_address
    persona_giuridica? ? headquarters_address : address
  end

  def customer_number
    persona_giuridica? ? headquarters_number : number
  end

  def customer_province_code
    persona_giuridica? ? headquarters_province.code : province.code
  end

  def customer_city
    persona_giuridica? ? headquarters_city.description : city.description
  end

  def customer_kind_for_ngpos
    I18n.t("ngpos.fields.customer.#{customer_kind_code}")
  end

  def customer_zip
    persona_giuridica? ? headquarters_zip : zip
  end

  def gender_for_ngpos
    gender == 'M' ? 'Maschio' : 'Femmina'
  end

  def address_data
    OpenStruct.new(address_detail)
  end

  def address_detail
    {
      street_type_id:         customer_kind.persona_giuridica? ? headquarters_address_street_type_id : address_street_type_id,
      address_street_name:    customer_kind.persona_giuridica? ? headquarters_address_street_name : address_street_name,
      location_number:        customer_kind.persona_giuridica? ? headquarters_number : number,
      city_and_province_code: customer_kind.persona_giuridica? ? headquarters_city.with_province_code : city.with_province_code,
      city_name:              customer_kind.persona_giuridica? ? headquarters_city.description : city.description,
      city_id:                customer_kind.persona_giuridica? ? headquarters_city_id : city_id,
      location_cap:           customer_kind.persona_giuridica? ? headquarters_zip : zip
    }
  end

  def floa_financing_attributes
    {
      uniq_name: uniq_name,
      address:   address,
      number:    number,
      zip:       zip,
      city:      city&.titleized_description,
      province:  province&.code,
      cf:        cf,
      vat:       vat
    }
  end

  def formatted_id_doc_date
    id_doc_date.strftime('%d/%m/%Y')
  end

  def formatted_birth_date
    birth_date.strftime('%d/%m/%Y')
  end

  def headquarters_city_ui_description
    if headquarters_city.present? and headquarters_province.present?
      "#{headquarters_city} [#{headquarters_province.code}]"
    end
  end

  def id_doc_kind_peoplesoft
    if id_doc_kind == 'CI' or id_doc_kind == 'Carta Identità'
      'C.I'
    elsif id_doc_kind == 'Patente'
      'PAT'
    elsif id_doc_kind == 'Passaporto'
      'PAS'
    else
      ident_doc_kind
    end
  end

  def id_doc_kind_health_card?
    identity_document_kind&.health_card?
  end

  def iso_utc_created_at
    created_at.utc.iso8601
  end

  def iso_utc_updated_at
    updated_at.utc.iso8601
  end

  def name_for_dhl_delivery_note
    uniq_name = calculate_uniq_name

    return uniq_name unless uniq_name.length > 25

    if ditta_individuale_or_persona_giuridica?
      uniq_name.truncate(25, omission: '')
    else
      "#{first_name.chr}. #{last_name.truncate(22, omission: '')}"
    end
  end

  def pda_data
    [
      pda_full_name,
      "\n",
      pda_birth_date,
      ' - ',
      pda_birth_place,
      "\n",
      pda_cf,
      ' - ',
      pda_gender,
      "\n",
      pda_customer_kind,
      ' ',
      pda_doc_issued_by,
      "\n",
      pda_doc_nationality,
      "\n",
      pda_mobile_phone,
      "\n",
      pda_email
    ].join('')
  end

  def pda_data_mnp
    [
      pda_full_name,
      ' - ',
      pda_cf,
      ' - ',
      pda_gender,
      "\n",
      pda_birth_date,
      ' - ',
      pda_birth_place,
      "\n",
      pda_customer_kind,
      ' ',
      pda_doc_issued_by,
      ' - ',
      pda_doc_nationality,
      "\n",
      pda_address,
      pda_mobile_phone,
      ' - ',
      pda_email
    ].join('')
  end

  def pda_full_name
    "Nome: #{first_name} - Cognome: #{last_name}"
  end

  def pda_birth_date
    "Data nascita: #{birth_date.try(:strftime, '%d/%m/%Y')}"
  end

  def pda_birth_place
    "Luogo di nascita: #{birth_place} (#{birth_province.try(:code)}) - #{birth_country.try(:description)}"
  end

  def pda_cf
    "Codice Fiscale: #{cf}"
  end

  def pda_gender
    "Sesso: #{gender}"
  end

  def pda_customer_kind
    "#{id_doc_kind}: #{id_doc_number}"
  end

  def pda_doc_issued_by
    "rilasciato da: #{doc_issued_by} il #{id_doc_date.try(:strftime, '%d/%m/%Y')}"
  end

  def pda_doc_nationality
    "Nazionalità documento: #{pda_doc_country_name}"
  end

  def pda_address
    !persona_giuridica? ? "Indirizzo: #{address}, #{number} #{zip} #{city} (#{province.try(:code)})\n" : ''
  end

  def pda_mobile_phone
    "Numero mobile: #{mobile_phone}"
  end

  def pda_email
    "E-mail: #{email}"
  end

  def pda_doc_country_name
    return nil unless id_doc_country

    id_doc_country.description || nil
  end

  def phone_activation_name
    persona_giuridica? ? company_name : name
  end

  def phone_activation_origin?
    origin == Customer::PHONE_ACTIVATION_ORIGIN
  end

  def prefixed_mobile_phone
    Phoner::Phone.parse(mobile_phone).format('%c%a%n')
  end

  def provide_company_kind_from_acea
    case acea_legal_form
    when 'S.A.S.'
      'Sas'
    when 'S.R.L.'
      'Srl'
    when 'S.N.C.'
      'Snc'
    when 'S.P.A.'
      'Spa'
    when 'S.COOP.A R.L.'
      'Scrl'
    when 'S.R.L.S.'
      'Srls'
    else
      'Altro'
    end
  end

  def set_acea_ateco
    code =     case company_kind
               when 'Sas'
                 'S.A.S.'
               when 'Srl'
                 'S.R.L.'
               when 'Snc'
                 'S.N.C.'
               when 'Spa'
                 'S.P.A.'
               when 'Scrl'
                 'S.COOP.A R.L.'
               when 'Srls'
                 'S.R.L.S.'
               else
                 nil
               end

    if code
      self.acea_legal_form = code
    end
  end
  def require_residence_section?
    !persona_giuridica?
  end

  def set_gender_from_cf
    return unless birth_date.present?
    return unless cf.present?

    self.gender = (cf[9..10] == birth_date.to_date.day.to_s.rjust(2, "0")) ? 'M' : 'F'
  end

  def shipping_phone_number
    mobile_phone
  end

  def signature_data
    { first_name: first_name, last_name: last_name, area_code: '+39', email: email }
  end

  def tls_address_data
    OpenStruct.new(address_detail.merge(first_name: first_name, last_name: last_name))
  end

  def w3_eligibility_payload
    { name: first_name, surname: last_name, ssn: cf, piva: vat }.to_json
  end

  def w3_search_full_payload
    { type: 'SSN', parameter: cf, skipBSNmessage: true, stack: 'WIND', canaleReq: 'POS' }
  end
end
