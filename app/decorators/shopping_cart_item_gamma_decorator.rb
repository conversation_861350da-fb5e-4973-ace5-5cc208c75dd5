module ShoppingCartItemGammaDecorator
  include ActionView::Helpers::NumberHelper

  ITEM_TYPE = 'Articolo'.freeze

  def gamma_csv_row
    [
      Order::DOCUMENT_CODE, 
      order.created_at.strftime('%d/%m/%Y'),
      order.id.to_s,
      order.dealer.gamma_code_cli,
      order.warehouse.gamma_code_dest_cli,
      '',
      order.consignment? ? 'CD' : '',
      '',
      order.gamma_payment_code,
      "#{Order::DOCUMENT_CODE}#{id}",
      ITEM_TYPE,
      item.code,
      item.name,
      quantity,
      number_with_precision(sci_price, separator: '.'),
      discount.positive? ? discount : '',
      order.gamma_consignment_value,
      '',
      order.force_zero_discount_value,
      '',
      '',
      '',
      '',
      '',
    ]
  end

  def gamma_csv_row_for_attiva_order
    [
      AttivaOrder::DOCUMENT_CODE, 
      order.attiva_order.created_at.strftime('%d/%m/%Y'),
      order.id.to_s,
      AttivaOrder::SUPPLIER_CODE,
      "#{order.dealer.gamma_code_cli} - #{order.warehouse.gamma_code_dest_cli}",
      '',
      '',
      '',
      AttivaOrder::PAYMENT_CODE,
      "#{AttivaOrder::DOCUMENT_CODE}#{id}",
      ITEM_TYPE,
      item.code,
      item.name,
      quantity,
      number_with_precision(item.gamma_purchase_price, separator: '.'),
      discount.positive? ? discount : '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      '',
    ]
  end
end
