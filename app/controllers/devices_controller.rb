class DevicesController < ApplicationController
  load_and_authorize_resource

  def destroy
    @device.destroy

    redirect_to user_path(@device.user)
  end

  def edit; end

  def unlock
    @device.unlock!
  end

  def untrust
    @device.update(otp_trusted: false)
  end

  def update
    @device.update device_params
  end

  private

  def device_params
    params.require(:device).permit(:model_description)
  end
end