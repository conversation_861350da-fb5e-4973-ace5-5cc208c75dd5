# coding: utf-8

class RechargesController < ApplicationController
  load_and_authorize_resource
  before_action :store_global_location, only: :index

  def index
    @recharge = Recharge.new
    begin
      if Recharges::RechargeHandler.provider == RechargeSize::PROVIDER_PAYMAT
        @credit_available_kolme ||= Paymat::Paymat.new.credit_available.gsub('.', '').to_i
      elsif Recharges::RechargeHandler.provider == RechargeSize::PROVIDER_EURONET
        @credit_available_kolme ||= Euronet.new({}).balance
      end
    rescue StandardError
      @credit_available_kolme = 0
    end
    if current_user.is?(:dealer)
      dealer_id                = current_user.dealer.id
      @dealer                  = Dealer.find(dealer_id)
      @dealers                 = [@dealer]
      @credit_available_dealer = current_user.dealer.recharges_credit_available.to_s
    end
    if current_user.internal_user?
      @dealers = Repositories::DealerRepository.new(current_user: current_user)
        .with_or_without_operators.order('name ASC').distinct
    end
    if params[:dealer_id] && params[:show_dealer_name]
      @dealer              = Dealer.find(params[:dealer_id])
      params[:dealer_name] = @dealer.name
    end

    @aggregated_operators_groups     = AggregatedOperator.with_post_sale_recharge_sizes.joins(:aggregated_operator_category).select('aggregated_operators.id, aggregated_operators.name, aggregated_operators.position, aggregated_operator_categories.description').group_by(&:description)
    @aggregated_operators_collection = AggregatedOperator.unscoped.where('aggregated_operators.id in (select distinct aggregated_operator_id from recharge_sizes)').order('name ASC').map { |ao| [ao.name, ao.id] }
    @recharge_sizes                  = RechargeSize
      .for_post_sale
      .order('sorting ASC')
      .map do |c|
      [c.name,
       c.id,
       { 'data-cif': c.cif?, 'data-pin': c.pin?, 'data-retry-on-ko': c.retry_on_ko?, 'data-operator': c.paymat_operator_id, 'data-posa': c.bypass_phone_number_check?, 'data-aggregated-operator': c.aggregated_operator_id, 'data-amount': c.stripped_amount }]
    end
    @amounts = RechargeSize.all.order(:amount).map(&:stripped_amount).uniq

    respond_to do |format|
      format.html do
        clear_search_if_resetted
        @search    = RechargeService.new.execute_search(current_user, recharge_search_params)
        @recharges = @search.results
      end
      format.csv do
        if !recharge_search_params[:search] && !current_user.is?(:dealer)
          redirect_to recharges_path, gflash(error: "Eseguire una ricerca prima di procedere con l'esportazione")
        else
          export
        end
      end
    end
  end

  def new
    if params[:dealer_id] or current_user.is?(:dealer)
      dealer_id = params[:dealer_id] || current_user.dealer.id
      @dealer   = Dealer.find(dealer_id)
      @dealers  = [@dealer]
    end
    @recharge = Recharge.new
    respond_to do |format|
      format.js
    end
  end

  def create
    @recharge.user_id = current_user.id
    
    if @recharge.valid?
      if Recharges::RechargeHandler.new.check_valid_not_telefonia_amount_for(current_user, @recharge)
        recharge = Recharges::RechargeHandler.new.create_and_execute!(recharge_params.merge!(user_id: current_user.id, remote_ip_address: request.remote_ip))
        if recharge.recharge_status == Recharge::SUCCESS
          redirect_to recharges_path(commit: I18n.t('search.clean')), gflash: { success: 'Ricarica avvenuta con successo.' }
        elsif recharge.recharge_status == Recharge::FAILED
          error_message = 'Ricarica non eseguita, controlla lo scontrino.'
          error_message = 'Numerazione errata' if recharge.feedback_paymat[:error_code] == 'Service_081'
          redirect_to recharges_path(commit: I18n.t('search.clean')), gflash: { error: error_message }
        else
          redirect_to recharges_path(commit: I18n.t('search.clean')), gflash: { success: 'Ricarica inserita con successo.' }
        end
      else 
        error_message = 'Sono in corso delle verifiche sull\'utilizzo del credito per ricariche.'
        current_user.dealer.update_columns(recharges_pin_disabled: true, recharges_pin_disabled_unlocked_at: nil)
        RechargeMailer.send_limit_not_telefonia_reached(recharge, current_user).deliver_now
        
        redirect_to recharges_path(commit: I18n.t('search.clean')), gflash: { error: error_message }
      end
    else
      if @recharge.recharge_size.present?
        redirect_to recharges_path(commit: I18n.t('search.clean')), gflash: { error: 'Il numero inserito non &egrave; valido, la ricarica non &egrave; stata creata.' }
      else
        redirect_to recharges_path(commit: I18n.t('search.clean')), gflash: { error: 'È necessario selezionare un taglio di ricarica per poter procedere.' }
      end
    end
  rescue StandardError => e
    Rails.logger.error e.inspect
    redirect_to recharges_path(commit: I18n.t('search.clean')), gflash: { error: 'Errore: ricarica non eseguita.' }
  end

  def edit
    @recharge = Recharge.find(params[:id])
  end

  def update
    @recharge = Recharge.find(params.to_unsafe_h[:id])
    if (recharge_params[:recharge_status] != Recharge::FAILED || @recharge.can_execute_recharge?) && @recharge.update(recharge_params.merge!(recharged_at: Time.now))

      @recharge.balanceable_amount = @recharge.recharge_size.amount_charged

      if @recharge.manually_executed_success?
        AccountBalance::AccountBalanceItem.new(@recharge).record!
      end

      respond_to do |format|
        format.html { redirect_to recharges_path(params.to_unsafe_h[:filter]), gflash: { success: 'Ricarica eseguita' } }
        format.json { respond_with_bip(@recharge) }
      end
    else
      if !@recharge.can_execute_recharge?
        redirect_to recharges_path, gflash: { error: 'Ricarica non eseguita causa credito insufficiente' }
      else
        render action: 'edit', alert: 'errore nel salvare la ricarica'
      end
    end
  end

  def show
    @recharge = Recharge.find(params[:id])
  end

  def show_receipt
    @recharge = Recharge.find(params[:id])
    respond_to do |format|
      format.html { render layout: false }
      format.js
    end
  end

  def send_receipt_via_mail
    @recharge = Recharge.find(params[:id])
    email     = params[:email]
    if is_a_valid_email?(email)
      RechargeMailer.send_receipt_via_mail(@recharge, email).deliver_now
      redirect_to recharges_path, gflash: { success: 'Email inviata con successo' }
    else
      redirect_to recharges_path, gflash: { error: 'Email non valida' }
    end
  end

  def refresh_credit
    begin
      if Recharges::RechargeHandler.provider == RechargeSize::PROVIDER_PAYMAT
        @credit_available ||= Paymat::Paymat.new.credit_available.gsub('.', '').to_i
      elsif Recharges::RechargeHandler.provider == RechargeSize::PROVIDER_EURONET
        @credit_available ||= Euronet.new({}).balance(true)
      end
    rescue StandardError
      @credit_available = 0
    end
    respond_to do |format|
      format.js
    end
  end

  def check_recharge_available
    @recharge_size = RechargeSize.find(params[:recharge_size_id])
    @dealer        = Dealer.find(params[:dealer_id]) if params[:dealer_id]
  end

  def destroy
    @recharge = Recharge.find(params[:id])
    if @recharge.update_attribute(:deleted, true)
      gflash(success: 'ricarica cancellata')
      redirect_back_or_default recharges_path
    end
  end

  def export
    filterd_data = RechargeService.new.execute_search(current_user, recharge_search_params, true).results
    export_data  = Recharges::RechargeHandler.export_excel(filterd_data)
    type         = 'text/csv; charset=utf-8; header=present'
    send_data(export_data, filename: "ricariche-#{DateTime.now.strftime('%d%m%Y')}.csv", type: type)
  end

  def already_recharged_today
    phone_number             = params.fetch(:phone_number, nil)
    @already_recharged_today = Recharge.where('recharged_at > ?', Time.zone.now.beginning_of_day).where(deleted: false, recharge_status: Recharge::SUCCESS, phone_number: phone_number).exists?
    respond_to do |format|
      format.json { render json: { already_recharged_today: @already_recharged_today } }
    end
  end

  private

  def clear_search_if_resetted
    params[:search].delete if params[:search].present? && params[:commit].present?
  end

  def is_a_valid_email?(email)
    (email =~ /\A([\w+-].?)+@[a-z\d-]+(\.[a-z]+)*\.[a-z]+\z/i)
  end

  def recharge_params
    params.require(:recharge).permit(
      :dealer_id,
      :deleted,
      :feedback_paymat,
      :kind,
      :mail_sent,
      :manually_executed,
      :notes,
      :notes_kolme,
      :phone_activation_id,
      :phone_number,
      :provider,
      :recharge_attempts,
      :recharge_size_id,
      :recharge_status,
      :recharged_at,
      :secure_token,
      :user_id
    )
  end

  def recharge_search_params
    params.permit(
      :page,
      search: [
                :aggregated_operator_id,
                :dealer_id,
                :dealer_name,
                :phone_number,
                :recharge_id,
                :recharge_amount,
                :recharge_kind,
                :recharge_operator,
                :recharge_status,
                :recharged_at_from,
                :recharged_at_to,
                :show_dealer_name,
                { user_id: [] }
              ]
    )
  end
end
