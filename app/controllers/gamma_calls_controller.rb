class GammaCallsController < ApplicationController
  load_and_authorize_resource
  INDEX_FIELDS = %i[id created_at name caller_type caller_id response_xml relaunched_at manually_confirmed_at].freeze

  def index
    authorize! :debug, GammaCall
    search = Repositories::GammaCallRepository.new(current_user: current_user).search(params)
    @gamma_calls = search.results
  end

  def show;
  end

  def destroy
    @gamma_call.destroy
    redirect_to gamma_calls_path
  end

  def run
    # begin
    #   Ws::WsProxy.remote_call(call_name: @gamma_call.name, caller_id: @gamma_call.caller_id, caller_type: @gamma_call.caller_type)
    #   @gamma_call.update(relaunched_at: Time.now, relaunching_user_id: current_user.id)
    #   gflash success: "chiamata rilanciata con successo"
    # rescue StandardError => e
    #   Rails.logger.info e.message
    #   Rails.logger.info e.backtrace
    gflash error: "Non e' piu' possibile effettuare chiamate a gamma"
    # end
    redirect_to gamma_calls_path
  end

  def manually_confirm
    @gamma_call.update(manually_confirmed_at: Time.now, manually_confirming_user_id: current_user.id)

    redirect_to gamma_calls_path
  end

  private

  def params_model_type
    params[:model_type]
  end
end
