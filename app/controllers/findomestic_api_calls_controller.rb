class FindomesticApiCallsController < ApplicationController
  before_action :authenticate_user!
  before_action :authorize_admin
  before_action :set_findomestic_api_call, only: [:show, :retry]

  def index
    @search = FindomesticApiCall.includes(:imei_reservation)
                                .ransack(params[:q])
    @findomestic_api_calls = @search.result
                                   .order(created_at: :desc)
                                   .page(params[:page])
                                   .per_page(30)
  end

  def show
    @imei_reservation = @findomestic_api_call.imei_reservation
  end

  def retry
    case @findomestic_api_call.call_type
    when 'activate_installment'
      retry_activate_installment
    when 'cancel_installment'
      retry_cancel_installment
    else
      redirect_to findomestic_api_calls_path,
                  gflash: { error: 'Cannot retry this type of API call' }
      return
    end

    redirect_to findomestic_api_call_path(@findomestic_api_call),
                gflash: { success: 'Retry scheduled successfully' }
  rescue StandardError => e
    redirect_to findomestic_api_call_path(@findomestic_api_call),
                gflash: { error: "Retry failed: #{e.message}" }
  end

  private

  def set_findomestic_api_call
    @findomestic_api_call = FindomesticApiCall.find(params[:id])
  end

  def authorize_admin
    authorize! :manage, FindomesticApiCall
  end

  def retry_activate_installment
    findomestic_service = FindomesticService.new(@findomestic_api_call.imei_reservation)
    result = findomestic_service.activate_financing

    unless result[:success]
      raise StandardError, result[:error]
    end
  end

  def retry_cancel_installment
    findomestic_service = FindomesticService.new(@findomestic_api_call.imei_reservation)
    result = findomestic_service.cancel_financing(:manual)

    unless result[:success]
      raise StandardError, result[:error]
    end
  end
end
