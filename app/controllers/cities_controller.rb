class CitiesController < ApplicationController
  autocomplete :city, :description,
               extra_data:    [:province_id, :description],
               display_value: :with_province_code

  load_and_authorize_resource only: [:for_warehouse_installation_areas]

  def for_warehouse_installation_areas
    @cities = City.matching_cities(@city)
                  .where("lower(cities.description) like ?", "#{params[:q]}%")
                  .order(:description).limit(20)

    respond_to do |format|
      format.json { render json: @cities.map { |c| { id: c.id, name: c.description } } }
    end
  end

  private

  def get_autocomplete_items(parameters)
    if params[:province_id].present?
      parameters = parameters.deep_merge(options: { where: { province_id: params[:province_id] } })
    end

    active_record_get_autocomplete_items(parameters)
  end
end
