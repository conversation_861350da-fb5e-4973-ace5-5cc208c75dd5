class Api::V1::PaymentExtrasController < ApplicationController
  skip_before_action :verify_authenticity_token
  skip_before_action :authenticate_user!

  layout false

  before_action :authenticate

  def accept_hipay
    @payment_extra.update_column(:state, "success")
  end

  def authenticate_error; end

  def cancel_hipay
    @payment_extra.update_column(:state, "canceled")
  end

  def decline_hipay
    @payment_extra.update_column(:state, "error")
  end

  def exception_hipay
    @payment_extra.update_column(:state, "exception")
  end

  def pending_hipay
    @payment_extra.update_column(:state, "pending")
  end

  def not_found; end

  def redirect_hipay
    @payment_extra.update_column(:state, "visited")
    success_callback = lambda { |payment_url| redirect_to payment_url, allow_other_host: true }
    failure_callback = lambda { |message| gflash error: message; render :authenticate_error, status: :internal_server_error, layout: false }
    HipayService.new.start_payment! @payment_extra, success_callback, failure_callback
  end

  protected

  def authenticate
    payment_id = JwtService.new.simple_decode(params[:token]).first["data"]
    @payment_extra = PaymentExtra.find(payment_id)
    render :already_paid if @payment_extra.success?
  rescue ActiveRecord::RecordNotFound => err
    render :not_found, status: :not_found, layout: false
  rescue StandardError => err
    render :authenticate_error, status: :internal_server_error, layout: false
  end
end
