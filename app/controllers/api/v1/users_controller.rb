module Api
  module V1
    class UsersController < ApplicationController
      skip_before_action :verify_authenticity_token, only: [:get_user_data]      
      skip_before_action :authenticate_user!, only: [:get_user_data]    
    

      def get_user_data
        token = request.headers['Authorization']&.split(' ')&.last
        
        unless token
          render json: { success: false, error: 'No token provided' }, status: :unauthorized
          return
        end

        result = CookieService.provide_user_from_cookie(token)
        
        if result[:user].present?
          @current_user = result[:user]
          render json: {
            success: true,
            user: {
              id: @current_user.id,
              email: @current_user.email,
              role: @current_user.role,
              first_name: @current_user.internal_user_detail.first_name,
              last_name: @current_user.internal_user_detail.last_name
            }
          }
        else
          render json: { success: false, error: result[:errors] || 'User not found' }, status: :unauthorized
        end
      rescue => e        
        render json: { success: false, error: e.message }, status: :unauthorized
      end
    end
  end
end 