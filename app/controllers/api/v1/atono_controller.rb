class Api::V1::Atono<PERSON>ontroller < ApplicationController
  skip_before_action :verify_authenticity_token
  skip_before_action :authenticate_user!

  def update_connection_status
    drop_pay_account_id = JwtService.new.decode(params[:token], false)[0]['data']
    drop_pay_account = DropPayAccount.unscoped.find(drop_pay_account_id)

    drop_pay_account.check_connection_status
  end

  def update_recursive_charges_authorization_status
    drop_pay_account_id = JwtService.new.decode(params[:token], false)[0]['data']
    drop_pay_account = DropPayAccount.find(drop_pay_account_id)

    drop_pay_account.update_recursive_charges_authorization_status(params['edata'])
  end
end