# frozen_string_literal: true

class ContractTermsController < ApplicationController
  load_and_authorize_resource

  def index
    @contract_terms = ContractTerm.includes(:operator).page(params[:page])
  end

  def show; end

  def new; end

  def create
    @contract_term = ContractTerm.new contract_term_params
    if @contract_term.save
      redirect_to contract_term_path(@contract_term), gflash: { success: "Condizione generale salvata con successo" }
    else
      render action: :new
    end
  end

  def edit; end

  def update
    if @contract_term.update contract_term_params
      redirect_to contract_term_path(@contract_term), gflash: { success: "Condizione generale modificata con successo" }
    else
      render action: :edit
    end
  end

  def destroy
    @contract_term.destroy
    redirect_to contract_terms_path, gflash: { success: "Condizione generale cancellata con successo" }
  end

  private

  def contract_term_params
    params.require(:contract_term).permit(
      :document,
      :kind,
      :md_very_enabled,
      :operator_id,
      :title,
      :visible
    )
  end
end
