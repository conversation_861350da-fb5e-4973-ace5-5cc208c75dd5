# encoding: utf-8

class NumberPortabilityDetailsController < ApplicationController
  include ActivationStepping
  include PhoneActivationsGflashHelper
  include SanitizeAttributes

  load_and_authorize_resource :phone_activation
  before_action :process_custom_source_operator, only: :create
  before_action :set_do_number_portability, only: %i[create update]
  before_action :strip_attributes, only: %i[create update]
  load_and_authorize_resource :number_portability_detail,
                              through:   :phone_activation,
                              singleton: true

  def create
    if @number_portability_detail.save
      if activation_next_step(@phone_activation) == phone_activations_path
        redirect_to activation_next_step(@phone_activation), gflash: save_success_gflash_params_for(@phone_activation)
      else
        redirect_to activation_next_step(@phone_activation)
      end
    else
      render :new
    end
  end

  def edit
    @number_portability_detail.valid?
  end

  def send_otp
    @number_portability_detail = NumberPortabilityDetail.new(number_portability_detail_params.merge(phone_activation_id: @phone_activation.id))
    if @number_portability_detail.phone_number && @number_portability_detail.source_operator
      response = NumberPortabilityDetails::OtpSenderClient.new(@phone_activation).send_message(@number_portability_detail)
    end

    render json: response
  end

  def update
    number_portability_detail_saved = @number_portability_detail.update number_portability_detail_params
    if number_portability_detail_saved
      @phone_activation.undraft_number_portability_detail!
    end

    respond_to do |format|
      if number_portability_detail_saved && @number_portability_detail.reload.state != 'draft'
        format.html { redirect_to activation_next_step(@phone_activation) }
      else
        format.html { render action: 'edit' }
      end
      format.json { respond_with_bip(@number_portability_detail) }
    end
  end

  def update_in_place
    respond_to do |format|
      fixed_params = prepare_for_update_columns number_portability_detail_params.to_h.except(:phone_number)
      @number_portability_detail.update_columns fixed_params unless fixed_params.blank?
      if number_portability_detail_params[:phone_number]
        @number_portability_detail.update(phone_number: number_portability_detail_params[:phone_number])
      end
      format.json { respond_with_bip @number_portability_detail }
    rescue StandardError => e
      @number_portability_detail.errors.add(:base, e.message)
      format.json { respond_with_bip @number_portability_detail }
    end
  end

  def update_in_place_without_validations
    if params[:number_portability_detail].present?
      adsl_migration_code_param  = params[:number_portability_detail][:adsl_migration_code]
      voice_migration_code_param = params[:number_portability_detail][:voice_migration_code]
      source_operator_param      = params[:number_portability_detail][:source_operator_id]
      activation_line_path_param = params[:number_portability_detail][:activation_line_path]

      @number_portability_detail.update_column(:adsl_migration_code, adsl_migration_code_param) if adsl_migration_code_param.present?
      @number_portability_detail.update_column(:voice_migration_code, voice_migration_code_param) if voice_migration_code_param.present?
      @number_portability_detail.update_column(:source_operator_id, source_operator_param) if source_operator_param.present?
      @number_portability_detail.update_column(:activation_line_path, activation_line_path_param) if activation_line_path_param.present?

      respond_to do |format|
        format.json { respond_with_bip @number_portability_detail }
      end
    end
  end

  def validate_otp
    response = NumberPortabilityDetails::OtpSenderClient.new(@phone_activation).validate_otp(params[:otp])

    render json: response
  end

  private

  # convert all boolean fields from true/false to 1/0,
  # cause update_columns place doesn't like true/false string
  def prepare_for_update_columns(args)
    boolean_keys = %i(credit_transfer sim_lost_or_stolen)
    args.each do |key, value|
      if key.to_sym.in? boolean_keys
        if value == "true"
          args[key] = 1
        elsif value == "false"
          args[key] = 0
        else
          raise "Valore non valido (inserire true/false): #{value}"
        end
      end
    end
    args
  end

  def number_portability_detail_params
    params.require(:number_portability_detail).permit(
      :adsl_migration_code,
      :different_adsl_migration_code,
      :different_owner,
      :has_additional_associated_numbers,
      :location_address,
      :location_address_street_type_id,
      :location_city,
      :location_number,
      :location_province_id,
      :location_zip,
      :owner_birth_date,
      :owner_birth_place,
      :owner_birth_province_id,
      :owner_cf,
      :owner_company_kind,
      :owner_company_name,
      :owner_document_date,
      :owner_document_number,
      :owner_document_expiry,
      :owner_gender,
      :owner_kind,
      :owner_identity_document_kind_id,
      :owner_first_name,
      :owner_last_name,
      :owner_vat,
      :phone_number,
      :prefix,
      :otp,
      :sim_lost_or_stolen,
      :source_operator_id,
      :voice_migration_code,
      :sim_serial,
      :contract_kind_id,
      :credit_transfer,
      additional_associated_numbers: NumberPortabilityDetail::MAX_ASSOCIATED_NUMBERS.map(&:to_s)
    )
  end

  def process_custom_source_operator
    custom_source_operator                               = params[:number_portability_detail].delete(:custom_source_operator)
    params[:number_portability_detail][:source_operator] = custom_source_operator if custom_source_operator.present?
  end

  def set_do_number_portability
    phone_activation_params = params[:number_portability_detail].delete(:phone_activation) || {}
    if phone_activation_params[:do_number_portability].blank? && @number_portability_detail
      flash.now[:notice] = "Devi selezionare una scelta per la portabilità"
      render action: @number_portability_detail.persisted? ? "edit" : "new"
    end
    phone_activation_params[:do_number_portability] ||= @phone_activation.do_number_portability
    @phone_activation.update_attribute(:do_number_portability,
                                       phone_activation_params[:do_number_portability])

    if activation_next_step(@phone_activation) == phone_activations_path
      redirect_to activation_next_step(@phone_activation), gflash: save_success_gflash_params_for(@phone_activation)
    elsif @phone_activation.do_number_portability == "N"
      redirect_to activation_next_step(@phone_activation)
    end
  end
end