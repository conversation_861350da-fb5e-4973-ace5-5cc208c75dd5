# encoding: utf-8

class InsurancesController < ApplicationController
  load_and_authorize_resource
  skip_load_resource only: %i[create use_voucher]

  def index
    @search     = Repositories::InsuranceRepository.new(current_user: current_user).search(params)
    @insurances = @search.result.page(params[:page] || 1)
  end

  def new
    @insurance.price       = 0
    @customer              = Customer.new
    @insurance.customer    = @customer
    @price_ranges          = EasycarePriceRange.all
    @warehouses            = WarehouseService.warehouses_for_user(current_user)
    @product_invoice_types = InsuranceService.product_invoice_type_for_user(current_user)
  end

  def create
    params                 = InsuranceService.build_params_for_user(insurance_params, current_user)
    @insurance             = Insurance.new(params)
    @price_ranges          = EasycarePriceRange.all
    @warehouses            = WarehouseService.warehouses_for_user(current_user)
    @product_invoice_types = InsuranceService.product_invoice_type_for_user(current_user)
    if @insurance.save
      redirect_to insurance_path(@insurance)
    else
      render :new
    end
  end

  def edit
    @price_ranges          = EasycarePriceRange.all
    @warehouses            = WarehouseService.warehouses_for_user(current_user)
    @product_invoice_types = InsuranceService.product_invoice_type_for_user(current_user)
  end

  def update
    @price_ranges = EasycarePriceRange.all
    @warehouses   = WarehouseService.warehouses_for_user(current_user)
    if @insurance.update(insurance_params)
      redirect_to insurance_path(@insurance)
    else
      render :edit
    end
  end

  def destroy
    gflash error: "La copertura e' stata confermata" if @insurance.sent?
    @insurance.destroy if !@insurance.sent?

    redirect_to insurances_path
  end

  def cancel
    @insurance = Insurance.find(params[:insurance_id])

    if @insurance.sent?
      gflash error: "La copertura e' stata confermata"
    end

    if cannot?(:cancel, @insurance)
      gflash error: "Non hai i permessi per annullare la copertura"
    end

    if can?(:cancel, @insurance) && !@insurance.sent?
      @insurance.cancel!
    end

    redirect_to insurances_path
  end

  def force_cancel
    @insurance = Insurance.find(params[:insurance_id])
    @insurance.cancel!
    gflash success: "La copertura e' stata annullata"
    redirect_to insurances_path
  end

  def document
    @insurance                              = Insurance.find(params[:insurance_id])
    response.headers["Content-Disposition"] = "attachment; filename=\"#{@insurance.service_number}.pdf\""
    insurance                               = InsuranceService.generate_easycare_pdf(@insurance)
    if insurance
      send_file insurance.path, filename: "#{@insurance.service_number}.pdf", type: "application/pdf"
    else
      respond_to do |format|
        format.html { redirect_to insurances_path }
      end
    end
  rescue StandardError
    respond_to do |format|
      format.html { redirect_to insurances_path }
    end
  end

  def autocomplete_warehouse_name
    term       = params[:term]
    dealer_id  = params[:dealer_id]
    warehouses = Warehouse.enabled.where("name LIKE ?", "%#{term}%")
    warehouses = warehouses.where(dealer_id: dealer_id) if dealer_id
    warehouses = warehouses.order(:name).select(%i[id name])
    warehouses = warehouses.map do |warehouse|
      {
        value: warehouse[:name],
        label: warehouse[:name],
        id:    warehouse[:id]
      }
    end
    render json: warehouses.to_json
  end

  def use_voucher
    existing_insurance_with_same_voucher = InsuranceService.insurances_for_role(current_user)
      .where(voucher_code: params[:insurance][:voucher_code]).first

    if existing_insurance_with_same_voucher
      @errors = existing_insurance_with_same_voucher.voucher_already_used_from
    elsif params[:insurance][:voucher_code].empty?
      @errors = Insurance::VOUCHER_MUST_BE_NOT_EMPTY
    else
      insurance              = Insurance.find(params[:insurance_id])
      insurance.voucher_code = params[:insurance][:voucher_code].upcase
      unless insurance.save
        @errors = insurance.errors.full_messages.join("<br/>")
      end
    end
    @error_id = params[:insurance_id]

    respond_to do |format|
      format.js
    end
  end

  private

  def insurance_params
    params.require(:insurance).except(:dealer, :warehouse).permit(
      :phone_activation_id,
      :dealer_id,
      :warehouse_id,
      :email,
      :mobile_phone,
      :origin,
      :phone_number,
      :product_brand,
      :product_invoice_number,
      :product_invoice_type,
      :product_invoiced_at,
      :product_model,
      :product_price,
      :product_serial,
      :product_type,
      :price,
      :dealer_price,
      :kolme_price,
      :agent_profit,
      :gamma_code,
      :sent,
      :service_number,
      :voucher_code,
      :customer_id,
      customer_attributes: %i[
        id
        address_street_name
        address_street_type_id
        birth_date
        cf
        cf_or_vat
        city_id
        city_ui
        company_name
        customer_kind_id
        email
        first_name
        gender
        headquarters_address_street_name
        headquarters_address_street_type_id
        headquarters_number
        headquarters_city_id
        headquarters_city_ui
        headquarters_zip
        last_name
        mobile_phone
        number
        origin
        vat
        zip
      ]
    )
  end

  def page_params
    {
      page:     params.fetch(:page, 1),
      per_page: 30
    }
  end
end
