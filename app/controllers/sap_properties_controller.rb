class SapPropertiesController < ApplicationController
  DEFAULT_SAP_PROPERTY = 'sap_internal_bank_institute'.freeze
  EXCLUDED_ATTRIBUTES  = %w(id created_at updated_at).freeze
  SAP_PROPERTIES       = %w(sap_internal_bank_institute sap_payment_kind sap_payment_term sap_withholding_tax).freeze

  before_action :authorize_user
  before_action :set_entity

  def authorize_user
    unless current_user.can?(:manage, SapInternalBankInstitute) &&
        current_user.can?(:manage, SapPaymentKind) &&
        current_user.can?(:manage, SapPaymentTerm) &&
        current_user.can?(:manage, SapWithholdingTax)

      raise CanCan::AccessDenied
    end
  end

  def index
    @entities          = @entity_class.paginate(page: params[:page], per_page: 20)
    @entity_attributes = sap_property_fields_for(@entity_name)
  end

  def create
    @entity = @entity_class.new(sap_property_params)

    if @entity.save
      redirect_to sap_properties_path(params[:entity_name])
    else
      render action: :new
    end
  end

  def edit
    @entity = @entity_class.find(params[:id])
  end

  def new
    @entity = @entity_class.new
  end

  def update
    @entity = @entity_class.find(params[:id])

    if @entity.update(sap_property_params)
      redirect_to sap_properties_path(params[:entity_name])
    else
      render action: :edit
    end
  end

  private

  def sap_property_fields_for(_entity_name)
    @entity_class.column_names.excluding(EXCLUDED_ATTRIBUTES)
  end

  def sap_property_params
    params.require(@entity_name).permit(Object.const_get(@entity_name.classify).column_names)
  end

  def set_entity
    @entity_name       = params[:entity_name] || DEFAULT_SAP_PROPERTY
    @entity_class      = Object.const_get(@entity_name.classify)
  end
end
