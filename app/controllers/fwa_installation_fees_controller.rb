class FwaInstallationFeesController < ApplicationController
  load_and_authorize_resource

  def index; end

  def update_all
    fwa_installation_fees_params[:fwa_installation_fees].each do |fwa_installation_fee_params|
      FwaInstallationFee.find(fwa_installation_fee_params[0]).update(fwa_installation_fee_params[1])
    end
  end

  private

  def fwa_installation_fees_params
    params.permit(
      fwa_installation_fees: {}
    )
  end
end
