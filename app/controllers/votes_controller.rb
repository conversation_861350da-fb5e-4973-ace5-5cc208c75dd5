class VotesController < InheritedResources::Base
  layout false, only: [:new]

  def new
    votable = extract_votable_from(params)
    @vote = Vote.new(votable: votable)
  end

  def create
    votable = extract_votable_from(params[:vote])
    params[:vote][:user_id] = current_user.id
    @vote = votable.build_vote(vote_params)
    respond_to do |format|
      if @vote.save
        format.html { gflash notice: "Voto registrato con successo"; redirect_to :phone_calls }
        format.json { render json: @vote }
      else
        format.html { gflash error: "Impossibile registrare il voto"; redirect_to :phone_calls }
        format.json { render json: @vote, status: :unprocessable_entity }
      end
    end
  end

  private

  def extract_votable_from(params)
    votable_id    = params.delete :votable_id
    votable_class = params.delete(:votable_type).singularize.classify.constantize
    votable_class.find(votable_id)
  end

  def vote_params
    params.require(:vote).permit(:user_id, :value)
  end
end
