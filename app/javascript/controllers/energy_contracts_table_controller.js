import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="energy-contracts-list"

export default class extends Controller {
  static targets = ["planRow", "downloadButton", "searchForm"]

  connect() {
    window.energyContractsTableController = this;
  }

  exportRecords() {
    const form = this.searchFormTarget;
    const url = new URL(form.action.replace('energy_contracts', 'energy_contracts/export')); // Modify the URL to target the export route

    // Collect form data and append to the URL as query parameters
    const formData = new FormData(form);
    formData.forEach((value, key) => {
      url.searchParams.append(key, value);
    });

    // Trigger the file download by navigating to the URL with form data as query parameters
    window.location.href = url.href;
  }

  convertToDate(dateString) {
    const [day, month, year] = dateString.split('/');
    return new Date(`${year}-${month}-${day}`);
  }

  toggle(event) {
    event.preventDefault()
    const button = event.currentTarget
    const contractId = button.dataset.contractId
    const parentRow = document.querySelector('#energy_contract-'+contractId);

    parentRow.classList.toggle('no-bottom-border')
    parentRow.classList.toggle('hidden')


    // Get all rows related to the contract
    const relatedRows = this.planRowTargets.filter(row => row.dataset.contractId === contractId)

    // Toggle the border and visibility for all related plan rows
    relatedRows.forEach((row, index) => {
      if (index < relatedRows.length - 1) {
        row.classList.toggle('no-bottom-border')
      }
      row.classList.toggle("hidden")
    })

    // Toggle the rotate class on the button
    //button.classList.toggle("rotate")
  }

  resetForm() {
    const form = document.getElementById('search-form');
    if (form) {
      // Iterate through each input field and set its value to an empty string
      form.querySelectorAll('input[type="text"], input[type="search"], input[type="date"]').forEach(input => {
        input.value = '';
      });

      // Clear other form elements like select fields if needed
      form.querySelectorAll('select').forEach(select => {
        select.selectedIndex = 0;
      });

      // Uncheck checkboxes and radio buttons if needed
      form.querySelectorAll('input[type="checkbox"], input[type="radio"]').forEach(input => {
        input.checked = false;
      });
    }
  }

  submitForm(event) {
    event.target.form.submit();
  }
}
