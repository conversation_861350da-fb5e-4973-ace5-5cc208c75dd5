import {Controller} from "@hotwired/stimulus";

const readOnlyMousedownHandler = (e) => {
  e.preventDefault();
  e.stopPropagation();
  return false;
}

const readOnlyKeydownHandler = (e) => {
  e.preventDefault();
  e.stopPropagation();
  return false;
}

export default class extends Controller {
    static values = {freshState: Boolean};
    static targets = ["destination", "powerUsed", "referenceMarket", "withdrawalClass",
        "estimatedAnnualWithdrawal", "tisgCategory", "useCategory", "addressFields",
        "checkboxAddress", "form", "submitButton", "customPowerUsed"];

    connect() {
        this.freshStateValue && this.toggleAddress() // Ensure the initial state is set correctly
        if (this.hasFormTarget) {
            this.formTarget.querySelectorAll("input, select, textarea").forEach(input => {
                input.addEventListener('change', this.checkInputs.bind(this))
                input.addEventListener('input', this.checkInputs.bind(this))
            })
        }

        $(".title_case").Setcase({caseValue: 'pascal', changeonFocusout: false})

        this.updateTisg()
    }

    checkInputs() {
        let allFilled = true
        this.formTarget.querySelectorAll("input, select, textarea").forEach(input => {
            if (!this.isHidden(input) && input.value.trim() === "") {
                allFilled = false
            }
        })
        this.submitButtonTarget.disabled = !allFilled
    }

    isHidden(el) {
        return (el.offsetParent === null)
    }

    toggleAddress() {
        const addressFields = this.addressFieldsTarget.querySelectorAll("input[type=text], input[type=number], input[type=hidden], select");

        if (this.checkboxAddressTarget.checked) {
            addressFields.forEach(input => {
                if (input.dataset.defaultValue !== null && input.dataset.defaultValue !== undefined) {
                    input.value = input.dataset.defaultValue;
                    if (input.tagName.toLowerCase() === 'select') {
                        this.makeSelectReadOnly(input);
                    }
                    input.setAttribute('readonly', true);
                  }
            });
        } else {
            addressFields.forEach(input => {
                input.value = '';
                if (input.tagName.toLowerCase() === 'select') {
                    this.makeSelectEditable(input);
                }
                input.removeAttribute('readonly');
              });
        }
    }

    updateReferenceMarket() {
        const powerUsed = this.powerUsedTarget.value;
        const energyContractId = this.powerUsedTarget.dataset.energycontractid;
        const searchParams = new URLSearchParams({
            power_used: powerUsed,
            custom_power_used: !this.customPowerUsedTarget.disabled
        }).toString();

        fetch(`/energy_contracts/${energyContractId}/energy_supply_details/update_reference_market?${searchParams}`)
            .then(response => response.json())
            .then(data => {
                this.clearAndAddBlankOption();

                const optionsMap = {};
                const otherOptions = [];
                const order = [
                    "Consumer Domestico",
                    "Consumer Altri Usi o Microimpresa <15kW",
                    "Piccole e Medie Imprese >15kW",
                    "Azienda"
                ];

                order.forEach(key => {
                    optionsMap[key] = null;
                });

                if (data.length > 0) {
                    data.forEach(option => {
                        const opt = document.createElement('option');
                        opt.text = this.getOptionText(option[0]);
                        opt.value = option[1];

                        if (order.includes(opt.text)) {
                            optionsMap[opt.text] = opt;
                        } else {
                            otherOptions.push(opt);
                        }
                    });

                    order.forEach(key => {
                        if (optionsMap[key]) {
                            this.referenceMarketTarget.add(optionsMap[key]);
                        }
                    });

                    otherOptions.forEach(opt => {
                        this.referenceMarketTarget.add(opt);
                    });
                }
            })
            .catch(error => console.error('Error fetching mercato di riferimento:', error));
    }

    getOptionText(optionText) {
        if (optionText === "Tutela graduale") {
            return "Piccole e Medie Imprese >15kW";
        } else if (optionText === "Salvaguardia") {
            return "Azienda";
        } else if (optionText === "Cliente Standard") {
            return "Consumer Domestico";
        } else if (optionText === "Tutela Microimpresa") {
            return "Consumer Altri Usi o Microimpresa <15kW";
        } else {
            return optionText;
        }
    }

    powerUsedHandler(event) {
        if (event.target.selectedOptions[0].dataset.customPowerUsed === 'true') {
            this.customPowerUsedTarget.disabled = false;
            this.customPowerUsedTarget.classList.remove('hidden');
        } else {
            this.customPowerUsedTarget.disabled = true;
            this.customPowerUsedTarget.classList.add('hidden')
        }

        this.updateReferenceMarket();
    }

    syncCustomPowerUsed(_) {
        clearTimeout(this.syncTimeout);

        this.syncTimeout = setTimeout(() => {
            if (this.customPowerUsedTarget.value) {
                this.powerUsedTarget.selectedOptions[0].value = this.customPowerUsedTarget.value;
            }
        }, 300);
    }

    clearAndAddBlankOption() {
        this.referenceMarketTarget.innerHTML = '';

        const blankOption = document.createElement('option');
        blankOption.text = 'Mercato di riferimento';
        blankOption.value = '';
        this.referenceMarketTarget.add(blankOption);
    }

    updateUseCategory(event) {
        const useСategory = encodeURIComponent(event.target.value);
        const energyContractId = event.target.dataset.energycontractid;

        fetch(`/energy_contracts/${energyContractId}/energy_supply_details/update_use_category?use_category=${useСategory}`)
            .then(response => response.json())
            .then(data => {
                this.useCategoryTarget.value = data.use_category;
                //this.useCategoryTarget.closest('.input').classList.remove("d-none");

                this.estimatedAnnualWithdrawalTarget.value = data.additional_field;
                this.estimatedAnnualWithdrawalTarget.dispatchEvent(new Event('change'));
                //this.estimatedAnnualWithdrawalTarget.closest('.input').classList.remove("d-none");
            })
            .catch(error => console.error('Error fetching mercato di riferimento:', error));
    }

    updateTisg() {
        if (this.tisgCategoryTarget.value == "uso tecnologico") {
            this.removeCOptions();
            this.useCategoryTarget.closest('.input').classList.remove("d-none");

            this.destinationTarget.closest('.input').classList.add("d-none");
            this.destinationTarget.value = '';

            this.estimatedAnnualWithdrawalTarget.value = '5000';
            this.estimatedAnnualWithdrawalTarget.dispatchEvent(new Event('change'));

            this.withdrawalClassTarget.closest('.input').classList.remove('d-none');
            const options = Array.from(this.withdrawalClassTarget.options);
            const classe3Option = options.find(option => option.text.startsWith('Classe 3'));
            if (classe3Option) {
                this.withdrawalClassTarget.value = classe3Option.value;
            } else {
                this.withdrawalClassTarget.value = '';
            }
        } else {
            this.addCOptions();
            this.useCategoryTarget.closest('.input').classList.add("d-none");
            this.useCategoryTarget.value = '';

            this.withdrawalClassTarget.closest('.input').classList.add('d-none');
            this.withdrawalClassTarget.value = 'Classe 1 (7gg)';

            this.destinationTarget.closest('.input').classList.remove("d-none");

            this.estimatedAnnualWithdrawalTarget.closest('.input').classList.remove("d-none");
            this.estimatedAnnualWithdrawalTarget.value = '';
            this.estimatedAnnualWithdrawalTarget.dispatchEvent(new Event('change'));
        }
    }

    addCOptions() {
        if (this.useCategoryTarget.tagName != 'SELECT') {
            return;
        }

        ['c1', 'c2', 'c3'].forEach(value => {
            let option = document.createElement("option");
            option.value = value;
            option.text = value;

            this.useCategoryTarget.add(option);
        });
    }

    removeCOptions() {
        if (this.useCategoryTarget.tagName != 'SELECT') {
            return;
        }

        ['c1', 'c2', 'c3'].forEach(value => {
            const option = document.querySelector(`#energy_supply_detail_use_category option[value="${value}"]`);
            if (option) {
                option.remove();
            }
        });
    }

    makeSelectReadOnly(select) {
      select.addEventListener('mousedown', readOnlyMousedownHandler);

      select.addEventListener('keydown', readOnlyKeydownHandler);
    }

    makeSelectEditable(select) {
      select.removeEventListener('mousedown', readOnlyMousedownHandler);
      select.removeEventListener('keydown', readOnlyKeydownHandler);
    }
}
