import {Controller} from "@hotwired/stimulus"

export default class extends Controller {
  get coverageCheckFormController() {
    return this.application.getControllerForElementAndIdentifier(
      this.element.closest('[data-controller="coverage-check"]'), 'coverage-check'
    )
  }

  connect() {
    window.dispatchEvent(new CustomEvent("dismissWaitingModal"));

    this.coverageCheckFormController.selectFirstAddress();
  }

  close() {
    this.element.classList.remove('show');
  }
}
