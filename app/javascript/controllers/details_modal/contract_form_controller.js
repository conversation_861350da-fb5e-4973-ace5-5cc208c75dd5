import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  connect() {
    this.element.querySelector('.field_with_errors')?.scrollIntoView();
    this.spinner = new Spinner();

    this.element.addEventListener("turbo:submit-start", this.showSpinner.bind(this));
    this.element.addEventListener("turbo:submit-end", this.hideSpinner.bind(this));
  }

  showSpinner() {
    this.spinner.spin(this.element);
  }

  hideSpinner() {
    this.spinner.stop();
  }
}
