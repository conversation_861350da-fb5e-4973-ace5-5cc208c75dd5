import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["submitButton"];

  emailRegExp = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

  emailValidate(event) {
    const inputElement = event.target;

    clearTimeout(this.validationTimeout);

    this.validationTimeout = setTimeout(() => {
      const isValid = this.emailRegExp.test(inputElement.value);
      this._updateInputState(inputElement, isValid, "Inserire un'email valida");
    }, 500);
  }

  _updateInputState(inputElement, isValid, errorMessage) {
    if (isValid) {
      this._clearError(inputElement);
    } else {
      this._showError(inputElement, errorMessage);
    }

    this._toggleSubmit(isValid);
  }

  _clearError(inputElement) {
    inputElement.classList.remove("invalid");

    const sibling = this._getMessageElement(inputElement);
    if (sibling) sibling.innerHTML = "";
  }

  _showError(inputElement, message) {
    inputElement.classList.add("invalid");

    let messageElement = this._getMessageElement(inputElement);

    if (!messageElement) {
      messageElement = this._createMessageElement();
      inputElement.insertAdjacentElement("afterend", messageElement);
    }

    messageElement.innerHTML = message;
  }

  _getMessageElement(inputElement) {
    const sibling = inputElement.nextElementSibling;
    return sibling && sibling.tagName === "P" ? sibling : null;
  }

  _createMessageElement() {
    const messageElement = document.createElement("p");

    messageElement.classList.add("error-message")
    return messageElement;
  }

  _toggleSubmit(isValid){
    this.submitButtonTarget.disabled = !isValid;
  }
}
