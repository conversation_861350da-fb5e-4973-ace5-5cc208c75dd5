class PurchaseOrderMailer < ActionMailer::Base
  helper(ApplicationHelper)

  default from: MailAddressProvider.from
  default to: MailAddressProvider.prodotti
  default cc: MailAddressProvider.it
  layout "system_email"

  def import_result(purchase_importer_response, file_path)
    @response = purchase_importer_response
    @filename = File.basename(file_path)
    attachments[@filename] = File.read(file_path)

    subject = purchase_importer_response.mail_subject

    mail(subject: subject)
  end

  def cma_result(unmatched_rows, errors, file_path)
    @unmatched_rows = unmatched_rows
    @errors = errors
    @filename = File.basename(file_path)
    attachments[@filename] = File.read(file_path)

    subject = I18n.t('mail.purchase_order.subject.cma_errors')

    mail(subject: subject)
  end
end
