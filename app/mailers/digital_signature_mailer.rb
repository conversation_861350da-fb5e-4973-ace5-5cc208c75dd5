class DigitalSignatureMailer < ActionMailer::Base
  helper(ApplicationHelper)

  default from: MailAddressProvider.from
  default to: MailAddressProvider.it

  layout "system_email"

  def send_download_error(exception, model, document_id, document_kind)
    @message          = exception.message
    @backtrace        = exception.backtrace
    @document_kind    = document_kind
    @document_kind_id = document_id
    @model            = model

    mail(subject: "Errore download documenti firma digitale: #{document_kind} #{model.namirial_id}")
  end

  def upload_documents_report(messages)
    @messages = messages
    mail(subject: "Report caricamento documenti contabili")
  end
end