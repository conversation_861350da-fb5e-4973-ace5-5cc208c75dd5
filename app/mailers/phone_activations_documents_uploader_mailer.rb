class PhoneActivationsDocumentsUploaderMailer < ActionMailer::Base
  helper(ApplicationHelper)

  default from: MailAddressProvider.from
  default to: MailAddressProvider.documentale
  default cc: MailAddressProvider.it
  layout "system_email"

  def notify_upload
  end

  def notify_upload_error(errors, phone_activation)
    @errors                        = errors
    @hex_id                        = phone_activation.hex_id
    @phone_activation_rendered_url = [server_url_for_env, "phone_activations", phone_activation.id].join("/")
    mail(subject: "Errore di upload documenti Attivazione #{@hex_id}")
  end

  def server_url_for_env
    Settings.application.full_secure_url
  end
end
