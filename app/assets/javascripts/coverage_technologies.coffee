class CoverageTechnologies
  constructor: () ->
    @$switchSelector = $('#switch-not-visible')
    @init()
    @bind()

  init: () =>
    @$switchSelector.bootstrapSwitch('state', @$switchSelector.is(':checked') )

  bind: () =>
    @$switchSelector.on 'switchChange.bootstrapSwitch', () =>
      @updateList()

  updateList: () =>
    location.href = "/coverage_technologies?show_not_visible=#{@$switchSelector.is(':checked')}"

$ ->
  if $('.coverage_technologies_scope').length > 0 && $('.switch-container').length > 0
    new CoverageTechnologies()
