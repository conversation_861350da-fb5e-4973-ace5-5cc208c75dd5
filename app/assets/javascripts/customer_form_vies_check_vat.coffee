class CustomerFormViesCheckVat
  constructor: (check_valid_vat) ->
    @form = $('.customer-form')
    @vat_input = $("#customer_vat")
    @check_valid_vat = check_valid_vat
    @vat_check_message = $("#vat_check_message")

    @bind()
    if ($('#check_vat_number').val() == 'true')
      @vat_input.trigger('change');

  bind: () =>
    @vat_input.on 'change', () =>
      value = @vat_input.val()

      @form.find(':submit').removeData('confirm')
      return if (!value || value.length < 11)
      return if !@check_valid_vat && $('#customer_headquarters_address_street_name').is(":hidden")

      @vat_check_message.removeClass('text-danger')
      @vat_check_message.html('Ricerca...')
      @setDisabledFields(true)
      url = "/customers/check_vat_number?vat_number=" + value
      $.getJSON(url, (response) =>
        if @check_valid_vat && response.message
          @vat_check_message.addClass('text-danger')
          @vat_check_message.html(response.message)
        else
          @vat_check_message.html('')
        return if (response.service_status == 'ko')

        if response.valid_vat
          $("#customer_company_name").val(response.company_name)
          @setAddressFields(response)
        else
          if @check_valid_vat
            @form.find(':submit').data('confirm', 'La verifica della Partita IVA non è andata a buon fine.\n\nVuoi proseguire comunque?')
      ).always =>
        @setDisabledFields(false)
        if !(@check_valid_vat && response.message)
          @vat_check_message.html('')

  setAddressFields: (response) =>
    $("#customer_headquarters_address_street_type_id").val(response.street_type_id)
    $("#customer_headquarters_address_street_name").val(response.street_name)
    $("#customer_headquarters_number").val(response.number)
    $("#customer_headquarters_city_ui").val(response.city_ui)
    $("#customer_headquarters_city_id").val(response.city_id)
    $("#customer_headquarters_zip").val(response.zip)

  setDisabledFields: (disabled) =>
    $("#customer_headquarters_address_street_type_id").prop("disabled", disabled)
    $("#customer_headquarters_address_street_name").prop("disabled", disabled)
    $("#customer_headquarters_number").prop("disabled", disabled)
    $("#customer_headquarters_city_ui").prop("disabled", disabled)
    $("#customer_headquarters_city_id").prop("disabled", disabled)
    $("#customer_headquarters_zip").prop("disabled", disabled)
$ ->
  if($('.customer-form').length > 0)
    new CustomerFormViesCheckVat(false)