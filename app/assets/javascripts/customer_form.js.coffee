class Residence
  TURISTA: "4"

  constructor: ->
    @container = $('.residence-fieldset')
    @title = $('.residence-title')
    @bind()
    @setTitle($('#customer_customer_kind_id'))

  bind: () =>
    $('#customer_customer_kind_id').on "change", (element) =>
      @setTitle($(element.target))

  setTitle: (target) =>
    if(target.val() == @TURISTA)
      @title.html($(@container).data('label-tu'))
    else
      @title.html($(@container).data('label'))

class CustomerForm
  constructor: () ->
    @form = $('.customer-form')
    @$marketingCommunicationsBox = @form.find('.marketing-communications')
    new Residence()
    @toggleAlertMessage()
    @bind()

  bind: () =>
    @$marketingCommunicationsBox.find('input:radio').bind 'change', () =>
      @toggleAlertMessage()

    $("#customer_birth_country_id").on 'change', () =>
      if $("#customer_birth_country_id").val() == "1"
        $("#customer_nationality").val("Italiana")
      else
        $("#customer_nationality").val("")

    $('.customer_cf_field_with_activation').on 'focusout', =>
      $("#save-customer-from-app-button").removeClass("hidden")
    
    @form.on "submit", () =>
      id = @form.data('customerId')

      if id
        url = "/customers/#{id}/check_for_preexisting_customer"
        $.getJSON(url, @form.serialize())
          .done(
            (response) =>
              if response['customer_is_preexisting']
                return unless confirm("Stai modificando un'anagrafica già presente su Spazio Kolme, confermi la correttezza dei dati inseriti?")
              
              @form.off('submit')
              @form.submit()
          )
        
        return false

  toggleAlertMessage: () =>
    if @$marketingCommunicationsBox.find('input:radio:checked').length < 6
      @$marketingCommunicationsBox.find('.alert').show()
    else
      @$marketingCommunicationsBox.find('.alert').hide()

$ ->
  if($('.customer-form').length > 0)
    new CustomerForm()
    window.App.draftPeopleGuard = new DraftPeopleGuard($('.customer-form'))
