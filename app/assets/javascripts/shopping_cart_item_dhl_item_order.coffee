class ShoppingCartItemDHLRow
  constructor: (shopping_cart_item_row) ->
    @id = shopping_cart_item_row.id
    @assignButton = $(shopping_cart_item_row).find(".assign-btn")
    @removeButton = $(shopping_cart_item_row).find(".remove-btn")
    @amountInExternalWarehouse = $(shopping_cart_item_row).find(".amount-in-external-warehouse")
    @unfulfilledItems = $(shopping_cart_item_row).find(".unfulfilled-items")
    @quantitySpan = $(shopping_cart_item_row).find(".fulfillable-quantity")

    @bind()

  bind: () =>
    @assignButton.on "click", () =>
      @assignButton.spin()

    @removeButton.on "click", () =>
      @removeButton.spin()

  removeButtonPresent: () =>
    @removeButton.is(':visible')

  updateButtonsVisibility: () =>
    @assignButton.spin(false)
    @removeButton.spin(false)

    if @unfulfilledItems.html() > 0
      @assignButton.hide()
      @removeButton.show()
    else
      @assignButton.show()
      @removeButton.hide()

  updateQuantitySpanBackground: () =>
    if @quantitySpan.html() <= @amountInExternalWarehouse.html()
      @quantitySpan.addClass("green")
      @quantitySpan.removeClass("red")
      @quantitySpan.removeClass("yellow")
    else if @quantitySpan.html() > 0 && @amountInExternalWarehouse.html() == "0"
      @quantitySpan.addClass("red")
      @quantitySpan.removeClass("green")
      @quantitySpan.removeClass("yellow")
    else if @quantitySpan.html() > @amountInExternalWarehouse.html()
      @quantitySpan.addClass("yellow")
      @quantitySpan.removeClass("red")
      @quantitySpan.removeClass("green")

  updateRow: (shopping_cart_item_result) =>
    shopping_cart_item = shopping_cart_item_result.shopping_cart_item

    @quantitySpan.html(shopping_cart_item.unfulfilled_quantity)
    @amountInExternalWarehouse.html(shopping_cart_item.stock_in_dhl_warehouse)
    @unfulfilledItems.html(shopping_cart_item.dhl_unsent_item_orders_count)

    @updateQuantitySpanBackground()

    @updateButtonsVisibility()

class ShoppingCartItemDHLFulfillPage
  constructor: () ->
    @assignButton = $('.assign-btn')
    @removeButton = $('.remove-btn')
    @completeAllButton = $('.complete-all-btn')
    @deleteAllButton = $('.delete-all-btn')
    @sendButton = $('.send-to-dhl-btn')
    @shoppingCartItemRows = []

    @init()
    @bind()

  bind: () =>
    @completeAllButton.on "click", () =>
      $('.spinnered').spin()
      @completeAllButton.addClass("disabled")

    @deleteAllButton.on "click", () =>
      $('.spinnered').spin()

  init: () =>
    for shopping_cart_item in $('.shopping_cart_item')
      @shoppingCartItemRows.push(new ShoppingCartItemDHLRow(shopping_cart_item))

  deleteAllRows: (shopping_cart_item_rows) =>
    $('.spinnered').spin(false)
    for shopping_cart_item_row in shopping_cart_item_rows
      @updateShoppingCartItemRow(shopping_cart_item_row.shopping_cart_item.id, shopping_cart_item_row)

    @updateButtonsVisibility()

  updateAllRows: (shopping_cart_item_rows) =>
    $('.spinnered').spin(false)
    for shopping_cart_item_row in shopping_cart_item_rows
      @updateShoppingCartItemRow(shopping_cart_item_row.shopping_cart_item.id, shopping_cart_item_row)

    @updateButtonsVisibility()

  updateButtonsVisibility: () =>
    @sendButton.addClass("disabled")
    @completeAllButton.addClass("disabled")
    @deleteAllButton.addClass("disabled")

    for row in @shoppingCartItemRows
      if row.removeButtonPresent()
        @sendButton.removeClass("disabled")
        @deleteAllButton.removeClass("disabled")
        break

    for row in @shoppingCartItemRows
      if not row.removeButtonPresent()
        @completeAllButton.removeClass("disabled")
        break

  updateShoppingCartItemRow: (shopping_cart_item_id, shopping_cart_item_result) =>
    for row in @shoppingCartItemRows
      if row.id == "#{shopping_cart_item_id}"
        row.updateRow(shopping_cart_item_result)

    @updateButtonsVisibility()

$ ->
  if $('.shopping_cart_item').length > 0
    window.App.ShoppingCartItemDHLFulfillPage = new ShoppingCartItemDHLFulfillPage()

