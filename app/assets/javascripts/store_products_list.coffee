class StoreProductsList
  constructor: (@store) ->
    @$storeProductsContainer = $("#store-products-container")
    @store.addListener(@)
    return unless @$storeProductsContainer?
    @$publicPrices = $('.public-price')
    @$dealerPrices = $('.dealer-prices')
    @init()

  init: () =>
    @$publicPrices.hide()
    @$dealerPrices.hide()
    @initFancybox()
    @disableButtons()
  
  initFancybox: () =>
    $('.lb').fancybox(
      {
        centerOnScroll: true,
        scrolling: 'no'
      }
    )

  disableButtons: () =>
    $('.disabled-with-tooltip').prop('disabled', true)
    $('.disabled-with-tooltip').attr('disabled', true)
    $('.disabled-with-tooltip').closest('#add-to-cart-button-popover').popover({
      trigger: 'hover',
      placement: (context, source) ->
        el = $(source)
        marginRight = $(window).width() - (el.offset().left + el.width())
        if (marginRight < 200)
          return 'left'
        else
          return 'right'
    })

  initCard: (card) ->
    publicPrice = card.find('.public-price')
    dealerPrices = card.find('.dealer-prices')
    publicPrice.hide()
    dealerPrices.hide()


  notify: (message) =>
    @$publicPrices = $('.public-price')
    @$dealerPrices = $('.dealer-prices')
    
    if message['show-public-price']?
      @$publicPrices.show('slow') & @$dealerPrices.hide() if message['show-public-price'] == true
      @$dealerPrices.show('slow') & @$publicPrices.hide() if message['show-public-price'] == false
    else
      messageKey = Object.keys(message)[0]
      messageKeyComponents = messageKey.split(':')
      if messageKeyComponents[0] == "show-public-price"
        notifiedCard = @$storeProductsContainer.find(".product-element[id='#{messageKeyComponents[1]}']")
        notifiedPublicPrice = $(notifiedCard).find('.public-price')
        notifiedDealerPrices = $(notifiedCard).find('.dealer-prices')

        notifiedPublicPrice.show() & notifiedDealerPrices.hide() if message[messageKey] == true
        notifiedDealerPrices.show() & notifiedPublicPrice.hide() if message[messageKey] == false

class PriceSwitch
  constructor: (@store) ->
    @$switchSelector = $('#switch-price')
    @init()
    @bind()

  init: () =>
    @$switchSelector.bootstrapSwitch('state', @$switchSelector.is(':checked') )
    @store.setValue('show-public-price', @$switchSelector.is(':checked'))
  
  initCard: (card) =>
    cardId = card.attr("id")
    @$switchSelector.bootstrapSwitch()
    @store.setValue("show-public-price:#{cardId}", @$switchSelector.is(':checked'))

  bind: () =>
    @$switchSelector.on 'switchChange.bootstrapSwitch', () =>
      @store.setValue('show-public-price', @$switchSelector.is(':checked'))
      @updatePublicPriceVisibility()

  updatePublicPriceVisibility: () =>
    $.ajax(
      url:"/store/products/update_visibility/show_public_price"
      type: 'POST',
      dataType: 'script',
      data: { 'value': @$switchSelector.is(':checked')}
      success: () ->
      error: () ->
    )

class NotAvailableSwitch
  constructor: () ->
    @$switchSelector = $('#switch-not-available')
    @init()
    @bind()

  init: () =>
    @$switchSelector.bootstrapSwitch('state', @$switchSelector.is(':checked') )

  bind: () =>
    @$switchSelector.on 'switchChange.bootstrapSwitch', () =>
      @updateProductsVisibility()

  updateProductsVisibility: () =>
    $.ajax(
      url:"/store/products/update_visibility/not_available"
      type: 'POST',
      dataType: 'script',
      data: { 'value': @$switchSelector.is(':checked')}
      success: () ->
        url = new URL($(location).attr("href"))
        params = new URLSearchParams(url.search)
        params.delete("not_available")

        location.href="#{url.pathname}?#{params.toString()}"
      error: () ->
    )

$ ->
  if $("#store-products-container").length > 0
    window.App.StoreProductsStore = new GenericStore()
    window.App.StoreProductsList = new StoreProductsList(window.App.StoreProductsStore)
    window.App.StoreProductsPriceSwitch = new PriceSwitch(window.App.StoreProductsStore)
    new NotAvailableSwitch()