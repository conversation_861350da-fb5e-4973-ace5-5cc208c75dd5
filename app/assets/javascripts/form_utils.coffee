# when clicking on an autoCOMPLETING field, the helper's <PERSON><PERSON> sets the 'autocomplete'
# attribute to 'off', that does not stop Chrome from autoFILL up its stuff.
# To really stop it, the usual nonexisting 'nope' value is afterwards set instead.
setAutocompleteAttributesToNope = () ->
  $("[data-autocomplete]").each(
      () ->
        $(this).on(
          "click",
          () -> $(this).attr("autocomplete", "nope")
        )
    )

$ ->
  if $("form").length > 0
    setAutocompleteAttributesToNope()
