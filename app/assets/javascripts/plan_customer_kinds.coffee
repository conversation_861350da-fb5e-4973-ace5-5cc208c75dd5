class PlanCustomerKinds
  constructor: () ->
    @clientePrivato = $(".customer_kind_1").find('input[type=checkbox]')
    @dittaIndividuale = $(".customer_kind_2").find('input[type=checkbox]')
    @personaGiuridica = $(".customer_kind_3").find('input[type=checkbox]')
    @turista = $(".customer_kind_4").find('input[type=checkbox]')
    @privateKindCheckboxes = [@clientePrivato, @turista]
    @businessKindCheckboxes = [@dittaIndividuale, @personaGiuridica]
    @allCustomerKindCheckboxes = [@clientePrivato, @dittaIndividuale, @personaGiuridica, @turista]
    @init()

  init: () =>
    @handleCheckboxes()
    @bind()

  bind: () =>
     for checkbox in @allCustomerKindCheckboxes
        checkbox.on 'change', =>
          @handleCheckboxes()

  handleCheckboxes: () =>
    if (@privateKindCheckboxes.some (checkbox) -> checkbox.is(":checked"))
      for checkbox in @businessKindCheckboxes
        checkbox.prop("disabled", true)
    
    if (@businessKindCheckboxes.some (checkbox) -> checkbox.is(":checked"))
      for checkbox in @privateKindCheckboxes
        checkbox.prop("disabled", true)

    if (@allCustomerKindCheckboxes.every (checkbox) -> !checkbox.is(":checked"))
      for checkbox in @allCustomerKindCheckboxes
        checkbox.prop("disabled", false)

    if (@privateKindCheckboxes.some (checkbox) -> checkbox.is(":checked")) && (@businessKindCheckboxes.some (checkbox) -> checkbox.is(":checked"))
      for checkbox in @allCustomerKindCheckboxes
        checkbox.prop("disabled", false)
        checkbox.prop( "checked", false)


$ ->
  if $(".plan_customer_kinds_box").length > 0
    window.App.PlanCustomerKinds = new PlanCustomerKinds()