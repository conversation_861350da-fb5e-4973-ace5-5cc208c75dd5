class PlanOperatorCheckboxContainer
  constructor: (checkboxContainer) ->
    @checkboxContainer = $(checkboxContainer)
    @checkbox = @checkboxContainer.find('input[type=checkbox]')

  reset: () =>
    @checkbox.prop('checked', false)

  toggleVisibility: (className) =>
    for container in @checkboxContainer
      if container.classList.contains(className)
        $(container).show()
      else
        $(container).hide()

class PlanPortabilityOperators
  constructor: () ->
    @portabilityCheckbox = $("#plan_has_specific_np_operators")
    @portabilityOperatorsSection = $(".plan-portability-operators-section")
    @url = $("#plan_service_kind_id").attr("url")
    @requestMnpVal = $("#plan_request_mnp").is(":checked")
    @checkboxContainerHandler = new PlanOperatorCheckboxContainer($(".portability-checkbox-container"))
    @init()

  init: () =>
    @handleOperatorsVisibility()

  bind: () =>
    $(".reload-np-operators").on 'change', =>
      @checkboxContainerHandler.reset()
      @hideShowOperators()

    @portabilityCheckbox.on 'click', =>
      @checkboxContainerHandler.reset()
      @handleOperatorsVisibility()

  handleOperatorsVisibility: () =>
    mandatoryMnpVal = $("#plan_mandatory_mnp").val()
    
    if @portabilityCheckbox.is(":checked")
      @portabilityOperatorsSection.show()
      $("#plan_request_mnp").prop('checked', true)
      $('#mandatory_mnp_container').show()
      $("#plan_mandatory_mnp").prop('checked', true)
    else
      @portabilityOperatorsSection.hide() 
      if @requestMnpVal == true
        $("#plan_request_mnp").prop('checked', true)
        $('#mandatory_mnp_container').show()
        $("#plan_mandatory_mnp").val(mandatoryMnpVal)
      else
        $("#plan_request_mnp").prop('checked', false)
        $('#mandatory_mnp_container').hide()
        $("#plan_mandatory_mnp").prop('checked', false)

    @hideShowOperators()
    @bind()

  hideShowOperators: () =>
    if $("#plan_service_kind_id").val() == "1" 
      serviceKind = "fisso" 
    else
      serviceKind = "mobile"

    @checkboxContainerHandler.toggleVisibility(serviceKind)


$ ->
  if $(".reload-np-operators").length > 0
    window.App.PortabilityOperators = new PlanPortabilityOperators()