class FwaInstallersIndex
  constructor: () ->
    @searchCityInput = $("#search_city")
    @searchProvinceInput = $("#search_province")
    @init()

  init: () =>
    @searchProvinceInput.bind 'railsAutocomplete.select', (event, data) =>
      @resetCityInput()

    if $(".rating-option[data-selected=true]").length > 0
      @setQualityDropdown($(".rating-option[data-selected=true]"))

    $(".rating-option").on "click", (event) =>
      event.preventDefault()
      @setQualityDropdown($(event.target).closest('.rating-option'))

  resetCityInput: () =>
    @searchCityInput.val("")
    $(@searchCityInput.data('id-element')).val("")

    if @searchCityInput.autocomplete( "instance" )
      @searchCityInput.autocomplete( "search", "" )

  setQualityDropdown: (seectedOption) =>
    ratingValue = $(seectedOption).data("value")
    if ratingValue != ''
      ratingStars = $(seectedOption).find(".stars").text()
      $("#selectedRating").html("<span class='stars'>#{ratingStars}</span>" + ' e più')
      $("#search_dealer_fwa_installation_rating_gteq").val(ratingValue)
      $("#selectedRating")[0].classList.add('selected')
    else
      $("#selectedRating").html("Qualità")
      $("#search_dealer_fwa_installation_rating_gteq").val('')
      $("#selectedRating")[0].classList.remove('selected')

$ ->
  if $('.fwa-installers').length > 0
    window.App.FwaInstallersIndex = new FwaInstallersIndex()
