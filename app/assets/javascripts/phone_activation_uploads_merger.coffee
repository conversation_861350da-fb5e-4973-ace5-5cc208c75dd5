class @PhoneActivationUploadsMergerContainer
	constructor: ->
		@init()

	init: =>
		containers = $('.uploaded-files-container')
		for container in containers
			new PhoneActivationUploadsMerger($(container))

class PhoneActivationUploadsMerger

	constructor: (documentBlock) ->
		@documentBlock = documentBlock
		@context = documentBlock.attr("data-context")
		@phoneActivationId = documentBlock.attr("data-phone-activation-id")
		@mergeDocumentsPath = documentBlock.attr("data-merge-documents-path") 

		@init()

	init: =>
		@checkouts = @documentBlock.find(".merge_checkout")
		@mergeButton = @documentBlock.find("#merge_button")
		@spinner = @documentBlock.find("#create_pdf_button_spinner_#{@context}_#{@phoneActivationId}")

		@bindCheckouts()
		@bindMergeButton()

	bindCheckouts: =>
		for checkout in @checkouts
			@bindCheckout($(checkout))

	bindCheckout: (checkout) =>
		checkout.off 'click'
		checkout.on 'click', (event) =>
			event.preventDefault()
			@toggleCheckout($(event.target).closest("a"))
			@manageMergeButtonVisibility()
			return false

	toggleCheckout: (checkout) =>
		if checkout.attr('data-value') is "1"
			checkout.html(checkout.html().replace("-check-square", "-square"))
			checkout.attr('data-value', "0")
		else
			checkout.html(checkout.html().replace("-square", "-check-square"))
			checkout.attr('data-value', "1")

	manageMergeButtonVisibility: =>
		if @checkouts.parent().find('[data-value="1"]').length > 1
			@mergeButton.show()
			@spinner.hide()
		else
			@mergeButton.hide()

	bindMergeButton: =>
		@mergeButton.off 'click'
		@mergeButton.on 'click', (event) =>
			event.preventDefault()

			@mergeButton.find("a").hide()
			@spinner.show()
			documentIds = @checkouts.parent().find('[data-value="1"]')
				.get().map (documentButton) => documentButton.id

			$.ajax
				url: @mergeDocumentsPath,
				type: "GET",
				data: "document_ids=#{documentIds}",
				dataType: "script"

			return false

$ ->
	if $('.uploaded-files-container').length > 0
		window.App.PhoneActivationUploadsMergerContainer = new PhoneActivationUploadsMergerContainer()