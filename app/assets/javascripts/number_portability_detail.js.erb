var NumberPortabilityDetail = (function(){

    var listen_do_number_portability_change = function(){
        $('#phone_activation_do_number_portability').change(function(){
            hide_show_details($(this).val());
        })
    }

    var listen_different_associated_numbers_clicks = function(){
      $('#has_associated_numbers').click(function(){
            hide_show_section(this, 'additional_associated_numbers');
            check_and_empty_fields(this, 'additional_associated_numbers');
        })
    }

    var listen_different_location_clicks = function(){
        $('#choose_different_location').click(function(){
            hide_show_section(this, 'different_location_details');
        })
    }
    var listen_sim_lost_or_stolen_change = function(){
        $('#number_portability_detail_sim_lost_or_stolen').click(function(){
          $('#sim_serial_field').toggle(!$(this).prop('checked'));
        })
    }

    var hide_show_section = function(check, divid){
        if ($(check).prop('checked')) {
            $('#'+divid).show();
        } else {
            $('#'+divid).hide();
        }
    }

    var hide_show_details = function(value) {
        if (value!='Y') {
            $('#do_number_portability_yes').hide();
            if(value=='N'){
              window.App.NumberPortabilityDetailForm.reset()
            }
        } else {
            $('#do_number_portability_yes').show();
        }
        if (value!='N') {
            $('#do_number_portability_no').hide();
        } else {
            $('#do_number_portability_no').show();
        }
    }

    var check_and_empty_fields = function(check, divid){
      if (!($(check).prop('checked'))) {
          $('#'+divid).find('input:text').val("");
      }
    }

    return {
        listen_different_associated_numbers_clicks: listen_different_associated_numbers_clicks,
        listen_different_location_clicks: listen_different_location_clicks,
        listen_do_number_portability_change: listen_do_number_portability_change,
        hide_show_details: hide_show_details,
        hide_show_section: hide_show_section,
        setup: function(){
            listen_different_associated_numbers_clicks();
            listen_sim_lost_or_stolen_change();
            hide_show_section($('#has_associated_numbers'), 'additional_associated_numbers');
            if ($('#choose_different_location')) {
                listen_different_location_clicks();
                hide_show_section($('#choose_different_location'), 'different_location_details');
            }
            listen_do_number_portability_change();
            hide_show_details($('#phone_activation_do_number_portability').val());
        }
    }
})()