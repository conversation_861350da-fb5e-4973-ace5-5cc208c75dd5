class PaymentRequestableTypeHiddenField
  constructor: (@selector, @store) ->
    @init()
    @store.addListener(@)

  init: () =>
    $(@selector).hide()

  reset: () =>
    $(@selector).val('')

  notify: (message) =>
    if message['payment-request-kind'] == 'phone_activation'
      $(@selector).val('PhoneActivation')
    else if message['payment-request-kind'] == 'financing'
      $(@selector).val('Financing')
    else
      @reset()

class PaymentRequestableIdField
  constructor: (@selector, @store) ->
    @init()
    @store.addListener(@)

  init: () =>
    $(@selector).hide()

  reset: () =>
    $(@selector).find('input').val('')

  notify: (message) =>
    if _.contains(['phone_activation', 'financing'], message['payment-request-kind'])
      $(@selector).show()
    else
      @reset()
      $(@selector).hide()

class ResettableField
  constructor: (@selector) ->

  reset: () =>
    $(@selector).val("")

class NotesTextArea
  constructor: (@selector, @store) ->
    @init()
    @store.addListener(@)

  init: () =>
    $(@selector).hide()

  reset: () =>
    $(@selector).find('textarea').val('')

  notify: (message) =>
    if _.contains(['generic', 'easycare_plus'], message['payment-request-kind'])
      $(@selector).show()
    else
      @reset()
      $(@selector).hide()

class PaymentRequestKindSelect
  constructor: (@selector, @store) ->
    @bindEvents()

  bindEvents: () =>
    $(@selector).bind 'change', (event, _ui) =>
      paymentRequestKind = event.target.value
      @store.setValue('payment-request-kind', paymentRequestKind)

  reset: () =>
    $(@selector).val("")
    @store.setValue('payment-request-kind', "")


class CreatePaymentRequestModal
  constructor: (@selector, store) ->
    @paymentRequestableTypeHiddenField = new PaymentRequestableTypeHiddenField('#payment_request_payment_requestable_type', store)
    @paymentRequestableIdField         = new PaymentRequestableIdField('.payment_request_payment_requestable_id', store)
    @notesTextArea                     = new NotesTextArea('.payment_request_notes', store)
    @paymentRequestKindSelect          = new PaymentRequestKindSelect('#payment_request_payment_request_kind', store)
    @dealerField                       = new ResettableField('#payment_request_dealer')
    @dealerIdField                     = new ResettableField('#payment_request_dealer_id')
    @amountField                       = new ResettableField('#payment_request_amount')
    @validate()
    @bind()
    @reset()

  bind: () =>
    $('#new_payment_request').submit =>
      if($('#new_payment_request').valid())
        @disableSubmit()

  disableSubmit: () =>
    $('#payment-request-submit').attr('disabled', true)
    $('#payment-request-submit').val('Attendi ...')

  enableSubmit: () =>
    $('#payment-request-submit').attr('disabled', false)
    $('#payment-request-submit').val('Crea richiesta di pagamento')

  showErrors: (errors) =>
    errors_pane = $(@selector).find('.errors')
    errors_pane.html('')
    errors_pane.html('<h4>Attenzione!</h4>')
    errors_pane.append("<p>" + message + "</p>") for message in errors
    errors_pane.show()

  reset: () =>
    $("#new_payment_request").validate().resetForm()
    @paymentRequestableTypeHiddenField.reset()
    @paymentRequestableIdField.reset()
    @notesTextArea.reset()
    @paymentRequestKindSelect.reset()
    @dealerField.reset()
    @dealerIdField.reset()
    @amountField.reset()

  validate: () =>
    $("#new_payment_request").validate({
      rules: {
        "payment_request[amount]":               { required: true },
        "payment_request[dealer]":               { required: true },
        "payment_request[payment_request_kind]": { required: true },
        "payment_request[payment_requestable_id]":
          remote:
            url: '/payment_requests/validate_resource',
            type: 'post',
            data:
              payment_requestable_type: ->
                $('#payment_request_payment_requestable_type').val()
              dealer_id: ->
                $('#payment_request_dealer_id').val()
      },
      messages: {
        "payment_request[amount]":               { required: "Inserire un importo per continuare" },
        "payment_request[dealer]":               { required: "Selezionare un partner per continuare" },
        "payment_request[payment_request_kind]": { required: "Selezionare un tipo di richiesta per continuare" },
        "payment_request[payment_requestable_id]": { remote: "L'oggetto di riferimento non è valido" },
      },
      wrapper: "div"
    })

$ ->
  $('#new-payment-request-modal').on 'show.bs.modal', ->
    store = new GenericStore()
    window.App.CreatePaymentRequestModal = new CreatePaymentRequestModal(@, store)
