class @AstForm
  @build: () ->
    if $(@_form_selector).length != 0
      window.App.ThreadKindForm = new this

  initFileButton: () =>
    $("#after_sale_thread_threadable_attributes_attachment, #after_sale_thread_threadable_attributes_attachments").on 'change', ->
      label = []
      for i in $(@).get(0).files
        label.push(i.name.replace(/\\/g, '/').replace(/.*\//, ''))
      $(@).parents('.input-group').find(':text').val(label.join(', '))
      $(@).parents('.input-group').find(':text').attr('placeholder', '')