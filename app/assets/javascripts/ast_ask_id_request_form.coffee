class AstAskIdRequestForm extends @AstForm
  @_form_selector = '#ast-ask-id-request-form'

  constructor: () ->
    @$form = $('#threadable_section form')
    @$submitButton = @$form.find('#create-ast-button')
    new CustomerSearchForm()
    new OneShotButton('#create-ast-button')
    @requiredFields = $('.required-for-ask-id')
    @idFields = $('.id-fields')
    @disableFutureDates()
    @disableSubmit()
    @initFileButton()
    @bind()

  bind: () =>
    for field in @requiredFields
      $(field).on 'change', () =>
        @validateForm()

    for field in @idFields
      $(field).on 'change', () =>
        @validateForm()

  disableFutureDates: () =>
    $('#after_sale_thread_threadable_attributes_requested_at').datepicker('option', 'beforeShowDay', (date) =>
      [ (date <= new Date()), '']
    )

  disableSubmit: () =>
    @$submitButton.prop('disabled', true)

  enableSubmit: () =>
    @$submitButton.prop('disabled', false)

  idFieldPresent: () =>
    $('.id-fields').toArray().some (field) -> $(field).val() != ''

  validateForm: () =>
    if (@requiredFields.toArray().every (field) -> $(field).val() != '') && @idFieldPresent()
      @enableSubmit()
    else
      @disableSubmit()
$ ->
  (window.App.ThreadKindFormRepository ||= new ThreadKindFormRepository()).register AstAskIdRequestForm