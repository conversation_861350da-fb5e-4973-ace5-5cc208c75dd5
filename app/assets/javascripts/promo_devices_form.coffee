class @PromoDeviceForm
  PHONE_NUMBER_REGEXP = /^3\d{8,9}$/

  constructor: () ->
    @warehouseSelect = new WarehouseSelect()
    @dealerAutocomplete = new DealerAutocomplete()
    @phoneNumber = $('#promo_device_very_phone_number')
    @serial = $('#promo_device_serial')
    @submitButton = $('#submit_promo_device_button')

    @dealerAutocomplete.init()
    @init()

  init: () =>
    $('.phone-number-error').hide()
    $('.phone-number-present').hide()
    $('.serial-format-error').hide()
    $('.serial-unavailable-error').hide()
    $('#promo-device-very-message').hide()
    $('.promo-device-phone_number-container').hide()

    @submitButton.prop('disabled', true)

    @bind()

  bind: () =>
    @validateForm()
    if @phoneNumber.length > 0
      @validatePhoneNumber()
    @validateSerial()
    if @serial.val() != ''
      @checkSerial()
    @bindSubmit()

  bindSubmit: () =>
    @submitButton.on 'click', =>
      if confirm 'Stai per inserire una Promo Device Plus con il seriale ' + @serial.val() + '. Sei sicuro di voler continuare?'
        @submitButton.prop('disabled', true)
        @submitButton.closest('form').submit()
      else
        return false

  checkPhoneNumber: () =>
    $.ajax
      type: 'GET',
      url: @phoneNumber.data('url').replace('%3Avery_phone_number', @phoneNumber.val()),
      success: (data) =>
        if data == true
          @phoneNumber.removeClass('not-valid')
          $('.phone-number-present').hide()
        else
          @phoneNumber.addClass('not-valid')
          $('.phone-number-present').show()

        @checkRequiredFields()
      error: (data) =>
        alert(data)
  checkRequiredFields: () =>
    required_fields = []
    for required_field in $('#new_promo_device .required :input')
      required_fields.push(required_field)
    if (required_fields.every (field) -> $(field).val() != '') && $('.not-valid').length == 0 && $('.serial-ok').length > 0
      @submitButton.prop('disabled', false)
    else
      @submitButton.prop('disabled', true)

  checkSerial: () =>
    $.ajax
      type: 'GET',
      url: @serial.data('url').replace('%3Aserial', @serial.val())
        .replace('%3Adealer_id', $('#promo_device_dealer_id').val())
        .replace('%3Awarehouse_id', $('#promo_device_warehouse_id').val()),
      success: (data) =>
        if data['status'] == 200
          @serial.addClass('serial-ok')
          @serial.removeClass('not-valid')
          $('.serial-unavailable-error').hide()

          $('#promo_device_item_id').val(data['item_id'])
          $('#promo_device_product_id').val(data['product_id'])

          $('#promo-device-message').html(data['message'])
          if data['very_mobile_operator']
            $('#promo-device-very-message').show()
            $('.promo-device-phone_number-container').show()
            @validatePhoneNumber()
          else
            $('.promo-device-phone_number-container').hide()
            @phoneNumber.val('')
        else
          @serial.addClass('not-valid')
          $('#promo_device_item_id').val(data['item_id'])
          $('#promo_device_product_id').val(data['product_id'])

          $('#promo-device-message').html(data['message'])

          if data['error']
            $('.serial-unavailable-error').show()
          else
            $('.serial-unavailable-error').hide()

        @checkRequiredFields()

  validateForm: () =>
    for field in $('#new_promo_device .required :input')
      $(field).on 'focusout', =>
        @checkRequiredFields()

    $('#promo_device_dealer_id').on 'change', =>
      if @serial.val() != ''
        @checkSerial()

    $('#promo_device_warehouse_id').on 'change', =>
        @checkRequiredFields()
        if @serial.val() != ''
          @checkSerial()

  validatePhoneNumber: () =>
    @phoneNumber.on "keyup input", =>    
      if @phoneNumber.val() == ''
        $('.phone-number-error').hide()
        @phoneNumber.removeClass('not-valid')                

    @phoneNumber.on "change", =>    
      if PHONE_NUMBER_REGEXP.test(@phoneNumber.val()) || @phoneNumber.val() == ''
        $('.phone-number-error').hide()
        @checkPhoneNumber()
      else
        $('.phone-number-error').show()
        @phoneNumber.addClass('not-valid')
        @submitButton.prop('disabled', true)
  validateSerial: () =>
    @serial.on 'change', =>
      $('.serial-burn-error').remove()
      $('.serial-format-error').hide()
      $('.serial-unavailable-error').hide()
      $('#promo-device-message').html('')

      if window.App.ImeiValidator.validate(@serial.val())
        $('.serial-format-error').hide()
        @checkSerial() if $('#promo_device_dealer_id').val() != '' && $('#promo_device_warehouse_id').val() != ''
      else
        if @serial.val() == ''
          $('.serial-format-error').hide()
        else
          $('.serial-format-error').show()
        @serial.addClass('not-valid')

        @checkRequiredFields()

class @PromoDeviceStore extends @GenericStore

class DealerAutocomplete
  constructor: () ->
    @store = window.App.PromoDeviceStore
    @autocompleteInput = $('#promo_device_dealer')
    @autocompleteId = $('#promo_device_dealer_id')
    @modelId = @autocompleteId.val()

  init: () =>
    @bind()
    if @modelId != ''
      @store.setValue('dealer_id', @modelId)

  bind: () =>
    @autocompleteInput.on 'railsAutocomplete.select', (_event, data) =>
      @modelId = data.item.id
      @store.setValue('dealer_id', @modelId)

class WarehouseSelect extends @RemoteSelect
  DEFAULT_LABEL: 'Punto Vendita'

  constructor: () ->
    @store = window.App.PromoDeviceStore
    super('.promo_device_warehouse_select')
    @init()

  init: () =>
    @store.addListener(@)
    @bind()
    if $('#promo_device_dealer').length > 0
      @disable()

  bind: () =>
    @select.on 'change', () =>
      @notifySelect()

  notify: (message) =>
    return unless message['dealer_id']?
    return unless @select.data('url')
    @url = @select.data('url').replace(':id', @store.getValue('dealer_id'))
    @fillSelect()
    @enable()

  notifySelect: =>
    @modelId = @select.find('option:selected').val()
    @store.setValue('warehouse_id', @modelId)

  modelLabel: ->
    'warehouse'

$ ->
  if $('#new_promo_device').length > 0
    window.App.PromoDeviceStore = new PromoDeviceStore()
    window.App.PromoDeviceForm = new PromoDeviceForm()


