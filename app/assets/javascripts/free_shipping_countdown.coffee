class @FreeShippingCountdown extends @Countdown
  SECONDS_IN_AN_HOUR = 60 * 60
  SECONDS_IN_A_DAY = 24 * 60 * 60

  constructor: (countdownContainer, banner) ->
    super(countdownContainer)
    @countdownContainer = countdownContainer
    @banner = banner

  getClockFace: () ->
    if @time // SECONDS_IN_AN_HOUR == 0
      return "MinuteCounter"
    else if @time // SECONDS_IN_A_DAY > 0
      return "DailyCounter"

  finalCallback: () =>
    if $.grep(@banner.find(".countdown"), (x) -> $(x).is(":visible")).length == 1
      @banner.slideToggle("slow")
    else
      $(@countdownContainer).parent().slideToggle("slow")
