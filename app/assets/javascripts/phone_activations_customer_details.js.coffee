window.kolmeApp ?= {}
window.kolmeApp.customerDetailsHandler = ->
  $('#admitted_chars').popover({ 
    html: true, 
    template: '<div class="admitted_chars_popover popover"><div class="arrow" style="left: 50%;"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>' 
  })

  $('#renew_notification_sender_alias').bind 'keypress', (event) ->
    admitted_chars = "ABCDEFGHIJKLMNOPQRSTUVXYWZabcdefghijklmnopqrstuvxywzèéùìòà0123456789!‘,.:;?\"€£$%()+–=@_#&*"
    key = if !event.charCode then event.charCode else event.which
    if admitted_chars.includes(String.fromCharCode(key)) or event.which == 8
      $('#admitted_chars').popover 'hide'
      true
    else
      $('#admitted_chars').popover 'show'
      event.preventDefault()
      false

  # $('#form_selector').find('input[type=radio]').on 'change', (event) ->
  #   $('#sms_form').addClass 'hidden'
  #   $('#email_form').addClass 'hidden'
  #   selectedId = $(event.target).val()
  #   $("##{selectedId}").removeClass 'hidden'

window.kolmeApp.countDown = (el) ->
  counter = (160 - el.value.length)
  if counter < 0
    $('#sms_remaining_chars').addClass 'label-danger'
    $('#sms_submit').addClass 'disabled'
  else
    $('#sms_remaining_chars').removeClass 'label-danger'
    $('#sms_submit').removeClass 'disabled'
  $('#sms_remaining_chars').text counter
