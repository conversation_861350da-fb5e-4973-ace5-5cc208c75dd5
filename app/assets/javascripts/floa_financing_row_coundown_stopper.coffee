class FloaFinancingRowCoundownStopper
  constructor: (@countdownHandler) ->
    @countdownHandler.startAll()

  refresh: (@countdownHandler) ->
    @countdownHandler.startAll()

  stopCountdown: (reservationId) ->
    countdownsToStop = @countdownHandler.countdowns.filter (countdown) -> countdown.reservationId == reservationId
    countdownsToStop[0]?.clock?.stop()
    $("#imei-reservations-table tr##{reservationId}").hide("slow")

$ ->
  if $('#floa_financings_list').length
    countdownHandler = new FloaFinancingCountdownHandler()
    window.App.FloaFinancingRowCoundownStopper = new FloaFinancingRowCoundownStopper(countdownHandler)
