class RechargeForm
  constructor: () ->
    @aggregatedOperatorField = $("#aggregated_operator_id")
    @dealerField = $("#recharge_dealer_id")
    @phoneNumberField = $("#recharge_phone_number")
    @rechargeSizeField = $("#recharge_recharge_size_id")
    @submitButton = $('#submit_recharge_button')
    @allFields = [@aggregatedOperatorField, @dealerField, @phoneNumberField, @rechargeSizeField]

    @creditAvailable = true

    window.App.AggregatedOperatorSelect = new AggregatedOperatorSelect()
    window.App.RechargeSizeSelect = new RechargeSizeSelect()

    window.App.AggregatedOperatorSelect.init()

    @init()

  init: () =>
    @phoneNumberField.prop('disabled', true)
    @submitButton.prop('disabled', true)
    @bind()

  bind: ()  =>
    for field in @allFields
      $(field).on 'change', =>
        @validateForm()
      $(field).on 'keyup', =>
        @validateForm()

    @phoneNumberField.on 'keyup', =>
      $('#pin_note').html('')

      if @phoneNumberField.val() == ''
        $('#already-recharged-warning').hide()
      else
        if @rechargeSizeField.find(':selected').data('cif')
          if !@validateFiscalCode(@phoneNumberField.val())
            $('#pin_note').html('Inserisci un codice fiscale valido')
        else
          if @rechargeSizeField.find(':selected').data('posa') == false && !@phoneNumberField.val().match(/^(3)\d*$/)
            $('#pin_note').html('Inserisci un numero valido')

      if @phoneNumberField.val().trim().length >= 9
        url = @phoneNumberField.data('already-recharged-url').replace(/@/, @phoneNumberField.val())
        $.getJSON
          url: url,
          success: (data) ->
            already_recharged_today = data['already_recharged_today']
            if (already_recharged_today)
              $('#already-recharged-warning').show()
            else
              $('#already-recharged-warning').hide()

    @submitButton.on 'click', =>
      pin = @rechargeSizeField.find(':selected').data('pin')
      retryOnKo = @rechargeSizeField.find(':selected').data('retry-on-ko')
      paysafecard = @rechargeSizeField.find(':selected').data('operator') == 19
      posa = @rechargeSizeField.find(':selected').data('posa')

      if (!pin && @phoneNumberField.val().length == 0)
        alert('Devi inserire il numero di telefono!')
        return false
      else
        amount = $('#recharge_size_amount').val()
        if (amount == 0)
          alert('E \' necessario selezionare un taglio di ricarica per poter procedere')
          return false
        else
          if (paysafecard)
            $("#paysafecard-modal").modal('show')
            $("#paysafecard-modal").modal({backdrop: 'static', keyboard: false})
            return false
          else if (pin)
            answer = confirm('Stai per generare un codice, ricordati di fornirlo al cliente')
            if (answer)
              @submitButton.prop('disabled', true)
              $('#new_recharge').submit()
            else
              return false
          else if (retryOnKo)
            answer = confirm("Questa tipologia di ricarica, al contrario delle altre, verrà ritentata fino ad esecuzione con successo e non oltre le ore 22 del giorno successivo all'inserimento.")
            if (answer)
              @submitButton.prop('disabled', true)
              $('#new_recharge').submit()
            else
              return false

          else if (posa)
            sentence = 'Stai per attivare la Carta con codice ' + @phoneNumberField.val() + ', vuoi procedere?'
            answer = confirm(sentence)
            if (answer)
              @submitButton.prop('disabled', true)
              $('#new_recharge').submit()
            else
              return false

          else
            sentence = 'Attenzione, stai ricaricando di €' + amount + ' il numero di telefono ' + @phoneNumberField.val()
            answer = confirm(sentence)
            if (answer)
              @submitButton.prop('disabled', true)
              $('#new_recharge').submit()
            else
              return false

    @aggregatedOperatorField.on 'change', =>
      if $("#aggregated_operator_id").val() == ''
        $('#operator-logo').html('')
      else
        @loadLogo($("#aggregated_operator_id").val())

  buildNumberForm: () =>
    @phoneNumberField.attr('maxLength', '10')
    @phoneNumberField.val('')
    $('#phone-icon').find('i').removeClass('fa-list-alt').removeClass('fa-id-card')
    $('#phone-icon').find('i').addClass('fa-phone')
    @phoneNumberField.attr('Placeholder', 'Numero')
    @phoneNumberField.unbind("keypress")
    @phoneNumberField.unbind("blur")
    @submitButton.val('Esegui')

  buildCodeForm: () =>
    @phoneNumberField.attr('maxLength', '19')
    @phoneNumberField.val('')
    $('#phone-icon').find('i').removeClass('fa-phone').removeClass('fa-id-card')
    $('#phone-icon').find('i').addClass('fa-list-alt')
    @phoneNumberField.attr('Placeholder', 'Codice')
    @phoneNumberField.unbind("keypress")
    @phoneNumberField.unbind("blur")
    @submitButton.val('Attiva')

  buildFiscalCodeForm: () =>
    @phoneNumberField.attr('maxLength', '16')
    @phoneNumberField.val('')
    $('#phone-icon').find('i').removeClass('fa-phone').removeClass('fa-list-alt')
    $('#phone-icon').find('i').addClass('fa-id-card')
    @phoneNumberField.attr('Placeholder', 'Codice fiscale del beneficiario')
    @phoneNumberField.Setcase(caseValue: 'upper', changeonFocusout: false)
    @filterCharInput()
    @submitButton.val('Esegui')

  filterCharInput: () =>
    @phoneNumberField.keypress (e) =>
      txt = String.fromCharCode(e.which)
      if(!txt.match(/[A-Za-z0-9]/)) 
        return false

  loadLogo: (id) =>
    $.ajax
      type: 'GET',
      url: 'aggregated_operators/:id/display_logo'.replace(':id', id)
      success: (data) =>
        $('#operator-logo').html(data)

  phoneNumberChecked: () =>
    return true if (@rechargeSizeField.find(':selected').data('pin') == true && @rechargeSizeField.find(':selected').data('cif') != true)
    return true if  @rechargeSizeField.find(':selected').data('posa') == true && @phoneNumberField.val() != ''
    if @rechargeSizeField.find(':selected').data('cif') == true
      @validateFiscalCode(@phoneNumberField.val())
    else
      @phoneNumberField.val().match(/^(3)(\d{8,9})$/)

  setCreditAvailable: (credit_available) =>
    @creditAvailable = credit_available
    @validateForm()

    if @creditAvailable
      $("label[for='recharge_phone_number']").show()
      @submitButton.show()
      $('#credit-unavailable-alert').hide()
    else
      $("label[for='recharge_phone_number']").hide()
      @submitButton.hide()
      $('#credit-unavailable-alert').show()

  setPhonePhoneNumberFieldStatus: () =>
    if (@rechargeSizeField.find(':selected').data('pin') == true && @rechargeSizeField.find(':selected').data('cif') != true) ||
    (!(@creditAvailable && [@dealerField, @rechargeSizeField].every (field) -> $(field).val() != ""))
      @phoneNumberField.prop('disabled', true)
      @phoneNumberField.val('')
    else
      @phoneNumberField.prop('disabled', false)

  validateForm: () =>
    @setPhonePhoneNumberFieldStatus()
    if (@creditAvailable && [@dealerField,
      @rechargeSizeField].every (field) -> $(field).val() != "") && @phoneNumberChecked()
      @submitButton.prop('disabled', false)
    else
      @submitButton.prop('disabled', true)

  validateFiscalCode: (cf) =>
    if ( cf == '' ) 
      return ''
    cf = cf.toUpperCase()
    if ( cf.length != 16 )
      return false
    validi = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    for i in [0..15]
      if( validi.indexOf( cf.charAt(i) ) == -1 )
        return false
    set1 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    set2 = "ABCDEFGHIJABCDEFGHIJKLMNOPQRSTUVWXYZ"
    setpari = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    setdisp = "BAKPLCQDREVOSFTGUHMINJWZYX"
    s = 0
    for i in [1..13] by 2
      s += setpari.indexOf( set2.charAt( set1.indexOf( cf.charAt(i) )))
    for i in [0..14] by 2
      s += setdisp.indexOf( set2.charAt( set1.indexOf( cf.charAt(i) )))
    if ( s%26 != cf.charCodeAt(15)-'A'.charCodeAt(0) )
      return false
    return true


class RechargeStore extends @GenericStore


class AggregatedOperatorSelect extends @RemoteSelect
  DEFAULT_LABEL: 'Operatore'

  constructor: () ->
    @store = window.App.RechargeStore
    super("#aggregated_operator_id")
    @init()

  init: () =>
    @bind()

  bind: () =>
    @select.on 'change', () =>
      return if window.App.RechargeForm.rechargeSizeField.find(':selected').data('aggregated-operator') == $('#aggregated_operator_id').val()

      @modelId = @select.find('option:selected').val()
      @store.setValue('aggregated_operator_id', @modelId)

  modelLabel: ->
    'aggregated_operator'


class RechargeSizeSelect extends @RemoteSelect
  DEFAULT_LABEL: 'Taglio'

  constructor: () ->
    @store = window.App.RechargeStore
    super("#recharge_recharge_size_id")
    @init()

  init: () =>
    @store.addListener(@)
    @bind()

  bind: () =>
    @select.on 'change', () =>
      rechargeSizeField = window.App.RechargeForm.rechargeSizeField
      phoneNumberField = window.App.RechargeForm.phoneNumberField
      submitButton = window.App.RechargeForm.submitButton

      if rechargeSizeField.find(':selected').data('aggregated-operator')? && rechargeSizeField.find(':selected').data('aggregated-operator') != +@store.getValue('aggregated_operator_id')
        window.App.RechargeForm.loadLogo(rechargeSizeField.find(':selected').data('aggregated-operator'))

      cif = rechargeSizeField.find(':selected').data('cif')
      pin = rechargeSizeField.find(':selected').data('pin')
      posa = rechargeSizeField.find(':selected').data('posa')
      aggregated_operator = rechargeSizeField.find(':selected').data('aggregated-operator')
      operator = rechargeSizeField.find(':selected').data('operator')
      amount = rechargeSizeField.find(':selected').data('amount')

      if ($("#aggregated_operator_id").val() != aggregated_operator)
        if (aggregated_operator?)
          $("#aggregated_operator_id").val(aggregated_operator)
        else
          $("#aggregated_operator_id").val('').change()

      #if ($("#recharge_operator_id").val() != operator)
      #  $("#recharge_operator_id").val(operator)

      if (amount?)
        $('#recharge-size-amount').html('€ ' + amount)

      if (cif)
#phoneNumberField.prop("disabled", false)
        window.App.RechargeForm.buildFiscalCodeForm()
        $('#pin_note').html('')
      else if (pin)
        phoneNumberField.val('')
        phoneNumberField.prop("disabled", true)
        $('#pin_note').html('Ricorda di dare il codice al cliente.')
        if (phoneNumberField.attr('maxLength') != '10')
          window.App.RechargeForm.buildNumberForm()
      else if (posa)
        $('#pin_note').html('')
        phoneNumberField.prop("disabled", false)
        window.App.RechargeForm.buildCodeForm()
      else
#phoneNumberField.prop("disabled", false)
        $('#pin_note').html('')
        if (phoneNumberField.attr('maxLength') != '10')
          window.App.RechargeForm.buildNumberForm()
      if (rechargeSizeField.val() != '')
        $.ajax({
          url: '/recharges/check_recharge_available',
          data: 'recharge_size_id=' + @select.val()
        })

      window.App.RechargeForm.validateForm()

  attributes: (option, new_option) =>
    new_option.attr({
      "data-cif": option.recharge_size.cif,
      "data-pin": option.recharge_size.pin,
      "data-retry-on-ko": option.recharge_size.retry_on_ko,
      "data-operator": option.recharge_size.paymat_operator_id,
      "data-posa": option.recharge_size.bypass_phone_number_check,
      "data-aggregated-operator": option.recharge_size.aggregated_operator_id,
      "data-amount": option.recharge_size.stripped_amount
    })

  notify: (message) =>
    @url = "/recharge_sizes/for_aggregated_operator?aggregated_operator_id=" + @store.getValue('aggregated_operator_id')

    @fillSelect()

  modelLabel: ->
    'recharge_size'

  notifySelect: () =>
    rechargeSizeField = window.App.RechargeForm.rechargeSizeField

    window.App.RechargeForm.validateForm()
    if rechargeSizeField.val() != ""
      $('#recharge-size-amount').html('€ ' + rechargeSizeField.find(':selected').data('amount'))
    else
      $('#recharge-size-amount').html('')


$ ->
  if $('#new_recharge').length > 0
    window.App.RechargeStore = new RechargeStore()
    window.App.RechargeForm = new RechargeForm()

    $(document).ready ->
      $('.hide').hide()
      $('.show').show()
      $('form input').on 'keydown', (e) ->
        if (e.keyCode == 13)
          e.preventDefault()
          return false