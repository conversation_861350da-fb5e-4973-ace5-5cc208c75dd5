class MobileAppHandler
  notifyAcquisitionCanceled: ->
    if $('#new-phone-activation-modal').is(':visible')
      $('#new-phone-activation-modal').modal('hide')
    $('#app-flow-interrupted-modal').modal('show')

  notifyAcquisitionFailed: (preventRetry) ->
    if $('#new-phone-activation-modal').is(':visible')
      $('#new-phone-activation-modal').modal('hide')

    if preventRetry
      $("#app-flow-failed-modal #cannot-retry-warning").show()
      $("#app-flow-failed-modal #retry-button").hide()

    $('#app-flow-failed-modal').modal('show')

$ ->
  if $('#new-phone-activation-modal-container').length > 0
    window.App.mobileAppHandler = new MobileAppHandler()