class UserRoleSelect
  constructor: (@store) ->
    @selector = $('#user_role')
    @parent_id_selector = $('#user_parent_id')
    @access_level_selector = $('#user_internal_user_detail_attributes_access_level')
    @partner_type_selector = $('#user_internal_user_detail_attributes_partner_type')
    @init()
    @bind()

  bind: () =>
    @selector.bind 'change', () =>
      if @selector.val() in ['agent', 'sub_agent']
        @store.setValue('hide-report', true)
      else
        @store.setValue('hide-report', false)
      @toggleParentAgent()
      @toggleAccessLevel()
      @toggleAgentOnlyFields()
      @togglePartnerType()

    @parent_id_selector.bind 'change', () =>
      @toggleAccessLevel()
      @toggleSubAgentPartnerType()

  init: () =>
    if @selector.val() in ['', 'agent', 'sub_agent']
      @store.setValue('hide-report', true)
    @toggleParentAgent()
    @toggleAccessLevel()
    @toggleAgentOnlyFields()
    @togglePartnerType()

  toggleParentAgent: () =>
    if @selector.val() in ['sub_agent']
      $('#parent_agent_container').show()
    else
      $('#parent_agent_container').hide()
      $('#user_parent_id').val('')

  toggleAccessLevel: () =>
    $('#access_level_container').show()
    $('#sub_agent_access_level_container').html('')

    if @selector.val() in ['sub_agent']
      @access_level_selector.hide()

      if @parent_id_selector.val() == ''
        $('#access_level_container').hide()
        return

      access_level_fake_select = $('<select disabled>').appendTo('#sub_agent_access_level_container');
      access_level_fake_select.empty()
      access_level_fake_select.append new Option(@parent_id_selector.find('option:selected').data('access-level'), '')
      $('#sub_agent_access_level_container').show()
    else
      @access_level_selector.show()
      $('#sub_agent_access_level_container').hide()

  toggleAgentOnlyFields: () =>
    if @selector.val() in ['agent', 'sub_agent']
      $('.agent_only_fields').show()
    else
      $('.agent_only_fields input').val('')
      $('.agent_only_fields').hide()

  togglePartnerType: () =>
    if @selector.val() == 'agent'
      $('#partner_type_container').show()
      @partner_type_selector.prop('disabled', false)
    else if @selector.val() == 'sub_agent'
      $('#partner_type_container').show()
      @toggleSubAgentPartnerType()
    else
      $('#partner_type_container').hide()

  toggleSubAgentPartnerType: () =>
    @partner_type_selector.prop('disabled', true)

    parent_id = @parent_id_selector.val()
    parent_option = @parent_id_selector.find("option[value='#{parent_id}']")
    parent_partner_type = parent_option.data('partner-type')

    @partner_type_selector.val(parent_partner_type)

class ReportAccessSelect
  constructor: (@store) ->
    @container = $('#report_access_level_container')
    @selector = $('#user_internal_user_detail_attributes_report_access_level')
    @store.addListener(@)

  notify: (message) =>
    if message['hide-report'] == true
      @container.hide()
      @selector.val(null)
    else
      @container.show()


class InternalUserForm
  constructor: (@store) ->
    new ReportAccessSelect(@store)
    new UserRoleSelect(@store)

$ ->
  if($('.internal_user_form').length > 0)
    new InternalUserForm(new GenericStore())