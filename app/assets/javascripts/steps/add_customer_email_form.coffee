class AddCustomerEmailForm
  constructor: (@component) ->
    @init()

  init: () =>
    @component.validate({
      rules: {
        "phone_activation[customer_attributes][email]": {
          email: true
        }
      },
      messages: {
        "phone_activation[customer_attributes][email]": {
          required: "Inserire un'email",
          email: "Inserire un'email valida"
        }
      },
      wrapper: "div"
    })

$ ->
  if $(".edit_phone_activation").length > 0
    new AddCustomerEmailForm($(".edit_phone_activation"))