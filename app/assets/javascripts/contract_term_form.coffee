class ContractTermForm
  constructor: () ->
    @kindSelect = $('#kind_operator_id')
    @kindField = $('#contract_term_kind')
    @operatorIdField = $('#contract_term_operator_id')

    @bind()
    @init()

  bind: () =>
    @kindSelect.on "change", () =>
      @setKind()
      @setOperatorId()

  init: () =>
    @setKindSelect()
    @setKind()
    @setOperatorId()

  setKind: () =>
    @kindField.val(@kindSelect.find(':selected').val())

  setKindSelect: () =>
    if @operatorIdField.val() != ''
      @kindSelect.find("option[data-operator-id='#{@operatorIdField.val()}']").prop('selected', true)
    else
      @kindSelect.val(@kindField.val())

  setOperatorId: () =>
    @operatorIdField.val(@kindSelect.find(':selected').data('operator-id'))

$ ->
  if ('.contract_term_form').length
    new ContractTermForm()