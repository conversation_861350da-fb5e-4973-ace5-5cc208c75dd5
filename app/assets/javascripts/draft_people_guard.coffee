class @DraftPeopleGuard
  constructor: (container, doBind = true, @warnUser = true) ->
    @container = $(container)
    @bind() if doBind

  bind: () ->
    if @container.data("unloadGuardActive") && !@bound
      if @warnUser
        $(window).on "beforeunload", @beforeUnloadCallback
      $(window).on "pagehide", @unloadCallback
      
      @bound = true
      
      $(".ok-to-unload").on "click", () =>
        @unbind()
  
  unbind: () ->
    @bound = false
    $(window).off "beforeunload"
    $(window).off "pagehide"

  unloadCallback: (event) =>
    event.preventDefault()
    event.returnValue = ""
    
    id = @container.data("phoneActivationId")
    actionUrl = @container.data("unloadActionUrl")
    token = $('meta[name="csrf-token"]').attr('content')

    formData = new FormData
    formData.append("id", id)
    formData.append("authenticity_token", token)

    sendBeacon = `function(url, formData) {
      return navigator.sendBeacon(url, formData)
    }`

    sendBeacon(actionUrl, formData)

  beforeUnloadCallback: (event) ->
    event.preventDefault()
    event.returnValue = ""
    
  advancePage: (url) ->
    @unbind()
    window.location.href = url