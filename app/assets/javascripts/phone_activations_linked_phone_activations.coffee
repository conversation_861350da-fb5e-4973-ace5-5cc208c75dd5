class LinkedPhoneActivationsStore
  constructor: (@$container, sourceActivationId) ->
    @state = {sourceActivationId: sourceActivationId}
    @listeners = []

  setState: (key, value) ->
    @state[key] = value
    listener.notify(key, value) for listener in @listeners

  setValidationResult: (validationResult, validationMessage, activationId) ->
    @state.validationResult = validationResult
    @state.validationMessage = validationMessage
    @state.linkedActivationId = activationId
    listener.notify('validationResult', validationResult) for listener in @listeners

  getState: () ->
    @state

  addListener: (listener) ->
    @listeners.push listener

class LinkedPhoneActivationsForm
  constructor: (@selector) ->
    sourceActivationId                   = $(@selector).data('activation-id')
    @store                               = new LinkedPhoneActivationsStore($('.edit_phone_activation'), sourceActivationId)
    @expectedLinkedActivationsCountInput = new ExpectedLinkedActivationsCountInput('#expected_linked_activations_count_wrapper', @store)
    @linkedActivationHexIdInput          = new LinkedActivationHexIdInput('#linked_activation_hex_id_wrapper', @store)
    @linkedStatusSelect                  = new LinkedStatusSelect('#phone_activation_linked_selection', @store)
    @formValidator                       = new FormValidator(@store)
    @submitButton                        = new SubmitButton('#linked_phone_activations_form_submit', @store)

class LinkedStatusSelect
  constructor: (@selector, @store) ->
    $select = $(@selector)
    @initStoreValue($select)
    @bindEvents($select)

  initStoreValue: ($select) ->
    @store.setState('linkedStatus', $select.val()) if !!$select.val()

  bindEvents: ($select) ->
    $select.bind 'change', (event, ui) =>
      linkedStatus = event.target.value
      @store.setState('linkedStatus', linkedStatus)

class ExpectedLinkedActivationsCountInput
  constructor: (@selector, @store) ->
    @store.addListener @
    $select = $(@selector).find('select')
    @initStoreValue($select)
    @bindEvents($select)

  initStoreValue: ($select) ->
    @store.setState('expectedLinkedActivationsCount', parseInt($select.val())) if !!$select.val()

  bindEvents: ($select) =>
    $select.bind 'change', (event, ui) =>
      expectedCount = parseInt event.target.value
      @store.setState('expectedLinkedActivationsCount', expectedCount)

  reset: () ->
    @store.setState('expectedLinkedActivationsCount', null)
    $(@selector).find('select').val('')

  notify: (key, value) ->
    return unless key == 'linkedStatus'
    state = @store.getState()
    if state.linkedStatus == 'linked_selection_parent'
      $(@selector).show()
    else
      @reset()
      $(@selector).hide()

class LinkedActivationHexIdInput
  constructor: (@selector, @store) ->
    @store.addListener @
    @bindEvents()
    @validateInput(@$hexIdInput().val())

  bindEvents: () =>
    @$hexIdInput().bind 'change', (event, ui) =>
      @validateInput(event.target.value)

  validateInput: (linkedActivationHexId) =>
    @store.setState('linkedActivationHexId', linkedActivationHexId)

  reset: () ->
    @store.setState('validationMessage', 'Inserisci un ID attivazione per continuare')
    @$hexIdInput().val('')
    @validateInput(@$hexIdInput().val())

  setMessage: (message) ->
    $(@selector).find('#validation-message').text(message)

  setSuccess: (linkedActivationId) ->
    @$formGroup().removeClass('has-error')
    @$formGroup().addClass('has-success')
    @setLinkedActivationId(linkedActivationId)

  setFailure: () ->
    @$formGroup().addClass('has-error')
    @$formGroup().removeClass('has-success')
    @setLinkedActivationId(null)

  setLinkedActivationId: (id) ->
    @$hiddenInput().val(id)

  $hexIdInput: () ->
    $(@selector).find('#phone_activation_linked_phone_activation_hex_id')

  $hiddenInput: () ->
    $(@selector).find('#phone_activation_linked_phone_activation_id')

  $formGroup: () ->
    $(@selector).find('.form-group')

  notify: (key, value) ->
    state = @store.getState()
    if key == 'linkedStatus'
      if state.linkedStatus == 'linked_selection_child'
        $(@selector).show()
      else
        @reset()
        $(@selector).hide()
    else if key == 'validationResult'
      if state.validationResult then @setSuccess(state.linkedActivationId) else @setFailure()
      if state.validationMessage then @setMessage(state.validationMessage)

class FormValidator
  constructor: (@store) ->
    @store.addListener @
    @validateAndNotify @store

  validateAndNotify: (store) ->
    state = store.getState()
    if state.linkedStatus == 'linked_selection_no'
      @store.setState('validationResult', true)
    else if state.linkedStatus == 'linked_selection_parent' && state.expectedLinkedActivationsCount > 0
      @store.setState('validationResult', true)
    else if state.linkedStatus == 'linked_selection_child' && !!state.linkedActivationHexId
      @performRemoteValidation(@store, state)
    else
      @store.setState('validationResult', false) # otherwise

  notify: (key, _value) ->
    return if key == 'validationResult'
    @validateAndNotify(@store)

  performRemoteValidation: (store, state) ->
    $.ajax
      type: "POST",
      url: "/phone_activations/validate_linked_activation",
      format: 'JSON',
      data:
        source_activation: state.sourceActivationId,
        target_activation: state.linkedActivationHexId
    .done (data, textStatus, jqXHR) =>
      @store.setValidationResult(data.valid, data.message, data.activation_id)
    .error (xhr, status, error) =>
      @store.setValidationResult(false, 'unknownError')

class SubmitButton
  constructor: (@selector, @store) ->
    @store.addListener @
    @setButtonState(@selector, @store)

  notify: (key, value) ->
    return unless key == 'validationResult'
    @setButtonState(@selector, @store)

  setButtonState: (selector, store) ->
    if @canSubmit(store)
      $(selector).enable(true)
    else
      $(selector).enable(false)

  canSubmit: (store) ->
    state = store.getState()
    state.validationResult == true

$ ->
  if $('.phone_activations_scope.linked_selection_page').length > 0
    window.App.linkedPhoneActivationsForm  = new LinkedPhoneActivationsForm('.edit_phone_activation')
