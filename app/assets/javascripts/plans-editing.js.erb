<% url = Bost::Application.routes.url_helpers %>
var Plan = (function(){
    
    var show_hide_options = function(form){
        operator_id = form.find('#plan_operator_id').val();
        $('.options_for_operator', form).each(function(index){
           if ( $(this).attr('data-operator') === operator_id ) {
               $(this).show();
           } else {
               $(this).hide();               
               $('input[type=checkbox]', this).attr('checked', false);
           }
        });
        disable_incompatible_options(form);
    }

    var disable_incompatible_options = function(form){
        return;
    }
    
    var matrix = {};
    
    var all_options;
    
    return {
        setup: function(form_id, options){
            var form = $("#"+form_id);
            form.find('#plan_operator_id').change(function() {
                show_hide_options(form);
            });
            if (options) {
                matrix = options;
            }
            all_options = _.map($('.plan_option_id', form), function(el){ return $(el).data('option'); });
            $('.plan_option_id', form).click(function(){
                disable_incompatible_options(form);
            })
            show_hide_options(form);
        }
    }
})()
