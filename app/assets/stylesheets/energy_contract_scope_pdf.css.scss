$gas: #0CBBEF;
$energy: #49B270;
$orange: #FF6A23;
$light-grey: #ECECEC;

@import "bootstrap";

.energy-contract-pdf-scope {
  *,
  *::before,
  *::after {
    box-sizing: border-box;
  }

  * {
    font-size: 17px;
    font-family: 'Inter';
  }

  html, body {
    margin: 0;
    padding: 0;
  }

  img {
    width: 100%;
  }

  div {
    break-inside: avoid;
    page-break-inside: avoid;
  }

  .bold {
    font-weight: 900;
  }

  .orange-text {
    color: $orange;
  }

  .gas {
    color: $gas;
  }

  .energy {
    color: $energy;
  }

  .white {
    color: white;
  }

  .black {
    color: black;
  }

  .large-text {
    font-size: 30px;
    line-height: 1.2em;
  }

  .small-text {
    font-size: 11px;
    line-height: 16px;
  }

  .shrink-text {
    letter-spacing: -1px;
  }

  .mb-0 {
    margin-bottom: 0;
  }

  .mb-20 {
    margin-bottom: 20px
  }

  .mb-30 {
    margin-bottom: 30px;
  }

  .mb-40 {
    margin-bottom: 40px;
  }

  .mb-80 {
    margin-bottom: 80px;
  }

  .img-contain {
    width: 100%;
    object-fit: contain;
  }

  .luce-gas-logo {
    width: 160px;
    height: 160px;
    margin-top: -35px;
    background-repeat: no-repeat;
  }

  .energy-supply-detail-summary-list-energy {
    list-style-type: none;
    padding-inline-start: 0;
    line-height: 35px;

    li::before {
      content: "•";
      font-size: 1.5em;
      margin-right: 5px;
      color: $energy;
    }
  }

  .energy-supply-detail-summary-list-gas {
    list-style-type: none;
    padding-inline-start: 0;
    line-height: 35px;

    li::before {
      content: "•";
      font-size: 1.5em;
      margin-right: 5px;
      color: $gas;
    }
  }

  .plans-description {
    background-color: $light-grey;
    border-radius: 10px;
    padding: 20px 30px;
  }

  .section-separator {
    border-bottom-width: 1px;
    border-bottom-style: solid;
    position: relative;

    p {
      width: 66%;
    }

    &.left-align {
      .section-label {
        left: -1px;
        right: auto;
      }
    }

    .section-label {
      background-color: currentColor;
      white-space: nowrap;
      border-radius: 5px 5px 0 0;
      position: absolute;
      bottom: -1px;
      right: -1px;
      overflow: hidden;
      text-align: center;
      width: 150px;

      svg {
        vertical-align: sub;
        fill: white;
      }
    }
  }

  .plan-card {
    border-radius: 10px;
    overflow: hidden;
    margin-right: 30px;
    height: 100%;

    .plan-card-header {
      width: 100%;
      padding: 5px 35px;
      line-height: 20px;

      span {
        line-height: 20px;
        color: white;
      }

      span:last-child {
        float: right;
      }
    }

    .plan-card-body {
      padding: 20px 35px;
      line-height: 20px;

      hr {
        border: none;
        border-top: 1px solid #cccccc;
        margin: 20px 0;
      }
    }
  }

  .plan-card-energy {
    border: 2px solid $energy;

    .plan-card-header {
      background-color: $energy;
    }
  }

  .plan-card-gas {
    border: 2px solid $gas;

    .plan-card-header {
      background-color: $gas;
    }
  }

  .plan-supply-detail {
    position: relative;

    > p:last-child {
      position: absolute;
      width: 100%;
      bottom: 0;
    }
  }

  .plan-summary {
    display: table;
    height: 100%;

    > div {
      display: table-cell;
      height: 100%;
      width: 50%;
    }

    > div:last-child {
      padding-left: 40px;
      vertical-align: top;
    }
  }
}
