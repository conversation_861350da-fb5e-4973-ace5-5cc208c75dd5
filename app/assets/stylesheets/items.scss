@import 'shared_definitions';

.items_scope.massive_create_page {
  $spacer-x: 30px;
  
  .mb-1 {
    margin-bottom: ($spacer-x * 0.25) !important;
  }

  .input {
    @extend .mb-1;
  }

  .input.massive_item_serials.field_with_errors {
    @extend .mb-1;
    span.error {
      display: none;
    }
  }
}
.massive_select{
  height: 30px !important;
}

.not_massive{
  margin-left: 10px
}

.item-panels{
  min-height: 50px;
  padding: 10px !important;
}

.gift_cube_btn {
  width: 37px;
  color: $promo-device-color !important;
}

.items_buttons_dealer {
  min-width: 200px !important
}
