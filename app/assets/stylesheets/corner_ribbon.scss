@import url('//fonts.googleapis.com/css?family=Noto+Sans:400,700');

.ribbon-wrapper{
  width: 110px;
  height: 88px;
  overflow: hidden;
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 999;
}

.ribbon {
  font: bold 15px Sans-Serif;
  text-align: center;
  -webkit-transform: rotate(-45deg);
  -moz-transform:    rotate(-45deg);
  -ms-transform:     rotate(-45deg);
  -o-transform:      rotate(-45deg);
  position: relative;
  padding: 7px 0;
  left: -32px;
  top: 18px;
  width: 130px;
}

.ribbon:before, .ribbon:after {
  content: "";
  border-top:   3px solid transparent;
  border-left:  3px solid transparent;
  border-right: 3px solid transparent;
  position:absolute;
  bottom: -3px;
}


.ribbon-news{
  @extend .ribbon;
  color: #404040;
  background-color: rgb(245, 246, 33);
}

.ribbon-promo {
  @extend .ribbon;
  color: #FFFFFF;
  background-color: #c9302c;
}

.ribbon-incoming {
  @extend .ribbon;
  color: #FFFFFF;
  background-color: #556B2F;
}

.ribbon-featured {
  @extend .ribbon;
  color: #FFFFFF;
  background-color: #FF9900;
}
