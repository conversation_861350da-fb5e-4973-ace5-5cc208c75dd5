.donut-header {
  background-color: #f4f4f4;
  padding: 3px;
  font-size: 14px;

  width: 880px !important;
  @media (max-width: 1115px) {
    width: 430px !important;
  }
}

.numbers-box-container {
  width: 450px !important;
}

.numbers-box {
  border: 1px solid #cccccc;
  padding: 0;
  position: relative;
  width: 430px !important;

  .donut-head {
    position: absolute;
    z-index: 1;
    width: 100%;
    left: 0;
    display: flex;
    justify-content: space-between;
    padding: 4px;
    align-items: flex-start;
  }

  .bubble {
    height: 64px;
    width: 64px;
    padding: 2px;
    text-align: center;
    border-radius: 50%;
    background: #929292;
    color: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    line-height: 1;
    font-size: 10px;

    &-big {
        font-size: 14px;
        font-weight: bold;
        padding-bottom: 2px;
    }

    &-red {
      color: red;
    }
  }

  .donut-char {
    position: absolute;
  }

  .donut-inner.w3 h4 {
    color: #ff6900
  }

  .donut-inner.very_mobile h4 {
    color: #00c74f
  }
  .donut-inner.koins h4 {
    color: #0f5594
  }

  .donut-inner.koins {
    .donut-title  {
      color: #0f5594;
    }

    p {
      margin: -15px 0 0 0;
    }
  }

  .donut-footer {
    background-color: #f4f4f4;
    padding: 3px;
    font-size: 14px
  }

  .number-image {
    max-width: 75px;
    top: 20px;
    width: 100%
  }

  .number-in-update-image {
    width: 50%;
    height: auto;
    display: block;
    margin-top: 40px;
    margin-bottom: 40px;
  }

  .rounded-number-badges {
    p {
      font-size: 16px;
    }

    span {
      min-width: 65px !important;
    }
    span.ko {
      color: #000000;
    }

    .ok.koins {
      background-color: #0f5594 !important;
    }

    .ko.koins {
      background-color: #d8e1eb !important;
    }

    .ok.w3 {
      background-color: #ff6900 !important;
    }

    .ko.w3 {
      background-color: #ffdccd !important;
    }

    .ok.very_mobile {
      background-color: #00c74f !important;
    }

    .ko.very_mobile {
      background-color: #d2fce3 !important;
    }

  }

  .donut{
    position: relative;
    margin-top: 30px;
    pointer-events: none;
    &-inner {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 16px;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        h4.donut-title{
            margin-bottom: 5px;
            margin-top: 0;
            font-weight: bold;
            font-size: 1.75em;
            position: relative;
        }

        .donut-count {
            font-weight: bold;
            font-size: 5.625em;
            margin: -10px 0 10px 0;
        }

        .donut-count-koins {
            font-size: 1.5em !important;
            margin: 0px 0 10px 0 !important;
            font-family: 'Arial', sans-serif !important;
            font-style: italic !important;
        }

        .donut-message {
            font-size: 1.5em;
            margin-top: -20px;
            font-style: italic;
        }
    }
    &--update{
      min-height: 300px;
      h4.donut-title{
        margin-bottom: -5%;
      }
      .donut-message {
        margin-top: -5%;
      }
    }
  }

  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.75em;
    color: black;
    z-index: 1;
  }
}
