/*
*= require_self
*= require font-awesome
 */

@import "shared_definitions";
@import "bootstrap";

@page {
  size: 210mm 297mm;
  margin: 10mm;
}

html, body {
  margin: 0;
  font-size: 12px;
}

.body-wrapper {
  padding: 0 20px;
}

.pdf_total_container * {
  font-family: arial, sans-serif;
  box-sizing: border-box;
}

h3, h4, {
  color: #FF6900;
  font-weight: bold;
}

h3 {
  font-size: 20px;
  text-align: center;
}

h5 {
  color: #FF6900;
}

.highlight {
  color: #FF6900;
}

.pdf-table {
  font-size: 11px;
  padding: 10px;
  border: 4px solid #97999B;
  border-radius: 12px;

  .table-row {
    padding: 10px;
    padding-bottom: 0px;

  }

  .table-row:not(:last-child)::after {
    margin: 0 auto;
    margin-bottom: 8px;
    width: 98%;
    display: block;
    border-bottom: 1px solid #97999B;
    padding-bottom: 8px;
  }
}

.logo-container {
  -moz-box-flex: 1;
  -webkit-box-flex: 1;
  box-flex: 1;
}

.logo {
  height: 50px;
}

.header-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 20px;
}

.service-disclaimer-container {
  -moz-box-flex: 1;
  -webkit-box-flex: 1;
  box-flex: 1;
  width: 50%;
}

.address-container {
  font-size: 11px;
  text-align: right;
}

ul {
  list-style-type: square;

  span {
    color: black;
  }
}
