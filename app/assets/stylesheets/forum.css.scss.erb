@import "bootstrap";
@import "shared_definitions";
@import 'thredded';
/*@import url(//fonts.googleapis.com/css?family=Open+Sans:300italic,400italic,600italic,700italic,800italic,300,400,600,700,800);*/

@import "font-awesome";
@import 'font_awesome5_webfont';
@import "summernote";

html {
  font-size: inherit;
}

#thredded-kolme-container {
  .avatar-preview{
    width: 180px;
  }

  .sidebar {
    padding-top: 10px;
    .kolme-logo {
      max-width: 100%;
      height: auto;
      padding-bottom: 10px;
    }
    .user-list-item {
      margin: 0px;
    }
  }

  .thredded--user-navigation--private-topics,
  .thredded--user-navigation--settings {
      visibility: hidden;
  }

  .thredded--main-container {
    // The padding and max-width are handled by the app's container.
    max-width: 90%;
  }

  .thredded--topics--follow-info,
  .thredded--user-navigation--moderation {
    display: none;
  }
}
