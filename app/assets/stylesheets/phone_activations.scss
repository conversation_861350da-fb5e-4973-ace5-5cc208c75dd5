@import 'shared_definitions';
@import "utilities/clearfix";

$background_color_light: #eee;
$background_color_dark: #313945;
$border_color_dark: #333;
$border_color_light: #ccc;
$text_color_dark: #333;
$text_color_light: #eee;
$detail_border_color: solid 2px #449eff;
$detail_background_color: #555555;

@mixin default_h2_style {
  float: left;
  background-color: $background_color_light;
  color: $text_color_dark;
  padding: 3px 2px;
  text-decoration: none;
}

@mixin timeline_entry {
  .timeline.dealer_timeline h1 {
    font-size: 14px;
  }
  .timeline_entry {
    padding-bottom: 5px;
    margin-bottom: 5px;

    .details {
      padding-top: 5px;
      margin-bottom: 5px;
      border-top: solid 1px $default_grey;
    }

    .light-border {
      border-top: solid 1px rgba(204, 204, 204, 0.33) !important;
    }

    .time {
      font-size: 11px;
      color: #999;
    }

    .time_title {
      font-size: 11px;
      color: $default_blu;
    }

    .ko_r_dark {
      color: #E8D208 !important;
    }

    .no-operations {
      color: #999;
      background: transparent !important;
      border: none !important;
    }

    .notes {
      font-size: 11px;
      padding-bottom: 10px;

      strong {
        font-style: normal;
        color: #999;
      }

      &.orange {
        color: $orange;
        font-weight: bold;
      }

      div.recovered_note {
        color: $default_ko_r_color;
      }
    }

    .buttons .recovery_note {
      resize: none;
    }

    .timeline_operation {
      padding-bottom: 2px;
      margin-bottom: 2px;
      font-variant: small-caps;
      font-size: 14px;

      .ko_r {
        background-color: $default_ko_r_color;
        color: $text_color_dark;
      }

      .ok {
        background-color: $default_ok_color;
        color: $text_color_dark;
      }

      .ko {
        background-color: $default_ko_color;
        color: $text_color_dark;
      }
    }

    &.dealer_timeline {
      font-size: 14px;

      .details {
        float: left;
        width: 100%;

        .time, .dealer_timeline_operation {
          float: left
        }

        .time {
          padding-top: 5px
        }

        .dealer_timeline_operation {
          margin-right: 10px;
          font-size: 16px;
          font-variant: small-caps;

          .ko_r {
            color: $default_ko_r_color;
          }

          .ok {
            color: $default_ok_color;
          }

          .ko {
            color: $default_ko_color;
          }
        }

        .notes {
          clear: both;
        }
      }
    }
  }
}

.phone_activation_recap {
  margin-top: 10px;
  padding: 0;
  @include border_radius_6;
  border: solid 4px $default_color_10;
  box-shadow: none;
  font-size: 20px;
  text-align: center;

  label {
    text-transform: lowercase;
    font-variant: small-caps;
    color: #7D7D7D;
    font-weight: 500;
  }

  span {
    font-size: 25px;
    color: $default_color;
    font-weight: 600;
  }
}

.phone_activation_header {
  position: relative;
  z-index: 1;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.38);
  background-color: #fff;
  border-top: dotted 1px $default_grey;

  div {
    padding-top: 5px;
    padding-bottom: 5px;
    vertical-align: middle;

    i {
      font-size: 18px;
      color: $default_color;
    }

    div.active {
      color: $default_color;
      font-weight: 500;

      i.number {
        background-color: $default_color;
        color: #fff;
        font-size: 11px;
        @include border_radius(12px);
        padding: 3px 5px;
      }
    }
  }
}

.column_66 {
  width: 66px;
}

.column_50_center {
  width: 50px;
  text-align: center
}

.column_100_centered {
  width: 100px;
  text-align: center
}

.column_120_centered {
  width: 120px;
  text-align: center
}

.column_145 {
  width: 145px;
}

.column_160_centered {
  width: 160px;
  text-align: center
}

.phone_activation_progress {
  position: relative;
  z-index: -1;
  height: 50px;
  margin-top: -44px;
  background-color: #e8e7e5;

  div.completed {
    height: 50px;
    background-color: $default_color_30;
  }
}

.phone_activations {
  position: relative;
  clear: both;

  .button-group {
    float: left;
    margin: 10px;
  }

  div.ok {
    width: 10px;
    height: 10px;
    background-color: $default_ok_color;
    border-radius: 20px;
  }

  div.ko {
    width: 10px;
    height: 10px;
    background-color: $default_ko_color;
    border-radius: 20px;
    margin: 0 auto;
  }
}

#container {
  //height: 100%;
  //width: 100%;
}

#container h2 {
  /*  margin: 0;
    margin-bottom: 5px;
    clear: both;*/
}

#phone_activation_main h2.title,
#phone_activation_main h2.title a {
  background-color: $background_color_dark;
  color: $text_color_light;
  padding: 3px 2px;
  text-decoration: none;
}

#phone_activation_main h2.title a {
  background: none;
  padding-left: 23px;
  display: block;
  z-index: 0;
}

#phone_activation_main #status h2.title {
  color: $text_color_dark;
}

h2.accordion.open {
  background: $background_color_light url(arrow_open_invert.png) 3px center no-repeat;
}

h2.accordion.close {
  background: $background_color_light url(arrow_close_invert.png) 3px center no-repeat;
}

#phone_activation_main h2.accordion.open {
  background: $background_color_dark url(arrow_open.png) 3px center no-repeat;
}

#phone_activation_main h2.accordion.close {
  background: $background_color_dark url(arrow_close.png) 3px center no-repeat;
}

#phone_activation_main h2.accordion_small.open {
  background: $background_color_light url(arrow_open_invert.png) 3px center no-repeat;
}

#phone_activation_main h2.accordion_small.close {
  background: $background_color_light url(arrow_close_invert.png) 3px center no-repeat;
}

#phone_activation_main.highlighted_section h2.accordion_small.close {
  background: $background_color_light;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    left: 3px;
    top: 0;
    bottom: 0;
    width: 20px;
    background: url(arrow_close_invert.png) center no-repeat;
    opacity: .2;
  }

  &:hover::before {
    opacity: .8;
  }
}

h2 span#lock_button {
  float: right;
  margin: 0 5px 0 0;
}

h2 span#show_visited {
  float: right;
  margin: 4px 5px 0 0;
  font-size: 16px;

  a {
    color: $default_grey;
  }
}

h2.accordion img {
  float: right;
  margin: 5px 5px 0 0;
}

h2 span#lock_button .button {
  font-variant: small-caps;
  font-size: 12px;
}

.content {
  clear: both;

  .popover {
    font-size: 12px !important;
  }
}

// Percentage
#progress {
  padding-bottom: 10px;

  h2 {
    margin-bottom: 10px;
  }
}

// Operations
#operations {
  width: 100%;
  clear: right;
  color: $text_color_light;
  overflow: hidden;
  padding: 10px 0;
  border-top: solid 2px $border_color_light;

  div {
    padding: 2px 0;
    margin: 2px 0;
  }
}

// Batch Operations
#batch_operations {
  padding: 10px 0;
  width: 100%;
  color: $text_color_light;
  border-top: solid 2px $border_color_light;
  overflow: hidden;

  div {
    padding: 2px 0;
    margin: 2px 0;
  }
}

// Timeline
#timeline {
  padding: 10px 0 62px 0;
  width: 100%;
  color: $text_color_light;
  border-top: solid 2px $border_color_light;
  overflow: hidden;
  @include timeline_entry;
}

// Phone Activation Details

table tr.phone_activation_details {
  td {
    background-color: $default_dark_color;
    color: #efefef;
    border-right: $detail_border_color;
    border-left: $detail_border_color;
    border-bottom: $detail_border_color;
    border-top: none;
  }

  hover td {
    background-color: #dedede;
    border: solid 1px #ededed;
  }

  .timeline {
    float: left;
    width: 100%;
    height: 300px;
    overflow: auto;
    padding: 0;
    @include timeline_entry;

    .timeline_entry {
      float: left;
      clear: both;
      padding: 10px;
      margin: 0;
      border: none;

      .details {
        width: 550px;
        margin-left: 50px;
        word-wrap: break-word;
      }

      .buttons {
        form {
          padding: 2px 0 0 0;
          margin-left: 50px;

          label {
            font-style: normal;
            color: #999;
            font-weight: bold;
            font-size: 10px;
          }

          textarea {
            float: left;
            width: 475px;
            height: 50px;
            resize: none;
          }

          .button {
            margin-left: 10px;

            &.disabled {
              &:disabled {
                font-style: italic;
                color: #ccc;
              }

              &:hover {
                background: none;
                background-color: #eee;
                color: #ccc;
                border: solid 1px #d4d4d4;
                cursor: default;
              }
            }
          }
        }
      }
    }
  }
}

.phone_activation_details {
  @include timeline_entry;
}

.phone_activation_files, .phone_activation_with_report_files, .official_pda_files {
  padding: 10px;
  margin: 10px 0;
  border: solid 1px $border_color_light;
  background-color: $background_color_light;
  color: $text_color_dark;

  h2 {
    font-variant: small-caps;
    padding: 0;
    margin: 0;
  }

  .file {
    border: $border_color_dark;
    background-color: #fff;
    height: 50px;
    padding: 10px 0;
    margin-bottom: 10px;

    img {
      float: left;
      padding-right: 10px;
    }

    div {
      float: left;
      line-height: 32px;
      vertical-align: middle;
    }

    a.btn.btn-default.link-delete.red {
      float: right;
      margin-right: 9px;
    }
  }
}

.action_buttons {
  img {
    padding: 0 10px;
  }
}

// spentotal

.phone_activation_show {

  .phone_activation_heading {
    background: $background_color_dark;
    color: #eeeeee;
    float: left;
    padding: 0 10px;
    font-size: 1.5em;
    z-index: 2;

    b {
      font-size: 14px;
    }

    span {
      font-size: 14px;
    }

    .orange span {
      color: $orange;
      font-weight: bold;
    }
  }

  .phone_activation_sidebar {
    float: left;
    position: relative;

    height: 100%;
    padding: 10px;
    background-color: $background_color_dark;
    color: #eee;
    overflow: auto;

    h2 {
      width: 100%;
      @include default_h2_style;

      span.accordion_anchor a {
        @include default_h2_style;
        background: none;
        padding-left: 23px;
        display: block;
        z-index: 0;
        font-size: 16px;
      }
    }
  }

  .phone_activation_main {
    float: left;
    padding: 0 10px;
    height: 100%;

    h2.accordion {
      clear: both;
      width: 100%;
      min-height: 35px !important;
      line-height: 28px;
    }

    h2.close {
      display: block;
      border: 1px solid red;
      float: none !important;
    }
  }

  .highlighted_section {
    background-color: #deeee0;

    h2.close {
      opacity: 1 !important;

      a {
        opacity: .2;
      }

      &:hover a {
        opacity: .8;
      }
    }
  }
}

#credit_card_cvv_modal {

  div.modal-header {
    background-color: #313945;
    color: white;
  }

  h4#myModalLabel {
    font-size: 1.5em;
  }

  button.close {
    &, &:hover, &:active {
      color: white !important;
      opacity: 1 !important;
    }
  }

  div.modal-footer {
    background-color: #ccc;
  }

}

.red-alert {
  color: red !important;
}

#do_number_portability_yes {
  margin-top: 10px;
}

//data composer
#data_composer_sidebar {
  padding-top: 8px;
  background-color: #fff;
  border-left: solid 7px #efefef;
  min-height: 1000px;

  .data_composer_sidebar_box {
    border: solid 2px rgba(60, 141, 222, 0.3);
    @include border_radius_6;
    margin-bottom: 20px;
  }

  #plan_description_container, #product_image_container {
    margin-bottom: 20px;
    margin-top: 10px;

    .option_name {
      @include phone_activation_label;
      text-transform: uppercase;
      line-height: 1;
      padding-bottom: 5px;
      font-size: 14px;
      font-weight: bold;
      text-align: center;
    }

    #plan_name {
      @include phone_activation_label;
      text-transform: uppercase;
      line-height: 1;
      padding-bottom: 20px;
      font-size: 20px;
      font-weight: bold;
      text-align: center;
    }

    #plan_description {
      text-align: justify;
      padding-bottom: 5px;
    }
  }

  #product_image_container {
    border: none;

    img {
      padding: 10px 0;
    }
  }

  #upfront_box, #monthly_box, #product_final_installment_box {
    text-align: center;
    margin-bottom: 20px;

    label {
      @include phone_activation_label;
      color: $default_dark_color;
      font-size: 25px;
    }

    #monthly, #upfront, #tcg, #activation_fee {
      font-size: 35px;
      color: $default_blu;
      font-weight: bold;
    }

    #monthly_description, #tcg, #activation_fee {
      font-size: 12px;
      font-weight: 300;
      float: left;
      width: 100%;
    }

    #upfront_without_vat {
      font-size: 12px;
      color: $default_blu;
      font-weight: bold;
    }
  }

  #product_final_installment_box {
    text-align: center;
    margin-bottom: 20px;

    label {
      @include phone_activation_label;
      color: $default_dark_color;
      font-size: 18px;
    }

    #product_final_installment {
      font-size: 28px;
      color: $default_blu;
      font-weight: bold;
    }
  }

  #monthly_description_container {
    height: 16px;
    padding: 0;
  }

  #mandatory_mnp {
    font-variant: small-caps;
    font-size: 15px;
  }
}

#data_composer {
  background-color: #fff;
  min-height: 1000px;

  .selector_title {
    @include phone_activation_label;
  }

  .selector_container_no_margin {
    margin-left: 0px;
    float: left;
  }

  .selector_container {
    margin-left: 20px;
    float: left;
    margin-bottom: 10px;

    .selector {
      border: solid 1px $default_grey;
      @include border_radius_6;
      float: left;
      padding: 5px;
      min-height: 62px;
      min-width: 200px !important;

      #phone_number_content {
        margin-top: 8px;
        text-align: center;
        font-size: 22px;
      }

      .checkboxes {
        float: left;
        margin-right: 10px;
      }

      #dealer_name {
        margin-top: 8px;
      }
    }

    input {
      margin-top: 10px;
    }

    select {
      margin-top: 8px;
    }
  }

  #operators, #customer_kinds {
    #operators_container, #customer_kinds_container {
      text-align:center;

      .error-container {
        text-align: center;
      }
    }
  }

  #customer_kinds {
    #customer_kinds_container {
      display: flex;
      justify-content: space-evenly;
      flex-wrap: wrap;

      .error-container {
        flex-basis: 100%;
        text-align: center;
      }
    }
  }

  #customer_kinds, #plan_categories, #do_number_portability {
    #do_number_portability_container {
      display: flex;
      justify-content: space-evenly;
      flex-wrap: wrap;

      .error-container {
        flex-basis: 100%;
        text-align: center;
      }
    }

    .customer_kind, .plan_category, .number_portability {
      float: left;
      height: 52px;
      font-size: 24px;
      text-align: center;
      padding: 0 15px;

      div {
        font-size: 12px;
      }

      &:hover, &.selected {
        color: $default_blu;
        cursor: pointer;

      }

      &.single_icon {
        float: none !important;
      }
    }
  }

  #product_categories, #renewables {
    .product_category, .renew_type {
      float: left;
      height: 52px;
      font-size: 24px;
      text-align: center;
      padding: 0 15px;

      div {
        font-size: 12px;
      }

      &:hover, &.selected {
        color: $default_blu;
        cursor: pointer;
      }

      &.single_icon {
        float: none !important;
      }
    }
  }

  #payment_methods {
    .payment_method {
      float: left;
      height: 52px;
      font-size: 24px;
      text-align: center;
      padding: 0 15px;

      div {
        font-size: 12px;
      }

      &:hover, &.selected {
        color: $default_blu;
        cursor: pointer;
      }

      &.single_icon {
        float: none !important;
      }
    }
  }

  #note {
    width: 600px;

    .selector {
      width: 100%;
    }
  }

  .actions {
    border-top: solid 1px $default_grey;
    padding: 10px 0;
    margin-bottom: 200px;
  }

  .pdf-links-container {
    display: flex;
    position: absolute;
    bottom: 0;
    margin-top: 10px;
  }

  .product-box-selector {
    display: flex;
  }

  #device-details-container {
    border-left: 1px solid;
    margin-left: 5px;
    padding-left: 5px;

    i {
      font-size: 14px;
    }

    span {
      font-size: 10px;
    }
  }
}

#customer_details {
  width: 810px;
  margin: auto;
}

#activation-label-preview-button {
  .btn {
    margin-bottom: 16px;
  }
}

#renew_notification_form .renew_field {
  padding-top: 7px;
}

.highlight-note {
  background-color: #FCEFA1;
}

.stored_plan_info {
  margin: 3px 0 0 3px;
  font-size: 15px;
  border: none;
}

.stored_plan_modal {
  border-left: none !important;
  height: auto !important;
}

.popover-sp {
  position: absolute;
  top: 0px !important;
  left: 0;
  z-index: 1060;
  display: none;
  max-width: 300px;
  padding: 1px;
  text-align: left;
  white-space: normal;
  background-color: #fff;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, .2);
  border-radius: 6px;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, .2);
  box-shadow: 0 5px 10px rgba(0, 0, 0, .2);
}

.popover-sp.right {
  margin-left: 10px;
}

.popover-sp > .arrow,
.popover-sp > .arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}

.popover-sp > .arrow {
  border-width: 11px;
}

.popover-sp > .arrow:after {
  content: "";
  border-width: 10px;
}

.popover-sp.right > .arrow {
  top: 25px !important;
  left: -11px;
  margin-top: -11px;
  border-right-color: #999;
  border-right-color: rgba(0, 0, 0, .25);
  border-left-width: 0;
}

.popover-sp.right > .arrow:after {
  bottom: -10px;
  left: 1px;
  content: " ";
  border-right-color: #fff;
  border-left-width: 0;
}

.easycare {
  padding-bottom: 10px
}

.easycare p {
  font-size: 16px;
}

.easycare ul, .easycare ul li {
  list-style-type: disc;
  padding: 3px;
  margin-left: 7px
}

.easycare_info {
  color: $default_blu;
}

.bottom_actions {
  padding: 5px;
}

.note-info-btn {
  background: none;
  color: #dc3545;
  border: none !important;
  padding: 3px !important;
  font-size: 15px !important;
}

.row-line-top {
  @extend .line-top;
  margin-left: 0px !important;
  margin-right: 0px !important;
  padding: 10px 0 0 0;
}

.product_final_installment_header {
  font-size: 12px !important;
  font-weight: normal !important;
  color: #7D7D7D !important;
}

.phone_activation_complete_panel {
  min-height: 150px;

  .found {
    color: green;
    font-size: 20px;
  }

  .not-found {
    color: red;
    font-size: 20px;
    padding-right: 5px;
  }

  .error {
    color: $default_ko_color;
    font-size: 13px;
    font-weight: 500;
  }
}

#simulation-alert {
  color: #3c8dde;
  display: inline-block;
}

.btn.btn-gray {
  background-color: rgba(125, 125, 125, 0.38);
}

#detached_modem {
  color: #ff3333;
  padding-right: 1%;
}

.ko-reason-error-section {
  padding: 3px 3px 3px 3px;

  margin-top: 10px;

  .ajax-result {
    padding: 0;

    i {
      margin-top: 5px;
    }
  }

  select {
    margin-top: 5px;
    height: 30px
  }
}

#phone_activation_do_add_promo_device {
  max-width: 30%;
  margin-top: 10px;
}

.summary_link {
  color: gray;
  font-size: 11px;
}

.highlighted_activation {
  color: #77dd77;
}

#numberPortabilityForm {
  .field_with_errors {
    width: 100%
  }
}

#different_owner_container {
  fieldset {
    padding-bottom: 10px;
  }
  h5 {
    padding-bottom: 5px;
    border-bottom:solid 2px $default_grey;
  }
}

.acc_doc_row {
  display: flex;
  position: relative;
}

.acc_doc_delete{
  position: absolute;
  bottom: -14px;
  right: -21px;
}
