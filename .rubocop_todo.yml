# This configuration was generated by
# `rubocop --auto-gen-config`
# on 2018-08-01 09:03:37 +0200 using RuboCop version 0.56.0.
# The point is for the user to remove these configuration records
# one by one as the offenses are removed from the code base.
# Note that changes in the inspected code, or installation of new
# versions of RuboCop, may require this file to be generated again.

# Offense count: 4
# Cop supports --auto-correct.
# Configuration parameters: Include, TreatCommentsAsGroupSeparators.
# Include: **/*.gemfile, **/Gemfile, **/gems.rb
Bundler/OrderedGems:
  Exclude:
    - 'Gemfile'
    - 'vendor/gems/omniauth-mailup/example/Gemfile'

# Offense count: 2
# Cop supports --auto-correct.
# Configuration parameters: Include, TreatCommentsAsGroupSeparators.
# Include: **/*.gemspec
Gemspec/OrderedDependencies:
  Exclude:
    - 'vendor/gems/omniauth-mailup/omniauth-mailup.gemspec'

# Offense count: 110
# Cop supports --auto-correct.
Layout/ArrayAlignment:
  Exclude:
    - 'db/fixtures/00_provinces.rb'

# Offense count: 2
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyleAlignWith.
# SupportedStylesAlignWith: either, start_of_block, start_of_line
Layout/BlockAlignment:
  Exclude:
    - 'vendor/gems/omniauth-mailup/spec/omniauth/strategies/mailup_spec.rb'

# Offense count: 6
# Cop supports --auto-correct.
Layout/BlockEndNewline:
  Exclude:
    - 'app/models/insurance.rb'
    - 'spec/lib/notifications/add_number_rebinding_spec.rb'
    - 'spec/lib/operations/callbacks_spec.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, IndentOneStep, IndentationWidth.
# SupportedStyles: case, end
Layout/CaseIndentation:
  Exclude:
    - 'config/schedule.rb'

# Offense count: 2
# Cop supports --auto-correct.
Layout/ClosingParenthesisIndentation:
  Exclude:
    - 'lib/phone_activations/pda/pda_attributes_builder.rb'
    - 'spec/lib/ws/request_builders/prenc_request_builder_spec.rb'

# Offense count: 2
# Cop supports --auto-correct.
Layout/CommentIndentation:
  Exclude:
    - 'old_cap/Capfile'

# Offense count: 1
# Cop supports --auto-correct.
Layout/ElseAlignment:
  Exclude:
    - 'config/initializers/attiva_service.rb'

# Offense count: 4
# Cop supports --auto-correct.
Layout/EmptyLineAfterMagicComment:
  Exclude:
    - 'config/schedule.rb'
    - 'db/fixtures/00_customer_kinds.rb'
    - 'db/fixtures/00_provinces.rb'
    - 'vendor/gems/omniauth-mailup/omniauth-mailup.gemspec'

# Offense count: 1
# Cop supports --auto-correct.
Layout/EmptyLinesAroundArguments:
  Exclude:
    - 'spec/lib/ws/calls/prenc_substitution_credit_note_spec.rb'

# Offense count: 17
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: empty_lines, no_empty_lines
Layout/EmptyLinesAroundBlockBody:
  Exclude:
    - 'Guardfile'
    - 'config/routes.rb'
    - 'db/fixtures/00_dealer_category.rb'
    - 'old_cap/deploy.rb'
    - 'spec/factories/phone_activation_kinds.rb'
    - 'spec/helpers/product_categories_helper_spec.rb'
    - 'spec/lib/notifications/add_number_rebinding_spec.rb'
    - 'spec/lib/operations/callbacks_spec.rb'
    - 'spec/lib/phone_activation_kinds/purchase_spec.rb'
    - 'spec/lib/phone_activations/pda/pda_attributes_builder_spec.rb'
    - 'spec/lib/repositories/offer_instance_repository_spec.rb'
    - 'spec/lib/ws/view_objects/preft_fattura_prodotto_promo_view_object_spec.rb'
    - 'spec/lib/ws/ws_proxy_spec.rb'
    - 'spec/models/phone_activation_spec.rb'

# Offense count: 11
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: empty_lines, empty_lines_except_namespace, empty_lines_special, no_empty_lines, beginning_only, ending_only
Layout/EmptyLinesAroundClassBody:
  Exclude:
    - 'app/models/offer_instance.rb'
    - 'app/models/phone_call.rb'
    - 'app/presenters/payment_presenter.rb'
    - 'lib/notifications/add_number_rebinding.rb'
    - 'lib/operations/checks/common_check.rb'
    - 'lib/operations/checks/composite_task.rb'
    - 'lib/operations/checks/easycare_check.rb'
    - 'lib/operations/checks/fattura_prodotto_promo_check.rb'
    - 'lib/operations/checks/ndc_immediato_promo_check.rb'
    - 'lib/phone_activations/pda/pda_attributes_builder.rb'

# Offense count: 4
# Cop supports --auto-correct.
Layout/EmptyLinesAroundMethodBody:
  Exclude:
    - 'app/helpers/payment_method_details_helper.rb'
    - 'spec/features/payment_requests/index_spec.rb'

# Offense count: 5
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: empty_lines, empty_lines_except_namespace, empty_lines_special, no_empty_lines
Layout/EmptyLinesAroundModuleBody:
  Exclude:
    - 'app/helpers/operation_outcomes_helper.rb'
    - 'app/helpers/payment_method_details_helper.rb'
    - 'app/helpers/products_helper.rb'
    - 'app/models/concerns/operation_constants.rb'
    - 'app/services/document_upload_context_service.rb'

# Offense count: 6
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyleAlignWith, AutoCorrect, Severity.
# SupportedStylesAlignWith: keyword, variable, start_of_line
Layout/EndAlignment:
  Exclude:
    - 'app/controllers/fixed_line_details_controller.rb'
    - 'app/services/linkem_coverage.rb'
    - 'config/initializers/attiva_service.rb'
    - 'lib/data_composer/renew_data_composer.rb'
    - 'lib/fast_item_importer.rb'
    - 'lib/importers/cities_updater.rb'

# Offense count: 3
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: normal, rails
Layout/IndentationConsistency:
  Exclude:
    - 'spec/lib/repositories/payment_request_repository_spec.rb'
    - 'vendor/gems/omniauth-mailup/spec/omniauth/strategies/mailup_spec.rb'

# Offense count: 5
# Cop supports --auto-correct.
# Configuration parameters: Width, IgnoredPatterns.
Layout/IndentationWidth:
  Exclude:
    - 'app/models/phone_activation.rb'
    - 'app/presenters/payment_presenter.rb'
    - 'config/initializers/attiva_service.rb'
    - 'config/initializers/rollbar.rb'
    - 'config/initializers/wrap_parameters.rb'

# Offense count: 24
# Cop supports --auto-correct.
Layout/LeadingCommentSpace:
  Exclude:
    - 'config/application.rb'
    - 'config/deploy/production.rb'
    - 'config/deploy/staging.rb'
    - 'config/environments/development.rb'
    - 'db/fixtures/01_operations.rb'
    - 'db/fixtures/07_pin_recharge_size.rb'
    - 'db/fixtures/08_pin_recharge_size_infinity.rb'
    - 'lib/phone_activations/pda/pda_attributes_builder.rb'

# Offense count: 7
# Cop supports --auto-correct.
Layout/MultilineBlockLayout:
  Exclude:
    - 'app/models/insurance.rb'
    - 'spec/lib/notifications/add_number_rebinding_spec.rb'
    - 'spec/lib/operations/callbacks_spec.rb'
    - 'spec/models/payment_method_detail_spec.rb'

# Offense count: 2
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: symmetrical, new_line, same_line
Layout/MultilineMethodCallBraceLayout:
  Exclude:
    - 'db/fixtures/00_operators.rb'
    - 'spec/lib/ws/request_builders/prenc_request_builder_spec.rb'

# Offense count: 24
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, IndentationWidth.
# SupportedStyles: aligned, indented, indented_relative_to_receiver
Layout/MultilineMethodCallIndentation:
  Exclude:
    - 'app/controllers/products_controller.rb'
    - 'lib/repositories/phone_activation_repository.rb'
    - 'spec/lib/notifications/add_number_rebinding_spec.rb'
    - 'spec/lib/operations/callbacks_spec.rb'
    - 'app/services/shipment_importer.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, IndentationWidth.
# SupportedStyles: aligned, indented
Layout/MultilineOperationIndentation:
  Exclude:
    - 'app/services/missing_operations_service.rb'

# Offense count: 2
# Cop supports --auto-correct.
Layout/SpaceAfterColon:
  Exclude:
    - 'spec/lib/ws/request_builders/prenc_request_builder_spec.rb'
    - 'spec/models/phone_activation_spec.rb'

# Offense count: 158
# Cop supports --auto-correct.
Layout/SpaceAfterComma:
  Exclude:
    - 'config/initializers/geocoder.rb'
    - 'db/fixtures/00_comparator_category.rb'
    - 'db/fixtures/00_dealer_segmentation.rb'
    - 'db/fixtures/00_job_kind.rb'
    - 'db/fixtures/00_ko_reasons.rb'
    - 'db/fixtures/00_offer_categories.rb'
    - 'db/fixtures/00_operator_numbers.rb'
    - 'db/fixtures/00_operators_position_update.rb'
    - 'db/fixtures/10_warehouse_brands.rb'
    - 'lib/data_composer/offer_data_composer.rb'
    - 'lib/data_composer/renew_data_composer.rb'
    - 'old_cap/deploy.rb'

# Offense count: 412
# Cop supports --auto-correct.
# Configuration parameters: AllowForAlignment.
Layout/SpaceAroundOperators:
  Exclude:
    - 'app/controllers/products_controller.rb'
    - 'app/helpers/application_helper.rb'
    - 'app/helpers/product_categories_helper.rb'
    - 'app/presenters/payment_request_presenter.rb'
    - 'config/application.rb'
    - 'config/environments/development.rb'
    - 'config/environments/staging.rb'
    - 'config/initializers/fog.rb'
    - 'db/fixtures/00_operators.rb'
    - 'db/fixtures/00_provinces.rb'
    - 'db/fixtures/02_internal_users.rb'

# Offense count: 27
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, EnforcedStyleForEmptyBraces.
# SupportedStyles: space, no_space
# SupportedStylesForEmptyBraces: space, no_space
Layout/SpaceBeforeBlockBraces:
  Exclude:
    - 'spec/features/payment_requests/create_from_phone_activation_spec.rb'
    - 'spec/lib/phone_activation_kinds/purchase_spec.rb'
    - 'spec/models/phone_activation_spec.rb'
    - 'test/models/add_product_to_cart_test.rb'
    - 'vendor/gems/omniauth-mailup/omniauth-mailup.gemspec'

# Offense count: 1
# Cop supports --auto-correct.
Layout/SpaceBeforeComma:
  Exclude:
    - 'db/fixtures/00_ko_reasons.rb'

# Offense count: 1
# Cop supports --auto-correct.
Layout/SpaceBeforeComment:
  Exclude:
    - 'config/initializers/geocoder.rb'

# Offense count: 2
# Cop supports --auto-correct.
Layout/SpaceBeforeSemicolon:
  Exclude:
    - 'old_cap/deploy.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: require_no_space, require_space
Layout/SpaceInLambdaLiteral:
  Exclude:
    - 'config/routes.rb'

# Offense count: 16
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, EnforcedStyleForEmptyBrackets.
# SupportedStyles: space, no_space, compact
# SupportedStylesForEmptyBrackets: space, no_space
Layout/SpaceInsideArrayLiteralBrackets:
  Exclude:
    - 'config/initializers/devise.rb'
    - 'config/initializers/ransack.rb'
    - 'db/fixtures/00_marital_status.rb'
    - 'db/fixtures/10_warehouse_brands.rb'

# Offense count: 12
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, EnforcedStyleForEmptyBraces, SpaceBeforeBlockParameters.
# SupportedStyles: space, no_space
# SupportedStylesForEmptyBraces: space, no_space
Layout/SpaceInsideBlockBraces:
  Exclude:
    - 'app/models/phone_activation.rb'
    - 'lib/operations/checks/operations_to_remove_checks.rb'
    - 'spec/helpers/product_categories_helper_spec.rb'
    - 'spec/lib/ws/request_builders/prenc_request_builder_spec.rb'
    - 'spec/models/phone_activation_spec.rb'

# Offense count: 96
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, EnforcedStyleForEmptyBraces.
# SupportedStyles: space, no_space, compact
# SupportedStylesForEmptyBraces: space, no_space
Layout/SpaceInsideHashLiteralBraces:
  Exclude:
    - 'config/initializers/fog.rb'
    - 'db/fixtures/00_family_products.rb'
    - 'db/fixtures/00_italy_purchases.rb'
    - 'db/fixtures/00_italy_sales.rb'
    - 'db/fixtures/00_operators.rb'
    - 'db/fixtures/00_provinces.rb'
    - 'lib/data_composer/renew_data_composer.rb'

# Offense count: 2
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: space, no_space
Layout/SpaceInsideParens:
  Exclude:
    - 'db/legacy/database_cleanup.rb'
    - 'lib/jobs/prenc_job.rb'

# Offense count: 2
# Cop supports --auto-correct.
Layout/SpaceInsidePercentLiteralDelimiters:
  Exclude:
    - 'config/initializers/assets.rb'

# Offense count: 8
# Cop supports --auto-correct.
# Configuration parameters: IndentationWidth.
Layout/IndentationStyle:
  Exclude:
    - 'db/fixtures/00_italy_sales.rb'
    - 'db/fixtures/00_operator_numbers.rb'
    - 'vendor/gems/omniauth-mailup/spec/omniauth/strategies/mailup_spec.rb'

# Offense count: 69
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: final_newline, final_blank_line
Layout/TrailingEmptyLines:
  Enabled: false

# Offense count: 24
# Cop supports --auto-correct.
# Configuration parameters: AllowInHeredoc.
Layout/TrailingWhitespace:
  Exclude:
    - 'app/models/concerns/bundle_unpackable.rb'
    - 'db/fixtures/00_operators.rb'
    - 'db/legacy/database_cleanup.rb'
    - 'old_cap/deploy/production.rb'
    - 'spec/factories/products.rb'
    - 'spec/models/payments/hipay_spec.rb'
    - 'spec/services/operator_service_spec.rb'
    - 'vendor/gems/omniauth-mailup/lib/omniauth/strategies/mailup.rb'
    - 'vendor/gems/omniauth-mailup/omniauth-mailup.gemspec'
    - 'vendor/gems/omniauth-mailup/spec/omniauth/strategies/mailup_spec.rb'

# Offense count: 90
Lint/AmbiguousBlockAssociation:
  Enabled: false

# Offense count: 2
Lint/BooleanSymbol:
  Exclude:
    - 'app/models/fixed_line_detail.rb'

# Offense count: 7
Lint/DuplicateMethods:
  Exclude:
    - 'app/decorators/financing_decorator.rb'
    - 'app/models/document.rb'
    - 'app/models/massive_item.rb'
    - 'app/models/operation_outcome.rb'
    - 'app/services/attiva_service/data_store_decorator.rb'
    - 'lib/data_composer/data_filter.rb'
    - 'lib/ws/view_objects/ftdiffc_view_object.rb'

# Offense count: 5
Lint/EmptyWhen:
  Exclude:
    - 'app/controllers/store/order_fulfillment_controller.rb'
    - 'app/services/attiva_service/common_updater.rb'

# Offense count: 17
Lint/IneffectiveAccessModifier:
  Exclude:
    - 'app/models/internal_user_phone_number.rb'
    - 'app/models/warehouse.rb'
    - 'app/services/item_event_service.rb'
    - 'lib/importers/reversal_importer.rb'
    - 'lib/jobs/ftdiffc_financing_job.rb'
    - 'lib/jobs/preft_phone_activation_job.rb'
    - 'lib/jobs/register_invoice_payment_job.rb'
    - 'lib/operations/checks/easycare_check.rb'
    - 'lib/operations/checks/end_user_invoice_check.rb'
    - 'lib/operations/checks/fattura_prodotto_promo_check.rb'
    - 'lib/operations/checks/free_change_check.rb'
    - 'lib/operations/checks/ndc_immediato_promo_check.rb'
    - 'lib/repositories/offer_instance_repository.rb'

# Offense count: 1
Lint/MissingSuper:
  Exclude:
    - 'app/models/paymat_setting.rb'

# Offense count: 1
# Cop supports --auto-correct.
Lint/PercentSymbolArray:
  Exclude:
    - 'lib/data_composer/product_data_composer.rb'

# Offense count: 47
Lint/RescueException:
  Enabled: false

# Offense count: 2
# Configuration parameters: Whitelist.
# Whitelist: present?, blank?, presence, try, try!
Lint/SafeNavigationChain:
  Exclude:
    - 'lib/invoice_invitations/ii_row_csv_builder.rb'
    - 'lib/phone_activation_kinds/common_phone_activation_kind.rb'

# Offense count: 2
# Cop supports --auto-correct.
Lint/ScriptPermission:
  Exclude:
    - 'Rakefile'
    - 'db/utils/post-checkout'

# Offense count: 9
Lint/ShadowingOuterLocalVariable:
  Exclude:
    - 'app/decorators/order_decorator.rb'
    - 'app/models/offer_instance.rb'
    - 'app/services/attiva_service/product_availability_importer.rb'
    - 'lib/data_composer/data_filter.rb'
    - 'lib/repositories/offer_instance_repository.rb'
    - 'lib/util/pdf_handler.rb'

# Offense count: 7
# Cop supports --auto-correct.
# Configuration parameters: IgnoreEmptyBlocks, AllowUnusedKeywordArguments.
Lint/UnusedBlockArgument:
  Exclude:
    - 'config/initializers/simple_form.rb'
    - 'config/initializers/thredded.rb'
    - 'db/fixtures/00_thread_kinds.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: AllowUnusedKeywordArguments, IgnoreEmptyMethods.
Lint/UnusedMethodArgument:
  Exclude:

# Offense count: 12
# Configuration parameters: ContextCreatingMethods, MethodCreatingMethods.
Lint/UselessAccessModifier:
  Exclude:
    - 'app/models/internal_user_phone_number.rb'
    - 'app/services/item_event_service.rb'
    - 'lib/jobs/ftdiffc_financing_job.rb'
    - 'lib/jobs/preft_phone_activation_job.rb'
    - 'lib/jobs/register_invoice_payment_job.rb'
    - 'lib/operations/checks/easycare_check.rb'
    - 'lib/operations/checks/end_user_invoice_check.rb'
    - 'lib/operations/checks/fattura_prodotto_promo_check.rb'
    - 'lib/operations/checks/free_change_check.rb'
    - 'lib/operations/checks/ndc_immediato_promo_check.rb'
    - 'lib/phone_activations/steps/step.rb'
    - 'lib/repositories/offer_instance_repository.rb'

# Offense count: 66
Lint/UselessAssignment:
  Enabled: false

# Offense count: 144
Metrics/PerceivedComplexity:
  Max: 147

# Offense count: 12
Naming/ConstantName:
  Exclude:
    - 'app/models/customer.rb'
    - 'app/models/offer_instance.rb'
    - 'app/models/order.rb'
    - 'app/models/plan.rb'
    - 'lib/modules/bost/level/base.rb'

# Offense count: 4
# Configuration parameters: EnforcedStyle.
# SupportedStyles: lowercase, uppercase
Naming/HeredocDelimiterCase:
  Exclude:
    - 'spec/features/warehouses/create_warehouse_spec.rb'
    - 'spec/features/warehouses/edit_warehouse_spec.rb'
    - 'spec/support/geocoding_helper.rb'

# Offense count: 6
Naming/MemoizedInstanceVariableName:
  Exclude:
    - 'app/models/attiva_orders_response.rb'
    - 'app/models/phone_activation.rb'
    - 'app/services/order_fulfillment/request_order_attiva.rb'
    - 'lib/ws/financial_report_service.rb'

# Offense count: 50
# Configuration parameters: NamePrefix, NamePrefixBlacklist, NameWhitelist, MethodDefinitionMacros.
# NamePrefix: is_, has_, have_
# NamePrefixBlacklist: is_, has_, have_
# NameWhitelist: is_a?
# MethodDefinitionMacros: define_method, define_singleton_method
Naming/PredicateName:
  Enabled: false

# Offense count: 7
# Configuration parameters: MinNameLength, AllowNamesEndingInNumbers, AllowedNames, ForbiddenNames.
# AllowedNames: io, id, to, by, on, in, at
Naming/MethodParameterName:
  Exclude:
    - 'app/controllers/phone_activations_controller.rb'
    - 'app/helpers/phone_activations_helper.rb'
    - 'app/mailers/notification_mailer.rb'    
    - 'lib/jobs/phone_call_reimporter_job.rb'

# Offense count: 2
# Configuration parameters: EnforcedStyle.
# SupportedStyles: snake_case, normalcase, non_integer
Naming/VariableNumber:
  Exclude:
    - 'spec/models/ast_edit_payment_type_spec.rb'
    - 'spec/services/thread_kind_service_spec.rb'

# Offense count: 2
# Cop supports --auto-correct.
# Configuration parameters: MaxKeyValuePairs.

# Offense count: 6
Security/Eval:
  Exclude:
    - 'app/controllers/ko_reasons_controller.rb'
    - 'app/controllers/warehouses/items_controller.rb'
    - 'app/models/customer.rb'

# Offense count: 4
Security/Open:
  Exclude:
    - 'app/controllers/application_controller.rb'
    - 'app/controllers/documents_controller.rb'
    - 'app/controllers/store/orders_controller.rb'
    - 'spec/support/sunspot.rb'

# Offense count: 1
# Cop supports --auto-correct.
Security/YAMLLoad:
  Exclude:
    - 'db/utils/post-checkout'

# Offense count: 11
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, ProceduralMethods, FunctionalMethods, IgnoredMethods.
# SupportedStyles: line_count_based, semantic, braces_for_chaining
# ProceduralMethods: benchmark, bm, bmbm, create, each_with_object, measure, new, realtime, tap, with_object
# FunctionalMethods: let, let!, subject, watch
# IgnoredMethods: lambda, proc, it
Style/BlockDelimiters:
  Exclude:
    - 'app/models/option_compatibility.rb'
    - 'lib/modules/bost/duplicatable.rb'
    - 'old_cap/deploy.rb'
    - 'spec/lib/notifications/add_number_rebinding_spec.rb'
    - 'spec/lib/operations/callbacks_spec.rb'
    - 'spec/models/payment_method_detail_spec.rb'

# Offense count: 3
# Cop supports --auto-correct.
# Configuration parameters: PreferredMethods.
Style/CollectionMethods:
  Exclude:
    - 'app/helpers/payment_method_details_helper.rb'
    - 'config/initializers/string.rb'
    - 'lib/phone_activations/pda/pda_attributes_builder.rb'

# Offense count: 1
Style/CommentedKeyword:
  Exclude:
    - 'app/models/internal_user_phone_number.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, SingleLineConditionsOnly, IncludeTernaryExpressions.
# SupportedStyles: assign_to_condition, assign_inside_condition
Style/ConditionalAssignment:
  Exclude:
    - 'Guardfile'

# Offense count: 55
Style/DateTime:
  Enabled: false

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: compact, expanded
Style/EmptyMethod:
  Exclude:
    - 'app/helpers/phone_activations_helper.rb'
    - '**/**'

# Offense count: 16
# Cop supports --auto-correct.
Style/ExpandPathArguments:
  Exclude:
    - 'Rakefile'
    - 'bin/bundle'
    - 'bin/rails'
    - 'bin/rake'
    - 'bin/rspec'
    - 'bin/setup'
    - 'config/application.rb'
    - 'config/boot.rb'
    - 'config/environment.rb'
    - 'config/puma.rb'
    - 'private_pub.ru'
    - 'script/rails'
    - 'vendor/gems/omniauth-mailup/omniauth-mailup.gemspec'
    - 'vendor/gems/omniauth-mailup/spec/spec_helper.rb'

# Offense count: 3
# Configuration parameters: EnforcedStyle.
# SupportedStyles: annotated, template, unannotated
Style/FormatStringToken:
  Exclude:
    - 'app/models/phone_activation_phone_number_presenter.rb'
    - 'spec/models/phone_activation_portfolio_code_spec.rb'

# Offense count: 22
Style/IdenticalConditionalBranches:
  Exclude:
    - 'app/controllers/after_sale_threads_controller.rb'
    - 'app/controllers/documents_controller.rb'
    - 'app/controllers/invoice_invitations_controller.rb'
    - 'app/controllers/offers_controller.rb'
    - 'app/controllers/payment_method_details_controller.rb'
    - 'app/controllers/payment_requests_controller.rb'
    - 'app/controllers/phone_activations_controller.rb'
    - 'app/controllers/store/orders_controller.rb'

# Offense count: 26
Style/IfInsideElse:
  Enabled: false

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: InverseMethods, InverseBlocks.
Style/InverseMethods:
  Exclude:
    - 'app/services/missing_operations_service.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: IgnoredMethods.
Style/MethodCallWithoutArgsParentheses:
  Exclude:
    - 'lib/phone_activations/pda/pda_attributes_builder.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: require_parentheses, require_no_parentheses, require_no_parentheses_except_multiline
Style/MethodDefParentheses:
  Exclude:
    - 'config/initializers/sunspot.rb'

# Offense count: 1
Style/MissingRespondToMissing:
  Exclude:
    - 'app/models/paymat_setting.rb'

# Offense count: 9
Style/MixinUsage:
  Exclude:
    - 'spec/features/products/moderate_product_spec.rb'
    - 'spec/features/user_roles/all_user_roles_can_login_spec.rb'
    - 'spec/helpers/application_helper_spec.rb'
    - 'spec/helpers/phone_activation_helper_spec.rb'
    - 'spec/models/phone_call_accessible_by_spec.rb'
    - 'spec/models/phone_call_spec.rb'
    - 'spec/rails_helper.rb'
    - 'spec/services/reversal_service_spec.rb'

# Offense count: 4
Style/MultilineTernaryOperator:
  Exclude:
    - 'app/controllers/phone_activations_controller.rb'
    - 'lib/access/dealer_access_handler.rb'
    - 'lib/operations/callbacks.rb'
    - 'lib/repositories/reports_generic_repository.rb'

# Offense count: 2
# Cop supports --auto-correct.
Style/MutableConstant:
  Exclude:
    - 'db/fixtures/00_provinces.rb'
    - 'vendor/gems/omniauth-mailup/lib/omniauth-mailup/version.rb'

# Offense count: 2
Style/NestedTernaryOperator:
  Exclude:
    - 'app/models/cost_matrix.rb'
    - 'app/models/offer_instance.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: IncludeSemanticChanges.
Style/NonNilCheck:
  Exclude:
    - 'config/initializers/string.rb'

# Offense count: 68
# Cop supports --auto-correct.
# Configuration parameters: AutoCorrect, EnforcedStyle.
# SupportedStyles: predicate, comparison
Style/NumericPredicate:
  Enabled: false

# Offense count: 1
Style/OptionalArguments:
  Exclude:
    - 'app/helpers/offer_instances_helper.rb'

# Offense count: 3
# Cop supports --auto-correct.
Style/RedundantParentheses:
  Exclude:
    - 'config/initializers/to_bool.rb'
    - 'lib/ws/view_objects/prenc_nota_di_credito_immediato_promo_view_object.rb'

# Offense count: 2
# Cop supports --auto-correct.
# Configuration parameters: AllowMultipleReturnValues.
Style/RedundantReturn:
  Exclude:
    - 'app/models/offer.rb'

# Offense count: 3
# Cop supports --auto-correct.
Style/RedundantSelf:
  Exclude:
    - 'app/models/payment_request.rb'
    - 'config/initializers/string.rb'
    - 'config/initializers/to_bool.rb'

# Offense count: 5
# Cop supports --auto-correct.
Style/RescueModifier:
  Exclude:
    - 'config/initializers/string.rb'
    - 'config/puma.rb'
    - 'old_cap/deploy.rb'
    - 'vendor/gems/omniauth-mailup/example/config.ru'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: ConvertCodeThatCanStartToReturnNil, Whitelist.
# Whitelist: present?, blank?, presence, try, try!
Style/SafeNavigation:
  Exclude:
    - 'db/fixtures/04_peoplesoft.rb'

# Offense count: 42
# Cop supports --auto-correct.
# Configuration parameters: AllowAsExpressionSeparator.
Style/Semicolon:
  Enabled: false

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: require_parentheses, require_no_parentheses
Style/StabbyLambdaParentheses:
  Exclude:
    - 'config/routes.rb'

# Offense count: 8
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, ConsistentQuotesInMultiline.
# SupportedStyles: single_quotes, double_quotes
Style/StringLiterals:
  Exclude:
    - 'bin/rails'
    - 'bin/rake'
    - 'bin/spring'

# Offense count: 2
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle.
# SupportedStyles: single_quotes, double_quotes
Style/StringLiteralsInInterpolation:
  Exclude:
    - 'vendor/gems/omniauth-mailup/example/config.ru'

# Offense count: 2
Style/StructInheritance:
  Exclude:
    - 'lib/importers/cities_updater.rb'
    - 'lib/jobs/operation_outcome_job.rb'

# Offense count: 40
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, MinSize.
# SupportedStyles: percent, brackets
Style/SymbolArray:
  Enabled: false

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: IgnoredMethods.
# IgnoredMethods: respond_to, define_method
Style/SymbolProc:
  Exclude:
    - 'config/initializers/string.rb'

# Offense count: 16
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyle, AllowSafeAssignment.
# SupportedStyles: require_parentheses, require_no_parentheses, require_parentheses_when_complex
Style/TernaryParentheses:
  Exclude:
    - 'lib/phone_activations/pda/pda_attributes_builder.rb'

# Offense count: 1
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyleForMultiline.
# SupportedStylesForMultiline: comma, consistent_comma, no_comma
Style/TrailingCommaInArguments:
  Exclude:
    - 'lib/phone_activations/pda/pda_attributes_builder.rb'

# Offense count: 11
# Cop supports --auto-correct.
# Configuration parameters: EnforcedStyleForMultiline.
# SupportedStylesForMultiline: comma, consistent_comma, no_comma
Style/TrailingCommaInArrayLiteral:
  Exclude:
    - 'db/fixtures/00_contract_kinds.rb'
    - 'db/fixtures/00_credit_card_kinds.rb'
    - 'db/fixtures/00_offer_categories.rb'
    - 'db/fixtures/00_payment_methods.rb'
    - 'db/fixtures/00_product_categories.rb'
    - 'db/fixtures/00_service_kinds.rb'
    - 'db/fixtures/00_thread_kinds.rb'
    - 'db/fixtures/00_view_warehouses.rb'
    - 'db/fixtures/09_pin_recharges_operator.rb'
    - 'db/legacy/database_cleanup.rb'
    - 'test/integration/phone_number_test.rb'

# Offense count: 5
# Cop supports --auto-correct.
Style/RedundantInterpolation:
  Exclude:
    - 'app/helpers/operation_outcomes_helper.rb'
    - 'config/initializers/simple_form.rb'
    - 'lib/phone_activations/pda/pda_attributes_builder.rb'

# Offense count: 2
# Cop supports --auto-correct.
Style/RedundantPercentQ:
  Exclude:
    - 'vendor/gems/omniauth-mailup/omniauth-mailup.gemspec'
