require 'rails_helper'

describe Jobs::WindTreCbDatasImporterJob do
  let(:file_path) { Rails.root.join('spec', 'fixtures', 'importers', 'wind_tre_cb_datas', 'wind_tre_cb_datas.csv') }
  let(:file_missing_headers_path)  { Rails.root.join('spec', 'fixtures', 'importers', 'wind_tre_cb_datas', 'wind_tre_cb_datas_missing_headers.csv') }
  let(:file_different_months_path) { Rails.root.join('spec', 'fixtures', 'importers', 'wind_tre_cb_datas', 'wind_tre_cb_datas_different_months.csv') }
  let(:file_misformats_path)       { Rails.root.join('spec', 'fixtures', 'importers', 'wind_tre_cb_datas', 'wind_tre_cb_datas_misformats.csv') }

  let(:job_file_path) { Rails.root.join('spec', 'fixtures', 'importers', 'wind_tre_cb_datas', 'wind_tre_cb_datas_job.csv') }

  subject { described_class.new(file_path: job_file_path) }

  after { FileUtils.rm_rf job_file_path }

  it { expect(subject).not_to be_nil }

  context '#perform' do
    let!(:warehouse) { create(:warehouse) }

    context 'success' do
      before { FileUtils.cp(file_path, job_file_path) }

      it 'sends a notification email' do
        expect { subject.perform }.to change {
          WindTreCbDatasImporterMailer.deliveries.select do |email|
            email.subject == "Importazione #{job_file_path.basename} avvenuta con successo"
          end.count
        }.by(1)
        expect { subject.perform }.to change { ImporterLog.count }.by(1)
      end
    end

    context 'failure' do
      context 'file with missing headers' do
        before { FileUtils.cp(file_missing_headers_path, job_file_path) }

        it 'sends a notification email' do
          expect { subject.perform }.to change {
            WindTreCbDatasImporterMailer.deliveries.select do |email|
              email.subject == "Errore importazione #{job_file_path.basename}"
            end.count
          }.by(1)
          expect { subject.perform }.to change { ImporterLog.count }.by(1)
        end
      end

      context 'file with different months' do
        before { FileUtils.cp(file_different_months_path, job_file_path) }

        it 'sends a notification email' do
          expect { subject.perform }.to change {
            WindTreCbDatasImporterMailer.deliveries.select do |email|
              email.subject == "Errore importazione #{job_file_path.basename}"
            end.count
          }.by(1)
          expect { subject.perform }.to change { ImporterLog.count }.by(1)
        end
      end

      context 'file with wrong data formats' do
        before { FileUtils.cp(file_misformats_path, job_file_path) }

        it 'sends a notification email' do
          expect { subject.perform }.to change {
            WindTreCbDatasImporterMailer.deliveries.select do |email|
              email.subject == "Errore importazione #{job_file_path.basename}"
            end.count
          }.by(1)
          expect { subject.perform }.to change { ImporterLog.count }.by(1)
        end
      end
    end
  end
end
