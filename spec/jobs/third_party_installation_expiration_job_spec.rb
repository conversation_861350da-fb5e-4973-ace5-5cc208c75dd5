require 'rails_helper'

describe Jobs::ThirdPartyInstallationExpirationJob do
  let(:phone_activation) { create(:phone_activation, :with_fixed_line_detail) }
  let(:appointment_detail) { create(:appointment_detail, status: :waiting, phone_activation: phone_activation) }
  let(:new_installation_alert_message) { create(:alert_message, :new_installation_request) }
  let!(:alert) { create(:alert, :with_dealer_and_warehouse, alert_message: new_installation_alert_message, alertable: appointment_detail, dealer: appointment_detail.dealer) }
  let(:appointment_service) { double }

  subject { described_class.new(appointment_detail.id, false).perform }

  context 'do nothing if appointment is not in waiting status' do
    before { appointment_detail.update_columns(status: 'draft') }

    it do
      subject

      expect(alert.reload.archived?).to be_falsey
      expect(appointment_detail.reload.status).to eq 'draft'
    end
  end

  context 'call with new jwt token appointments' do
    let(:first_available_dealer) { create(:dealer, name: 'First available dealer') }
    let(:second_available_dealer) { create(:dealer, name: 'Second available dealer') }

    before do
      allow_any_instance_of(AppointmentDetail).to receive(:dealers_available_for_installation).and_return [first_available_dealer, second_available_dealer]
      Timecop.freeze Time.now
    end
    after { Timecop.return }

    context 'called when appointment is assigned to first dealer' do
      it do
        dealer_id = appointment_detail.dealer_id
        expect(AppointmentDetailService).to receive(:new).with(appointment_detail).and_return(appointment_service)
        expect(appointment_service).to receive(:notify_new_installation_to).with([first_available_dealer, second_available_dealer], 3)
        subject

        expect(alert.reload.archived?).to be_truthy
        expect(appointment_detail.reload.status).to eq 'waiting'
        expect(appointment_detail.refused_dealers.count).to eq 1
        expect(appointment_detail.refused_dealers.first['dealer_id']).to eq dealer_id
        expect(appointment_detail.refused_dealers.first['expired']).to be_truthy
        expect(appointment_detail.refused_dealers.first['refused_at']).not_to be_nil
      end
    end

    context 'called when massive assignment is in progress' do
      let!(:first_dealer_alert) { create(:alert, :with_dealer_and_warehouse, alert_message: new_installation_alert_message, alertable: appointment_detail, dealer: first_available_dealer) }
      let!(:second_dealer_alert) { create(:alert, :with_dealer_and_warehouse, alert_message: new_installation_alert_message, alertable: appointment_detail, dealer: second_available_dealer) }

      before { appointment_detail.rejected_by(appointment_detail.dealer_id) }

      subject { described_class.new(appointment_detail.id, true).perform }

      it do
        expect(AppointmentDetailMailer).to receive_message_chain(:to_be_reassigned, :deliver_now)
        subject

        expect(appointment_detail.reload.dealer_id).to be_nil
        expect(alert.reload.archived?).to be_truthy
        expect(appointment_detail.reload.status).to eq 'waiting'
        expect(appointment_detail.refused_dealers.count).to eq 3
        expect(appointment_detail.refused_dealers.second['dealer_id']).to eq first_available_dealer.id
        expect(appointment_detail.refused_dealers.second['expired']).to be_truthy
        expect(appointment_detail.refused_dealers.second['refused_at']).not_to be_nil
        expect(appointment_detail.refused_dealers.third['dealer_id']).to eq second_available_dealer.id
        expect(appointment_detail.refused_dealers.third['expired']).to be_truthy
        expect(appointment_detail.refused_dealers.third['refused_at']).not_to be_nil
      end
    end
  end
end