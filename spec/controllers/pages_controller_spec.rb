# encoding: utf-8

require "rails_helper"

describe PagesController, type: :controller do
  let!(:admin) { create(:user, :admin) }

  before do
    stub_authentication admin
  end

  describe "#new" do
    it "assigns access flags" do
      get :new
      expect(assigns(:access_flags)).not_to be_nil
    end
  end

  context "#create" do
    context "when passing empty form values" do
      before { create(:dealer_category, :standard_dealer) }

      let(:create_params) do
        {
          utf8: "✓",
          page: {
            "title"               => "titolo",
            "subtitle"            => "",
            "published"           => "0",
            "cms_template_id"     => "1",
            "body"                => "",
            "dealer_category_ids" => [""],
            "operator_ids"        => [""],
            "access_flag_ids"     => [""]
          },
          "commit" => "Salva"
        }
      end

      it "doesnt crash" do
        post :create, params: create_params
        expect(response.code).to eq "422"
      end
    end
  end

  context '#home_page' do
    let!(:dealer_user) { create(:user, :dealer_user, :with_dealer_contact) }

    before { stub_authentication dealer_user.reload }

    context 'standard dealer' do
      it do
        get :home_page
        expect(response).to render_template('pages/news')
      end
    end

    context 'md_very dealer' do
      before { dealer_user.dealer.update_columns(kind: 'md_very') }

      it do
        allow(HomeMdVeryBox).to receive(:all_valid?).and_return true

        get :home_page
        expect(response).to render_template('pages/home_md_very')
      end
    end

    context 'tls dealer' do
      before { dealer_user.dealer.update_columns(kind: 'operator_tls') }

      it do
        allow(dealer_user).to receive(:can?).and_return true

        get :home_page
        expect(response).to redirect_to(new_phone_activation_path)
      end

      it 'redirect to alerts if dealer not have ability to activations' do
        get :home_page
        expect(response).to redirect_to(alerts_path)
      end
    end

    context 'franchising' do
      before { dealer_user.dealer.update_columns(kind: 'franchising') }

      it 'without lockme access flag' do
        allow(dealer_user).to receive(:can?).and_return true

        get :home_page
        expect(response).to redirect_to(dealer_path(dealer_user.dealer))
      end

      it 'with lockme access flag' do
        dealer_user.dealer_contact.access_flags << create(:access_flag, :access_imei_reservations)

        get :home_page
        expect(response).to redirect_to(new_imei_reservation_path)
      end
    end

    context 'gallery_gd' do
      before { dealer_user.dealer.update_columns(kind: 'gallery_gd') }

      it 'without lockme access flag' do
        allow(dealer_user).to receive(:can?).and_return true

        get :home_page
        expect(response).to redirect_to(alerts_path)
      end

      it 'with lockme access flag' do
        dealer_user.dealer_contact.access_flags << create(:access_flag, :access_imei_reservations)

        get :home_page
        expect(response).to redirect_to(new_imei_reservation_path)
      end
    end
  end

  context "#sort" do
    it "save new sorting" do
      page1 = create(:page, position: 0)
      page2 = create(:page, position: 1)

      post :sort, params: { pages: [page2.id.to_s, page1.id.to_s] }

      expect(response.code).to eq "200"
      page1.reload
      page2.reload
      expect(page1.position).to eq(1)
      expect(page2.position).to eq(0)
    end
  end

  context "#sort_posts" do
    let(:posts_per_page) { 3 }
    let(:total_post_no) { 12 }
    let(:cms_page) { stub_model(Page) }

    let!(:cms_page_posts) do
      random_positions = (0...total_post_no).to_a.shuffle

      (0...total_post_no).map do |i|
        post = create(:cms_post, page_id: cms_page.id)
        post.update_column(:position, random_positions[i])
        post
      end
    end

    let(:other_page_post) do
      create(:cms_post, page_id: cms_page.id + 1, position: 123)
    end

    let(:posts_in_page_3) do
      (0...posts_per_page).map do |i|
        cms_page_posts.detect do |post|
          post.position == 2 * posts_per_page + i && post.page_id = cms_page.id
        end
      end
    end

    let(:shuffled_posts) { [posts_in_page_3.second, posts_in_page_3.first, posts_in_page_3.third] }

    before { post :sort_posts, params: { cms_posts: shuffled_posts.map(&:id).map(&:to_s) } }

    it "re-sorts posts of a certain (Cms) page after one is dragged off in a (pagination) page in the middle" do
      expect(posts_in_page_3.second.reload.position).to eq 6
      expect(posts_in_page_3.first.reload.position).to eq 7
      expect(posts_in_page_3.third.reload.position).to eq 8
    end

    it "doesn't affect posts relative to other CmsPages" do
      expect(other_page_post.reload.position).to eq 123
    end
  end
end
