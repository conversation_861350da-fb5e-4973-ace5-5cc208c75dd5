require 'rails_helper'

RSpec.describe InvoicePaymentsController, type: :controller do
  let!(:admin) { create(:user, :admin) }
  let(:dealer) { create(:dealer) }
  let(:operator_tre) { create(:operator, :tre) }
  let(:valid_attributes) do
    {
      dealer_id:      dealer.id,
      invoice_date:   '2016-09-21',
      invoice_number: '53',
      note:           '',
      tipo_iva:       '22',
      tipo_ritenuta:  '11.5',
      sent_to_gamma:  true
    }
  end

  before do
    dealer.operators << operator_tre
    stub_authentication admin
  end

  describe 'GET #index' do
    it 'assigns all invoice_payments as @invoice_payments' do
      invoice_payment = InvoicePayment.create! valid_attributes
      get :index, params: {}
      expect(assigns(:invoice_payments)).to eq([invoice_payment])
    end
  end

  describe 'GET #edit' do
    it 'assigns the requested invoice_payment as @invoice_payment' do
      invoice_payment = InvoicePayment.create! valid_attributes
      get :edit, params: { id: invoice_payment.to_param }
      expect(assigns(:invoice_payment)).to eq(invoice_payment)
    end
  end

  describe 'PUT #update' do
    context 'with valid params' do
      let(:new_attributes) do
        {
          invoice_number: '53'
        }
      end

      it 'updates the requested invoice_payment' do
        invoice_payment = InvoicePayment.create! valid_attributes
        patch :update, params: { id: invoice_payment.to_param, invoice_payment: new_attributes }
        invoice_payment.reload
        expect(invoice_payment.invoice_number).to eq('53')
      end

      it 'assigns the requested invoice_payment as @invoice_payment' do
        invoice_payment = InvoicePayment.create! valid_attributes
        patch :update, params: { id: invoice_payment.to_param, invoice_payment: valid_attributes }
        expect(assigns(:invoice_payment)).to eq(invoice_payment)
      end

      it 'redirects to the list of invoice_payments' do
        invoice_payment = InvoicePayment.create! valid_attributes
        patch :update, params: { id: invoice_payment.to_param, invoice_payment: valid_attributes }
        expect(response).to redirect_to(invoice_payments_path)
      end
    end
  end
end
