require "rails_helper"
require "fileutils"

describe AccountingDocumentsController, type: :controller do
  let(:admin) { create(:user, :admin) }
  let(:dealer) { create(:user, :dealer_contact_access_admin) }

  let(:accounting_document) do
    create(:accounting_document, :with_attachment, id: 1234, registration_number: 5678)
  end

  before do
    allow(Downloaders::DocumentDownloader)
      .to receive_message_chain(:new, :download)
      .and_return OpenStruct.new(error: "", success: "")
    allow(PrivatePub).to receive(:publish_to)
    accounting_document
  end

  after :each do
    FileUtils.rm_rf Rails.root
      .join("public/data/accounting_document/attachment/1234/contract.pdf")
  end

  context "show" do
    before { stub_authentication dealer }

    it "does not launch an asynchronous job with an attachment already present" do
      expect(Delayed::Job).not_to receive(:enqueue)
      get :show, xhr: true, params: { id: "1234" }
    end

    it "launches an asynchronous job if no attachment is present" do
      create(:accounting_document, id: 5678)
      expect(Delayed::Job).to receive(:enqueue)
      get :show, xhr: true, params: { id: "5678" }
    end
  end

  context "download_attachment" do
    it "logs the download for a dealer" do
      stub_authentication dealer
      expect do
        get :download_attachment, params: { id: "1234" }
      end.to change(AccountingDocumentDownload, :count).by(1)
      expect(AccountingDocument.find(1234).accounting_document_downloads)
        .to eq [AccountingDocumentDownload.last]
    end

    it "doesn't log the download for an admin" do
      stub_authentication admin
      expect do
        get :download_attachment, params: { id: "1234" }
      end.not_to change(AccountingDocumentDownload, :count)
    end
  end
end
