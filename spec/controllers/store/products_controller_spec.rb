require "rails_helper"

describe Store::ProductsController, type: :controller do
  let(:admin) { create(:user, :admin) }
  let(:dealer_contact) { create(:dealer_contact, :access_orders) }
  let(:dealer) { create(:dealer, :generic_dealer, :with_warehouse, dealer_contacts: [dealer_contact]) }
  let(:user) { create(:user, :dealer_user, dealer_contact: dealer.dealer_contacts.first) }

  it "ask not existent facet as admin" do
    stub_authentication admin
    get :index, params: { facets: "wrong_facet-Param" }
    expect(response).to redirect_to "/news"

    #ask not existent facet as user
    stub_authentication user
    get :index, params: { facets: "wrong_facet-Param" }
    expect(response).to redirect_to "/news"

    #set_price_list_image only index and financeable
    stub_authentication admin

    get :index
    expect(assigns(:price_list_image)).to eq(PriceListImage.first)
  end

  context '#index' do
    before { stub_authentication admin }

    it do
      get :index

      expect(assigns(:context)).to eq('orders')
    end
  end

  context '#financeable' do
    before { stub_authentication admin }

    it do
      get :index

      expect(assigns(:context)).to eq('orders')
    end
  end

  context "#show" do
    let(:gray_color) { create(:product_color, :gray) }
    let(:green_color) { create(:product_color, name: 'green') }
    let!(:green_iphone_11) do
      create(:product, :iphone,
             name:                             "iPhone 11 Midnight Green",
             commercial_name:                  "iPhone 11",
             product_color:                    green_color,
             has_default_color:                true,
             can_be_requested:                 true,
             amount_in_system_owner_warehouse: 0)
    end

    let!(:gray_iphone_11) do
      create(:product, :iphone,
             name:            "iPhone 11 Space Gray",
             code:            "0TAL0800OU0007",
             uuid:            "7f0eb400-f058-b761-e62c-4f3504c4a4c5",
             commercial_name: "iPhone 11",
             product_color:   gray_color)
    end

    let!(:gray_iphone_11_pro) do
      create(:product, :iphone,
             name:             "iPhone 11 Pro Space Gray",
             code:             "0TAL0800OU0008",
             uuid:             "7f0eb400-f058-b761-e62c-4f3504c4a4c6",
             commercial_name:  "iPhone 11 Pro",
             product_color:    gray_color,
             can_be_requested: true)
    end

    subject { get :show, params: params }

    context "given a color" do
      let(:params) do
        {
          id:          gray_iphone_11.to_param,
          product_ids: [green_iphone_11, gray_iphone_11].map(&:id)
        }
      end

      before { stub_authentication user }

      before { subject }

      it "creates the color-related variables" do
        expect(assigns(:product_obj).colors).to eq [green_color.code, gray_color.code]
        expect(assigns(:product_obj).presented_colors).to eq ["#{green_color.name} (in arrivo)", "#{gray_color.name} (non ord.)"]
        expect(assigns(:product_obj).product_ids).to eq [green_iphone_11.id, gray_iphone_11.id]
      end
    end
  end
end
