require 'rails_helper'

describe PhoneActivationsController, type: :controller do
  let(:admin) { build_stubbed(:user, :admin) }
  let(:agent) { create(:user, :agent) }
  let!(:agent_internal_user_detail) { create(:internal_user_detail, user: agent) }

  let(:phone_activation) { create(:phone_activation, :abbonamento_ricaricabile, :with_dealer_contact) }

  subject { patch :update, params: params }

  context "as admin" do
    before { stub_authentication admin }

    before { phone_activation.product_item.update_attribute(:state, 'activating') }
    before { controller.instance_variable_set(:@phone_activation, phone_activation) }

    let!(:old_item) { phone_activation.product_item }
    let!(:new_item) { create(:item, :iphone_dealer_item_instock) }

    let(:params) { { id: phone_activation.id, phone_activation: { product_serial_id: new_item.id } } }

    subject { patch :update, params: params }

    it do
      subject
      expect(phone_activation.reload.product_item_id).to eq new_item.id
      expect(new_item.reload.activating?).to be_truthy
      expect(old_item.reload.instock?).to be_truthy
    end

    context 'Cvp offline' do
      let(:params) { { id: phone_activation.id, phone_activation: { cvp_offline: true } } }

      it do
        subject

        expect(phone_activation.reload.cvp_offline).to be_truthy
      end
    end

    context 'updating an activation with a customer from the mobile app' do
      let!(:minor_customer) do
        create(:customer,
               cf:         '****************',
               cf_or_vat:  '****************',
               birth_date: Date.new(2012, 3, 29),
               state:      'draft')
      end

      before do
        phone_activation.plan.update_columns(app_ready: true)
        phone_activation.customer.do_draft!
        phone_activation.update(minor_customer: minor_customer)
      end

      it 'marks the customers as added from app' do
        subject
        expect(phone_activation.customer.reload.added_from_app?).to be_truthy
        expect(phone_activation.minor_customer.reload.added_from_app?).to be_truthy
      end
    end
  end

  describe 'update inserita da partner phone activation' do
    context 'as admin' do
      before { phone_activation.update(signature_data: { signature_kind: 'digital' }) }
      before { create(:operation_outcome, :inserita_da_partner, phone_activation: phone_activation) }
      before { stub_authentication admin }

      let(:params) { { id: phone_activation.id, phone_activation: { signature_data: { signature_kind: 'paper' } } } }

      it do
        subject

        expect(phone_activation.reload.paper_signature?).to be_truthy
      end
    end

    context 'as agent' do
      before { phone_activation.update(signature_data: { signature_kind: 'digital' }) }
      before { phone_activation.warehouse.update(agent_wind_tre_id: agent.id) }
      before { stub_authentication agent }

      let(:params) { { id: phone_activation.id, phone_activation: { signature_data: { signature_kind: 'paper' } } } }

      context 'without operation outcome' do
        it do
          subject
  
          expect(response.code).to eq '302'
          expect(session[:gflash]).to be_nil
          expect(phone_activation.reload.paper_signature?).to be_truthy
        end
      end

      context 'with operation outcome' do
        before { create(:operation_outcome, :inserita_da_partner, phone_activation: phone_activation) }

        it do
          subject

          expect(response.code).to eq '302'
          expect(session[:gflash][:error]).to eq ['Non sei autorizzato ad accedere a questa funzionalità']
          expect(phone_activation.reload.paper_signature?).to be_falsey
        end
      end
    end

    context 'as dealer' do
      let(:params) { { id: phone_activation.id, phone_activation: { signature_data: { signature_kind: 'paper' } } } }

      before { phone_activation.user.dealer_contact.access_flags << create(:access_flag, :access_activations) }
      before { phone_activation.update(signature_data: { signature_kind: 'digital' }, dealer_id: phone_activation.user.dealer.id ) }
      before { stub_authentication phone_activation.user }

      context 'without operation outcome' do
        it do
          subject
  
          expect(response.code).to eq '302'
          expect(session[:gflash]).to be_nil
          expect(phone_activation.reload.paper_signature?).to be_truthy
        end
      end

      context 'with operation outcome' do
        before { create(:operation_outcome, :inserita_da_partner, phone_activation: phone_activation) }

        it do
          subject

          expect(response.code).to eq '302'
          expect(session[:gflash][:error]).to eq ['Non sei autorizzato ad accedere a questa funzionalità']
          expect(phone_activation.reload.paper_signature?).to be_falsey
        end
      end
    end
  end
end