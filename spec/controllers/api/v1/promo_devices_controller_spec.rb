require 'rails_helper'
require 'carrierwave/test/matchers'

describe Api::V1::PromoDevicesController do
  before { allow_any_instance_of(PromoDevice).to receive(:should_burn_imei).and_return(true) }
  context '#update' do
    context 'unauthorized (401) without header' do
      before { patch :update, params: { id: '123' } }

      it { expect(response.status).to eq 401 }
    end

    context 'Promo device OK' do
      let(:promo_device) { create(:promo_device, sent_to_robot: true) }
      let(:token) { generate_token("#{PromoDeviceRobot::NGPOS_PREFIX}#{promo_device.id}", Time.now.to_i + 100) }
      
      let(:params) do
        { 'status'          => 'OK',
          'spazioToken'     => token,
          'error_message'   => '',
          'resources_links' => [],
          'id'              => "#{PromoDeviceRobot::NGPOS_PREFIX}#{promo_device.id}" }
      end

      before do 
        request.env['HTTP_AUTHORIZATION'] = "Token token=#{token}"
        patch :update, params: params
      end

      it do
        expect(promo_device.reload.activated?).to be_truthy
        expect(promo_device.reload.activated_at).not_to be_nil
      end
    end

    context 'Promo device KO' do
      let(:promo_device) { create(:promo_device, sent_to_robot: true) }
      let(:token) { generate_token("#{PromoDeviceRobot::NGPOS_PREFIX}#{promo_device.id}", Time.now.to_i + 100) }
      
      let(:params) do
        { 'status'          => 'KO',
          'spazioToken'     => token,
          'error_message'   => 'Error in promo device',
          'resources_links' => ["uploads/robot-worklogs/#{promo_device.id}/Tentativo_11.zip"],
          'id'              => "#{PromoDeviceRobot::NGPOS_PREFIX}#{promo_device.id}" }
      end

      before do 
        promo_device.audits.destroy_all
        request.env['HTTP_AUTHORIZATION'] = "Token token=#{token}"
        patch :update, params: params
      end

      it "include resources links" do
        expect(promo_device.reload.rejected?).to be_truthy
        expect(promo_device.reload.audits.first.audited_changes.keys).to eq ['robot_ko']
        expect(promo_device.reload.audits.first.audited_changes.values[0].first).to include('Error in promo device')
        expect(promo_device.reload.audits.first.audited_changes.values[0].first).not_to include("https://kolme-test.s3-eu-west-1.amazonaws.com/uploads/robot-worklogs/#{promo_device.id}/Tentativo_11.zip")
        expect(promo_device.reload.audits.first.audited_changes.values[0].first).not_to include('Scarica file zip debug')
        expect(promo_device.reload.audits.last.audited_changes.keys).to eq ['robot_debug']
        expect(promo_device.reload.audits.last.audited_changes.values[0].first).to include('Error in promo device')
        expect(promo_device.reload.audits.last.audited_changes.values[0].first).to include("https://kolme-test.s3-eu-west-1.amazonaws.com/uploads/robot-worklogs/#{promo_device.id}/Tentativo_11.zip")
        expect(promo_device.reload.audits.last.audited_changes.values[0].first).to include('Scarica file zip debug')
      end
    end

    context 'Promo device KO_R' do
      let(:promo_device) { create(:promo_device, sent_to_robot: true) }
      let(:token) { generate_token("#{PromoDeviceRobot::NGPOS_PREFIX}#{promo_device.id}", Time.now.to_i + 100) }
      
      let(:params) do
        { 'status'          => 'KO_R',
          'spazioToken'     => token,
          'error_message'   => 'Error in promo device',
          'resources_links' => ["uploads/robot-worklogs/#{promo_device.id}/Tentativo_11.zip"],
          'id'              => "#{PromoDeviceRobot::NGPOS_PREFIX}#{promo_device.id}" }
      end

      before do 
        promo_device.audits.destroy_all
        request.env['HTTP_AUTHORIZATION'] = "Token token=#{token}"
        patch :update, params: params
      end

      it "include resources links" do
        expect(promo_device.reload.in_progress?).to be_truthy
        expect(promo_device.reload.audits.first.audited_changes.keys).to eq ['robot_ko_r']
        expect(promo_device.reload.audits.first.audited_changes.values[0].first).to include('Error in promo device')
        expect(promo_device.reload.audits.first.audited_changes.values[0].first).not_to include("https://kolme-test.s3-eu-west-1.amazonaws.com/uploads/robot-worklogs/#{promo_device.id}/Tentativo_11.zip")
        expect(promo_device.reload.audits.first.audited_changes.values[0].first).not_to include('Scarica file zip debug')
        expect(promo_device.reload.audits.last.audited_changes.keys).to eq ['robot_debug']
        expect(promo_device.reload.audits.last.audited_changes.values[0].first).to include('Error in promo device')
        expect(promo_device.reload.audits.last.audited_changes.values[0].first).to include("https://kolme-test.s3-eu-west-1.amazonaws.com/uploads/robot-worklogs/#{promo_device.id}/Tentativo_11.zip")
        expect(promo_device.reload.audits.last.audited_changes.values[0].first).to include('Scarica file zip debug')
      end
    end

    context 'internal server error (500) without valid token' do
      before { @request.env['HTTP_AUTHORIZATION'] = 'Token token=notvalidtoken' }
      before { patch :update, params: { id: '123' } }
      
      it { expect(response.status).to eq 500 }
    end

    context 'forbidden with expired token' do
      before { @request.env['HTTP_AUTHORIZATION'] = "Token token=#{generate_token('123', Time.now.to_i - 10)}" }
      before { patch :update, params: { id: '123' } }
      
      it { expect(response.status).to eq 403 }
    end

    context 'forbidden with different hex_id expired token' do
      before { @request.env['HTTP_AUTHORIZATION'] = "Token token=#{generate_token('1234', Time.now.to_i - 10)}" }
      before { patch :update, params: { id: '123' } }
      
      it { expect(response.status).to eq 403 }
    end

    context 'not found (404) without existing promo_device' do
      let(:token) { generate_token('123', Time.now.to_i + 100) }
      let(:params) { { id: '123', status: 'OK', error_message: '' } }
      before { request.env['HTTP_AUTHORIZATION'] = "Token token=#{token}" }
      before { patch :update, params: params }
      
      it { expect(response.status).to eq 404 }
    end
  end

  def generate_token(id, expired_time)
    exp_payload = { data: id, exp: expired_time }
    hmac_secret = Settings.api.hmac_secret
    JWT.encode exp_payload, hmac_secret, 'HS256'
  end
end
