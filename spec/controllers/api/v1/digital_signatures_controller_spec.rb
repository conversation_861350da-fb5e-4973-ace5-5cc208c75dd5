require 'rails_helper'
require 'carrierwave/test/matchers'

describe Api::V1::DigitalSignaturesController do
  let(:phone_activation) { create(:phone_activation, :with_callbacks, :abbonamento_ricaricabile) }
  before { phone_activation.operator.update_column(:code, 'w3') }
  context 'next_step' do
    context 'wrong hex_id' do
      before { get :next_step_sign, params: { namirial_id: 'not valid', kind: 'phone_activation' } }

      it { expect(response.status).to eq 404 }
    end

    context 'present id' do
      before { expect(DigitalSignatureService).to receive_message_chain(:new, :next_step).and_return DigitalSignature::NamirialClientStepResponse.new('InProgress', {}) }
      before { expect(PrivatePub).to receive(:publish_to).with("/notify_digital_signature/#{phone_activation.id}", 'window.App.SignModal.nextStep()') }
      before { get :next_step_sign, params: { namirial_id: phone_activation.hex_id, kind: 'phone_activation' } }

      it { expect(response.status).to eq 200 }
    end

    context 'completed?' do
      before { expect(DigitalSignatureService).to receive_message_chain(:new, :next_step).and_return DigitalSignature::NamirialClientStepResponse.new('Completed', {}) }
      before { get :next_step_sign, params: { namirial_id: phone_activation.hex_id, kind: 'phone_activation' } }

      it { expect(response.status).to eq 200 }
    end

    context 'rejected?' do
      before { expect(PrivatePub).to receive(:publish_to).with("/notify_digital_signature/#{phone_activation.id}", 'window.App.SignModal.reject()') }
      before { expect(DigitalSignatureService).to receive_message_chain(:new, :next_step).and_return DigitalSignature::NamirialClientStepResponse.new('Rejected', {}) }
      before { get :next_step_sign, params: { namirial_id: phone_activation.hex_id, kind: 'phone_activation' } }

      it { expect(response.status).to eq 200 }
    end

    context 'error' do
      let(:response_error) { DigitalSignature::NamirialClientStepResponse.new('Error', error_message: 'this is an `error`') }
      before { expect(DigitalSignatureService).to receive_message_chain(:new, :next_step).and_return response_error }
      before { expect(PrivatePub).to receive(:publish_to).with("/notify_digital_signature/#{phone_activation.id}", "window.App.DigitalSignatureTimeline.addError('#{response_error.error_message}')") }
      before { get :next_step_sign, params: { namirial_id: phone_activation.hex_id, kind: 'phone_activation' } }

      it { expect(response.status).to eq 200 }
    end
  end

  context 'complete_sign' do
    let(:signature_data) do
      { 'steps'  => [{ 'log_id' => nil, 'status' => 'InProgress', 'full_name' => 'Carlo Jean', 'document_id' => nil, 'redirection_url' => 'https://windtre-test.signanywhere.com/workstepredirector/sign?identifier=FQTknbNm3R/eI66qg/RkP8bGOOOKyJBf4p2SVxCKPDzBO1Gtp63NthC7w6cI2ap9M5osE1Esl4N5ERFtFu7LLg==' },
                    { 'log_id' => nil, 'status' => 'InProgress', 'full_name' => 'Carlo Jean', 'document_id' => nil, 'redirection_url' => 'https://windtre-test.signanywhere.com/workstepredirector/sign?identifier=FQTknbNm3R/eI66qg/RkP8bGOOOKyJBf4p2SVxCKPDzBO1Gtp63NthC7w6cI2ap9M5osE1Esl4N5ERFtFu7LLg==' },
                    { 'log_id' => nil, 'status' => 'InProgress', 'full_name' => 'Fabio Mazzon', 'document_id' => nil, 'redirection_url' => 'https://windtre-test.signanywhere.com/workstepredirector/sign?identifier=fpsFjyP8pqIrhJapwgywRxzXbhUExj~lAPQbstL2ToHU6HnpFeZ8NM072W4an9swJjwPbcGyNL0uKxMlzzpLoQ==' },
                    { 'log_id' => nil, 'status' => 'InProgress', 'full_name' => 'Fabio Mazzon', 'document_id' => nil, 'redirection_url' => 'https://windtre-test.signanywhere.com/workstepredirector/sign?identifier=fpsFjyP8pqIrhJapwgywRxzXbhUExj~lAPQbstL2ToHU6HnpFeZ8NM072W4an9swJjwPbcGyNL0uKxMlzzpLoQ==' }],
        'dealer' => { 'email' => '<EMAIL>', 'number' => '3385063330', 'last_name' => 'Mazzon', 'first_name' => 'Fabio', 'update_number_otp' => 'false' }, 'customer' => { 'email' => '<EMAIL>', 'number' => '3334167742', 'last_name' => 'Jean', 'first_name' => 'Carlo' }, 'envelope_id' => '25845c07-2adb-439b-8d3c-86d4b6485253' }
    end
    before { phone_activation.update_column(:signature_data, signature_data) }

    context 'download job correctly enqueued' do
      before { create(:operation, :digital_signature) }
      before { expect(Delayed::Job).to receive(:enqueue).at_least(:once) }
      before { expect(PrivatePub).to receive(:publish_to).at_least(:once) }
      before { allow_any_instance_of(PhoneActivation).to receive(:next_editing_step).and_return nil }
      before { allow_any_instance_of(PhoneActivation).to receive(:phone_activation_kind_code).and_return 'SSMDW3' }
      before { allow_any_instance_of(PhoneActivation).to receive(:operations).and_return phone_activation.phone_activation_kind.operations }
      before { allow_any_instance_of(PhoneActivation).to receive(:phone_activation_kind_operations).and_return phone_activation.phone_activation_kind.phone_activation_kind_operations }
      before { allow_any_instance_of(PhoneActivation).to receive(:set_free_shipping?).and_return false }

      before { get :complete_sign, params: { namirial_id: phone_activation.hex_id, kind: 'phone_activation' } }

      it 'create operation outcomes and upload documents in ko_r status' do
        expect(phone_activation.documents.count).to eq 0
        expect(phone_activation.reload.signature_obj.completed?).to be_truthy
        expect(phone_activation.reload.operation_outcomes.count).to eq 3
        expect(phone_activation.reload.operation_outcomes.map(&:operation_id)).to include(Operation::DIGITAL_SIGNATURE)
        expect(phone_activation.reload.operation_outcomes.last.operation_id).to eq Operation::UPLOAD_DOCUMENTI
        expect(phone_activation.reload.operation_outcomes.last.status).to eq 'visited'
        expect(phone_activation.reload.operation_outcomes.last.busy).to be_truthy

        expect(response.status).to eq 200
      end
    end

    context 'wrong hex_id' do
      before { get :complete_sign, params: { namirial_id: 'not valid', kind: 'phone_activation' } }

      it do
        expect(response.status).to eq 200
        expect(JSON.parse(response.body)['message']).to eq 'unknown phone activation with hex_id: not valid'
      end

    end

    context 'return if rejected' do
      let(:signature_data) do
        { 'steps' => [{ 'log_id' => nil, 'status' => 'Rejected', 'full_name' => nil, 'document_id' => nil, 'redirection_url' => nil }] }
      end
      before { phone_activation.update_column(:signature_data, signature_data) }
      before { get :complete_sign, params: { namirial_id: phone_activation.hex_id, kind: 'phone_activation' } }

      it { expect(response.status).to eq 200 }
    end

    context 'already_processed' do
      let(:signature_data) do
        { 'processed' => true, 'steps' => [{ 'log_id' => nil, 'status' => 'Completed', 'full_name' => nil, 'document_id' => nil, 'redirection_url' => nil }] }
      end
      before { phone_activation.update_column(:signature_data, signature_data) }

      before { get :complete_sign, params: { namirial_id: phone_activation.hex_id, kind: 'phone_activation' } }
      it do
        expect(JSON.parse(response.body)['message']).to eq 'already completed'
        expect(phone_activation.reload.signature_obj.completed?).to be_truthy
      end
    end
  end
end
