require 'rails_helper'

RSpec.describe Api::V1::WindtreEcommerce::FindomesticController, type: :controller do
  let(:imei_reservation) { create(:imei_reservation, findomestic_data: findomestic_data) }
  let(:findomestic_data) do
    {
      'financial_company' => 'findomestic',
      'windtre_order_id' => 'WT123456',
      'application_id' => 'APP123456'
    }
  end
  let!(:api_call) do
    create(:findomestic_api_call,
           imei_reservation: imei_reservation,
           application_id: 'APP123456',
           call_type: 'create_application')
  end

  describe 'POST #installment_notification' do
    let(:valid_params) do
      {
        applicationId: 'APP123456',
        applicationStatus: 'COMPLETED',
        installmentStatus: 'APPROVED',
        eventType: 'APPLICATION_COMPLETE'
      }
    end

    context 'with valid APPLICATION_COMPLETE notification' do
      context 'when installmentStatus is APPROVED' do
        it 'confirms the reservation and requests liquidation' do
          expect_any_instance_of(ImeiReservationItemService).to receive(:confirm_reservation)
          expect(Delayed::Job).to receive(:enqueue).with(
            an_instance_of(Jobs::FindomesticLiquidationJob),
            hash_including(queue: 'findomestic_liquidation')
          )

          post :installment_notification, params: valid_params

          expect(response).to have_http_status(:ok)
          expect(JSON.parse(response.body)['success']).to be true
        end
      end

      context 'when installmentStatus is DECLINED' do
        let(:declined_params) do
          valid_params.merge(installmentStatus: 'DECLINED')
        end

        it 'resets item status and cancels the reservation' do
          item_double = double('item', present?: true, may_return_instock?: true)
          allow(imei_reservation).to receive(:item).and_return(item_double)
          expect(item_double).to receive(:return_instock!)
          expect(item_double).to receive(:update!).with(notes: nil)
          expect_any_instance_of(ImeiReservationItemService).to receive(:terminate_reservation).with(:cancel!)

          post :installment_notification, params: declined_params

          expect(response).to have_http_status(:ok)
          expect(JSON.parse(response.body)['success']).to be true
        end
      end
    end

    context 'with APPLICATION_PREAPPROVED notification' do
      let(:preapproved_params) do
        {
          applicationId: 'APP123456',
          applicationStatus: 'PREAPPROVED',
          installmentStatus: 'CREATED',
          eventType: 'APPLICATION_PREAPPROVED'
        }
      end

      it 'suspends the reservation' do
        expect(imei_reservation).to receive(:may_suspend?).and_return(true)
        expect(imei_reservation).to receive(:suspend!)

        post :installment_notification, params: preapproved_params

        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['success']).to be true
      end

      it 'does not suspend if not allowed' do
        expect(imei_reservation).to receive(:may_suspend?).and_return(false)
        expect(imei_reservation).not_to receive(:suspend!)

        post :installment_notification, params: preapproved_params

        expect(response).to have_http_status(:ok)
      end
    end

    context 'with missing applicationId' do
      it 'returns bad request' do
        post :installment_notification, params: { installmentStatus: 'APPROVED' }

        expect(response).to have_http_status(:bad_request)
        expect(JSON.parse(response.body)['error']).to eq('Missing applicationId')
      end
    end

    context 'with non-existent reservation' do
      it 'returns not found' do
        post :installment_notification, params: { applicationId: 'NONEXISTENT' }

        expect(response).to have_http_status(:not_found)
        expect(JSON.parse(response.body)['error']).to eq('ImeiReservation not found')
      end
    end

    context 'with unknown event type' do
      let(:unknown_params) do
        valid_params.merge(eventType: 'UNKNOWN_EVENT')
      end

      it 'logs warning and returns success' do
        expect(KolmeLogger).to receive(:warn).with(/Unknown Findomestic notification event/)

        post :installment_notification, params: unknown_params

        expect(response).to have_http_status(:ok)
      end
    end

    context 'when service returns error' do
      it 'returns unprocessable entity' do
        service_double = instance_double(ImeiReservationItemService)
        response_double = double('response', error: double('error', present?: true, message: 'Service error'))
        
        allow(ImeiReservationItemService).to receive(:new).and_return(service_double)
        allow(service_double).to receive(:response).and_return(response_double)
        allow(service_double).to receive(:confirm_reservation)

        post :installment_notification, params: valid_params

        expect(response).to have_http_status(:unprocessable_entity)
        expect(JSON.parse(response.body)['message']).to eq('Service error')
      end
    end

    context 'when exception occurs' do
      it 'returns internal server error' do
        allow(ImeiReservation).to receive(:joins).and_raise(StandardError, 'Database error')
        expect(KolmeLogger).to receive(:error).with(/Findomestic webhook error/)

        post :installment_notification, params: valid_params

        expect(response).to have_http_status(:internal_server_error)
        expect(JSON.parse(response.body)['error']).to eq('Internal server error')
      end
    end
  end

  describe 'private methods' do
    let(:controller_instance) { described_class.new }
    let(:service_double) { instance_double(ImeiReservationItemService, imei_reservation: imei_reservation) }
    let(:notification_data) { { installmentStatus: 'APPROVED' } }

    describe '#request_liquidation' do
      it 'enqueues liquidation job for Findomestic reservations' do
        expect(Delayed::Job).to receive(:enqueue).with(
          an_instance_of(Jobs::FindomesticLiquidationJob),
          hash_including(queue: 'findomestic_liquidation')
        )

        controller_instance.send(:request_liquidation, imei_reservation)
      end

      it 'does not enqueue job for non-Findomestic reservations' do
        non_findomestic_reservation = create(:imei_reservation)
        
        expect(Delayed::Job).not_to receive(:enqueue)

        controller_instance.send(:request_liquidation, non_findomestic_reservation)
      end
    end
  end
end
