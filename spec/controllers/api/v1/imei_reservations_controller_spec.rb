require 'rails_helper'

describe Api::V1::ImeiReservationsController, type: :controller do
  let!(:whitelisted_ip) { create(:whitelisted_ip, ip: '*************' ) }
  before do
    request.env['REMOTE_ADDR'] = '*************'
  end

  describe '#show' do
    before { Timecop.freeze(Time.now) }
    before { Timecop.return }

    before do
      request.headers['Authorization'] = "Bearer #{JwtService.new.encode_without_expiration('imei_check')}"
      request.headers['Content-Type'] = "application/json"
      request.headers['Accept'] = "application/json"

      get :get_token
      @token = JSON.parse(response.body)['token']
    end

    let(:imei_reservation) { create :imei_reservation }

    it 'imei present not shipped / sent to dhl' do
      request.headers['Authorization'] = "Bearer #{@token}"

      get :show, params: { imei_or_serial: imei_reservation.item_serial }

      expect(response.code).to eq '404'

      expect(JSON.parse(response.body)['imei_reservation']).to be_nil
    end

    it 'imei present shipped / sent to dhl' do
      imei_reservation.update(state: ['shipped', 'sent_to_dhl'].sample)
      request.headers['Authorization'] = "Bearer #{@token}"

      get :show, params: { imei_or_serial: imei_reservation.item_serial }

      expect(response.code).to eq '200'

      expect(JSON.parse(response.body)['imei_reservation']).not_to be_nil
    end

    it 'imei not found' do
      request.headers['Authorization'] = "Bearer #{@token}"
      get :show, params: { imei_or_serial: "not present imei or serial" }

      expect(response.code).to eq '404'

      expect(JSON.parse(response.body)['imei_reservation']).to be_nil
    end

    it 'expired token' do
      Timecop.travel(1.hour.from_now)
      request.headers['Authorization'] = "Bearer #{@token}"
      get :show, params: { imei_or_serial: "not present imei or serial" }

      expect(response.code).to eq '403'
    end

    it 'not allowed ip' do
      imei_reservation.update(state: ['shipped', 'sent_to_dhl'].sample)
      request.headers['Authorization'] = "Bearer #{@token}"
      request.env['REMOTE_ADDR'] = '*************'

      get :show, params: { imei_or_serial: imei_reservation.item_serial }

      expect(response.code).to eq '403'
    end
  end

  describe '#get_token' do
    it 'correct header' do
      request.headers['Authorization'] = "Bearer #{JwtService.new.encode_without_expiration('imei_check')}"
      get :get_token

      expect(response.status).to eq(200)
      expect(JSON.parse(response.body)['token']).not_to be_nil
    end

    it 'wrong bearer header' do
      request.headers['Authorization'] = "Bearer wrongvalue"
      get :get_token

      expect(response.status).to eq(403)
      expect(JSON.parse(response.body)['message']).to eq 'token not valid'
    end
  end
end
