require 'rails_helper'

RSpec.describe OffersController, type: :controller do
  before { sign_in create(:user, :admin) }

  describe 'GET #rebuild_all' do
    it 'returns a success response' do
      get :rebuild_all
      expect(response.status).to eq 302
    end
  end
  describe 'GET #rebuild(offer)' do
    let(:offer) { create(:offer, :tre_rateale) }
    it 'returns a success response' do
      get :rebuild, params: { id: offer.id }

      expect(response.status).to eq 302
    end
  end
end