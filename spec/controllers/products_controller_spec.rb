# encoding: utf-8
# frozen_string_literal: true

require 'rails_helper'

describe ProductsController, type: :controller do
  let(:admin) { create(:user, :admin) }

  before do
    stub_authentication admin
  end

  describe 'Creating a bundle' do
    let(:wind_business) { create(:operator, :wind_business) }
    let(:standard_dealer) { create(:dealer_category, :standard_dealer) }
    let(:iphone) { create(:product, :iphone) }
    let(:statistic_group1) { create(:statistic_group1) }
    let(:statistic_group2) { create(:statistic_group2) }
    let(:family_product) { create(:family_product) }
    let(:company_brand) { create(:company_brand) }
    let(:italy_purchase) { create(:italy_purchase) }
    let(:italy_purchase_return) { create(:italy_purchase_return) }
    let(:italy_sales) { create(:italy_sales) }
    let(:italy_sales_return) { create(:italy_sales_return) }
    let(:sap_distribution_chain) { create(:sap_distribution_chain) }
    let(:sap_product_kind) { create(:sap_product_kind) }
    let(:sap_product_cluster) { create(:sap_product_cluster) }
    let(:sap_product_supplier) { create(:sap_product_supplier) }
    let(:sap_valuation_class) { create(:sap_valuation_class) }
    let(:how_many_bundle_items) { 2 } # NOTE: this can be changed up to 100
    let(:products) do
      [].tap do |products|
        how_many_bundle_items.times do
          products << create(:product, :valid)
        end
      end
    end

    let(:bundle_params) do
      {
        product: {
          name:                        'Bundle di prova',
          sap_name:                    'Bundle di prova',
          commercial_name:             'Bundle di prova',
          code:                        'bun001',
          competence:                  'Generico',
          product_type:                'b',
          product_kind_list:           '',
          sim_kind_list:               '',
          can_be_requested:            '1',
          activable_system_owner:      '0',
          serial:                      '1',
          max_qty_orderable:           '1',
          operator_ids:                [wind_business.id],
          store_tag_list:              'Accessori',
          dealer_category_ids:         [
                                         standard_dealer.id
                                       ],
          product_category_ids:        [
                                         ''
                                       ],
          vat_type:                    'I22',
          dealer_price_vat_excluded_a: '600.00',
          dealer_price_vat_excluded_b: '600.00',
          dealer_price_vat_excluded_c: '600.00',
          default_percent_discount:    '0',
          public_price_list:           '0.0',
          bundle_items_attributes:     item_attributes_from(products),
          bundle_vat_a:                '132.00',
          bundle_vat_b:                '132.00',
          bundle_vat_c:                '132.00',
          dont_show_public_price:      '0',
          loanable:                    '0',
          reward:                      '0.0',
          agent_profit_a:              '0.0',
          agent_profit_b:              '0.0',
          agent_profit_c:              '0.0',
          view_in_matrix:              '0',
          mandatory_mnp:               '0',
          recharge_size_id:            '',
          sim_format:                  '',
          description:                 '',
          visibility:                  'y',
          company_brand_id:            company_brand.id,
          family_product_id:           family_product.id,
          statistic_group1_id:         statistic_group1.id,
          statistic_group2_id:         statistic_group2.id,
          statistic_group3_id:         '',
          gamma_purchase_price:        '',
          italy_sales_id:              italy_sales.id,
          italy_purchase_id:           italy_purchase.id,
          italy_sales_return_id:       italy_sales_return.id,
          italy_purchase_return_id:    italy_purchase_return.id,
          sap_distribution_chain_id:   sap_distribution_chain.id,
          sap_product_kind_id:         sap_product_kind.id,
          sap_product_cluster_id:      sap_product_cluster.id,
          sap_product_supplier_id:     sap_product_supplier.id,
          sap_valuation_class_id:      sap_valuation_class.id,
          height:                      '',
          width:                       '',
          depth:                       '',
          weight:                      '',
          immaterial:                  'false',
          product_macro_category_id:   create(:product_macro_category, :wind_tre_smartphone ).id
        },
        commit:  'Crea Prodotto'
      }
    end

    it 'creates a bundle succesfully' do
      post :create, params: bundle_params
      expect(response).to have_http_status :redirect
      expect(Product.bundles.count).to eq(1)
      expect(Product.last.bundle_items.count).to eq(how_many_bundle_items)
    end

    %i(
      sap_distribution_chain_id
      sap_product_kind_id
      sap_product_cluster_id
      sap_product_supplier_id
      sap_valuation_class_id
    ).each do |param|
      it "requires #{param} SAP parameter" do
        product_params = attributes_for(:product, :valid)
        product_params[param] = nil
        post :create, params: { product: product_params, commit: 'Crea Prodotto' }
        expect(response).to have_http_status :unprocessable_entity
      end
    end

    def item_attributes_from(products)
      {}.tap do |product_attributes|
        products.each_with_index do |product, index|
          product_attributes[index.to_s] = attributes_for_product(product)
        end
      end
    end

    def attributes_for_product(product)
      {
        item_id:                     product.id,
        quantity:                    '1',
        dealer_price_vat_excluded_a: '600',
        agent_profit_a:              '0.0',
        dealer_price_vat_excluded_b: '600',
        agent_profit_b:              '0.0',
        dealer_price_vat_excluded_c: '600',
        agent_profit_c:              '0.0'
      }
    end
  end

  describe '#by_code' do
    context 'when product found' do
      let!(:product) { create(:product, :valid, code: 'ASD') }

      it 'returns the product vat and vat type' do
        get :by_code, params: { code: 'ASD', format: :json }
        expect(response).to have_http_status :success
        parsed = JSON.parse response.body
        expect(parsed.keys).to include('vat')
        expect(parsed.keys).to include('vat_type')
        expect(parsed.keys).to include('id')
      end
    end

    context 'when product not found' do
      it 'returns 404 not found' do
        get :by_code, params: { code: 'ASD', format: :json }
        expect(response).to have_http_status :not_found
      end
    end
  end

  describe '#non_sim_for_activation' do
    it 'does what it needs to do without failing' do
      params = {
        'data_composer'    => 'true',
        'dealer_name'      => 'Architrave 3000',
        'phone_activation' => {
          'user_id'             => '1975',
          'dealer_id'           => '1819',
          'operator_id'         => '',
          'tariffa_cb'          => '',
          'product_category_id' => '',
          'plan_id'             => '',
          'offer_category_id'   => '',
          'offer_id'            => '3',
          'payment_method_id'   => '',
          'change_warehouse'    => '0'
        }
      }

      get :non_sim_for_activation, params: params
      expect(response).to be_successful
    end
  end

  describe 'product name autocomplete' do
    let!(:activable_product) { create(:product, :iphone_4s, product_type: 'pa') }
    let!(:non_activable_product) { create(:product, :iphone, product_type: 's') }
    let!(:bundle) { create(:product, :bundle, name: 'iPhone X bundle') }

    it 'scoped (activable) products name autocomplete' do
      get :autocomplete_product_name, params: { term: 'iphone' }
      parsed_response = JSON.parse response.body

      expect(parsed_response.map { |res| res['value'] }).to include activable_product.name
      expect(parsed_response.map { |res| res['value'] }).not_to include non_activable_product.name, bundle.name
    end

    it 'unscoped products name autocomplete' do
      get :autocomplete_non_bundle_product_name, params: { term: 'iphone' }
      parsed_response = JSON.parse response.body

      expect(parsed_response.map { |res| res['value'] }).to include activable_product.name, non_activable_product.name
      expect(parsed_response.map { |res| res['value'] }).not_to include bundle.name
    end
  end

  describe 'brand' do
    let!(:iphone_product) { create(:product, :iphone) }
    let!(:lumia_product) { create(:product, :lumia) }

    it 'iphone_product' do
      get :brand, params: { id: iphone_product.id }
      parsed_response = JSON.parse response.body

      expect(parsed_response).to eq [
                                      { 'company_brand' =>
                                                           { 'id'   => iphone_product.company_brand.id,
                                                             'name' => iphone_product.company_brand.description.to_s } }
                                    ]
    end

    it 'lumia_product' do
      get :brand, params: { id: lumia_product.id }
      parsed_response = JSON.parse response.body

      expect(parsed_response).to eq [
                                      { 'company_brand' =>
                                                           { 'id'   => lumia_product.company_brand.id,
                                                             'name' => lumia_product.company_brand.description.to_s } }
                                    ]
    end

    it 'no product' do
      get :brand, params: { id: -1 }
      parsed_response = JSON.parse response.body

      expect(parsed_response).to eq [
                                      { 'company_brand' =>
                                                           { 'id'   => nil,
                                                             'name' => nil } }
                                    ]
    end
  end

  describe 'by_brand' do
    let!(:iphone_product) do
      create(:product, :iphone,
             activable_system_owner:       true,
             enabled_for_imei_reservation: true,
             dhl_supply_chain:             true, dhl_pigeon_house: true,
             bookable_quantity:            8)
    end
    let!(:lumia_product) { create(:product, :lumia_dhl, bookable_quantity: 13) }

    it 'iphone brand' do
      get :by_brand, params: { brand_id: iphone_product.company_brand_id }
      parsed_response = JSON.parse response.body

      expect(parsed_response).to eq [
                                      {
                                        'product' => {
                                          'id'             => iphone_product.id,
                                          'name'           => iphone_product.name,
                                          'product_colors' => '[]'
                                        }
                                      }
                                    ]
    end

    it 'iphone brand with iphone out of stock' do
      iphone_product.update(bookable_quantity: 0, enabled_for_imei_reservation: true)
      iphone_product.reload

      get :by_brand, params: { brand_id: iphone_product.company_brand_id }
      parsed_response = JSON.parse response.body

      expect(parsed_response).to eq [
                                      {
                                        'product' => {
                                          'id'             => iphone_product.id,
                                          'name'           => "#{iphone_product.name} (in arrivo)",
                                          'product_colors' => '[]'
                                        }
                                      }
                                    ]
    end

    it 'lumia brand' do
      get :by_brand, params: { brand_id: lumia_product.company_brand_id }
      parsed_response = JSON.parse response.body

      expect(parsed_response).to eq [
                                      {
                                        'product' => {
                                          'id'             => lumia_product.id,
                                          'name'           => lumia_product.name,
                                          'product_colors' => '[]'
                                        }
                                      }
                                    ]
    end

    it 'no brand' do
      get :by_brand, params: { brand_id: -1 }
      parsed_response = JSON.parse response.body

      expect(parsed_response).to eq [
                                      {
                                        'product' => {
                                          'id'             => iphone_product.id,
                                          'name'           => iphone_product.name,
                                          'product_colors' => '[]'
                                        }
                                      },
                                      {
                                        'product' => {
                                          'id'             => lumia_product.id,
                                          'name'           => lumia_product.name,
                                          'product_colors' => '[]'
                                        }
                                      }
                                    ]
    end

    context 'franchising' do
      let(:user) { create(:user, :dealer_contact_access_imei_reservations) }
      before { stub_authentication user }
      before { user.dealer.kind = 'franchising' }

      it do
        get :by_brand, params: { brand_id: iphone_product.company_brand_id }
        parsed_response = JSON.parse response.body

        expect(parsed_response).to eq []

        iphone_product.update_columns(enabled_for_imei_reservation_franchising: true)
        get :by_brand, params: { brand_id: iphone_product.company_brand_id }
        parsed_response = JSON.parse response.body
        expect(parsed_response).to eq [
                                        {
                                          'product' => {
                                            'id'             => iphone_product.id,
                                            'name'           => iphone_product.name,
                                            'product_colors' => '[]'
                                          }
                                        }
                                      ]
      end
    end

    context 'gallery_gd' do
      let(:user) { create(:user, :dealer_contact_access_imei_reservations) }
      before { stub_authentication user }
      before { user.dealer.kind = 'gallery_gd' }

      it do
        get :by_brand, params: { brand_id: iphone_product.company_brand_id }
        parsed_response = JSON.parse response.body

        expect(parsed_response).to eq []

        iphone_product.update_columns(enabled_for_imei_reservation_gallery: true)
        get :by_brand, params: { brand_id: iphone_product.company_brand_id }
        parsed_response = JSON.parse response.body
        expect(parsed_response).to eq [
                                        {
                                          'product' => {
                                            'id'             => iphone_product.id,
                                            'name'           => iphone_product.name,
                                            'product_colors' => '[]'
                                          }
                                        }
                                      ]
      end
    end
  end

  describe 'image_for_activation' do
    let(:product) { create(:product, :iphone) }
    let(:params) do
      { 'phone_activation' => { 'product_id' => product.id } }
    end

    it do
      get :image_for_activation, params: params
      parsed_response = JSON.parse response.body
      expect(parsed_response).to eq({})

      create(:product_image, product: product)
      get :image_for_activation, params: params
      parsed_response = JSON.parse response.body
      expect(parsed_response.keys).to match_array(['image_url', 'image_title'])

      product.update_column(:final_installment, 0)
      get :image_for_activation, params: params
      parsed_response = JSON.parse response.body
      expect(parsed_response.keys).to match_array(['image_url', 'image_title'])

      product.update_column(:final_installment, 10)
      get :image_for_activation, params: params
      parsed_response = JSON.parse response.body
      expect(parsed_response.keys).to match_array(['image_url', 'image_title', 'final_installment'])
      expect(parsed_response['final_installment']).to eq '€ 10,00'
    end
  end

  describe 'image_for_imei_reservation' do
    let(:product) { create(:product, :iphone) }

    context 'no images present' do
      it do
        get :image_url_for_imei_reservation, params: { id: product.to_param }

        parsed_response = JSON.parse response.body

        expect(parsed_response).to eq('image' => nil)
      end
    end

    context 'images present' do
      let(:image_filename) { 'immagineschedaprodotto22323.jpg' }
      let(:prefix_path) { 'uploads/product_images' }
      let(:image_path) do
        "spec/fixtures/product_images/#{image_filename}"
      end
      let(:local_image) do
        Rails.root.join image_path
      end
      let(:product_image) { create(:product_image, product: product, image: File.new(local_image), title: 'Scheda prodotto 22323') }

      before do
        allow(product).to receive(:product_images).and_return [product_image]
      end

      it 'default size' do
        get :image_url_for_imei_reservation, params: { id: product.to_param }

        parsed_response = JSON.parse response.body

        expect(parsed_response).to eq(
          'image' => {
            'url'   => "/#{prefix_path}/#{product.id}/#{product_image.id}/#{image_filename}",
            'title' => 'Scheda prodotto 22323'
          }
        )
      end

      it 'specifying a size' do
        get :image_url_for_imei_reservation, params: { id: product.to_param, size: 'thumb' }

        parsed_response = JSON.parse response.body

        expect(parsed_response).to eq(
          'image' => {
            'url'   => "/#{prefix_path}/#{product.id}/#{product_image.id}/thumb_#{image_filename}",
            'title' => 'Scheda prodotto 22323'
          }
        )
      end
    end
  end

  describe 'update' do
    let(:product) { create(:product, :iphone) }

    before { create(:dealer_category, :standard_dealer) }

    it do
      post :update, params: { id: product.id, product: { available_promo_device_very: true,
        consignment: true, enabled_for_imei_reservation_franchising: true } }

      expect(product.reload.consignment).to be_truthy
      expect(product.reload.enabled_for_imei_reservation_franchising).to be_truthy
      expect(product.reload.available_promo_device_very).to be_truthy
    end
  end
end
