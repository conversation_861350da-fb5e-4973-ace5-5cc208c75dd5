require "rails_helper"

RSpec.describe StreetTypesController, type: :controller do
  context "With valid permissions" do
    let(:valid_attributes) do
      {
        value: "Via",
        ps: "Via"
      }
    end

    let(:invalid_attributes) do
      {
        value: nil,
        ps: nil
      }
    end

    let(:admin) { create(:user, :admin) }

    before do
      stub_authentication admin
    end

    describe "GET index" do
      it "assigns all street_types as @street_types" do
        street_type = StreetType.create! valid_attributes
        get :index, params: {}
        expect(assigns(:street_types)).to eq([street_type])
      end
    end

    describe "GET new" do
      it "assigns a new street_type as @street_type" do
        get :new, params: {}
        expect(assigns(:street_type)).to be_a_new(StreetType)
      end
    end

    describe "POST create" do
      describe "with valid params" do
        it "creates a new StreetType" do
          expect do
            post :create, params: { street_type: valid_attributes }
          end.to change(StreetType, :count).by(1)
        end

        it "assigns a newly created street_type as @street_type" do
          post :create, params: { street_type: valid_attributes }
          expect(assigns(:street_type)).to be_a(StreetType)
          expect(assigns(:street_type)).to be_persisted
        end

        it "redirects to the created portability_operator" do
          post :create, params: { street_type: valid_attributes }
          expect(response).to redirect_to(street_types_url)
        end
      end

      describe "with invalid params" do
        it "assigns a newly created but unsaved street_type as @street_type" do
          post :create, params: { street_type: invalid_attributes }
          expect(assigns(:street_type)).to be_a_new(StreetType)
        end

        it "re-renders the 'new' template" do
          post :create, params: { street_type: invalid_attributes }
          expect(response).to render_template("new")
        end
      end
    end

    describe "PUT update" do
      let!(:street_type) { StreetType.create! valid_attributes }

      context "with valid params" do
        let(:new_attributes) do
          {
            value: "Viale",
            ps: "Viale",
            ds: "Viale"
          }
        end

        it "updates the requested street_type" do
          patch :update, params: { id: street_type.to_param, street_type: new_attributes }
          street_type.reload
          expect(street_type.value).to eq("Viale")
          expect(street_type.ps).to eq("Viale")
          expect(street_type.ds).to eq("Viale")
        end

        it "assigns the requested street_type as @street_type" do
          patch :update, params: { id: street_type.to_param, street_type: valid_attributes }
          expect(assigns(:street_type)).to eq(street_type)
        end

        it "redirects to edit" do
          patch :update, params: { id: street_type.to_param, street_type: valid_attributes }
          expect(response).to redirect_to(street_types_url)
        end
      end

      context "with invalid params" do
        it "assigns the street_type as @street_type" do
          patch :update, params: { id: street_type.to_param, street_type: invalid_attributes }
          expect(assigns(:street_type)).to eq(street_type)
        end

        it "re-renders the 'edit' template" do
          patch :update, params: { id: street_type.to_param, street_type: invalid_attributes }
          expect(response).to render_template("edit")
        end
      end
    end

    context "DELETE destroy" do
      let!(:street_type) { StreetType.create! valid_attributes }

      it "destroys the requested street_type" do
        expect do
          delete :destroy, params: { id: street_type.to_param }
        end.to raise_error(AbstractController::ActionNotFound)
      end
    end
  end

  context "with invalid permissions" do
    let(:user) { create(:user, :super_user) }

    before do
      stub_authentication user
    end

    describe "GET index" do
      it "redirects back to root_path" do
        get :index, params: {}
        expect(response).to redirect_to(root_url)
        expect(response.code).to eq "302"
      end
    end
  end
end
