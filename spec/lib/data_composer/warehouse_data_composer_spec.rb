require 'rails_helper'

describe DataComposer::WarehouseDataComposer do
  let(:dealer) { create(:dealer, :with_warehouse) }
  let!(:second_warehouse) { create(:warehouse, dealer: dealer, name: 'Second warheouse') }
  let(:user) { create(:user, :dealer_contact_access_activations) }
  let(:phone_activation) { build_stubbed(:phone_activation, :fissa_w3_no_product, dealer: dealer) }
  let!(:offer_instance) { create(:offer_instance, sim_id: nil, sim_ids: '', product_id: nil, offer: phone_activation.offer) }
  let(:options) do
    { dealer_id: dealer.id, user_id: user.id, current_user: current_user }
  end

  before { allow(DataComposer::OfferDataComposer).to receive_message_chain(:new, :data).and_return [phone_activation.offer] }

  subject { described_class.new(options) }

  context 'current user !agent' do
    let(:current_user) { create(:user, :admin) }

    it { expect(subject.data).to eq dealer.warehouses }
  end

  context 'current user is agent' do
    let(:current_user) { create(:user, :agent) }

    before { second_warehouse.update(agent_wind_tre_id: current_user.id) }

    it { expect(subject.data).to eq [second_warehouse] }
  end
end