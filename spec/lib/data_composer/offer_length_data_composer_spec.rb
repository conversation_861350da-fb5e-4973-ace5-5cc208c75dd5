require 'rails_helper'

describe DataComposer::OfferLengthDataComposer, solr: true do
  let(:second_plan) { create(:plan, :top_infinito) }
  let!(:first_offer_instance) { create(:offer_instance, length: 10) }
  let!(:second_offer_instance) { create(:offer_instance, plan: second_plan, length: 20) }

  before do
    Sunspot.index OfferInstance.all
    Sunspot.commit(true)
  end

  it do
    expect(described_class.new({}).data).to eq [10, 20]
    expect(described_class.new({ operator_id: first_offer_instance.operator_id, plan_id: second_plan.id, dealer_id: 1, status: 'test' }).data).to eq [20]
    expect(described_class.new({length: nil}).data).to eq [10, 20]
  end
end
