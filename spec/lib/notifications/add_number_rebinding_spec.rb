require "rails_helper"

describe Notifications::AddNumberRebinding do

  let(:phone_activation) { stub_model(PhoneActivation) }
  let(:operation_attivazione_completata) { stub_model(Operation, id: OperationConstants::ATTIVAZIONE_COMPLETATA) }
  let(:operation_controllo_mdp) { stub_model(Operation, id: OperationConstants::CONTROLLO_METODO_DI_PAGAMENTO) }
  let(:operation_number_rebinding) { stub_model(Operation, id: OperationConstants::SEGNALAZIONE_NUMERO_ATTIVO) }
  let(:alert_message) { double }
  let(:parameters) {
    {
      operation_id: OperationConstants::CONTROLLO_METODO_DI_PAGAMENTO,
      phone_activation_id: phone_activation.id,
      status: "ko",
      alert_message: alert_message
    }
  }

  before { allow(PhoneActivation).to receive(:find).and_return phone_activation }

  context "#available? " do
    before { allow(phone_activation).to receive(:next_operation_is)
                                          .with(OperationConstants::SEGNALAZIONE_NUMERO_ATTIVO)
                                          .and_return true }
    context "with status ko" do
      it { expect(described_class.new(parameters).available?).to be_falsey }
    end

    context "status ok,operation_id OperationConstants::CONTROLLO_METODO_DI_PAGAMENTO" do
      before { parameters.merge!(status: "ok") }
      it { expect(described_class.new(parameters).available?).to be_truthy }
    end

    context "status ok,operation_id not OperationConstants::CONTROLLO_METODO_DI_PAGAMENTO" do
      before { parameters.merge!(status: "ok") }
      before { parameters.merge!(operation_id: OperationConstants::ATTIVAZIONE_COMPLETATA) }
      it { expect(described_class.new(parameters).available?).to be_falsey }
    end

    context "without next operation valid" do
      before { allow(phone_activation).to receive(:next_operation_is)
                                            .with(OperationConstants::SEGNALAZIONE_NUMERO_ATTIVO)
                                            .and_return false }
      before { parameters.merge!(status: "ok") }
      it { expect(described_class.new(parameters).available?).to be_falsey }
    end
  end

  context "#notify" do
    before { expect(alert_message).to receive_message_chain(:new, :insert_message)
                                        .with(Alerts::PhoneActivationAlertMessage::PHONE_ACTIVATION_ADD_NUMBER_REBINDING) }
    it { described_class.new(parameters).notify }
  end

end
