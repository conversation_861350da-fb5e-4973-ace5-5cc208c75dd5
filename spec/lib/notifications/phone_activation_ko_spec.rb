require "rails_helper"

describe Notifications::PhoneActivationKo do
  let(:customer) { stub_model(Customer) }
  let(:phone_activation) { stub_model(PhoneActivation, customer: customer) }
  let(:alert_message) { double }
  let(:parameters) do
    {
      operation_id:        OperationConstants::ASSEGNAZIONE_SERIALE,
      phone_activation_id: phone_activation.id,
      status:              "ko",
      alert_message:       alert_message
    }
  end

  before { allow(PhoneActivation).to receive(:find).and_return phone_activation }

  context "#available?" do
    it { expect(described_class.new(parameters).available?).to be_truthy }
  end

  it "with OperationConstants::ATTIVAZIONE_VERIFICATA" do
    parameters[:operation_id] = OperationConstants::ATTIVAZIONE_VERIFICATA

    expect(described_class.new(parameters).available?).to be_truthy
  end
  it "with OperationConstants::ATTIVAZIONE_VERIFICATA" do
    parameters[:operation_id] = OperationConstants::VERIFICA_CONVERGENZA

    expect(described_class.new(parameters).available?).to be_truthy
  end
end
