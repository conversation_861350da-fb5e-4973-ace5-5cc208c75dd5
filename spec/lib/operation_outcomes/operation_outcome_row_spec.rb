require 'rails_helper'

describe OperationOutcomes::OperationOutcomeRow do
  let(:phone_activation) { create(:phone_activation, :abbonamento_ricaricabile, main_phone_number: '33333', w3_customer_code: 'xxxx') }
  let(:operation_outcome_attivazione_completata) { create(:operation_outcome,
                                                          :attivazione_completata,
                                                          phone_activation: phone_activation,
                                                          note:             'Lorem ipsum') }

  subject { described_class.new(operation_outcome_attivazione_completata) }

  it { expect(subject.send(:provide_fields_for, false)).to eq OperationOutcomes::OperationOutcomeRow::ATTIVAZIONE_COMPLETATA_OK_FIELDS }

  describe '#render_timeline(is_dealer)' do
    let(:attivazione_completata_additional_info) do
      phone_number  = "#{OperationOutcome.human_attribute_name(:main_phone_number)}: #{phone_activation.main_phone_number}"
      customer_code = "#{OperationOutcome.human_attribute_name(:w3_customer_code)}: #{phone_activation.w3_customer_code}"
      "<ul><li>#{phone_number}</li><li>#{customer_code}</li></ul>"
    end

    it do
      expect(subject.render_timeline(false)).to eq "<div>Note: Lorem ipsum</div>#{attivazione_completata_additional_info}"
      expect(subject.render_timeline(true)).to eq "<div>Note: Lorem ipsum</div>#{attivazione_completata_additional_info}"
    end

    it 'without infos' do
      phone_activation.update_columns(main_phone_number: nil, w3_customer_code: nil)

      expect(subject.render_timeline(false)).to eq "<div>Note: Lorem ipsum</div>"
      expect(subject.render_timeline(true)).to eq "<div>Note: Lorem ipsum</div>"
    end

    context 'operation_outcome INSERITA_DA_PARTNER' do
      let(:operation_outcome_inserita_da_partner) { create(:operation_outcome, :inserita_da_partner, phone_activation: phone_activation, status: 'ok') }

      subject { described_class.new(operation_outcome_inserita_da_partner) }
      
      context 'when phone_activation is linked to a parent energy contract' do
        let(:parent_energy_contract) { create(:energy_contract) }

        before { phone_activation.update_columns(parent_energy_contract_id: parent_energy_contract.id) }

        it do
          expect(subject.render_timeline(false)).to include('Duplicata da Luce&Gas')
          expect(subject.render_timeline(false)).to include(parent_energy_contract.hex_id)
        end
      end
    end

    context 'does not modify ATTIVAZIONE_COMPLETATA_OK_FIELDS' do
      it do
        subject.render_timeline(true)
        expect(described_class::ATTIVAZIONE_COMPLETATA_OK_FIELDS).to eq(
                                                                       {
                                                                         activation_date:     'text_field:required',
                                                                         note:                'text_area',
                                                                         operator_created_at: 'text_field:required',
                                                                         private_note:        'text_area'
                                                                       }
                                                                     )
      end

      context 'fixed activation' do
        let(:fixed_phone_activation_kind) { create(:phone_activation_kind, :w3frmd) }
        let(:w3_operator) { create(:operator, :w3) }

        before { phone_activation.update(phone_activation_kind: fixed_phone_activation_kind) }

        it do
          expect(subject.render_timeline(false)).to eq "<div>Note: Lorem ipsum</div>#{attivazione_completata_additional_info}"
          expect(subject.render_timeline(true)).to eq "<div>Note: Lorem ipsum</div>#{attivazione_completata_additional_info}"

          phone_activation.update(operator: w3_operator)
          expect(subject.render_timeline(false)).to eq "<div>Note: Lorem ipsum</div><div>Verifica l'avanzamento del Provisioning accedendo a POS Evo nel tab dedicato: Verifica stato ordini Fisso</div>#{attivazione_completata_additional_info}"
          expect(subject.render_timeline(true)).to eq "<div>Note: Lorem ipsum</div><div>Verifica l'avanzamento del Provisioning accedendo a POS Evo nel tab dedicato: Verifica stato ordini Fisso</div>#{attivazione_completata_additional_info}"
        end
      end
    end

    context 'operation_outcome SPEDIZIONE_EFFETTUATA_TRACKING' do
      let(:operation_outcome_spedizione_effettuata) { create(:operation_outcome, :spedizione_effettuata_tracking, phone_activation: phone_activation, status: 'ok') }
      let!(:shipment) { create(:shipment, forwarder_id: nil, tracking_url: nil, shippable: operation_outcome_spedizione_effettuata) }

      before do
        operation_outcome_spedizione_effettuata.operation.fields = {
          ok:   {},
          ko:   {
            ko_reason_id: 'select:required',
            note:         'text_area',
            private_note: 'text_area'
          },
          ko_r: {
            ko_reason_id: 'select:required',
            note:         'text_area',
            private_note: 'text_area'
          }
        }
      end

      it do
        expect(described_class.new(operation_outcome_spedizione_effettuata).render_timeline(false)).to eq "Spedizione in partenza, in attesa del codice di tracciamento da parte del corriere.<br/>Rif. DHL: #{shipment.padded_id}"
        expect(described_class.new(operation_outcome_spedizione_effettuata).render_timeline(true)).to eq 'Spedizione in partenza, in attesa del codice di tracciamento da parte del corriere.'
      end
    end

    context 'operation_outcome PHONE_ACTIVATION_CONFIRMED' do
      let(:operation_outcome_attivazione_confermata) { create(:operation_outcome, :attivazione_confermata, phone_activation: phone_activation, status: 'ko') }

      before { phone_activation.update_columns(ko_at: Time.now) }

      it do
        expect(described_class.new(operation_outcome_attivazione_confermata).render_timeline(false)).to eq "<div>Motivazione: </div><div>Data ko #{I18n.l(Date.today)}</div>"
        expect(described_class.new(operation_outcome_attivazione_confermata).render_timeline(true)).to eq "<div> </div><div>Data ko #{I18n.l(Date.today)}</div>"
      end
    end

    context 'operation_outcome ATTESA_APPUNTAMENTO' do
      let(:operation_outcome_attesa_appuntamento) { create(:operation_outcome, :attesa_appuntamento, phone_activation: phone_activation, status: 'ok', note: 'Note') }

      before do
        operation_outcome_attesa_appuntamento.operation.fields = {
          ok: { note: 'text_area' }
        }
      end

      it { expect(described_class.new(operation_outcome_attesa_appuntamento).render_timeline(false)).to eq '<div>Note: Note</div>' }
    end

    context 'ko_r operation outcome' do
      before { operation_outcome_attivazione_completata.update(status: 'ko_r') }

      context 'from a robot operation' do
        let(:robot_note) { "Errore in step Verifica carta di credito <br/><a href='https://kolme-production.s3-eu-west-1.amazonaws.com/uploads/robot-worklogs/b3ecd/Tentativo_1_d9b85e3da6a89f9a662dee481046d353beb8d103.zip' target='_blank'>File allegato 0</a>" }

        before { operation_outcome_attivazione_completata.update(user_id: 1, private_note: robot_note) }

        context 'when S3 contents are no longer present' do
          before { operation_outcome_attivazione_completata.update(created_at: (Settings.phone_activations.days_to_robot_logs_expiry + 1).days.ago) }
          it 'hides S3 contents links' do
            expect(subject.render_timeline(false)).to eq "<div>Motivazione: </div><div>Note: Lorem ipsum</div><div>Note Private Kolme: Errore in step Verifica carta di credito</div>"
          end
        end
      end

      context 'with ko_r robot operation outcome' do
        before { Timecop.freeze Time.now }
        after { Timecop.return }
        let(:service) { double }
        before { operation_outcome_attivazione_completata.update(include_last_robot_screenshot: true,
                                                                 additional_data:               { robot_debug: { screenshot: 'https://kolme-staging.s3-eu-west-1.amazonaws.com/uploads/robot-worklogs/e36b1/Tentativo_5_LastScreen.png',
                                                                                                                 zip:        'https://kolme-staging.s3-eu-west-1.amazonaws.com/uploads/robot-worklogs/e36b1/Tentativo_5_73f6e71df7fb456fb612b2b7f11abfde340f4e51.zip' }
                                                                 }) }
        context 'call s3 for screenshot' do
          # before { expect(S3FileService).to receive(:new).and_return(service) }
          # before { expect(service).to receive(:safe_url_for).with('uploads/robot-worklogs/e36b1/Tentativo_5_LastScreen.png', Time.now + 1.hour ).and_return 'this_is_a_safe_url' }

          it do
            result = subject.render_timeline(false)

            expect(result).to include("<i class=\"fa fa-paperclip\"></i> Clicca qui per visualizzare l&#39;esito di Pos")
            expect(result).to include("/phone_activations/#{operation_outcome_attivazione_completata.phone_activation.id}/operation_outcomes/#{operation_outcome_attivazione_completata.id}/robot_attachment?kind=#{OperationOutcome::SCREENSHOT_CONTENT}")
          end
        end

        context 'without screenshot present' do
          before { operation_outcome_attivazione_completata.update(include_last_robot_screenshot: true, additional_data: { robot_debug: { screenshot: nil, zip: 'https://kolme-staging.s3-eu-west-1.amazonaws.com/uploads/robot-worklogs/e36b1/Tentativo_5_73f6e71df7fb456fb612b2b7f11abfde340f4e51.zip' } }) }

          it { expect(subject.render_timeline(false)).not_to include 'this_is_a_safe_url' }
        end
      end

      context 'when robot debug is missing' do
        before { operation_outcome_attivazione_completata.update(include_last_robot_screenshot: true, additional_data: {}) }

        it { expect(subject.render_timeline(false)).not_to include("Clicca qui per visualizzare l'esito di Pos") }
      end

      context 'when additional data is missing' do
        before { operation_outcome_attivazione_completata.update(include_last_robot_screenshot: true, additional_data: nil) }

        it { expect(subject.render_timeline(false)).not_to include("Clicca qui per visualizzare l'esito di Pos") }
      end
    end

    context 'ATTIVAZIONE_INSERITA' do
      let(:operation_outcome_attivazione_inserita) { create(:operation_outcome, :attivazione_inserita, phone_activation: phone_activation, status: 'ok') }

      subject { described_class.new(operation_outcome_attivazione_inserita) }

      it 'includes the w3_customer_code information' do
        expected_info = "<ul><li>#{OperationOutcome.human_attribute_name(:w3_customer_code)}: #{phone_activation.w3_customer_code}</li></ul>"
        expect(subject.render_timeline(false)).to include(expected_info)
      end

      it 'does not include w3_customer_code when it is not present' do
        phone_activation.update_columns(w3_customer_code: nil)
        expect(subject.render_timeline(false)).not_to include(OperationOutcome.human_attribute_name(:w3_customer_code))
      end

      it 'does not include the additional info when status is not ok' do
        operation_outcome_attivazione_inserita.update(status: 'ko')
        expect(subject.render_timeline(false)).not_to include(OperationOutcome.human_attribute_name(:w3_customer_code))
      end
    end
  end
end
