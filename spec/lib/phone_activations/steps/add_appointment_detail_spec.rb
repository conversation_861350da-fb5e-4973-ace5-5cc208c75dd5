require 'rails_helper'

describe PhoneActivations::Steps::AddAppointmentDetail do
  it { expect(described_class::MENU_LABEL).to eq 'Appuntamento' }
  it { expect(described_class::ROUTE).to eq 'add_appointment_detail' }

  let(:province) { create(:province, :genova, area_code: '2') }
  let(:city) { create(:city, :genova, province: province) }
  let(:fixed_line_detail) { create(:fixed_line_detail, city: city) }
  let(:phone_activation) { fixed_line_detail.phone_activation }

  subject { described_class.new(phone_activation: phone_activation) }

  describe '#available?' do
    it do
      expect(subject.available?).to be_falsey

      #plan direct_installation flag
      phone_activation.plan.direct_installation = true
      expect(subject.available?).to be_falsey

      #dealer enabled_for_fwa_installation
      allow_any_instance_of(Dealer).to receive(:fwa_installer_status_active?).and_return true
      expect(subject.available?).to be_falsey

      #installation_area matching fixed_plan province area code
      fixed_line_detail.location_province.update(area_code: '4')
      phone_activation.warehouse.city = city
      expect(subject.available?).to be_truthy

      #warehouse no kolme installation
      phone_activation.warehouse.update(no_kolme_installation: true)
      expect(subject.available?).to be_falsey
      phone_activation.warehouse.update(no_kolme_installation: nil)

      create(:appointment_detail, phone_activation: phone_activation, customer_preferences: 'preferences')
      expect(subject.available?).to be_falsey
    end
  end

  describe '#completed?' do
    let(:appointment_detail) { build(:appointment_detail, phone_activation: phone_activation) }

    it do
      expect(subject.completed?).to be_falsey

      appointment_detail.save
      expect(subject.completed?).to be_truthy
    end
  end
end