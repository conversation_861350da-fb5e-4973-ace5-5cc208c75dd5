require 'rails_helper'

describe PhoneActivations::Steps::AddMissingDocumentsStep do
  let!(:mnp_application_setting) { create(:application_setting, :portability_otp, value: '1') }
  let(:phone_activation) { build_stubbed(:phone_activation, :ricaricabile_solo_sim, do_number_portability: 'Y') }

  it do
    expect(described_class::MENU_LABEL).to eq 'Acquisizione documenti MNP'
    expect(described_class::ROUTE).to eq 'add_missing_documents'
  end

  subject { described_class.new(phone_activation: phone_activation) }

  context '#available?' do
    context 'phone_activation !duplicating' do
      it { expect(subject.available?).to be_falsey }
    end

    context 'phone_activation duplicating' do
      let(:parent_activation) { stub_model(PhoneActivation) }

      before { phone_activation.parent_phone_activation = parent_activation }

      it { expect(subject.available?).to be_truthy }
    end
  end

  context '#completed?' do
    let(:parent_activation) { stub_model(PhoneActivation) }

    before { phone_activation.parent_phone_activation = parent_activation }

    it { expect(subject.completed?).to be_falsey }

    it 'with all documents' do
      allow(phone_activation).to receive(:has_all_mnp_documents?).and_return true

      expect(subject.completed?).to be_truthy
    end

    context 'phone activations that not require sim document' do
      let(:business_customer_kind) { build_stubbed(:customer_kind, :persona_giuridica) }
      let(:fiscal_code_document) { create(:document, :document_file, content: 'fiscal_code', documentable: parent_activation) }

      it do
        phone_activation.plan.customer_kinds = [business_customer_kind]
        expect(subject.completed?).to be_falsey

        parent_activation.documents = [fiscal_code_document]
        expect(subject.completed?).to be_truthy
      end
    end
  end

  xcontext 'duplicating with different owner' do
    let(:parent_activation) { stub_model(PhoneActivation) }
    let(:portability_operator) { create(:portability_operator, :mobile) }
    let(:phone_activation) { create(:phone_activation, :ricaricabile_solo_sim, do_number_portability: 'Y') }
    let(:nmp) { build(:number_portability_detail, :different_owner, phone_activation: phone_activation) }
    let!(:fiscal_code_document) { create(:document, :document_file, kind: 'documents', content: 'fiscal_code', documentable: phone_activation) }
    let!(:sim_document) { create(:document, :document_file, kind: 'sim_card', content: 'sim', documentable: phone_activation) }
    before do
      phone_activation.parent_phone_activation = parent_activation
      nmp.source_operator                 = portability_operator
      nmp.skip_app_validations                 = true
      nmp.save
    end
    it 'check step' do
      expect(phone_activation.next_editing_step).to include('add_missing_documents')

      #with all required documentssun
      create(:document, :document_file, kind: 'different_owner_mnp_documents', content: 'fiscal_code', documentable: phone_activation)
      create(:document, :document_file, kind: 'different_owner_mnp_documents', content: 'sim', documentable: phone_activation)

      expect(phone_activation.next_editing_step).not_to include('add_missing_documents')
      expect(described_class.new(phone_activation: phone_activation).completed?).to be_truthy
    end

  end
end
