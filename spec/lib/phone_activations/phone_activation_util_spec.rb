require "spec_helper"

describe PhoneActivations::PhoneActivationUtil do
  context "PhoneActivations::PhoneActivationUtil.show_unlock_buttons?(params)" do
    subject { PhoneActivations::PhoneActivationUtil.show_unlock_buttons?(params) }

    context "when search doesnt include :activated_at_from param" do
      let(:params) { { search: { first_param: "first_value", second_param: "second_value", third_param: "third_value" } } }
      it           { is_expected.to be_falsey }
    end

    context "when search includes :activated_at_from param, but less than 2 other params" do
      let(:params) { { search: { activated_at_from: 2.days.ago, first_param: "first_value" } } }
      it           { is_expected.to be_falsey }
    end

    context "when search includes :activated_at_from param and at least 2 other params" do
      let(:params) { { search: { activated_at_from: 2.days.ago, first_param: "first_value", second_param: "second_value" } } }
      it           { is_expected.to be_truthy }
    end

    context "it rejects blank values" do
      let(:params) { { search: { activated_at_from: 2.days.ago, first_param: "", second_param: "second_value" } } }
      it           { is_expected.to be_falsey }
    end

    context "the param show is not counted" do
      let(:params) { { search: { activated_at_from: 2.days.ago, show: "1", second_param: "second_value" } } }
      it           { is_expected.to be_falsey }
    end

    context "the param activated_at_to is not counted" do
      let(:params) { { search: { activated_at_from: 2.days.ago, activated_at_to: 2.days.from_now, second_param: "second_value" } } }
      it           { is_expected.to be_falsey }
    end

    context "facets are counted" do
      let(:params) do
        {
          search: { activated_at_from: 2.days.ago, second_param: "second_value" },
          PhoneActivation::ALL_FACETS.first => "facet_value"
        }
      end
      it           { is_expected.to be_truthy }
    end

    context "when search params are nil" do
      let(:params) { nil }
      it           { is_expected.to be_falsey }
    end
  end
end
