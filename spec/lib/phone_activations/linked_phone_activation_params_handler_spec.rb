require "rails_helper"

describe PhoneActivations::LinkedPhoneActivationParamsHandler do
  let(:phone_activation) { mock_model(PhoneActivation, id: 1) }

  it "deletes linked hex_id param from hash" do
    params = { linked_phone_activation_hex_id: "abcde" }
    expect(described_class.handle_params(params)).not_to include(linked_phone_activation_hex_id: "abcde")
  end

  it "adds must_confirm_linked_selection: true" do
    params = { linked_phone_activation_hex_id: "abcde" }
    expect(described_class.handle_params(params)).not_to include(must_confirm_linked_selection: true)
  end

  context "deletes parent phone activation reference" do
    it "with phone_activation_params[:linked_selection] == linked_selection_no" do
      params = {
        linked_phone_activation_hex_id: "abcde",
        linked_selection: "linked_selection_no",
        linked_phone_activation_id: 123,
        must_confirm_linked_selection: true,
        expected_linked_activations_count: 5
      }
      expect(described_class.handle_reset_params_on_duplication(params)).to include(linked_phone_activation_id: nil)
      expect(described_class.handle_reset_params_on_duplication(params)).to include(must_confirm_linked_selection: false)
      expect(described_class.handle_reset_params_on_duplication(params)).to include(expected_linked_activations_count: 0)
    end

    it "with phone_activation_params[:linked_selection] == linked_selection_parent" do
      params = {
        linked_phone_activation_hex_id: "abcde",
        linked_selection: "linked_selection_parent",
        linked_phone_activation_id: 123,
        must_confirm_linked_selection: true,
        expected_linked_activations_count: 5
      }
      expect(described_class.handle_reset_params_on_duplication(params)).to eq params
    end
  end
end
