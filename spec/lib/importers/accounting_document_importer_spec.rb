require "rails_helper"

describe Importers::AccountingDocumentImporter, vcr: true do
  let(:success_callbacks) { [proc {}] }
  let(:failure_callbacks) { [proc { |e| KolmeLogger.error(e) }] }
  let(:args) { { vat: "***********", doc_kind: "documento_di_trasporto", company_name: "FM Informatica" } }

  context "create accounting documents", :solr do
    context "executed once" do
      before do
        described_class.new(args).run(success_callbacks, failure_callbacks)
      end

      it { expect(AccountingDocument.count).to be 149 }
      it {Sunspot.commit(true); expect(Sunspot.search(AccountingDocument) { paginate page: 1, per_page: AccountingDocument.count }.hits.count).to eq 149 }
    end

    context "executed twice" do
      it "imported twice does not duplicate items" do
        described_class.new(args).run(success_callbacks, failure_callbacks)
        described_class.new(args).run(success_callbacks, failure_callbacks)
        expect(AccountingDocument.count).to eq 149
        expect(AccountingDocument.first.total).to eq "468,76"
        Sunspot.commit(true)
        expect(Sunspot.search(AccountingDocument) { paginate page: 1, per_page: AccountingDocument.count }.hits.count).to eq 149
      end
    end

    context "daily download documents from gamma" do
      let(:args) { { vat: "***********", doc_kind: "documento_di_trasporto", company_name: "FM Informatica", mode: "daily" } }

      before { expect(Delayed::Job).to receive(:enqueue) }

      it "import" do
        VCR.use_cassette("Importers::AccountingDocumentImporter/create_accounting_documents/gamma_accounting_documents") do
          described_class.new(args).run(success_callbacks, failure_callbacks)
          expect(AccountingDocument.count).to eq 149
        end
      end
    end

    context "update accounting document without delete" do
      let(:import_session) { Digest::MD5.hexdigest (Time.now + 10.seconds).to_s }
      let!(:accounting_document_existent) { create(:accounting_document, total: "455,76", doc_kind: "CLI_DDT", doc_number: "16", sezionale: "SK", vat: "***********", import_session: import_session) }

      it "import" do
        VCR.use_cassette("Importers::AccountingDocumentImporter/create_accounting_documents/gamma_accounting_documents") do
          original_accounting_document_id = accounting_document_existent.id
          described_class.new(args).run(success_callbacks, failure_callbacks)
          document_post_import = AccountingDocument.where(doc_kind: "CLI_DDT", doc_number: "16", sezionale: "SK", vat: "***********").first

          expect(document_post_import.reload.id).to eq original_accounting_document_id
          expect(document_post_import.reload.total).to eq "468,76"
          expect(document_post_import.reload.import_session).to eq import_session
        end
      end
    end
  end
end
