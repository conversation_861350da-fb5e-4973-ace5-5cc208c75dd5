require 'rails_helper'

describe Util::FacetsUtil, :solr do
  let!(:product) { create(:product, :lumia, can_be_requested: true) }
  let!(:product_for_agent) { create(:product, :iphone, can_be_requested: true, store_tag_list: ["tag1", Product::ORDERABLE_BY_AGENTS_TAG]) }
  let!(:accessory_product) { create(:product, :parrot_drone, can_be_requested: true) }

  let(:params) { {
                   "facets"       => "can_be_requested-1/macro_category-wind_tre_smartphone",
                   "facet_params" =>
                      { "can_be_requested" => [1] }
                 } }
  let(:current_user) { build_stubbed(:user, :admin) }
  let(:search) { Repositories::ProductSolrRepository.new.search(params, current_user, false, {}) }

  let(:splitted_facets) { subject.split_facets(params['facet_params']) }

  before { Sunspot.index Product.all; Sunspot.commit(true) }

  subject { described_class.new(search, :orders, current_user) }

  context '#split_facets(params)' do
    it do
      expect(splitted_facets[1].keys).to eq(["macro_category", "can_be_requested", "store_tag_list", "company_brand"])
      expect(splitted_facets[1]['store_tag_list'].map(&:value)).to eq(["tag1", "tag2", "Acquistabili Agenti"])
    end

    context 'dealer user' do
      let(:current_user) { create(:user, :dealer_user) }
      let!(:dealer_contact) { create(:dealer_contact, user: current_user) }

      it do
        current_user.reload
        expect(splitted_facets[1].keys).to eq(["macro_category", "store_tag_list", "company_brand"])
        expect(splitted_facets[1]['store_tag_list'].map(&:value)).to eq(["tag1", "tag2"])
      end
    end
  end
end
