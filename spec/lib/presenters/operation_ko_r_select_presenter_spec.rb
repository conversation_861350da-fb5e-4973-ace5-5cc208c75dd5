require 'rails_helper'

describe OperationKoRSelectPresenter do
  let!(:ko_reason_1) { create(:ko_reason, id: 1, description: 'Sospeso Generico') }
  let!(:ko_reason_2) { create(:ko_reason, id: 2, description: 'Altro') }
  let!(:operation_2) { create(:operation, id: 2, description: 'Upload documenti') }
  let!(:operation_3) { create(:operation, id: 3, description: 'Controllo documenti') }
  let!(:search) { double }

  let(:operation_ko_r_facets) {
    OpenStruct.new(rows: [OpenStruct.new(value: '3_1', count: 1),
                          OpenStruct.new(value: '2_1', count: 3),
                          OpenStruct.new(value: '3_2', count: 1)])

  }

  context '#collection' do
    before { allow(search).to receive(:facet).and_return operation_ko_r_facets }

    subject { described_class.new(search, {}) }

    it do
      expect(subject.collection).to eq [
                                         ['(Controllo documenti) Altro', '3_2'],
                                         ['(Controllo documenti) Sospeso Generico', '3_1'],
                                         ['(Upload documenti) Sospeso Generico', '2_1']
                                       ]
    end
  end
end