require "rails_helper"

describe FinancingPresenter do
  let(:financing_before_electronic_invoice) { stub_model(Financing, created_at: DateTime.new(2018, 12, 31)) }
  let(:financing_after_electronic_invoice) { stub_model(Financing, created_at: Time.now) }

  let(:template) { ActionView::Base.empty }

  it { expect(described_class.new(financing_after_electronic_invoice, template).sella_vat).to eq "P.IVA 02675650028" }
  it { expect(described_class.new(financing_before_electronic_invoice, template).sella_vat).to eq "CF./P.IVA 02007340025" }
end