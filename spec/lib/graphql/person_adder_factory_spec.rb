require "rails_helper"

describe Graphql::PersonAdderFactory do
  let(:phone_activation) do
    create(:phone_activation, :abbonamento_ricaricabile)
  end

  context "App ready activation with draft customer " do
    before do
      phone_activation.plan.update_column(:app_ready, true)
      phone_activation.customer.update_column(:state, "draft")
    end

    it { expect(described_class.for(phone_activation)).to eq Graphql::CustomerAdder }
  end

  context "Activation without draft payment method" do
    before do
      create(:payment_method_detail, phone_activation: phone_activation, state: "draft")
    end

    it { expect(described_class.for(phone_activation)).to eq Graphql::PaymentMethodDetailAdder }
  end

  context "Activation with draft different owner mnp" do
    before { allow(phone_activation).to receive(:next_editing_step_action).and_return 'add_number_portability_detail' }

    it { expect(described_class.for(phone_activation)).to eq Graphql::DifferentOwnerNumberPortabilityDetailAdder }
  end

  context "Activation in unsupported state" do
    before do
      phone_activation.update(customer: nil)
    end

    it do
      expect { described_class.for(phone_activation) }
        .to raise_error Graphql::PersonAdderFactory::UnsupportedStateError, "phone activation does not support a document acquisition in its present state"
    end
  end
end