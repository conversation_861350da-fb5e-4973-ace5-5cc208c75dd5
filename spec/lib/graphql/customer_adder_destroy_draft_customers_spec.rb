require 'rails_helper'

describe Graphql::CustomerAdder do
  describe '#add_person', vcr: true do
    let(:old_phone_activation) { create(:phone_activation, :fissa_w3_no_product) }
    let(:customer) { old_phone_activation.customer }
    let(:new_phone_activation) { create(:phone_activation, :abbonamento_ricaricabile, customer: customer) }

    let(:customer_input) do
      {
        cf:            customer.cf_or_vat,
        first_name:    customer.first_name,
        last_name:     customer.last_name,
        birth_date:    customer.birth_date.to_s,
        birth_place:   "#{customer.birth_place} (#{customer.birth_province.code})",
        birth_country: '',
        city:          customer.city.description,
        nationality:   'ITALIAN'
      }
    end

    let(:args) do
      {
        id:                        new_phone_activation.id,
        customer_input_type:       customer_input,
        minor_customer_input_type: {}
      }
    end

    before do
      Plan.update_all(app_ready: true)
      customer.update_column(:state, 'draft')
    end

    subject { described_class.new(args) }

    it do
      subject.add_person

      expect(new_phone_activation.reload.customer_id).to eq old_phone_activation.customer_id
      expect(old_phone_activation.reload.customer).not_to be_nil
    end

    context 'minor_customer' do
      let(:minor_customer) { create(:customer, :second_customer, state: 'draft') }

      let(:minor_customer_input) do
        {
          cf:            minor_customer.cf_or_vat,
          first_name:    minor_customer.first_name,
          last_name:     minor_customer.last_name,
          birth_date:    minor_customer.birth_date.to_s,
          birth_place:   "#{minor_customer.birth_place} (#{minor_customer.birth_province.code})",
          birth_country: '',
          city:          minor_customer.city.description,
          nationality:   'ITALIAN'
        }
      end

      before do
        new_phone_activation.plan.update_column(:doppia_anagrafica, true)
        old_phone_activation.update_column(:minor_customer_id, minor_customer.id)

        args[:minor_customer_input_type] = minor_customer_input
      end

      it do
        subject.add_person

        expect(new_phone_activation.reload.minor_customer_id).to eq old_phone_activation.minor_customer_id
        expect(old_phone_activation.reload.minor_customer).not_to be_nil
      end
    end
  end
end