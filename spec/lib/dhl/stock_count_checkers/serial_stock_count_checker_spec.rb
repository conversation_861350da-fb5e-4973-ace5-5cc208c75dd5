require 'rails_helper'

describe Dhl::StockCountCheckers::SerialStockCountChecker do
  let!(:lumia_item_outside_pigeon_house) do
    create(:item, :lumia_dhl_item, product: create(:product, :lumia_dhl_outside_pigeon_house))
  end

  context '::find_mismatches(product)' do
    subject { described_class.new(lumia_item_outside_pigeon_house.product).find_mismatches }

    it 'returns nil if there are no mismatches for the product' do
      is_expected.to be_empty
    end

    context 'mismatches found' do
      let(:mismatched_aisow_error) do
        { amount_in_system_owner_warehouse: { actual: 1, on_db: 3 } }
      end
      let(:mismatched_bq_and_aisow_error) do
        {
          bookable_quantity:                { actual: 1, on_db: 4 },
          amount_in_system_owner_warehouse: { actual: 1, on_db: 3 }
        }
      end

      before { lumia_item_outside_pigeon_house.product.update_columns(amount_in_system_owner_warehouse: 3) }

      context 'only in amount_in_system_owner_warehouse' do
        it 'returns a proper error hash' do
          is_expected.to eq mismatched_aisow_error
        end
      end

      context 'also in bookable quantity' do
        context 'if there are no unshipped orders or financings for the product' do
          before { lumia_item_outside_pigeon_house.product.update_columns(bookable_quantity: 4) }

          it 'adds it to errors' do
            is_expected.to eq mismatched_bq_and_aisow_error
          end
        end

        context 'if there are unshipped item orders to make up for the difference' do
          let(:order) { stub_model(Order) }
          let(:shopping_cart) { create(:shopping_cart) }
          let(:shopping_cart_item) do
            create(:shopping_cart_item, quantity: 2,
                   owner:                         shopping_cart,
                   item:                          lumia_item_outside_pigeon_house.product)
          end
          let(:fulfill) { create(:fulfill, order: order, origin: 'dhl') }
          let!(:item_order) do
            create(:item_order, shopping_cart_item: shopping_cart_item, order: order, fulfill: fulfill)
          end

          before { lumia_item_outside_pigeon_house.product.update_columns(bookable_quantity: 0) }

          it 'returns only the error on aisow' do
            is_expected.to eq mismatched_aisow_error
          end
        end

        context 'if there are unshipped financings to make up for the difference' do
          before do
            create(:financing, product: lumia_item_outside_pigeon_house.product)
            create(:financing, product: lumia_item_outside_pigeon_house.product, state: :shipped)
          end

          before { lumia_item_outside_pigeon_house.product.update_columns(bookable_quantity: 0) }

          it 'returns only the error on aisow' do
            is_expected.to eq mismatched_aisow_error
          end
        end
      end
    end
  end
end