require 'rails_helper'

describe Jobs::Sap::CreditNoteImporterJob do
  before do
    FileUtils.mkdir_p(Settings.sap.credit_note_import_path)
    FileUtils.mkdir_p(Settings.sap.credit_note_log_path)
    SapMailer.deliveries.clear
  end
  after do
    FileUtils.rm_rf(Settings.sap.credit_note_import_path)
    FileUtils.rm_rf(Settings.sap.credit_note_log_path)
  end

  describe '#perform' do
    context 'without files' do
      it { expect(described_class.new.perform).to be_nil }
    end

    context 'file with missing columns' do
      let(:file_path) { Rails.root.join('spec/fixtures/importers/sap/credit_note_with_missing_columns.csv') }
      before { FileUtils.cp(file_path, Settings.sap.credit_note_import_path) }

      before { described_class.new.perform }

      it do
        expect(File.exist?("#{Settings.sap.credit_note_log_path}/credit_note_with_missing_columns.csv")).to be_truthy
        expect(SapMailer.deliveries.count).to eq 1
        expect(SapMailer.deliveries.first.subject).to include('Errore in')
        expect(SapMailer.deliveries.first.body).to include('Missing columns in CSV file header')
      end
    end

    context 'with files' do
      let(:file_path) { Rails.root.join('spec/fixtures/importers/sap/credit_note.csv') }
      before { FileUtils.cp(file_path, Settings.sap.credit_note_import_path) }

      let!(:dealer) { create(:dealer, id: 1) }
      let(:warehouse) { create(:warehouse, dealer: dealer) }
      let!(:item_1) { create(:item, :iphone_dealer_item_instock, warehouse: warehouse, serial: '359201860837906') }

      context 'without errors' do
        let!(:item_2) { create(:item, :iphone_dealer_item_instock, warehouse: warehouse, serial: '359201860837907') }
        let!(:item_3) { create(:item, :iphone_dealer_item_instock, warehouse: warehouse, serial: '359201860837908') }

        it do
          described_class.new.perform

          # G2 tipo_documento row
          expect(item_1.reload.credit_note).to eq '6001000058'
          expect(item_1.credit_note_amount).to eq 600
          expect(item_1.credit_note_date).to eq Date.new(2024, 7, 16)
          expect(item_1.logistic_number).to be_nil
          # CBRE tipo_documento row
          expect(item_2.reload.credit_note).to eq '6001000068'
          expect(item_2.credit_note_amount).to eq 600
          expect(item_2.credit_note_date).to eq Date.new(2024, 7, 16)
          expect(item_2.logistic_number).to be_nil
          # F2 tipo_documento row
          expect(item_3.reload.invoice_number).to eq '6001000078'
          expect(item_3.invoice_recipient).to eq dealer.ragione_sociale
          expect(item_3.invoice_issued_at).to eq Date.new(2024, 7, 16)
          expect(item_3.logistic_number).to eq 'SAP4567893'

          expect(File.exist?("#{Settings.sap.credit_note_log_path}/credit_note.csv")).to be_truthy
          expect(SapMailer.deliveries.count).to eq 1
          expect(SapMailer.deliveries.first.subject).not_to include('Errore in')
        end

        it 'duplicated phone number permit save' do
          item_1.update_column(:phone_number, '3334167742')
          item_2.update_column(:phone_number, '3334167742')

          described_class.new.perform

          # G2 tipo_documento row
          expect(item_1.reload.credit_note).to eq '6001000058'
          expect(item_1.credit_note_amount).to eq 600
          expect(item_1.credit_note_date).to eq Date.new(2024, 7, 16)
          expect(item_1.logistic_number).to be_nil
          # CBRE tipo_documento row
          expect(item_2.reload.credit_note).to eq '6001000068'
          expect(item_2.credit_note_amount).to eq 600
          expect(item_2.credit_note_date).to eq Date.new(2024, 7, 16)
          expect(item_2.logistic_number).to be_nil
          # F2 tipo_documento row
          expect(item_3.reload.invoice_number).to eq '6001000078'
          expect(item_3.invoice_recipient).to eq dealer.ragione_sociale
          expect(item_3.invoice_issued_at).to eq Date.new(2024, 7, 16)
          expect(item_3.logistic_number).to eq 'SAP4567893'

          expect(File.exist?("#{Settings.sap.credit_note_log_path}/credit_note.csv")).to be_truthy
          expect(SapMailer.deliveries.count).to eq 1
          expect(SapMailer.deliveries.first.subject).not_to include('Errore in')
        end

        context 'multiple serials on F2 column' do
          let(:file_path) { Rails.root.join('spec/fixtures/importers/sap/credit_note_multiple_serials.csv') }
          it do
            described_class.new.perform

            expect(item_2.reload.invoice_number).to eq '6001000078'
            expect(item_2.invoice_recipient).to eq dealer.ragione_sociale
            expect(item_2.invoice_issued_at).to eq Date.new(2024, 7, 16)
            expect(item_2.logistic_number).to eq 'SAP4567893'

            expect(item_3.reload.invoice_number).to eq '6001000078'
            expect(item_3.invoice_recipient).to eq dealer.ragione_sociale
            expect(item_3.invoice_issued_at).to eq Date.new(2024, 7, 16)
            expect(item_3.logistic_number).to eq 'SAP4567893'
          end
        end

        context 'separated - string with serial in row[\'seriale\']' do
          let(:file_path) { Rails.root.join('spec/fixtures/importers/sap/credit_note_string_splitted.csv') }
          it do
            described_class.new.perform

            expect(item_1.reload.invoice_number).to be_nil
            expect(item_1.invoice_recipient).to be_nil
            expect(item_1.invoice_issued_at).to be_nil
            expect(item_1.logistic_number).to be_nil

            expect(item_2.reload.invoice_number).to eq '6001000078'
            expect(item_2.invoice_recipient).to eq dealer.ragione_sociale
            expect(item_2.invoice_issued_at).to eq Date.new(2024, 7, 16)
            expect(item_2.logistic_number).to eq 'SAP4567893'

            expect(item_3.reload.invoice_number).to eq '6001000078'
            expect(item_3.invoice_recipient).to eq dealer.ragione_sociale
            expect(item_3.invoice_issued_at).to eq Date.new(2024, 7, 16)
            expect(item_3.logistic_number).to eq 'SAP4567893'
          end
        end

        context 'with dealer not found (F2 tipo_documento row)' do
          let(:file_path) { Rails.root.join('spec/fixtures/importers/sap/credit_note_no_dealer.csv') }
          before { FileUtils.cp(file_path, Settings.sap.credit_note_import_path) }

          it do
            described_class.new.perform

            expect(item_3.reload.invoice_number).to eq '6001000078'
            expect(item_3.invoice_recipient).to be_nil
            expect(item_3.invoice_issued_at).to eq Date.new(2024, 7, 16)

            expect(File.exist?("#{Settings.sap.credit_note_log_path}/credit_note_no_dealer.csv")).to be_truthy
            expect(SapMailer.deliveries.count).to eq 1
            expect(SapMailer.deliveries.first.subject).to include('Errore in')
            expect(SapMailer.deliveries.first.body).to include('Partner non trovato con id 2 (seriale 359201860837908)')
          end
        end
      end

      context 'with missing serials' do
        let(:file_path) { Rails.root.join('spec/fixtures/importers/sap/credit_note_with_missing_serials.csv') }

        it do
          described_class.new.perform

          expect(File.exist?("#{Settings.sap.credit_note_log_path}/credit_note_with_missing_serials.csv")).to be_truthy
          expect(SapMailer.deliveries.count).to eq 1
          expect(SapMailer.deliveries.first.subject).to include('Errore in')
          expect(SapMailer.deliveries.first.body).to include('Articoli non trovati con seriale 359201860668715')
          expect(SapMailer.deliveries.first.body).to include('Articoli non trovati con seriale 359201860671263')
        end
      end

      context 'with invalid date' do
        let(:file_path) { Rails.root.join('spec/fixtures/importers/sap/credit_note_with_invalid_date.csv') }

        it do
          described_class.new.perform

          expect(File.exist?("#{Settings.sap.credit_note_log_path}/credit_note_with_invalid_date.csv")).to be_truthy
          expect(SapMailer.deliveries.count).to eq 1
          expect(SapMailer.deliveries.first.subject).to include('Errore in')
          expect(SapMailer.deliveries.first.body).to include('invalid date nella riga con seriale: 359201860837906')
        end
      end

      context 'with invalid amount' do
        let(:file_path) { Rails.root.join('spec/fixtures/importers/sap/credit_note_with_invalid_amount.csv') }

        it do
          described_class.new.perform

          expect(File.exist?("#{Settings.sap.credit_note_log_path}/credit_note_with_invalid_amount.csv")).to be_truthy
          expect(SapMailer.deliveries.count).to eq 1
          expect(SapMailer.deliveries.first.subject).to include('Errore in')
          expect(SapMailer.deliveries.first.body).to include('invalid value for Float(): &quot;600..00&quot; nella riga con seriale: 359201860837906')
        end
      end

      context 'empty file' do
        let(:file_path) { Rails.root.join('spec/fixtures/importers/sap/empty_file_credit_note.csv') }

        it do
          described_class.new.perform

          expect(File.exist?("#{Settings.sap.credit_note_log_path}/empty_file_credit_note.csv")).to be_truthy
          expect(SapMailer.deliveries.count).to eq 1
          expect(SapMailer.deliveries.first.subject).not_to include('Errore in')
          expect(SapMailer.deliveries.first.body).to include('Il file importato spec/fixtures/data/SAP/DettagliNdCFatture/empty_file_credit_note.csv è vuoto')
        end
      end

      context 'empty row file' do
        let(:file_path) { Rails.root.join('spec/fixtures/importers/sap/empty_row_credit_note.csv') }

        it do
          described_class.new.perform

          expect(File.exist?("#{Settings.sap.credit_note_log_path}/empty_row_credit_note.csv")).to be_truthy
          expect(SapMailer.deliveries.count).to eq 1
          expect(SapMailer.deliveries.first.subject).not_to include('Errore in')
          expect(SapMailer.deliveries.first.body).to include('Il file importato spec/fixtures/data/SAP/DettagliNdCFatture/empty_row_credit_note.csv è vuoto')
        end
      end
    end
  end
end
