require 'rails_helper'

describe Jobs::UpsellingImporterJob do
  let(:job) { OpenStruct.new(created_at: Time.now) }
  let(:fixture_path) { Rails.root.join('spec/fixtures/importers/upselling_importer/upsellings.csv') }
  let(:file_path) { 'spec/fixtures/importers/upselling_importer/upsellings_test.csv' }
  let(:import_kind) { 'update_and_add' }
  let(:importer) { double }
  let(:current_user) { create(:user, :admin) }

  let(:args) {
    {
      current_user_id: current_user.id,
      import_kind:     import_kind,
      file_path:       file_path,
      importer:        importer
    }
  }

  before { FileUtils.cp(fixture_path, file_path) }
  before { UpsellingImporterMailer.deliveries.clear }
  before { expect(importer).to receive_message_chain(:new, :import).and_return OpenStruct.new(errors: [], file_errors: [], mail_subject: 'this is a test') }

  subject { described_class.new(args).perform }

  it do
    subject

    expect(UpsellingImporterMailer.deliveries.count).to eq 1
    expect(UpsellingImporterMailer.deliveries.first.recipients).to eq ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
    expect(UpsellingImporterMailer.deliveries.first.body.encoded).to include 'Lanciato dall&#39;utente <EMAIL>'
  end
end
