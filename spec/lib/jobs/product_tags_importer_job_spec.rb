require 'rails_helper'

describe Jobs::ProductTagsImporterJob do
  let(:file_path) { [Rails.root, 'spec', 'fixtures', 'importers', 'product_tags', 'product_tags.csv'].join('/') }
  let(:processing_path) { [Rails.root, 'product_tags.csv'].join('/') }
  let(:failure_file_path) { [Rails.root, 'spec', 'fixtures', 'importers', 'product_tags', 'product_tags_missing_headers.csv'].join('/') }
  let(:failure_processing_path) { [Rails.root, 'product_tags_missing_headers.csv'].join('/') }
  let(:current_user) { create(:user, :admin) }

  before { ProductFieldsImporterMailer.deliveries.clear }

  context 'success' do
    before { FileUtils.cp_r(file_path, processing_path) }

    it do
      described_class.new(file_path: processing_path, import_kind: 'delete_and_add', current_user_id: current_user.id).perform

      expect(File.exist?(processing_path)).to be_falsey
      expect(ProductFieldsImporterMailer.deliveries.count).to eq 1
      expect(ProductFieldsImporterMailer.deliveries.last.subject).to eq "Errori importazione tag 'Tipologie prodotto'"
      expect(ProductFieldsImporterMailer.deliveries.last.to).to eq ['<EMAIL>', '<EMAIL>', '<EMAIL>']
      expect(ProductFieldsImporterMailer.deliveries.last.cc).to eq ['<EMAIL>']
      expect(ImporterLog.count).to eq 1
      expect(ProductFieldsImporterMailer.deliveries.last.body.encoded).to include 'Lanciato dall&#39;utente <EMAIL>'
    end
  end

  context 'failure' do
    before { FileUtils.cp_r(failure_file_path, failure_processing_path) }

    it do
      described_class.new(file_path: failure_processing_path, import_kind: 'delete_and_add', current_user_id: current_user.id).perform

      expect(File.exist?(failure_processing_path)).to be_falsey
      expect(ProductFieldsImporterMailer.deliveries.count).to eq 1
      expect(ProductFieldsImporterMailer.deliveries.first.subject).to eq "Errori importazione tag 'Tipologie prodotto'"
      expect(ProductFieldsImporterMailer.deliveries.first.to).to eq ['<EMAIL>', '<EMAIL>', '<EMAIL>']
      expect(ProductFieldsImporterMailer.deliveries.first.cc).to eq ['<EMAIL>']
      expect(ImporterLog.count).to eq 1
      expect(ProductFieldsImporterMailer.deliveries.first.body.encoded).to include 'Lanciato dall&#39;utente <EMAIL>'
    end
  end
end
