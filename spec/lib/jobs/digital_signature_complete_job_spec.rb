require 'rails_helper'

describe Jobs::DigitalSignatureCompleteJob do

  let(:signature_data) do
    { 'processed'   => true, 'steps' => [{ 'log_id' => nil, 'status' => 'InProgress', 'full_name' => '<PERSON>', 'document_id' => nil, 'redirection_url' => 'https://windtre-test.signanywhere.com/workstepredirector/sign?identifier=FQTknbNm3R/eI66qg/RkP8bGOOOKyJBf4p2SVxCKPDzBO1Gtp63NthC7w6cI2ap9M5osE1Esl4N5ERFtFu7LLg==' },
                                         { 'log_id' => nil, 'status' => 'InProgress', 'full_name' => '<PERSON>', 'document_id' => nil, 'redirection_url' => 'https://windtre-test.signanywhere.com/workstepredirector/sign?identifier=FQTknbNm3R/eI66qg/RkP8bGOOOKyJBf4p2SVxCKPDzBO1Gtp63NthC7w6cI2ap9M5osE1Esl4N5ERFtFu7LLg==' },
                                         { 'log_id' => nil, 'status' => 'InProgress', 'full_name' => 'Fabio Mazzon', 'document_id' => nil, 'redirection_url' => 'https://windtre-test.signanywhere.com/workstepredirector/sign?identifier=fpsFjyP8pqIrhJapwgywRxzXbhUExj~lAPQbstL2ToHU6HnpFeZ8NM072W4an9swJjwPbcGyNL0uKxMlzzpLoQ==' },
                                         { 'log_id' => nil, 'status' => 'InProgress', 'full_name' => 'Fabio Mazzon', 'document_id' => nil, 'redirection_url' => 'https://windtre-test.signanywhere.com/workstepredirector/sign?identifier=fpsFjyP8pqIrhJapwgywRxzXbhUExj~lAPQbstL2ToHU6HnpFeZ8NM072W4an9swJjwPbcGyNL0uKxMlzzpLoQ==' }],
      'dealer'      => { 'email' => '<EMAIL>', 'number' => '3385063330', 'last_name' => 'Mazzon', 'first_name' => 'Fabio', 'update_number_otp' => 'false' }, 'customer' => { 'email' => '<EMAIL>', 'number' => '3334167742', 'last_name' => 'Jean', 'first_name' => 'Carlo' },
      'envelope_id' => 'd0affe6c-be94-462f-9615-c02e1c1f4a82' }
  end

  let(:phone_activation) { create(:phone_activation, :with_callbacks, :fissa_w3_no_product, signature_data: signature_data) }
  before { phone_activation.operator.update_column(:code, 'w3') }

  subject { described_class.new(phone_activation.id).perform }

  context 'download documents', vcr: true do
    before do
      create(:operation_outcome, :digital_signature, status: 'ok', phone_activation: phone_activation)
      create(:operation_outcome, :inserita_da_partner, status: 'ok', phone_activation: phone_activation)
      create(:operation_outcome, :upload_documenti, status: 'visited', user_id: 1, busy: true, phone_activation: phone_activation)

      phone_activation.operator.update(operation_ids: [Operation::UPLOAD_DOCUMENTI])
      PhoneActivationKind.generate_or_update({ phone_activation_kind_id: phone_activation.phone_activation_kind.id, operation_ids: [Operation::UPLOAD_DOCUMENTI.to_s] })
    end

    context 'with all required documents' do
      let!(:document) { create(:document, :document_file, documentable: phone_activation) }

      it do
        subject
        expect(phone_activation.reload.signature_obj.completed?).to be_truthy
        expect(phone_activation.reload.operation_outcomes.last.operation_id).to eq Operation::UPLOAD_DOCUMENTI
        expect(phone_activation.reload.operation_outcomes.last.status).to eq 'ok'
        expect(phone_activation.reload.operation_outcomes.last.user_id).to eq phone_activation.user_id
        expect(phone_activation.reload.signature_obj.download_attempts).to eq 1
        expect(phone_activation.last_completed_operations.where(operation_id: Operation::DIGITAL_SIGNATURE))
      end
    end

    context 'other documents still required' do
      it do
        phone_activation.alerts.destroy_all

        subject
        expect(phone_activation.reload.signature_obj.completed?).to be_truthy
        expect(phone_activation.reload.operation_outcomes.last.operation_id).to eq Operation::INSERITA_DA_PARTNER
        expect(phone_activation.reload.signature_obj.download_attempts).to eq 1
        expect(phone_activation.reload.alerts.count).to eq 1
      end
    end
  end

  context 'download documents error', vcr: true do
    let(:operation_digital_signature) { create(:operation_outcome, :digital_signature, status: 'ok', phone_activation: phone_activation) }
    before do
      operation_digital_signature
      create(:operation_outcome, :inserita_da_partner, status: 'ok', phone_activation: phone_activation)
      create(:operation_outcome, :upload_documenti, status: 'visited', user_id: 1, busy: true, phone_activation: phone_activation)
    end

    it 'before 5 attempts' do
      expect(Delayed::Job).to receive(:enqueue)
      subject

      expect(phone_activation.reload.operation_outcomes.last.status).to eq 'visited'
      expect(phone_activation.reload.digital_signature?).to be_truthy
    end

    context 'after 5 attempts' do
      before do
        phone_activation.update_column(:signature_data, signature_data.merge(download_attempts: 5))
        phone_activation.alerts.destroy_all
        expect(Delayed::Job).not_to receive(:enqueue)
      end

      it do
        expect(PrivatePub).to receive(:publish_to).at_least(:once)
        subject

        expect(phone_activation.reload.operation_outcomes.last.operation_id).to eq 1
        expect(phone_activation.operation_outcomes.last.status).to eq 'ok'
        expect(phone_activation.paper_signature?).to be_truthy
        expect(operation_digital_signature.reload.note).to eq 'Firma digitale su Namirial fallita, attivazione modificata in automatico in firma cartacea'
        expect(phone_activation.alerts.count).to eq 1
      end

      context 'only_digital_signature activations' do
        before { phone_activation.update_columns(signature_data: phone_activation.signature_data.merge(signature_kind: 'digital_only')) }

        it do
          subject

          expect(phone_activation.reload.paper_signature?).to be_falsey
          expect(operation_digital_signature.reload.note).to eq 'Firma digitale su Namirial fallita'
          expect(phone_activation.alerts.count).to be_zero
        end
      end
    end
  end

  context 'uncomplete_digital_signature' do
    before { phone_activation.update_column(:signature_data, phone_activation.signature_data.except('processed')) }
    before { create(:operation_outcome, :upload_documenti, status: 'visited', user_id: 1, busy: true, phone_activation: phone_activation) }

    it do
      subject
      expect(phone_activation.reload.signature_obj.completed?).to be_falsey
      expect(phone_activation.reload.signature_obj.download_attempts).to eq 1
      expect(phone_activation.reload.operation_outcomes.last.operation_id).to eq Operation::UPLOAD_DOCUMENTI
      expect(phone_activation.reload.operation_outcomes.last.status).to eq 'ko'
      expect(phone_activation.reload.operation_outcomes.last.username).to eq 'System'
      expect(phone_activation.reload.operation_outcomes.last.ko_reason_id).to eq KoReason::FAILED_DIGITAL_SIGNATURE
      expect(phone_activation.reload.ko_at).not_to be_nil
    end
  end
end
