require "rails_helper"

describe Jobs::GammaExchange::PickItemsDetailsFilesJob do
  let(:config) { Settings.gamma_exchange_config }
  let(:import_path) do
    Rails.root.join(config.root_path, config.import, config.flows.items_details)
  end
  let(:work_path) do
    Rails.root.join(config.work_path, config.import, config.flows.items_details)
  end

  context "initialize" do
    subject { described_class.new }

    it "should pick the right job if not specified" do
      expect(subject.importer_job).to eq Jobs::ItemsUpdater<PERSON>ob
    end

    it "should set the right paths" do
      expect(subject.import_path).to eq import_path
      expect(subject.work_path).to eq work_path
    end
  end

  context "#perform" do
    let(:importer_job) { double }
    let(:job) { double }
    let(:first_file_name) { "Export_NAP_NC_20200423_17:12:23.csv" }
    let(:second_file_name) { "Export_NAP_NC_20200423_17:15:43.csv" }
    let(:presence_flag_file_name) { "endftp.txt" }

    subject { described_class.new(importer_job: importer_job) }

    before do
      allow(importer_job).to receive(:new).and_return job
      allow(importer_job).to receive(:perform)
    end

    context "with files to download" do
      before do
        allow(Dir).to receive(:children).and_return([presence_flag_file_name, first_file_name, second_file_name])
      end

      it do
        expect(Dir).to receive(:children).with(import_path)

        expect(FileUtils).to receive(:mv).with(
          [import_path, first_file_name].join("/"),
          [work_path, first_file_name].join("/")
        )
        expect(FileUtils).to receive(:mv).with(
          [import_path, second_file_name].join("/"),
          [work_path, second_file_name].join("/")
        )

        expect(importer_job).to receive(:new).with(file_path: [work_path, first_file_name].join("/"))
        expect(importer_job).to receive(:new).with(file_path: [work_path, second_file_name].join("/"))
        expect(Delayed::Job).to receive(:enqueue).twice.with(job, queue: "gamma_exchange_items_details_importer", priority: 5)

        expect(FileUtils).to receive(:rm).with([import_path, presence_flag_file_name].join("/"))

        subject.perform

        expect(GammaExchangeSession.items_details_imports.count).to eq 2
        expect(GammaExchangeSession.items_details_imports.first.filename).to eq first_file_name
        expect(GammaExchangeSession.items_details_imports.last.filename).to eq second_file_name
      end
    end

    context "with some files but without presence flag file" do
      before do
        allow(Dir).to receive(:children).and_return([first_file_name])
      end

      it do
        expect(Dir).to receive(:children).with(import_path)

        expect(importer_job).not_to receive(:new)
        expect(Delayed::Job).not_to receive(:enqueue)

        expect(FileUtils).not_to receive(:mv)
        expect(FileUtils).not_to receive(:rm)

        subject.perform

        expect(GammaExchangeSession.items_details_imports).to be_empty
      end
    end

    context "with some already processed file" do
      let!(:gamma_exchange_session) do
        create(:gamma_exchange_session, :items_details, filename: first_file_name)
      end
      let(:repeated_file_mail) { double }

      before do
        allow(Dir).to receive(:children).and_return([presence_flag_file_name, first_file_name, second_file_name])
        allow(GammaExchangeMailer).to receive(:repeated_import_file_name).and_return repeated_file_mail
      end

      context "file not present in the working directory" do
        it "moves it normally" do
          expect(Dir).to receive(:children).with(import_path)

          expect(importer_job).not_to receive(:new).with(
            file_path: [work_path, first_file_name].join("/")
          )

          expect(GammaExchangeMailer).to receive(:repeated_import_file_name).with(filename: first_file_name, flow: :items_details)
          expect(repeated_file_mail).to receive(:deliver_now)

          expect(FileUtils).to receive(:mv).with(
            [import_path, first_file_name].join("/"),
            [work_path, first_file_name].join("/")
          )

          expect(FileUtils).to receive(:mv).with(
            [import_path, second_file_name].join("/"),
            [work_path, second_file_name].join("/")
          )

          expect(importer_job).to receive(:new).with(file_path: [work_path, second_file_name].join("/"))
          expect(Delayed::Job).to receive(:enqueue).once.with(job, queue: "gamma_exchange_items_details_importer", priority: 5)

          expect(FileUtils).to receive(:rm).with([import_path, presence_flag_file_name].join("/"))

          subject.perform

          expect(GammaExchangeSession.items_details_imports.count).to eq 2
          expect(GammaExchangeSession.items_details_imports.last.filename).to eq second_file_name
        end
      end

      context "file still in the working directory" do
        before do
          allow(File).to receive(:exist?).with([work_path, first_file_name].join("/")).and_return true
          allow(File).to receive(:exist?).with([work_path, second_file_name].join("/")).and_return false
          allow(SecureRandom).to receive(:urlsafe_base64).and_return "AAAA"
        end

        it "moves it to a name with a random suffix" do
          expect(Dir).to receive(:children).with(import_path)

          expect(importer_job).not_to receive(:new).with(file_path: [work_path, first_file_name].join("/"))

          expect(GammaExchangeMailer).to receive(:repeated_import_file_name).with(filename: first_file_name, flow: :items_details)
          expect(repeated_file_mail).to receive(:deliver_now)

          expect(SecureRandom).to receive(:urlsafe_base64).once.with 3

          expect(FileUtils).to receive(:mv).with(
            [import_path, first_file_name].join("/"),
            [work_path, "#{first_file_name}_AAAA"].join("/")
          )

          expect(FileUtils).to receive(:mv).with(
            [import_path, second_file_name].join("/"),
            [work_path, second_file_name].join("/")
          )

          expect(importer_job).to receive(:new).with(file_path: [work_path, second_file_name].join("/"))
          expect(Delayed::Job).to receive(:enqueue).once.with(job, queue: "gamma_exchange_items_details_importer", priority: 5)

          expect(FileUtils).to receive(:rm).with([import_path, presence_flag_file_name].join("/"))

          subject.perform

          expect(GammaExchangeSession.items_details_imports.count).to eq 2
          expect(GammaExchangeSession.items_details_imports.last.filename)
            .to eq second_file_name
        end
      end
    end
  end
end