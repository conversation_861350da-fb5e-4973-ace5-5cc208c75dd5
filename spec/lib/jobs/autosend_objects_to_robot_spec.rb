require "rails_helper"

describe Jobs::AutosendObjectsToRobotJob, :solr do
  let(:phone_activation) { create(:phone_activation, :abbonamento_ricaricabile) }

  subject { described_class.new }

  it do
    allow(phone_activation).to receive(:can_autosend_to_robot?).and_return false
    Sunspot.index phone_activation
    Sunspot.commit(true)
    expect(OperationOutcomes::Processors::SendToRobotProcessor).not_to receive(:new)
    subject.perform
  end

  context "activation to autosend" do
    before do
      allow(phone_activation).to receive(:can_autosend_to_robot?).and_return true
      Sunspot.index phone_activation
      Sunspot.commit(true)
      allow_any_instance_of(PhoneActivation).to receive(:next_operation_is).with(OperationConstants::ATTIVAZIONE_COMPLETATA).and_return true
      allow_any_instance_of(PhoneActivation).to receive(:next_operation_is).with(OperationConstants::CHIUSURA_CONVERGENZA).and_return false
    end

    it do
      expect(OperationOutcomes::Processors::SendToRobotProcessor).to receive_message_chain(:new, :run)
      subject.perform
    end
  end
end
