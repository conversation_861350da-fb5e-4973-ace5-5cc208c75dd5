require "rails_helper"

describe Operations::Strategies::OperationStrategy do
  let(:phone_activation) { create(:phone_activation, :eolo_casa) }
  let(:params) do
    {
      phone_activation_id: phone_activation.id
    }
  end

  subject { described_class.new(params) }

  it do
    expect(subject.respond_to?(:create_operation_outcome)).to be_truthy
    expect(subject.respond_to?(:attributes_for_operation_outcome)).to be_truthy
    expect { subject.attributes_for_operation_outcome }.to raise_error(NotImplementedError, "attributes_for_operation_outcome must be implemented by class specific implementation")
  end
end