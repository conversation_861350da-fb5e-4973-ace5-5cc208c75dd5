require "rails_helper"

describe Operations::TlsUpfrontPayment do
  let!(:phone_activation) { stub_model(PhoneActivation, dealer: stub_model(Dealer, name: "Generic dealer", role: "dealer")) }

  context "#required?" do
    it "from kolme warehouse" do
      expect(described_class.new(phone_activation).required?).to be_falsey

      phone_activation.upfront = 100
      expect(described_class.new(phone_activation).required?).to be_truthy
    end
  end

  context "#operation_id" do
    it { expect(described_class.new(phone_activation).operation_id).to eq OperationConstants::PAGAMENTO_ANTICIPO }
  end
end