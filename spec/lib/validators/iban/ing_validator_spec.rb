require "rails_helper"

describe Validators::Iban::IngValidator do
  let(:phone_activation) { stub_model(PhoneActivation) }
  let(:service_kind_mobile) { stub_model(ServiceKind, code: "mobile") }
  let(:service_kind_fisso) { stub_model(ServiceKind, code: "fisso") }
  before do
    allow(phone_activation).to receive(:payment_method_detail).and_return stub_model(PaymentMethodDetail, rid_iban: "***************************", is_rid?: true)
  end

  context "operator tre and mobile service kind is valid because require pinpad code" do
    before do
      allow(phone_activation).to receive(:operator).and_return stub_model(Operator, code: "3")
      allow(phone_activation).to receive_message_chain(:plan, :service_kind).and_return service_kind_mobile
    end

    it {  }#expect(described_class.new(phone_activation).valid?).to be_truthy }
  end

  context "operator not tre and mobile service kind" do
    before do
      allow(phone_activation).to receive(:operator).and_return stub_model(Operator, code: "fastweb")
      allow(phone_activation).to receive_message_chain(:plan, :service_kind).and_return service_kind_mobile
    end

    it { expect(described_class.new(phone_activation).valid?).to be_truthy }
  end

  context "operator tre and service kind fisso" do
    before do
      allow(phone_activation).to receive(:operator).and_return stub_model(Operator, code: "3")
      allow(phone_activation).to receive_message_chain(:plan, :service_kind).and_return service_kind_fisso
    end

    it { expect(described_class.new(phone_activation).valid?).to be_truthy }
  end

  context "operator not tre and service kind fisso" do
    before do
      allow(phone_activation).to receive(:operator).and_return stub_model(Operator, code: "fastweb")
      allow(phone_activation).to receive_message_chain(:plan, :service_kind).and_return service_kind_fisso
    end

    it { expect(described_class.new(phone_activation).valid?).to be_truthy }
  end

  context "#require_pinpad_code?" do
    let(:payment_method_detail) { stub_model(PaymentMethodDetail, code: "rid", rid_iban: "***************************") }
    let(:phone_activation_wind) { stub_model(PhoneActivation, operator: stub_model(Operator, code: "wind")) }
    let(:phone_activation_tre) { stub_model(PhoneActivation, operator: stub_model(Operator, code: "3")) }

    before { allow(phone_activation_wind).to receive_message_chain(:plan, :service_kind).and_return service_kind_mobile }
    before { allow(phone_activation_wind).to receive(:payment_method_detail).and_return payment_method_detail }

    before { allow(phone_activation_tre).to receive_message_chain(:plan, :service_kind).and_return service_kind_mobile }
    before { allow(phone_activation_tre).to receive(:payment_method_detail).and_return payment_method_detail }

    it { expect(described_class.new(phone_activation_wind).require_pinpad_code?).to be_falsey }
    it { expect(described_class.new(phone_activation_tre).require_pinpad_code?).to be_truthy }
  end
end