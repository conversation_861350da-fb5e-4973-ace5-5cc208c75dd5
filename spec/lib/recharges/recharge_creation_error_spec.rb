require "rails_helper"

describe Recharges::RechargeCreationError do
  let!(:provider) { create(:application_setting, :recharges_provider) }
  let(:dealer) { create(:dealer, :generic_dealer) }
  let!(:recharge_size) { create(:recharge_size, :tre_euro_10, :with_paymat_operator) }
  let!(:second_recharge_size) { create(:recharge_size, :tre_euro_10_contextual, paymat_operator: recharge_size.paymat_operator) }

  let(:recharge_params) do
    {
      phone_number:     "**********",
      recharge_size_id: second_recharge_size.id,
      user_id:          1,
      dealer_id:        dealer.id
    }
  end

  let(:recharge_kind) { Recharges::ManualRecharge.new(recharge_params) }

  context "create insufficient credit recharge for dealer" do
    before { described_class.new(recharge_kind: recharge_kind).create_errors(Recharges::RechargeHandler::INSUFFICIENT_CREDIT, second_recharge_size, false) }

    it { expect(Recharge.last.euronet_transactions.last.error_message).to eq Recharges::RechargeHandler::INSUFFICIENT_CREDIT }
    it { expect(Recharge.last.euronet_transactions.last.call_type).to eq EuronetTransaction::OUT_OF_CREDIT }
    it { expect(Recharge.last.recharge_size_id).to eq second_recharge_size.id }
  end
end