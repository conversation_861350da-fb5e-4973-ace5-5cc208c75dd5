require 'rails_helper'

describe GammaExport::GammaInvoicePaymentsCsvExporter, :gamma do
  let(:dealer) { create(:dealer, :with_warehouse, gamma_code_for: '12345') }
  let(:invoice_payment) { create(:invoice_payment, dealer: dealer, invoice_date: Date.parse('18/06/2021'), created_at: Date.parse('09/06/2021')) }
  let!(:first_ii_row) { create(:ii_row, :provvigione, dealer: dealer, invoice_payment: invoice_payment, operator: 'FWA', reward: 200) }
  let!(:second_ii_row) { create(:ii_row, :commissione, dealer: dealer, invoice_payment: invoice_payment, operator: 'Aria', reward: 120.87) }
  let!(:w3_business_ii_row) { create(:ii_row, :commissione, dealer: dealer, invoice_payment: invoice_payment, operator: 'Windtre Business', reward: 10) }

  subject { described_class.new }

  before do
    Timecop.freeze(Time.parse('10/06/2021 09:01:37'))
    FileUtils.mkdir_p(Settings.gamma_ftp_path)
  end

  after do
    Timecop.return
    FileUtils.rm_rf(Settings.gamma_ftp_path)
  end

  context '#export' do
    context 'without building errors' do
      it do
        expect(GammaCsvUploader).to receive_message_chain(:new, :store!)
        subject.export

        expect(subject.rows).to eq [
          [
            'OF',
            invoice_payment.created_at.strftime('%d/%m/%Y'),
            invoice_payment.invoice_number,
            '12345',
            '',
            '',
            '',
            '',
            'B3F',
            "OF#{invoice_payment.id}FW",
            'Articolo',
            'COMP_FWA',
            'Commissioni installazioni FWA 18/06/2021',
            1,
            '205.00',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            ''
          ],
          [
            'OF',
            invoice_payment.created_at.strftime('%d/%m/%Y'),
            invoice_payment.invoice_number,
            '12345',
            '',
            '',
            '',
            '',
            'B3F',
            "OF#{invoice_payment.id}WB",
            'Articolo',
            'PROV_BUS',
            'Provvigioni Windtre Business 18/06/2021',
            1,
            '140.87',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            ''
          ]
        ]
        expect(File.exist?("#{Settings.gamma_ftp_path}/20210610_090137_Documenti_OF.csv")).to be_truthy
      end

      it 'does not export with dealer kolme' do
        dealer.update_column(:name, 'Kolme')

        subject.export

        expect(subject.rows).to be_empty
      end
    end
  end
end
