require 'rails_helper'

describe GammaExport::GammaOrdersCsvExporter, :gamma do
  let(:dealer) { create(:dealer, :with_warehouse, gamma_code_cli: '12345') }
  let(:order) do
    create(:order,
           :gamma_exportable,
           dealer:                 dealer,
           warehouse:              dealer.warehouses.first,
           created_at:             DateTime.parse('09/06/2021'),
           aasm_state:             :partially_processed,
           gamma_payment_code:     'BAN',
           checkout_shipping_cost: 8,
           transaction_fees:       3)
  end

  let(:shopping_cart) { create(:shopping_cart, order: order) }

  let(:product) { create(:product, :iphone) }
  let(:second_product) { create(:product, :lumia_dhl) }

  let!(:shopping_cart_item) { create(:shopping_cart_item, owner: shopping_cart, item: product, quantity: 10, sci_price: 299.99) }
  let!(:second_shopping_cart_item) { create(:shopping_cart_item, owner: shopping_cart, item: second_product, quantity: 1, sci_price: 100) }

  subject { described_class.new }

  before do
    Timecop.freeze(Time.parse('10/06/2021 09:01:37'))
    FileUtils.mkdir_p(Settings.gamma_ftp_path)
  end

  after do
    Timecop.return
    FileUtils.rm_rf(Settings.gamma_ftp_path)
  end

  context '#export' do
    before do
      order.warehouse.update(gamma_code_dest_cli: '09876')
      order.order_states.update_all(created_at: Date.yesterday)
    end

    context 'without building errors' do

      it do
        expect(GammaCsvUploader).to receive_message_chain(:new, :store!)
        subject.export

        expect(subject.rows.map { |row| row[18] }.uniq.first).to eq ""
        expect(subject.rows).to eq [
                                     ['OC', '09/06/2021', "#{order.id}", '12345', '09876', '', '', '', 'BAN', "OC#{shopping_cart_item.id}", 'Articolo', "#{product.code}", "#{product.name}", 10, '299.99', '', '', '', '', '', '', '', '', ''],
                                     ['OC', '09/06/2021', "#{order.id}", '12345', '09876', '', '', '', 'BAN', "OC#{second_shopping_cart_item.id}", 'Articolo', "#{second_product.code}", "#{second_product.name}", 1, '100.00', '', '', '', '', '', '', '', '', ''],
                                     ['OC', '09/06/2021', "#{order.id}", '12345', '09876', '', '', '', 'BAN', "OCS#{order.id}", 'Spesa', 'TRASPSTD', 'Spese di trasporto', 1, '8.00', '', '', '', '', '', '', '', '', ''],
                                     ['OC', '09/06/2021', "#{order.id}", '12345', '09876', '', '', '', 'BAN', "OCI#{order.id}", 'Spesa', 'SPINCSTD', 'Spese di incasso', 1, '3.00', '', '', '', '', '', '', '', '', '']
                                   ]
        expect(File.exist?("#{Settings.gamma_ftp_path}/20210610_090137_Documenti_OC.csv")).to be_truthy
      end

      it 'consignment' do
        order.products.update_all(consignment: true)
        order.update(consignment: true)

        expect(GammaCsvUploader).to receive_message_chain(:new, :store!)
        subject.export
        
        expect(subject.rows.map { |row| row[16] }.uniq.first).to eq ""
        expect(subject.rows.map { |row| row[6] }.uniq).to eq ["CD"]
        expect(File.exist?("#{Settings.gamma_ftp_path}/20210610_090137_Documenti_OC.csv")).to be_truthy
      end

      it 'zero discount' do
        order.update(force_zero_discount: true)
        expect(GammaCsvUploader).to receive_message_chain(:new, :store!)
        subject.export

        expect(subject.rows.map { |row| row[18] }.uniq.first).to eq "1"
        expect(subject.rows).to eq [
                                     ['OC', '09/06/2021', "#{order.id}", '12345', '09876', '', '', '', 'BAN', "OC#{shopping_cart_item.id}", 'Articolo', "#{product.code}", "#{product.name}", 10, '299.99', '', '', '', '1', '', '', '', '', ''],
                                     ['OC', '09/06/2021', "#{order.id}", '12345', '09876', '', '', '', 'BAN', "OC#{second_shopping_cart_item.id}", 'Articolo', "#{second_product.code}", "#{second_product.name}", 1, '100.00', '', '', '', '1', '', '', '', '', ''],
                                     ['OC', '09/06/2021', "#{order.id}", '12345', '09876', '', '', '', 'BAN', "OCS#{order.id}", 'Spesa', 'TRASPSTD', 'Spese di trasporto', 1, '8.00', '', '', '', '1', '', '', '', '', ''],
                                     ['OC', '09/06/2021', "#{order.id}", '12345', '09876', '', '', '', 'BAN', "OCI#{order.id}", 'Spesa', 'SPINCSTD', 'Spese di incasso', 1, '3.00', '', '', '', '1', '', '', '', '', '']
                                   ]
        expect(File.exist?("#{Settings.gamma_ftp_path}/20210610_090137_Documenti_OC.csv")).to be_truthy
      end

      it 'does not export kolme orders' do
        order.dealer.update(name: 'Kolme')

        subject.export
        expect(subject.rows.count).to be_zero
      end
    end

    # context 'with building errors' do
    #   before { order.update_column(:dealer_id, nil) }
    #
    #   it do
    #     expect(CsvExporterMailer).to receive_message_chain(:export_error, :deliver_now)
    #     subject.export
    #
    #     expect(subject.errors).to eq ["Ordine #{order.id}: undefined method `gamma_code_cli' for nil:NilClass"]
    #   end
    # end
  end
end
