require 'rails_helper'

describe Orders::OrderFulfillmentJob do
  let(:shopping_cart) { create(:shopping_cart) }
  let(:order) { create(:order, shopping_cart: shopping_cart, aasm_state: 'paid') }

  subject { described_class.new(order) }

  context 'order with recharges only' do
    let!(:sci_recharges) { create(:shopping_cart_item, :recharge_1000, owner: shopping_cart) }

    it do
      subject.perform

      expect(order.reload.processed?).to be_truthy
      expect(order.reload.item_orders.count).to eq 1
      expect(order.reload.item_orders.first.shopping_cart_item).to eq sci_recharges
      expect(order.reload.fulfills.system_owner.count).to eq 1
      expect(order.reload.fulfills.system_owner.last.sent_at).not_to be_nil
    end
  end

  context 'order with dhl items only' do
    let!(:sci_dhl) { create(:shopping_cart_item, :dhl, owner: shopping_cart) }
    let!(:item) { create(:item, :lumia_dhl_item, state: 'instock') }

    context 'can complete allocation and send fulfill to dhl' do
      it do
        expect(Jobs::DhlDeliveryOrderExporterJob).to receive_message_chain(:new, :perform)
        subject.perform

        expect(order.reload.waiting_for_dhl?).to be_truthy
        expect(order.reload.item_orders.count).to eq 1
        expect(order.reload.item_orders.first.shopping_cart_item).to eq sci_dhl
        expect(order.reload.fulfills.dhl.count).to eq 1
        expect(order.reload.fulfills.dhl.first.sent_to_dhl?).to be_truthy
        expect(order.reload.fulfills.dhl.first.shipment).not_to be_nil
        expect(item.reload.booked?).to be_truthy
      end

      context 'incomplete allocation' do
        before { sci_dhl.update_column(:quantity, 2) }

        it do
          expect(Jobs::DhlDeliveryOrderExporterJob).not_to receive(:new)
          subject.perform

          expect(order.reload.paid?).to be_truthy
          expect(order.reload.item_orders.count).to eq 1
          expect(order.reload.item_orders.first.shopping_cart_item).to eq sci_dhl
          expect(order.reload.fulfills.dhl.count).to eq 1
          expect(order.reload.fulfills.dhl.first.sent_to_dhl?).to be_falsey
          expect(order.reload.fulfills.dhl.first.shipment).to be_nil
          expect(item.reload.booked?).to be_truthy
        end

        context 'cma context' do
          before { order.update_columns(aasm_state: 'partially_processed') }

          it do
            expect(Jobs::DhlDeliveryOrderExporterJob).to receive_message_chain(:new, :perform)
            described_class.new(order, 'cma').perform

            expect(order.reload.partially_processed?).to be_truthy
            expect(order.reload.item_orders.count).to eq 1
            expect(order.reload.fulfills.dhl.count).to eq 1
            expect(order.reload.fulfills.dhl.first.sent_to_dhl?).to be_truthy
            expect(order.reload.fulfills.dhl.first.shipment).not_to be_nil
            expect(item.reload.booked?).to be_truthy
          end
        end
      end
    end
  end

  context 'order with recharges and dhl items' do
    let!(:sci_recharges) { create(:shopping_cart_item, :recharge_1000, owner: shopping_cart) }
    let!(:sci_dhl) { create(:shopping_cart_item, :dhl, owner: shopping_cart) }
    let!(:item) { create(:item, :lumia_dhl_item, state: 'instock') }

    it do
      expect(Jobs::DhlDeliveryOrderExporterJob).to receive_message_chain(:new, :perform)
      subject.perform

      expect(order.reload.waiting_for_dhl?).to be_truthy
      expect(order.reload.item_orders.count).to eq 2
      expect(order.reload.fulfills.system_owner.count).to eq 1
      expect(order.reload.fulfills.system_owner.last.sent_at).not_to be_nil
      expect(order.reload.fulfills.dhl.count).to eq 1
      expect(order.reload.fulfills.dhl.first.sent_to_dhl?).to be_truthy
      expect(order.reload.fulfills.dhl.first.shipment).not_to be_nil
      expect(item.reload.booked?).to be_truthy
    end

    context 'disabled dhl export' do
      let!(:dhl_export_disabled) { create(:application_setting, :dhl_export_disabled) }

      it do    
        subject.perform

        expect(order.reload.waiting_for_dhl?).to be_falsey
        expect(order.reload.item_orders.count).to eq 2
        expect(order.reload.fulfills.system_owner.count).to eq 1
        expect(order.reload.fulfills.system_owner.last.sent_at).not_to be_nil
        expect(order.reload.fulfills.dhl.count).to eq 1
        expect(order.reload.fulfills.dhl.first.sent_to_dhl?).to be_falsey
        expect(order.reload.fulfills.dhl.first.shipment).to be_nil
        expect(item.reload.booked?).to be_truthy
      end
    end

    context 'cma context' do
      before { order.update_columns(aasm_state: 'partially_processed') }

      context 'incomplete allocation' do
        before { sci_dhl.update_column(:quantity, 2) }

        it 'send only available items and not send empty fulfills' do
          expect(Jobs::DhlDeliveryOrderExporterJob).to receive_message_chain(:new, :perform)
          described_class.new(order, 'cma').perform

          expect(order.reload.partially_processed?).to be_truthy
          expect(order.reload.item_orders.count).to eq 1
          expect(order.reload.fulfills.dhl.count).to eq 1
          expect(order.reload.fulfills.dhl.first.sent_to_dhl?).to be_truthy
          expect(order.reload.fulfills.dhl.first.shipment).not_to be_nil
          expect(item.reload.booked?).to be_truthy
        end
      end
    end
  end

  context 'order awaiting_confirmation' do
    let!(:sci_recharges) { create(:shopping_cart_item, :recharge_1000, owner: shopping_cart) }
    let!(:sci_dhl) { create(:shopping_cart_item, :dhl, owner: shopping_cart) }
    let!(:item) { create(:item, :lumia_dhl_item, state: 'instock') }

    before { order.update_column(:aasm_state, 'awaiting_confirmation') }

    it do
      expect(Jobs::DhlDeliveryOrderExporterJob).not_to receive(:new)
      subject.perform

      expect(order.reload.item_orders.count).to eq 1
      expect(order.reload.item_orders.first.shopping_cart_item).to eq sci_dhl
      expect(order.reload.fulfills.dhl.count).to eq 1
      expect(order.reload.fulfills.system_owner.count).to be_zero
      expect(order.reload.fulfills.dhl.first.sent_to_dhl?).to be_falsey
      expect(order.reload.fulfills.dhl.first.shipment).to be_nil
      expect(item.reload.booked?).to be_truthy
    end
  end

  context 'bundle unpack still executing' do
    let(:bundle_product) { create(:product, :valid, :bundle) }
    let!(:sci_bundle) { create(:shopping_cart_item, item: bundle_product, owner: shopping_cart) }

    it do
      expect(Delayed::Job).to receive(:enqueue).exactly(3).times
      subject.perform
    end
  end
end