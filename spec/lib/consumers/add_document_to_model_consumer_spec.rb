require "rails_helper"

describe Consumers::AddDocumentToModelConsumer do
  let(:accounting_document) { create(:accounting_document) }
  let(:invoice_payment) { create(:invoice_payment) }
  let(:downloaded_file) { Rack::Test::UploadedFile.new(Rails.root.join("spec/support/documents/contract.pdf")) }
  let(:downloaded_temp_file) {}

  after do
    FileUtils.rm_rf(Dir["public/data/accounting_document/*"])
    FileUtils.rm_rf(Dir["public/data/invoice_payment/*"])
  end

  context "success call" do
    it "works for an AccountingDocument" do
      download = described_class.new(accounting_document).success(downloaded_file)
      expect(download.success).to eq "AccountingDocument #{accounting_document.id} => caricato correttamente"
      expect(accounting_document.attachment_identifier).to eq "contract.pdf"
    end

    context "works for an InvoicePayment" do
      it do
        download = described_class.new(invoice_payment).success(downloaded_file)
        expect(download.success).to eq "InvoicePayment #{invoice_payment.id} => caricato correttamente"
        expect(invoice_payment.attachment_identifier).to eq "contract.pdf"
      end

      context 'when IiRow of InvoicePayment is not valid (ii_row converted to 0 vat)' do
        let!(:ii_row) { create(:ii_row, :commissione, invoice_payment: invoice_payment, operator: 'FWA') }

        before { ii_row.update_columns(vat_code: 'N1') }

        it 'attach file anyway' do
          download = described_class.new(invoice_payment.reload).success(downloaded_file)
          expect(download.success).to eq "InvoicePayment #{invoice_payment.id} => caricato correttamente"
          expect(invoice_payment.reload.attachment_identifier).to eq "contract.pdf"
        end
      end
    end
  end

  context "failure call" do
    it "works for an AccountingDocument" do
      failure_download = described_class.new(accounting_document).failure("error in upload")
      expect(failure_download.error).to eq "ERRORE : AccountingDocument #{accounting_document.id} => error in upload"
      expect(accounting_document.attachment_identifier).to be_nil
    end

    it "works for an InvoicePayment" do
      failure_download = described_class.new(invoice_payment).failure("error in upload")
      expect(failure_download.error).to eq "ERRORE : InvoicePayment #{invoice_payment.id} => error in upload"
      expect(invoice_payment.attachment_identifier).to be_nil
    end
  end
end
