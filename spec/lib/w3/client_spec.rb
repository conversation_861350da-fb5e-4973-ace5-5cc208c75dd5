require 'rails_helper'

# Tunnel for staging calls
####################################################################
# get_check_email
# ssh -f -N -L 8080:ml-in-2.apps.windtre.it:443 deploy@************
# add on your hosts file (/etc/hosts) 127.0.0.1 ml-in-2.apps.windtre.it
####################################################################
# get_token || burn_imei
# ssh -f -N -L 4443:api-np.windtre.it:443 deploy@************
# add on your hosts file (/etc/hosts) 127.0.0.1 api-np.windtre.it

describe W3::Client do
  subject { described_class }
  let(:fixed_line_detail) { build_stubbed(:fixed_line_detail, city: build_stubbed(:city, :milano), location_province: build_stubbed(:province, :milano)) }
  let(:dealer) { create(:dealer, :generic_dealer) }
  let!(:warehouse) { create(:warehouse, dealer: dealer) }

  let!(:w3_remote_api_env) { create(:application_setting, :w3_remote_api_env, value: 'test') }

  describe '#check_address', vcr: true do
    context 'when the API returns a successful response' do
      let(:payload) do
        {
          address: 'Corsica',
          brand: '',
          particella: '',
          country: 'ITALIA',
          city: 'Milano',
          toponymy: 'Viale',
          postalCode: '20133',
          streetNumber: '45',
          province: 'MI'
        }
      end

      it 'returns the correct response' do
        expect(W3::Client.check_address(payload)['status']).to eq('OK')
        expect(ApiCallLog.count).to eq 1
      end
    end

    context 'when the API returns an error' do
      it 'returns an error response' do
        expect(W3::Client.check_address({})['status']).to eq('error')
        expect(W3::Client.check_address({})['messages']).to match_array(['400 Bad Request'])
      end
    end
  end

  describe '#check_vat', vcr: true do
    let(:vat_number) { '01983070681' }

    context 'when the API returns a successful response' do
      it 'returns the correct response' do
        response = W3::Client.check_vat(vat_number)

        expect(response.api_response).to eq(JSON.parse('{"data":false,"status":"OK","errorCodes":[],"messages":[]}'))
      end
    end

    context 'when the API returns an error' do
      let(:auth_token) { OpenStruct.new(success?: false, errors: ['Token not valid']) }
      let(:expected_result) do
        {
          'data'       => false,
          'status'     => 'KO',
          'errorCodes' => [],
          'messages'   => ['Token not valid']
        }
      end

      before do
        allow(W3::Client).to receive(:get_token).and_return(auth_token)
      end

      it 'returns an error response' do
        response = W3::Client.check_vat(vat_number)

        expect(response.api_response).to eq(expected_result)
      end
    end

    it 'creates an ApiCallLog and returns failure on timeout' do
      allow(RestClient::Request).to receive(:execute).and_raise(RestClient::Exceptions::Timeout.new)
      allow(W3::Client).to receive(:get_token).and_return(OpenStruct.new(success?: true, token: 'test'))

      expect do
        result = described_class.check_vat('01983070681')
        expect(result.success?).to be(false)
        expect(result.errors).to eq('Request Timeout')
      end.to change(ApiCallLog, :count).by(1)

      log = ApiCallLog.last
      expect(log.kind).to eq('check_vat')
      expect(JSON.parse(log.response)).to eq({ 'error' => 'Timeout', 'message' => 'Request Timeout' })
    end
  end

  describe '#get_token' do
    it do
      VCR.use_cassette('W3Client/get_token/success') do
        result = subject.get_token

        expect(result.success?).to eq(true)
        expect(result.token).to eq('zHvjeGJfh6PLOMabZmB9f3oL6XEv')
      end
    end

    it 'creates an ApiCallLog and returns failure on timeout' do
      allow(RestClient::Request).to receive(:execute).and_raise(RestClient::Exceptions::Timeout.new)

      expect do
        result = described_class.get_token
        expect(result.success?).to be(false)
        expect(result.errors).to eq('Request Timeout')
      end.to change(ApiCallLog, :count).by(1)

      log = ApiCallLog.last
      expect(log.kind).to eq('get_token')
      expect(JSON.parse(log.response)).to eq({ 'error' => 'Timeout', 'message' => 'Request Timeout' })
    end
  end

  context '#burn_imei' do
    it 'should return the ok success message if burned' do
      VCR.use_cassette('W3Client/burn_imei/success', allow_playback_repeats: true) do
        result = subject.burn_imei('357865020278078', warehouse)

        expect(result.success).to eq(true)
        expect(result.code).to eq(:ok)
        expect(result.remote_errors).to be_nil
        expect(result.api_kind).to eq('burn_imei')
        expect(result.api_request).to be_present
        expect(result.api_response).to be_present
      end
    end
    it 'should return the wrong_status failure if the imei was already burned' do
      VCR.use_cassette('W3Client/burn_imei/wrong_status', allow_playback_repeats: true) do
        result = subject.burn_imei('357865020278078', warehouse)
        expect(result.success).to eq(false)
        expect(result.code).to eq(:wrong_status)
        expect(result.remote_errors).to eq('KO - IMEI status not valid')
        expect(result.api_kind).to eq('burn_imei')
        expect(result.api_request).to be_present
        expect(result.api_response).to be_present
      end
    end
    it 'should return the not_found failure if the imei was not valid' do
      VCR.use_cassette('W3Client/burn_imei/not_found', allow_playback_repeats: true) do
        result = subject.burn_imei('000000000000000', warehouse)
        expect(result.success).to eq(false)
        expect(result.code).to eq(:not_found)
        expect(result.remote_errors).to eq('KO - IMEI not found')
        expect(result.api_kind).to eq('burn_imei')
        expect(result.api_request).to be_present
        expect(result.api_response).to be_present
      end
    end
    it 'should return the authorization failure if the access_token was not obtained' do
      VCR.use_cassette('W3Client/burn_imei/authorization', allow_playback_repeats: true) do
        result = subject.burn_imei('000000000000000', warehouse)
        expect(result.success).to eq(false)
        expect(result.code).to eq(:authorization)
        expect(result.remote_errors).to eq('Token not valid')
        expect(result.api_kind).to eq('burn_imei')
        expect(result.api_request).to be_nil
        expect(result.api_response).to be_present
      end
    end
    it 'should return the server failure if a communication error happened' do
      VCR.use_cassette('W3Client/burn_imei/server', allow_playback_repeats: true) do
        stub_request(:any, [Settings.w3[ApplicationSetting.w3_remote_api_env].endpoint, 'burn-imei'].join('/')).to_timeout
        result = subject.burn_imei('000000000000000', warehouse)
        expect(result.success).to eq(false)
        expect(result.code).to eq(:server)
        expect(result.remote_errors).to be_present
        expect(result.api_kind).to eq('burn_imei')
        expect(result.api_request).to be_present
        expect(result.api_response).to be_present
      end
    end

    it 'creates an ApiCallLog and returns failure on timeout' do
      allow(W3::Client).to receive(:get_token).and_return(OpenStruct.new(success?: true, token: 'test'))
      allow(RestClient::Request).to receive(:execute).and_raise(RestClient::Exceptions::Timeout.new)

      expect do
        result = described_class.burn_imei('000000000000000', warehouse)
        expect(result.success).to be(false)
        expect(result.remote_errors).to eq('Request Timeout')
      end.to change(ApiCallLog, :count).by(1)

      log = ApiCallLog.last
      expect(log.kind).to eq('burn_imei')
      expect(JSON.parse(log.response)).to eq({ 'error' => 'Timeout', 'message' => 'Request Timeout' })
    end

    context 'with error POS-ERR-01' do
      let(:auth_token) { OpenStruct.new(success?: true, token: 'token') }

      let(:mock_response) do
        double(
          code: 200,
          body: '{"status":"FAIL","errorCodes":["POS-ERR-01"],"messages":[{"type":"BSN","message":"Siamo spiacenti il servizio non è al momento disponibile"},{"type":"TECH"}]}',
          headers: { content_type: 'application/json' }
        )
      end

      before do
        allow(W3::Client).to receive(:get_token).and_return(auth_token)
        allow(RestClient::Request).to receive(:execute).and_return(mock_response)
      end

      it 'creates an ApiCallLog and returns server error code' do
        result = subject.burn_imei("357865020278078", warehouse)

        expect(result.success).to eq(false)
        expect(result.code).to eq(:server)
      end
    end

    context 'should return an internal server error' do
      let(:auth_token) { OpenStruct.new(success?: true, token: 'token') }

      let(:mock_response) do
        double(
          code: 500,
          body: '{"error": "Internal Server Error", "message": "No message available"}',
          headers: { content_type: 'application/json' }
        )
      end

      before do
        allow(W3::Client).to receive(:get_token).and_return(auth_token)
        allow(RestClient::Request).to receive(:execute).and_raise(RestClient::InternalServerError.new(mock_response))
      end

      it "should return internal_server_error" do
        result = subject.burn_imei("357865020278078", warehouse)

        expect(result.success).to eq(false)
        expect(result.code).to eq(:internal_server_error)
      end
    end
  end

  describe '#get_check_email', vcr: true do
    let!(:w3_remote_api_env) { create(:application_setting, :w3_remote_api_env, value: 'test') }

    it 'WINDTRE	Consumer	****************	<EMAIL>	Nuovo	SI' do
      response = described_class.get_check_email(email:      '<EMAIL>',
                                                 identifier: '****************',
                                                 brand:      'WINDTRE',
                                                 market:     'CONSUMER')

      expect(response.flag_check_email).to eq 'N'
      expect(response.counter_email).to eq 0
    end

    it 'WINDTRE	Consumer	PGLMTT93T10H501O	<EMAIL>	Coppia esistente	SI' do
      response = described_class.get_check_email(email:      '<EMAIL>',
                                                 identifier: 'PGLMTT93T10H501O',
                                                 brand:      'WINDTRE',
                                                 market:     'CONSUMER')
      expect(response.flag_check_email).to eq 'Y'
      expect(response.counter_email).to eq 1
      expect(ApiCallLog.count).to eq 1
    end
    it 'WINDTRE	Consumer	****************	<EMAIL>	Nuovo ma email esistente su altri ma <= 5	SI' do
      response = described_class.get_check_email(email:      '<EMAIL>',
                                                 identifier: '****************',
                                                 brand:      'WINDTRE',
                                                 market:     'CONSUMER')

      expect(response.flag_check_email).to eq 'N'
      expect(response.counter_email).to eq 4
      expect(ApiCallLog.count).to eq 1
    end
    it 'WINDTRE	Consumer	****************	<EMAIL>	Nuovo ma email esistente su altri ma >5	NO' do
      response = described_class.get_check_email(email:      '<EMAIL>',
                                                 identifier: '****************',
                                                 brand:      'WINDTRE',
                                                 market:     'CONSUMER')

      expect(response.flag_check_email).to eq 'N'
      expect(response.counter_email).to eq 6
      expect(ApiCallLog.count).to eq 1
    end
    it 'WINDTRE	MicroBusiness	****************	<EMAIL>	Nuovo	SI' do
      response = described_class.get_check_email(email:      '<EMAIL>',
                                                 identifier: '****************',
                                                 brand:      'WINDTRE',
                                                 market:     'MICROBUSINESS')

      expect(response.flag_check_email).to eq 'N'
      expect(response.counter_email).to eq 0
      expect(ApiCallLog.count).to eq 1
    end
    it 'WINDTRE	MicroBusiness	PGLMTT93T10H501O	<EMAIL>	Coppia esistente	SI' do
      response = described_class.get_check_email(email:      '<EMAIL>',
                                                 identifier: 'PGLMTT93T10H501O',
                                                 brand:      'WINDTRE',
                                                 market:     'MICROBUSINESS')

      expect(response.flag_check_email).to eq 'Y'
      expect(response.counter_email).to eq 1
      expect(ApiCallLog.count).to eq 1
    end
    it 'WINDTRE	MicroBusiness	****************	<EMAIL>	Nuovo ma email esistente su altri ma <= 5	SI' do
      response = described_class.get_check_email(email:      '<EMAIL>',
                                                 identifier: '****************',
                                                 brand:      'WINDTRE',
                                                 market:     'MICROBUSINESS')

      expect(response.flag_check_email).to eq 'N'
      expect(response.counter_email).to eq 4
      expect(ApiCallLog.count).to eq 1
    end
    it 'WINDTRE	MicroBusiness	****************	<EMAIL>	Nuovo ma email esistente su altri ma >5	NO' do
      response = described_class.get_check_email(email:      '<EMAIL>',
                                                 identifier: '****************',
                                                 brand:      'WINDTRE',
                                                 market:     'MICROBUSINESS')

      expect(response.flag_check_email).to eq 'N'
      expect(response.counter_email).to eq 6
      expect(ApiCallLog.count).to eq 1
    end
    it 'Very	Consumer	****************	<EMAIL>	Nuovo	SI' do
      response = described_class.get_check_email(email:      '<EMAIL>',
                                                 identifier: '****************',
                                                 brand:      'VERY',
                                                 market:     'CONSUMER')

      expect(response.flag_check_email).to eq 'N'
      expect(response.counter_email).to eq 0
      expect(ApiCallLog.count).to eq 1
    end
    it 'Very	Consumer	PGLMTT93T10H501O	<EMAIL>	Coppia esistente	SI' do
      response = described_class.get_check_email(email:      '<EMAIL>',
                                                 identifier: 'PGLMTT93T10H501O',
                                                 brand:      'VERY',
                                                 market:     'CONSUMER')
      expect(response.flag_check_email).to eq 'Y'
      expect(response.counter_email).to eq 1
      expect(ApiCallLog.count).to eq 1
    end
    it 'Very	Consumer	****************	<EMAIL>	Nuovo ma email esistente su altri ma <= 5	SI' do
      response = described_class.get_check_email(email:      '<EMAIL>',
                                                 identifier: '****************',
                                                 brand:      'VERY',
                                                 market:     'CONSUMER')

      expect(response.flag_check_email).to eq 'N'
      expect(response.counter_email).to eq 4
      expect(ApiCallLog.count).to eq 1
    end
    it 'Very	Consumer	****************	<EMAIL>	Nuovo ma email esistente su altri ma >5	NO' do
      response = described_class.get_check_email(email:      '<EMAIL>',
                                                 identifier: '****************',
                                                 brand:      'VERY',
                                                 market:     'CONSUMER')

      expect(response.flag_check_email).to eq 'N'
      expect(response.counter_email).to eq 6
      expect(ApiCallLog.count).to eq 1
    end

    it 'error' do
      w3_remote_api_env.update_column(:value, 'wrong_env')
      response = described_class.get_check_email(email:      '<EMAIL>',
                                                 identifier: '****************',
                                                 brand:      'VERY',
                                                 market:     'CONSUMER')

      expect(response.flag_check_email).to be_nil
      expect(response.counter_email).to be_nil
      expect(ApiCallLog.count).to be_zero
    end
  end

  describe '#products', vcr: true do
    context 'when the API returns a successful response' do
      let(:payload) do
        {
          codiceFiscalePiva: '',
          lineId: '',
          market: 'CONSUMER',
          salesForce: 'Dealer Wind 3',
          address: {
            addressName: 'Corsica',
            addressType: 'Viale',
            city: 'Milano',
            codeToponymic: 'Viale',
            country: 'ITALIA',
            egonAddress: '',
            egonStreetNumber: '',
            istatCode: '03015146',
            province: 'MI',
            streetNumber: '45',
            tipoRichiesta: 'PAM',
            zipCode: '20133'
          },
          secondLineVoip: false
        }
      end

      it 'returns the correct response' do
        expect(W3::Client.products(payload)['status']).to eq('OK')
        expect(ApiCallLog.count).to eq 1
      end
    end

    context 'when the API returns an error' do
      it 'returns an error response' do
        expect(W3::Client.products({})['status']).to eq('error')
        expect(W3::Client.products({})['messages']).to match_array(['400 Bad Request'])
      end
    end
  end

  describe '#search_full(customer)', vcr: true do
    let(:customer) { build_stubbed(:customer, cf: '****************') }

    it 'success' do
      result = subject.search_full(customer)

      expect(result.success).to be_truthy
      expect(result.account_id).to eq 'P1000190432'
    end

    context 'customer not found' do
      before { customer.cf = '****************' }

      it do
        result = subject.search_full(customer)

        expect(result.success).to be_truthy
        expect(result.account_id).to be_nil
      end
    end

    it 'creates an ApiCallLog and returns failure on timeout' do
      allow(W3::Client).to receive(:get_token).and_return(OpenStruct.new(success?: true, token: 'test'))
      allow(RestClient::Request).to receive(:execute).and_raise(RestClient::Exceptions::Timeout.new)

      expect do
        result = described_class.search_full(customer)
        expect(result.success).to be(false)
        expect(result.remote_errors).to eq('Request Timeout')
      end.to change(ApiCallLog, :count).by(1)

      log = ApiCallLog.last
      expect(log.kind).to eq('search_full')
      expect(JSON.parse(log.response)).to eq({ 'error' => 'Timeout', 'message' => 'Request Timeout' })
    end

    # context 'customer without lines' do
    #   before { customer.cf = '****************' }

    #   it do
    #     result = subject.search_full(customer)

    #     expect(result.success).to be_truthy
    #     expect(result.account_id).to eq []
    #   end
    # end
  end

  describe '#line_by_account_id(account_id)', vcr: true do
    let(:account_id) { 'P1000190432' }

    it do
      result = subject.line_by_account_id(account_id)

      expect(result.success).to be_truthy
      expect(result.lines.count).to eq 14
      expect(result.lines.map { |l| l['mobile'] }).to eq [true, false, false, false, false, false, false, false, false, false, false, false, false, false]
    end

    it 'creates an ApiCallLog and returns failure on timeout' do
      allow(W3::Client).to receive(:get_token).and_return(OpenStruct.new(success?: true, token: 'test'))
      allow(RestClient::Request).to receive(:execute).and_raise(RestClient::Exceptions::Timeout.new)

      expect do
        result = described_class.line_by_account_id('P1000190432')
        expect(result.success).to be(false)
        expect(result.remote_errors).to eq('Request Timeout')
      end.to change(ApiCallLog, :count).by(1)

      log = ApiCallLog.last
      expect(log.kind).to eq('line_by_account_id')
      expect(JSON.parse(log.response)).to eq({ 'error' => 'Timeout', 'message' => 'Request Timeout' })
    end
  end

  describe '#check_eligibility(customer)' do
    let(:customer) { build_stubbed(:customer, cf: '****************') }

    it 'creates an ApiCallLog and returns failure on timeout' do
      allow(RestClient::Request).to receive(:execute).and_raise(RestClient::Exceptions::Timeout.new)
      allow(W3::Client).to receive(:get_token).and_return(OpenStruct.new(success?: true, token: 'test'))

      expect do
        result = described_class.check_eligibility(customer)
        expect(result.success?).to be(false)
        expect(result.remote_errors).to eq('Request Timeout')
      end.to change(ApiCallLog, :count).by(1)

      log = ApiCallLog.last
      expect(log.kind).to eq('check_eligibility')
      expect(JSON.parse(log.response)).to eq({ 'error' => 'Timeout', 'message' => 'Request Timeout' })
    end
  end

  describe '#verify_document', vcr: true do
    context 'when the document is valid' do
      let(:payload) do
        {
          "dateBirth":           '1941-09-24',
          "documentIssuingDate": '2011-02-03',
          "documentNumber":      '*********',
          "documentType":        'CDI_W',
          "fiscalCode":          '****************',
          "name":                'GIANCARLO',
          "surname":             'BORDIGNON'
        }
      end

      it do
        response = subject.verify_document(payload)
        expect(response.success).to be_falsey
        expect(response.response['messages']).to eq [
          { 'type' => 'BSN', 'message' => "Carta d'Identità scaduta" }
        ]
      end
    end

    context 'when verification fails' do
      let(:payload) do
        {
          "dateBirth":           '1980-05-15',
          "documentIssuingDate": '1920-01-10',
          "documentNumber":      'X987654',
          "documentType":        'CDI',
          "fiscalCode":          '****************',
          "name":                'MARIO',
          "surname":             'ROSSI'
        }
      end

      it 'returns failure with appropriate error message' do
        response = subject.verify_document(payload)
        expect(response).to eq(OpenStruct.new(
          success: false,
          response: {
            "data" => {"result" => false},
            "status" => "OK",
            "errorCodes" => [],
            "messages" => [
              {
                "type" => "BSN",
                "message" => "L'anno di emissione della Carta d'Identità deve essere maggiore o uguale all'anno di nascita del cliente"
              }
            ]
          },
          remote_errors: nil
        ))
      end
    end

    context 'when bad request is sent' do
      let(:invalid_payload) do
        {
          "documentType": 'INVALID_TYPE',
          "fiscalCode":   'INVALID12345'
        }
      end

      let(:remote_errors) do
        {
          "timestamp" => "Mon, 3 Mar 2025 11:00:12 GMT",
          "status" => 400,
          "error" => "Bad Request",
          "message" => "Missing value(s): [dateBirth, documentIssuingDate, documentNumber, name, surname] in request body",
          "path" => "/document-check"
        }
      end

      it 'returns a bad request error' do
        response = subject.verify_document(invalid_payload)
        expect(response).to eq(OpenStruct.new(success: false, remote_errors: remote_errors))
      end
    end

    context 'when server error occurs' do
      let(:payload) do
        {
          "dateBirth":           '1941-09-24',
          "documentIssuingDate": '2011-02-03',
          "documentNumber":      '*********',
          "documentType":        'CDI_W',
          "fiscalCode":          '****************',
          "name":                'GIANCARLO',
          "surname":             'BORDIGNON'
        }
      end
      let(:http_response) { double('http_response', code: 500, body: 'Internal Server Error') }
      let(:server_error) { RestClient::ExceptionWithResponse.new(http_response) }

      it 'handles server errors gracefully' do
        allow(W3::Client).to receive(:execute_request).and_raise(server_error)

        response = subject.verify_document(payload)
        expect(response).to eq(OpenStruct.new(
          success: false,
          remote_errors: 'Internal Server Error'
        ))
      end
    end
  end
end

