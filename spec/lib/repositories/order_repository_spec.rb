require "rails_helper"

describe Repositories::OrderRepository, :solr do
  let(:dealer) { create(:dealer, :generic_dealer, name: "Architrave 3000") }
  let(:order) { create(:order, dealer: dealer) }
  let(:current_user) { stub_model(User, role: "admin") }

  let(:params) do
    {
      dealer_name: order.dealer.name
    }
  end

  before { Sunspot.index order;Sunspot.commit(true) }
  before { allow(current_user).to receive_message_chain(:internal_user_detail, :access_level).and_return AccessLevel::ALL }

  subject { described_class.new(current_user: current_user) }

  context "#search(params)" do
    it do
      expect(Sunspot.search(Order).total).to eq 1
      expect(Sunspot.search(Order).results).to include order
      expect(subject.search(dealer_name: "Architrave 3000").results).to include order
      expect(subject.search(dealer_name: "Architrave").results).to include order
      expect(subject.search(dealer_name: "Archi").results).to include order
      expect(subject.search(dealer_name: "Architrave 3000", order_id: order.id.to_s).results).to include order
      expect(subject.search(dealer_name: "Architrave", order_id: order.id.to_s).results).to include order
      expect(subject.search(dealer_name: "Archi", order_id: order.id.to_s).results).to include order
    end

    context "search status" do
      it do
        expect(subject.search(order_filter_items_products: nil).results).not_to be_empty
        expect(subject.search(order_filter_items_products: "unfulfilled_system_in_stock").results).to be_empty
      end

      context "with item in stock" do
        let(:iphone_4s) { create(:product, :iphone_4s, origin: "system") }
        let!(:iphone_4s_instock) { create(:item, :in_system_owner_warehouse, product: iphone_4s, state: "instock") }
        let(:shopping_cart) { create(:shopping_cart, dealer: dealer, order: order) }
        let!(:shopping_cart_item) { create(:shopping_cart_item, owner: shopping_cart, item: iphone_4s) }

        before { Sunspot.index order;Sunspot.commit(true) }

        it { expect(subject.search(order_filter_items_products: "unfulfilled_system_in_stock").results).to include(order) }
      end

      context "with item in stock origin attiva" do
        let(:iphone_4s) { create(:product, :iphone_4s,
                                 origin:          "attiva", attiva_brand: create(:attiva_brand),
                                 attiva_category: create(:attiva_category), attiva_subcategory: create(:attiva_subcategory)) }
        let!(:iphone_4s_instock) { create(:item, :in_system_owner_warehouse, product: iphone_4s, state: "instock") }
        let(:shopping_cart) { create(:shopping_cart, dealer: dealer, order: order) }
        let!(:shopping_cart_item) { create(:shopping_cart_item, owner: shopping_cart, item: iphone_4s) }

        before { Sunspot.index order; Sunspot.commit(true)}

        it { expect(subject.search(order_filter_items_products: "unfulfilled_system_in_stock").results).to be_empty }
      end

      context "with item fulfilled origin kolme" do
        let(:iphone_4s) { create(:product, :iphone_4s) }
        let!(:iphone_4s_instock) { create(:item, :in_system_owner_warehouse, product: iphone_4s, state: "instock") }
        let(:shopping_cart) { create(:shopping_cart, dealer: dealer, order: order) }
        let!(:shopping_cart_item) { create(:shopping_cart_item, owner: shopping_cart, item: iphone_4s, quantity: 1) }
        let(:fulfill) { create(:fulfill, order: order, sent_at: Time.now) }
        let!(:item_order) { create(:item_order, shopping_cart_item: shopping_cart_item, item: iphone_4s_instock, order: order, fulfill: fulfill, sent_at: Date.yesterday) }

        before { Sunspot.index order; Sunspot.commit(true)}

        it { expect(subject.search(order_filter_items_products: "unfulfilled_system_in_stock").results).to be_empty }
      end

      context "with not all items in stock" do
        let(:iphone_4s) { create(:product, :iphone_4s, origin: "system") }
        let(:iphone) { create(:product, :iphone, origin: "system") }
        let!(:iphone_4s_instock) { create(:item, :in_system_owner_warehouse, product: iphone_4s, state: "instock") }
        let(:shopping_cart) { create(:shopping_cart, dealer: dealer, order: order) }
        let!(:shopping_cart_item) { create(:shopping_cart_item, owner: shopping_cart, item: iphone_4s) }
        let!(:shopping_cart_item_iphone) { create(:shopping_cart_item, owner: shopping_cart, item: iphone) }

        before { Sunspot.index order; Sunspot.commit(true)}

        it { expect(subject.search(order_filter_items_products: "unfulfilled_system_in_stock").results).to be_empty }
      end
    end

    context "search product" do
      let(:iphone_4s) { create(:product, :iphone_4s) }
      let(:iphone_5s) { create(:product, :iphone_5s) }

      let(:order_iphone_4s) { create(:order, dealer: dealer) }
      let(:order_iphone_5s) { create(:order, dealer: dealer) }

      let(:shopping_cart) { create(:shopping_cart, dealer: dealer, order: order_iphone_4s) }
      let!(:shopping_cart_item) { create(:shopping_cart_item, :recharge, owner: shopping_cart, item: iphone_4s) }

      let(:shopping_cart_1) { create(:shopping_cart, dealer: dealer, order: order_iphone_5s) }
      let!(:shopping_cart_item_1) { create(:shopping_cart_item, :recharge, owner: shopping_cart_1, item: iphone_5s) }

      let(:params) do
        {
          product_name: "iPhone White"
        }
      end

      let(:params_iphone_5s) do
        {
          product_name: "iPhone 5S 8GB White"
        }
      end

      before { Sunspot.index [order_iphone_4s, order_iphone_5s]; Sunspot.commit(true) }

      it do
        expect(Sunspot.search(Order).results.size).to eq 3
        expect(subject.search(params).results).to include(order_iphone_5s, order_iphone_4s)
        expect(subject.search(params_iphone_5s).results).not_to include(order_iphone_4s)
        expect(subject.search(params_iphone_5s).results).to include(order_iphone_5s)
      end
    end

    context "#find_fulfillable_orders_for({ product_name: product.name })" do
      let(:kolme_iphone_4s) { create(:product, :iphone_4s, origin: "system") }
      let(:item) { create(:item, :in_system_owner_warehouse, product: kolme_iphone_4s) }
      let(:fulfill) { create(:fulfill, order: order, sent_at: Time.now) }

      let(:shopping_cart) { KF.create(:shopping_cart, dealer: dealer, order: order) }
      let!(:shopping_cart_item) { create(:shopping_cart_item, quantity: 1, owner: shopping_cart, item: kolme_iphone_4s) }

      context "order with unfulfilled kolme" do
        before { Sunspot.index order; Sunspot.commit(true)}

        it { expect(subject.find_fulfillable_orders_for({ product_name: kolme_iphone_4s.name })).to include(order) }
      end

      context "order with fulfilled kolme" do
        let!(:item_order) { create(:item_order, shopping_cart_item: shopping_cart_item, item: item, order: order, fulfill: fulfill, sent_at: Date.yesterday) }
        before { Sunspot.index order; Sunspot.commit(true)}

        it { expect(subject.find_fulfillable_orders_for({ product_name: kolme_iphone_4s.name })).not_to include(order) }
      end

      context "order with quantity > 1 fulfilled kolme (partially fulfilled)" do
        let!(:item_order) { create(:item_order, shopping_cart_item: shopping_cart_item, item: item, order: order, fulfill: fulfill) }
        before { shopping_cart_item.update_column(:quantity, 2) }
        before { Sunspot.index order; Sunspot.commit(true)}

        it { expect(subject.find_fulfillable_orders_for({ product_name: kolme_iphone_4s.name })).to include(order) }
      end

      context "order with quantity > 1 fulfilled kolme (partially fulfilled) and different product name" do
        let!(:item_order) { create(:item_order, shopping_cart_item: shopping_cart_item, item: item, order: order, fulfill: fulfill) }
        before { shopping_cart_item.update_column(:quantity, 2) }
        before { Sunspot.index order; Sunspot.commit(true)}

        it { expect(subject.find_fulfillable_orders_for({ product_name: "not present product" })).not_to include(order) }
      end
    end

    context "#cleanable_ids" do
      let!(:order) { create(:order, created_at: 2.month.ago, aasm_state: "payment1") }
      let!(:second_order) { create(:order, created_at: Date.yesterday, aasm_state: "payment1") }

      it do
        expect(described_class.cleanable_ids).to include order.id
        expect(described_class.cleanable_ids).not_to include second_order.id
      end
    end

    context 'shipment_padded_id' do
      let(:fulfill) { create(:fulfill, order: order, origin: 'dhl') }
      let!(:shipment) { create(:shipment, shippable: fulfill, id: 1) }

      before { Sunspot.index order.reload; Sunspot.commit(true) }

      it do
        expect(subject.search(shipment_padded_id: shipment.padded_id).results).to include order
        expect(subject.search(shipment_padded_id: 'KD000000023').results).not_to include order
      end
    end
  end

  context '#self.expired_fulfill' do
    let(:order) { create(:order) }

    it do
      expect(described_class.expired_fulfill).not_to include(order)

      order.update_columns(aasm_state: 'partially_processed')
      expect(described_class.expired_fulfill).not_to include(order)

      order.update_columns(payment_recap: ['30', 'F'])
      expect(described_class.expired_fulfill).not_to include(order)

      order.update_columns(created_at: (Order::REMOVE_UNFULFILL_DAYS - 1).days.ago)
      expect(described_class.expired_fulfill).not_to include(order)

      order.update_columns(created_at: Order::REMOVE_UNFULFILL_DAYS.days.ago)
      expect(described_class.expired_fulfill).to include(order)
    end
  end
end
