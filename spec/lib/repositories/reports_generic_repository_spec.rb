require 'rails_helper'

describe Repositories::ReportsGenericRepository do
  let(:user_access) { double }
  let(:args) do
    {
      user_access:  user_access,
      current_user: double
    }
  end

  let!(:report_generic) { create(:report_generic, schedule_kind: 'giornaliero', schedule_data: { 'hour_1' => '1' }) }
  let!(:second_report_generic) { create(:report_generic, name: 'report 2', schedule_kind: 'non_programmato') }

  before { allow(user_access).to receive(:agent?).and_return false }
  before { allow(user_access).to receive(:operator_ids).and_return [] }

  it { expect(described_class.new(args)).not_to be_nil }
  it { expect(described_class.new(args).search({}).result.count).to eq 2 }

  context 'search report with agent id' do
    let(:agent) { create(:user, :agent) }
    let(:report_user) { create :report_user }
    let(:search_params) do
      {
        search: {
          'name_cont'    => '',
          'id_eq'        => '',
          'email_cont'   => '',
          'sql_cont'     => '',
          'hour_eq'      => '',
          'agents_id_eq' => "#{agent.id}"
        }
      }
    end

    before do
      report_generic.agents << agent
    end

    it { expect(described_class.new(args).search(search_params).result).to include report_generic }
    it { expect(described_class.new(args).search(search_params).result).not_to include second_report_generic }
  end

  context 'search scheduled' do
    let(:search_params) do
      {
        search: {
          'name_cont'  => '',
          'id_eq'      => '',
          'email_cont' => '',
          'sql_cont'   => '',
          'scheduled'  => 'true'
        }
      }
    end

    it { expect(described_class.new(args).search(search_params).result).to include report_generic }
    it { expect(described_class.new(args).search(search_params).result).not_to include second_report_generic }
  end
end