require 'rails_helper'

describe AppointmentDetails::Exporter do
  let(:time) { Time.now }

  before { Timecop.freeze(time) }

  subject { described_class.new('APPOINTMENT_', Province::SIELTE_SYSTEM) }

  it { expect(subject.file_name).to eq "APPOINTMENT_#{time.strftime('%Y%m%d')}.XLSX" }

  after { FileUtils.rm_rf(subject.file_path) }
  after { Timecop.return }

  context "#export" do
    let(:phone_activation) { create(:phone_activation, :with_fixed_line_detail, main_phone_number: '028012480')}
    let!(:first_appointment_detail)  { create(:appointment_detail, phone_activation: phone_activation) }
    let!(:second_appointment_detail) { create(:appointment_detail) }

    context "with data to be exported" do
      context "without building errors" do
        before { subject.export }

        context "exporting all appointments" do
          it do
            expect(subject.rows.count).to eq 3

            full_path = [subject.file_path, subject.file_name].join("/")
            expect(File.exist?(full_path)).to be_truthy
            expect(Roo::Spreadsheet.open(full_path).sheet("APPOINTMENT_#{time.strftime('%Y%m%d')}").last_row).to eq 3
          end
        end
      end

      context "with building errors" do
        let(:row_builder) { double }
        let(:row_builder_instance) { double }
        let(:raising_row_builder_instance) { double }

        subject { described_class.new('APPOINTMENT_', Province::SIELTE_SYSTEM, row_builder: row_builder) }

        before do
          expect(row_builder).to receive(:header_row).and_return ['HEADER']
          expect(row_builder).to receive(:new).with(first_appointment_detail).and_return row_builder_instance
          expect(row_builder).to receive(:new).with(second_appointment_detail).and_return raising_row_builder_instance
          expect(row_builder_instance).to receive(:build).and_return ["test"]
          expect(raising_row_builder_instance).to receive(:build).and_raise StandardError.new("this is an exception")

          CsvExporterMailer.deliveries.clear

          subject.export
        end

        it "collects the errors generated by the row builder" do
          expect(subject.errors).to eq ["Installazione FWA con id #{second_appointment_detail.id} (this is an exception)"]
        end

        it "sends a notification email" do
          expect(CsvExporterMailer.deliveries.count).to eq 1
        end

        it "generates the csv file correctly" do
          full_path = [subject.file_path, subject.file_name].join("/")
          expect(File.exist?(full_path)).to be_truthy
          expect(Roo::Spreadsheet.open(full_path).sheet("APPOINTMENT_#{time.strftime('%Y%m%d')}").last_row).to eq 2
        end
      end
    end

    context 'headers' do
      it do
        subject = described_class.new('APPOINTMENT_', Province::SIELTE_SYSTEM)
        subject.export
        expect(subject.rows.first).to eq ["Codice Ordine CRM", "Numero Telefonico Principale", "Indirizzo Cliente", "Data appuntamento", "Inizio appuntamento"]

        subject = described_class.new('APPOINTMENT_', Province::SITE_SYSTEM)
        subject.export
        expect(subject.rows.first).to eq ["Nominativo cliente", "Codice Ordine CRM", "Numero Telefonico Principale", "Indirizzo Cliente", "Data appuntamento", "Inizio appuntamento"]
      end
    end
  end

  context "with no data to be exported" do
    it "generates a file with just the header" do
      subject.export

      expect(subject.rows).to eq [["Codice Ordine CRM", "Numero Telefonico Principale", "Indirizzo Cliente", "Data appuntamento", "Inizio appuntamento"]]
    end
  end
end
