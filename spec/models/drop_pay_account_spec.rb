require 'rails_helper'

describe DropPayAccount, type: :model do
  it { should belong_to(:dealer) }
  it { should have_many(:drop_pay_account_rows) }

  it { expect(described_class.state_in_activation).to be_empty }
  let!(:system_drop_pay_account) { create(:drop_pay_account, :system) }
  let(:drop_pay_account) { create(:drop_pay_account) }

  it do
    expect(drop_pay_account.state_not_enabled?).to be_truthy
    expect(drop_pay_account.recursive_charges_authorization_not_required?).to be_truthy
    expect(drop_pay_account.state_humanize).to eq 'Non abilitato'
    expect(drop_pay_account.recursive_charges_authorization_humanize).to eq 'Non richiesta'
  end

  context 'validations' do
    let(:drop_pay_account) { build(:drop_pay_account) }

    it { expect(drop_pay_account.valid?).to be_truthy }

    context 'state' do
      it 'rid_iban && mobile_phone' do
        drop_pay_account.enabled = true
        [:in_activation, :active].each do |key|
          drop_pay_account.rid_iban     = nil
          drop_pay_account.mobile_phone = nil
          drop_pay_account.state        = key
          drop_pay_account.valid?

          expect(drop_pay_account.errors.attribute_names).to include(:rid_iban, :mobile_phone)

          #length
          drop_pay_account.rid_iban     = '123'
          drop_pay_account.mobile_phone = '222'
          drop_pay_account.valid?

          expect(drop_pay_account.errors.attribute_names).to include(:rid_iban, :mobile_phone)

          #format
          drop_pay_account.rid_iban     = Faker::Alphanumeric.alphanumeric(number: 27)
          drop_pay_account.mobile_phone = '**********'
          drop_pay_account.valid?

          expect(drop_pay_account.errors.attribute_names).to include(:rid_iban, :mobile_phone)

          #accepted
          drop_pay_account.rid_iban     = '***************************'
          drop_pay_account.mobile_phone = '**********'
          drop_pay_account.opened_at    = Time.now
          drop_pay_account.valid?

          expect(drop_pay_account.errors.attribute_names).not_to include(:rid_iban, :mobile_phone, :opened_at)
        end
      end

      it 'opened_at presence' do
        [:active].each do |key|
          drop_pay_account.rid_iban     = '***************************'
          drop_pay_account.mobile_phone = '**********'
          drop_pay_account.opened_at    = nil
          drop_pay_account.closed_at    = Time.now
          drop_pay_account.state        = key
          drop_pay_account.valid?

          expect(drop_pay_account.opened_at).not_to be_nil
        end
      end

      it 'deactivated closed_at presence' do
        drop_pay_account.state = :deactivated
        drop_pay_account.valid?

        expect(drop_pay_account.errors.attribute_names).not_to include(:closed_at)
      end
    end
  end

  context '#after_save' do
    let!(:confirm_connection_alert) { create(:alert, :with_dealer_and_warehouse,
                                                     alertable: drop_pay_account,
                                                     alert_message: create(:alert_message, :drop_pay_account_confirm_connection)) }

    before do
      drop_pay_account.update_columns(
        enabled: true,
        state: 'active',
        opened_at: Time.now,
        connection_response: '{response: \'dummy_response\'}'
      )
    end

    context 'disabling account' do
      it do
        drop_pay_account.update(enabled: false)
        expect(drop_pay_account.alerts.visible.count).to be_zero
      end
    end

    context 'update data that require re-initialization api' do
      it do
        expect(Delayed::Job).to receive(:enqueue).exactly(1).times

        drop_pay_account.update(rid_iban: '***************************', mobile_phone: '**********')

        expect(drop_pay_account.state).to eq 'in_activation'
        expect(drop_pay_account.opened_at).to be_nil
        expect(drop_pay_account.closed_at).to be_nil
        expect(drop_pay_account.authorization_updated_at).to be_nil
        expect(drop_pay_account.recursive_charges_authorization).to eq 'not_required'
        expect(drop_pay_account.connection_response).to be_nil
        expect(drop_pay_account.bank_code).to be_nil
        expect(drop_pay_account.pull_id).to be_nil

        expect(drop_pay_account.alerts.visible.count).to be_zero
      end

      context 'disabled account' do
        it do
          expect(Delayed::Job).to receive(:enqueue).exactly(1).times

          drop_pay_account.update(rid_iban: '***************************', mobile_phone: '**********', enabled: false)

          expect(drop_pay_account.state).to eq 'not_enabled'
          expect(drop_pay_account.authorization_updated_at).to be_nil
          expect(drop_pay_account.recursive_charges_authorization).to eq 'not_required'
          expect(drop_pay_account.connection_response).to be_nil
          expect(drop_pay_account.bank_code).to be_nil
          expect(drop_pay_account.pull_id).to be_nil

          drop_pay_account.update(enabled: true)

          expect(drop_pay_account.state).to eq 'in_activation'
        end
      end
    end
  end

  context '#send_connect_authorization_request' do
    it do
      expect(Delayed::Job).to receive(:enqueue)
      drop_pay_account.send_connect_authorization_request
      expect(drop_pay_account.state).to eq DropPayStatus::IN_ACTIVATION
    end
  end

  context '#send_recursive_charges_authorization_request' do
    it do
      expect(Delayed::Job).to receive(:enqueue)
      drop_pay_account.send_recursive_charges_authorization_request
      expect(drop_pay_account.recursive_charges_authorization).to eq RecursiveChargesAuthorization::REQUIRED
    end
  end

  context '#send_authorizations_request' do
    let(:dealer_drop_pay_account) { create(:drop_pay_account, :dealer,
                                           rid_iban: '***************************') }

    it do
      expect_any_instance_of(Atono::Client).not_to receive(:connect)
      expect_any_instance_of(Atono::Client).not_to receive(:pull)
      dealer_drop_pay_account.send_authorizations_request
    end

    it do
      expect_any_instance_of(Atono::Client).to receive(:connect)

      dealer_drop_pay_account.update_columns(enabled: true)
      dealer_drop_pay_account.send_authorizations_request
    end

    it do
      expect_any_instance_of(Atono::Client).not_to receive(:connect)

      dealer_drop_pay_account.update_columns(enabled: true, state: DropPayStatus::ACTIVE)
      dealer_drop_pay_account.send_authorizations_request
    end

    it do
      expect_any_instance_of(Atono::Client).to receive(:connect)
      expect_any_instance_of(Atono::Client).not_to receive(:pull)

      dealer_drop_pay_account.update_columns(enabled: true, recursive_charges_authorization: RecursiveChargesAuthorization::GRANTED)
      dealer_drop_pay_account.send_authorizations_request
    end

    it do
      expect_any_instance_of(Atono::Client).not_to receive(:connect)
      expect_any_instance_of(Atono::Client).not_to receive(:pull)

      dealer_drop_pay_account.update_columns(enabled: true, state: DropPayStatus::ACTIVE, recursive_charges_authorization: RecursiveChargesAuthorization::GRANTED)
      dealer_drop_pay_account.send_authorizations_request
    end
  end

  context '#update_connection_status(connection_response)' do
    let(:dealer_drop_pay_account) { create(:drop_pay_account, :dealer,
                                           rid_iban: '***************************') }
    let!(:confirm_connection_alert) { create(:alert, :with_dealer_and_warehouse,
                                                     alertable: dealer_drop_pay_account,
                                                     alert_message: create(:alert_message, :drop_pay_account_confirm_connection)) }

    let(:connection_response) { { 'id' => 'dummy_id' } }

    subject { dealer_drop_pay_account.reload.update_connection_status(connection_response) }

    before { dealer_drop_pay_account.update_columns(state: 'in_activation') }

    context 'connection granted' do
      before do
        dealer_drop_pay_account.update(
                                      connection_response: JSON.parse('{"id": "' + connection_response['id'] + '"}'),
                                      bank_code: 'dummy')
        connection_response['status'] = 'GRANTED'
      end

      it do
        subject

        expect(dealer_drop_pay_account.reload.opened_at).not_to be_nil
        expect(dealer_drop_pay_account.state).to eq DropPayStatus::ACTIVE
        expect(dealer_drop_pay_account.alerts.visible.count).to be_zero
      end
    end

    context 'connection revoked' do
      before do
        dealer_drop_pay_account.update(
                                      connection_response: JSON.parse('{"id": "' + connection_response['id'] + '"}'),
                                      bank_code: 'dummy',
                                      opened_at: Date.yesterday)
        connection_response['status'] = 'REVOKED'
      end

      it do
        subject

        expect(dealer_drop_pay_account.reload.closed_at).not_to be_nil
        expect(dealer_drop_pay_account.state).to eq DropPayStatus::DEACTIVATED
        expect(dealer_drop_pay_account.alerts.visible.count).to be_zero
      end
    end

    context 'call without state update' do
      before do
        dealer_drop_pay_account.update(
                                      connection_response: JSON.parse('{"id": "' + connection_response['id'] + '"}'),
                                      bank_code: 'dummy')
        connection_response['status'] = 'WAITING'
      end

      it do
        subject

        expect(dealer_drop_pay_account.reload.opened_at).to be_nil
        expect(dealer_drop_pay_account.state).to eq DropPayStatus::IN_ACTIVATION
        expect(dealer_drop_pay_account.alerts.visible.count).to eq 1
      end
    end

    context 'wrong connection_id' do
      before do
        dealer_drop_pay_account.update(
                                      connection_response: JSON.parse('{"id": "' + connection_response['id'] + '"}'),
                                      bank_code: 'dummy',
                                      opened_at: Date.yesterday)
        connection_response['id'] = 'wrong_id'
      end

      it do
        expect(KolmeLogger).to receive(:error)

        subject
      end
    end
  end

  context '#update_recursive_charges_authorization_status(pull_response)' do
    let(:dealer_drop_pay_account) { create(:drop_pay_account, :dealer,
                                           rid_iban:  '***************************',
                                           bank_code: 'eSyLmKbIWWAlmnqrCmT5CdtrHb7ackUk') }

    let(:pull_response) { { 'id' => 'dummy_pull_id' } }

    before { dealer_drop_pay_account.update_columns(state: 'in_activation', pull_id: 'dummy_pull_id') }

    subject { dealer_drop_pay_account.update_recursive_charges_authorization_status(pull_response) }

    context 'authorization granted' do
      before { pull_response['status'] = 'RUNNING' }

      it do
        subject

        expect(dealer_drop_pay_account.reload.recursive_charges_authorization).to eq RecursiveChargesAuthorization::GRANTED
      end
    end

    context 'authorization denied' do
      before { pull_response['status'] = 'REFUSED' }

      it do
        subject

        expect(dealer_drop_pay_account.reload.recursive_charges_authorization).to eq RecursiveChargesAuthorization::DENIED
      end
    end

    context 'authorization revoked' do
      before { pull_response['status'] = 'REVOKED' }

      it do
        subject

        expect(dealer_drop_pay_account.reload.recursive_charges_authorization).to eq RecursiveChargesAuthorization::REVOKED
      end
    end

    context 'wrong pull_id' do
      before { pull_response['id'] = 'wrong_id' }

      it do
        expect(KolmeLogger).to receive(:error)

        subject
      end
    end

    context 'pull_response have same status of current' do
      before do
        pull_response['status'] = 'RUNNING'
        dealer_drop_pay_account.update_columns(recursive_charges_authorization: RecursiveChargesAuthorization::GRANTED)
      end

      it { expect{ subject }.not_to change{ dealer_drop_pay_account.authorization_updated_at } }
    end
  end

  describe 'audited' do
    around do |example|
      Audited.auditing_enabled = true
      example.run
      Audited.auditing_enabled = false
    end

    it { expect{ drop_pay_account.update(mobile_phone: '**********') }.to change{ drop_pay_account.audits.count }.by(1) }
  end
end
