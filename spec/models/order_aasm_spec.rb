# frozen_string_literal: true

require 'rails_helper'

describe Order do
  let(:user)                      { create(:user, :dealer_user) }
  let(:dealer)                    { create(:dealer, :generic_dealer) }
  let(:shopping_cart)             { create(:shopping_cart, dealer: dealer) }
  let(:shopping_cart_item)        { create(:shopping_cart_item, :recharge, owner: shopping_cart) }
  let(:shopping_cart_item_second) { create(:shopping_cart_item, :iphone, owner: shopping_cart) }

  let(:order) do
    create(:order,
           shopping_cart: shopping_cart,
           dealer:        dealer,
           user:          user,
           aasm_state:    'awaiting_confirmation')
  end

  context '#can_transition_to_paid?' do
    before do
      order.update_columns(aasm_state: 'awaiting_confirmation')
      create(:order_state, order: order, state: 'paid')
    end

    it { expect(order.reload.may_payment_done?).to be_falsey }
  end

  context 'decrement fido on order paid in part with fido' do
    before { order.update_column(:payment_recap, ['F', '30']) }

    it 'awaiting_cc_confirmation' do
      order.update_column(:aasm_state, 'credit_card')
      order.awaiting_cc_confirmation!

      expect(order.dealer.reload.daily_operations.to_f).to eq(12.2)
      expect(order.order_states.last.additional_info).to eq({ decremented_fido: true })
    end

    it 'to_pay' do
      order.update_columns(aasm_state: 'payment3')
      order.bank_transfer_payment!

      expect(order.dealer.reload.daily_operations.to_f).to eq(12.2)
      expect(order.order_states.last.additional_info).to eq({ decremented_fido: true })
    end

    it 'to_pay_credit_card' do
      order.update_columns(aasm_state: 'payment3')
      order.to_pay_credit_card_later!

      expect(order.dealer.reload.daily_operations.to_f).to eq(12.2)
      expect(order.order_states.last.additional_info).to eq({ decremented_fido: true })
    end

    it 'awaiting_cc_confirmation only with fido' do
      order.update_column(:aasm_state, 'credit_card')
      order.update_column(:payment_recap, ['F'])
      order.awaiting_cc_confirmation!

      expect(order.dealer.reload.daily_operations.to_f).to eq(12.2)
      expect(order.order_states.last.additional_info).to eq({ decremented_fido: true })
    end

    it 'to_pay only with fido' do
      order.update_columns(aasm_state: 'payment3')
      order.update_column(:payment_recap, ['F'])
      order.bank_transfer_payment!

      expect(order.dealer.reload.daily_operations.to_f).to eq(12.2)
      expect(order.order_states.last.additional_info).to eq({ decremented_fido: true })
    end

    it 'to_pay_credit_card only with fido' do
      order.update_columns(aasm_state: 'payment3')
      order.update_column(:payment_recap, ['F'])
      order.to_pay_credit_card_later!

      expect(order.dealer.reload.daily_operations.to_f).to eq(12.2)
      expect(order.order_states.last.additional_info).to eq({ decremented_fido: true })
    end

    it 'awaiting_cc_confirmation only with fido' do
      order.update_column(:aasm_state, 'credit_card')
      order.update_column(:payment_recap, ['30'])
      order.awaiting_cc_confirmation!

      expect(order.dealer.reload.daily_operations.to_f).to eq(0)
      expect(order.order_states.last.additional_info).not_to eq({ decremented_fido: true })
    end

    it 'to_pay only with fido' do
      order.update_columns(aasm_state: 'payment3')
      order.update_column(:payment_recap, ['30'])
      order.bank_transfer_payment!

      expect(order.dealer.reload.daily_operations.to_f).to eq(0)
      expect(order.order_states.last.additional_info).not_to eq({ decremented_fido: true })
    end

    it 'to_pay_credit_card only with fido' do
      order.update_columns(aasm_state: 'payment3')
      order.update_column(:payment_recap, ['30'])
      order.to_pay_credit_card_later!

      expect(order.dealer.reload.daily_operations.to_f).to eq(0)
      expect(order.order_states.last.additional_info).not_to eq({ decremented_fido: true })
    end

  end

  context 'decrement credit on order paid' do
    it 'payment_done not fido' do
      order.update_column(:aasm_state, 'credit_card')
      order.update_column(:payment_recap, ['B'])
      order.payment_done!

      expect(order.dealer.reload.daily_operations.to_f).to eq(12.2)
      expect(order.order_states.last.additional_info).to eq({ decremented_fido: true })
    end

    it 'payment_done only fido' do
      order.update_column(:aasm_state, 'credit_card')
      order.update_column(:payment_recap, ['F'])
      order.payment_done!

      expect(order.dealer.reload.daily_operations.to_f).to eq(12.2)
      expect(order.order_states.last.additional_info).to eq({ decremented_fido: true })
    end

    it 'payment_done not only fido' do
      order.update_column(:aasm_state, 'credit_card')
      order.update_column(:payment_recap, ['F', '30'])
      order.payment_done!

      expect(order.dealer.reload.daily_operations.to_f).to eq(12.2)
      expect(order.order_states.last.additional_info).to eq({ decremented_fido: true })
    end
  end

  context 'restore_dealer_fido' do
    before { order.update_column(:payment_recap, ['F']) }

    it 'restore the dealer fido' do
      order.payment_done!
      expect(order.dealer.reload.daily_operations.to_f).to eq(12.2)
      expect(order.order_states.last.additional_info).to eq({ decremented_fido: true })

      order.cancel!

      expect(order.dealer.reload.daily_operations.to_f).to be_zero
      expect(order.order_states.last.additional_info).to eq({})
    end
  end
end
