require "rails_helper"
require "carrierwave/test/matchers"

describe OperatorLogoUploader do
  include CarrierWave::Test::Matchers

  let(:operator) { mock_model(Operator) }
  let(:uploader) { OperatorLogoUploader.new(operator, :logo) }
  let(:path_to_file) { Rails.root.join "spec", "fixtures", "operator_logos", "fastweb.png" }

  before do
    OperatorLogoUploader.enable_processing = true
    File.open(path_to_file) { |f| uploader.store!(f) }
  end

  after do
    OperatorLogoUploader.enable_processing = false
    uploader.remove!
  end

  context "the small version" do
    it "scales down a landscape image to fit within 102 by 42 pixels" do
      expect(uploader.small).to be_no_larger_than(102, 42)
    end
  end

  it "makes the image readable only to the owner and not executable" do
    # expect(uploader).to have_permissions(0o600)
  end
end
