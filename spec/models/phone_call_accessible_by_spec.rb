require "rails_helper"
include IntegrationHelpers

describe PhoneCall do
  context ".accessible_by(ability)" do
    before { stub_geocoding_requests }

    context "when user is dealer" do
      let!(:dealer)                      { create(:dealer, :generic_dealer, :with_warehouse) }
      let!(:other_dealer)                { create(:dealer, :generic_dealer, :with_warehouse, name: "<PERSON>") }
      let!(:user_phone_call)             { create(:phone_call, dealer: dealer) }
      let!(:user_phone_call_with_errors) { create(:phone_call, :with_error, dealer: dealer) }
      let!(:other_phone_call)            { create(:phone_call, dealer: other_dealer) }
      let!(:dealer_contact)              { create(:dealer_contact, dealer: dealer, primary_contact: true) }
      let!(:user)                        { create(:user, :dealer_user, dealer_contact: dealer_contact) }
      let(:ability)                      { AbilityFactory.build_ability_for user }
      specify                            { expect(user_phone_call_with_errors.phone_call_import_errors_count).to be_positive }
      specify                            { expect(described_class.accessible_by(ability).to_a).to eq([user_phone_call]) }
    end

    context "when user is agent" do
      let!(:user)                 { create(:user, :agent) }
      let!(:internal_user_detail) { create(:internal_user_detail, user: user) }
      let!(:dealer)               { create(:dealer, :generic_dealer) }
      let!(:warehouse)            { create(:warehouse, sub_agent_wind_tre_id: user.id, dealer_id: dealer.id) }
      let!(:user_phone_call)      { create(:phone_call, dealer: dealer) }
      let!(:other_phone_call)     { create(:phone_call) }
      let(:ability)               { AbilityFactory.build_ability_for user.reload }
      specify                     { expect(described_class.accessible_by(ability)).to include(user_phone_call) }
    end
  end
end