require "rails_helper"

describe FulfillTransportDocumentNumber, type: :model do
  context "getting numbers for current year" do
    it "gets a sequence of numbers" do
      expect(FulfillTransportDocumentNumber.get(2017)).to eq(1)
      expect(FulfillTransportDocumentNumber.count).to eq(1)
      expect(FulfillTransportDocumentNumber.last.attributes.symbolize_keys).to include(year: 2017, number: 1)

      expect(FulfillTransportDocumentNumber.get(2017)).to eq(2)
      expect(FulfillTransportDocumentNumber.count).to eq(2)
      expect(FulfillTransportDocumentNumber.last.attributes.symbolize_keys).to include(year: 2017, number: 2)
    end
  end

  context "doesn't allow duplicates" do
    let(:last_number) { FulfillTransportDocumentNumber.get(2017) }
    subject { FulfillTransportDocumentNumber.new(year: 2017) }
    it { is_expected.not_to allow_value(last_number).for(:number) }
    it { is_expected.to allow_value(last_number + 1).for(:number) }
  end
end
