require 'rails_helper'

describe NumberPortabilityDetail do
  let(:phone_activation) { build_stubbed(:phone_activation) }
  let(:number_portability_detail) { create(:number_portability_detail, phone_activation: phone_activation) }
  let(:identity_document_kind) { build_stubbed(:identity_document_kind, :identity_card) }
  let(:foreign_identity_card) { build_stubbed(:identity_document_kind, :foreign_identity_card) }

  context 'set owner_document_issued_by' do
    it do
      number_portability_detail.different_owner = true
      number_portability_detail.save(validate: false)
      expect(number_portability_detail.owner_document_issued_by).to be_nil

      number_portability_detail.owner_identity_document_kind = identity_document_kind
      number_portability_detail.save(validate: false)
      expect(number_portability_detail.owner_document_issued_by).to eq identity_document_kind.very_issued_by

      number_portability_detail.owner_identity_document_kind = foreign_identity_card
      number_portability_detail.save(validate: false)
      expect(number_portability_detail.owner_document_issued_by).to eq foreign_identity_card.very_issued_by
    end
  end
end
