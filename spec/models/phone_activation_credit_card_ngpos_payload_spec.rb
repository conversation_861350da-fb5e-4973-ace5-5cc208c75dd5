require 'rails_helper'

describe PhoneActivation do
  context '#credit_card_ngpos_payload' do
    let(:payment_method_detail) { build_stubbed(:payment_method_detail, :with_different_accountholder) }
    let(:phone_activation) { build_stubbed(:phone_activation,
                                           :with_callbacks,
                                           :abbonamento_ricaricabile,
                                           payment_method_detail: payment_method_detail,
                                           upfront:               49.99,
                                           monthly:               10.99,
                                           public_price:          509,
                                           monthly_only_sim:      4.99,
                                           sending_cc_to_robot:   true) }
    let(:jwt_token) { "token1234" }

    before { allow(payment_method_detail).to receive(:payment_method).and_return(phone_activation.payment_method) }

    before { expect(JwtService).to receive_message_chain(:new, :encode).with("VMP_#{phone_activation.hex_id}").and_return jwt_token }

    let(:expected_payload) do
      {
        "status":          0,
        "priority":        phone_activation.priority_for_ngpos,
        "spazioId":        "VMP_#{phone_activation.hex_id}",
        "spazioToken":     jwt_token,
        "searchKeyClient": "#{phone_activation.customer.cf}",
        "dealerCode":      "#{phone_activation.warehouse.tre_code}",
        "customerInfo":    {
          "hasUnderage":      false,
          "customerType":     "Privato",
          "personalDataInfo": {
            "surname":                 "#{phone_activation.customer.last_name}",
            "name":                    "#{phone_activation.customer.first_name}",
            "birthDate":               "#{phone_activation.customer.birth_date.strftime('%FT%T.%LZ')}",
            "fiscalCode":              "#{phone_activation.customer.cf}",
            "vat":                     phone_activation.customer.vat,
            "birthPlace":              "#{phone_activation.customer.birth_place}",
            "birthProvince":           "#{phone_activation.customer.birth_province.code}",
            "birthNation":             "#{phone_activation.customer.birth_country.posng_value}",
            "gender":                  "Maschio",
            "addressUrbanDesignation": "#{phone_activation.customer.address_street_type.ps}",
            "addressName":             "#{phone_activation.customer.address_street_name}",
            "addressNumber":           "#{phone_activation.customer.number}",
            "addressPlace":            "",
            "addressCity":             "#{phone_activation.customer.city.description}",
            "addressZipCode":          "#{phone_activation.customer.zip}",
            "addressProvince":         "#{phone_activation.customer.province.code}",
            "addressNation":           "ITALIA",
            "tourist":                 phone_activation.customer.turista?
          },
          "contactInfo":      {
            "phoneNumber":       "#{phone_activation.customer.mobile_phone}",
            "secondPhoneNumber": "",
            "email":             "#{phone_activation.customer.email}",
          },
          "documentInfo":     {
            "type":         "#{phone_activation.customer.id_doc_kind_ngpos}, #{phone_activation.customer.id_doc_kind_very}",
            "releasedBy":   "",
            "nationality":  "#{phone_activation.customer.id_doc_country.posng_value}",
            "released":     "#{phone_activation.customer.id_doc_date.strftime('%FT%T.%LZ')}",
            "number":       "#{phone_activation.customer.id_doc_number}",
            "municipality": "",
            "province":     ""
          },
        },
        "creditCardInfo":  {
          "typeCard":           "",
          "number":             "#{phone_activation.payment_method_detail.asterisk_cc_number}",
          "expirationYear":     "",
          "expirationMonth":    "",
          "otherHolder":        phone_activation.payment_method_detail.different_accountholder,
          "SMSTelephoneNumber": "#{phone_activation.payment_method_detail.different_accountholder_phone_number}",
          "creditCardHolder":   {
            "holderFirstName": "#{phone_activation.payment_method_detail.different_accountholder_first_name}",
            "holderLastName":  "#{phone_activation.payment_method_detail.different_accountholder_last_name}",
            "birthDate":       "#{phone_activation.payment_method_detail.different_accountholder_birth_date.strftime('%FT%T.%LZ')}",
            "fiscalCode":      "#{phone_activation.payment_method_detail.different_accountholder_cf_or_vat}",
            "documentType":    "#{phone_activation.payment_method_detail.different_accountholder_id_doc_ngpos_name}",
            "issueDate":       "#{phone_activation.payment_method_detail.different_accountholder_id_doc_date.strftime('%FT%T.%LZ')}",
            "documentNumber":  "#{phone_activation.payment_method_detail.different_accountholder_id_doc_number}"
          }
        },
        "CustomerType":    "Privato",
        "ContractType":    "Postpagato",
        "dryRun":          true,
        "isModernFlow":    true
      }
    end

    it { expect(phone_activation.ngpos_payload).to eq expected_payload }
  end
end