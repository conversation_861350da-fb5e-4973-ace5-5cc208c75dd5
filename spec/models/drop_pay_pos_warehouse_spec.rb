require 'rails_helper'

describe Warehouse do
  let(:warehouse) { build(:warehouse) }

  it do
    expect(warehouse.drop_pay_pos_state_not_required?).to be_truthy
    expect(warehouse.drop_pay_pos_state_humanize).to eq 'Non richiesto'
  end

  context 'validations' do
    it { expect(warehouse.valid?).to be_truthy }

    context 'drop_pay_pos_state != not required' do
      before { warehouse.drop_pay_pos_state = :required }
      before { warehouse.valid? }

      it { expect(warehouse.errors.attribute_names).to include(:drop_pay_pos_state_changed_at) }
    end
  end
end
