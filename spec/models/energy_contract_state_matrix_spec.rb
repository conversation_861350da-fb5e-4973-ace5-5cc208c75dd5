require 'rails_helper'

RSpec.describe EnergyContractStateMatrix, type: :model do
  describe '#get_message_for_dealer' do
    let(:matrix) { build(:energy_contract_state_matrix, message: 'standard message', kolme_master_message: 'kolme master message') }
    let(:energy_contract) { build(:energy_contract) }

    context 'with regular dealer' do
      before do
        allow(energy_contract).to receive(:dealer_kolme_master?).and_return(false)
      end

      it 'returns standard message for regular dealer' do
        expect(matrix.get_message_for_dealer(energy_contract)).to eq('standard message')
      end
    end

    context 'with kolme master dealer' do
      before do
        allow(energy_contract).to receive(:dealer_kolme_master?).and_return(true)
      end

      it 'returns kolme master message when present' do
        expect(matrix.get_message_for_dealer(energy_contract)).to eq('kolme master message')
      end

      it 'returns standard message when kolme master message is blank' do
        matrix.kolme_master_message = nil
        expect(matrix.get_message_for_dealer(energy_contract)).to eq('standard message')
      end
    end
  end
end
