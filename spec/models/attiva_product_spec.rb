require "rails_helper"

describe AttivaProduct do
  let(:attiva_brand)        { create(:attiva_brand) }
  let(:attiva_category)     { create(:attiva_category) }
  let(:attiva_subcategory)  { create(:attiva_subcategory) }
  let(:company_brand)       { create(:company_brand) }

  let(:product) do
    create(:product, :attiva_imported, attiva_brand: attiva_brand, attiva_category: attiva_category,
                                       attiva_subcategory: attiva_subcategory, company_brand: company_brand)
  end

  let(:attiva_product) { create(:attiva_product) }

  subject { attiva_product }

  it "has sensible attributes" do
    expect(subject.sensible_attributes).to eq(attiva_product.attributes.slice(*described_class::SENSIBLE_ATTRS))
  end

  describe "with an old attiva_product already saved" do
    before do
      create(:attiva_product, name: "old product import", product: product)
    end

    let(:attiva_product) { create(:attiva_product, product: product) }

    it "has product" do
      expect(subject.has_product?).to be_truthy
    end
  end

  describe "#update_image", :vcr do
    subject { attiva_product.upload_image }

    describe "without image_url" do
      it "return nil" do
        expect(subject).to be_nil
      end
    end

    context "when has image_url" do
      let(:gallery_product_images) { double }
      let(:prod_image) { create(:product_image, product: product, image: File.open(local_image)) }

      before do
        allow(product).to receive(:gallery_product_images).and_return(gallery_product_images)
        allow(gallery_product_images).to receive(:first_or_create).and_return(prod_image)
        allow(prod_image).to receive(:remote_image_url=).and_return(prod_image)
      end

      let(:image_filename) { "immagineschedaprodotto22323.jpg" }
      let(:prefix_path)    { "uploads/product_images" }

      let(:image_path) do
        "spec/fixtures/product_images/#{image_filename}"
      end

      let(:local_image) do
        Rails.root.join image_path
      end

      let(:remote_image_url) do
        "https://placeholdit.imgix.net/~text?txtsize=33&txt=350%C3%97150&w=350&h=150"
      end

      let(:image_url) { "http://aws.attiva.com/images/immagineschedaprodotto21993.jpg" }

      let(:attiva_product) do
        create(:attiva_product, product: product, image_url: image_url)
      end

      context "with product.product_images empty" do
        it "cannot be nil" do
          expect(subject).to_not be_nil
        end

        it "create a product_image" do
          expect(subject.valid?).to be_truthy
        end
      end

      context "with product.product_images not empty" do
        let(:image_filename) { "immagineschedaprodotto22323.jpg" }
        let(:prefix_path)    { "uploads/product_images" }

        let(:image_path) do
          "spec/fixtures/product_images/#{image_filename}"
        end

        let(:local_image) do
          Rails.root.join image_path
        end

        before do
          create_list(:product_image, 2, product: product, image: File.new(local_image))

          allow(product).to receive(:gallery_product_images).and_return(gallery_product_images)
          allow(gallery_product_images).to receive(:first_or_create).and_return(product.product_images.first)
          allow(product.product_images.first).to receive(:remote_image_url=).and_return(product.product_images.first)
        end

        it "update the first one" do
          expect(subject).to_not eq product.product_images.last
          expect(subject).to eq product.product_images.first
          expect(subject.image.url).to eq "/#{prefix_path}/#{product.id}/#{subject.id}/#{image_filename}"
        end
      end
    end
  end

  describe "#image_size_changed" do
    subject { attiva_product.image_size_changed? }
    context "when there is a product with images" do
      before do
        local_image = Rails.root.join "spec/fixtures/product_images/immagineschedaprodotto22323.jpg"
        create(:product_image, product: product, image: File.new(local_image))
        attiva_product.product = product
        attiva_product.save!
      end

      context "and attiva_product has no image_url" do
        it "returns true" do
          expect(subject).to be_truthy
        end
      end
    end
  end
end
