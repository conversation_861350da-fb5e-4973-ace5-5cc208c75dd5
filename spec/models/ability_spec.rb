require "rails_helper"

describe "Ability" do
  context "Phone calls" do
    shared_examples_for "internal user" do
      specify { assert_can(user, :index, PhoneCall) }
      specify { assert_can(user, :show, PhoneCall) }
    end

    shared_examples_for "dealer user" do
      specify { assert_can(user, :index, PhoneCall) }
      specify { assert_can(user, :show, PhoneCall) }
      specify { assert_cannot(user, :destroy, PhoneCall) }
      specify { assert_cannot(user, :see_import_details, PhoneCall) }
    end

    context "Only admin" do
      specify { assert_can(build(:user, :admin), :destroy, PhoneCall) }
      specify { assert_can(build(:user, :admin), :see_import_details, PhoneCall) }
    end

    context "Internal users" do
      it_should_behave_like("internal user") { let(:user) { build(:user, :admin) } }
      it_should_behave_like("internal user") { let(:user) { build(:user, :super_user) } }
      it_should_behave_like("internal user") { let(:user) { build(:user, :role_user) } }
    end

    context "Other roles" do
      it_should_behave_like("dealer user") { let(:user) { build(:user, :dealer_user, dealer: build(:dealer, :generic_dealer)) } }
      it_should_behave_like("dealer user") { let(:user) { build(:user, :agent, internal_user_detail: build(:internal_user_detail)) } }
    end

    context "full search" do
      it do
        agent = build(:user, :agent, internal_user_detail: build(:internal_user_detail))

        assert_can build(:user, :admin), :full_search, PhoneCall
        assert_can build(:user, :super_user), :full_search, PhoneCall
        assert_can build(:user, :role_user), :full_search, PhoneCall
        assert_can agent, :full_search, PhoneCall
        assert_cannot build(:user, :dealer_user), :full_search, PhoneCall
      end
    end
  end

  def assert_can(user, do_what, target)
    ability = AbilityFactory.build_ability_for(user)
    expect(ability.can?(do_what, target)).to be_truthy, "Expected user with role #{user.role} to be able to #{do_what} on #{target.class.name}: #{target}"
  end

  def assert_cannot(user, do_what, target)
    ability = AbilityFactory.build_ability_for(user)
    expect(ability.can?(do_what, target)).to be_falsey, "Expected user with role #{user.role} not to be able to #{do_what} on #{target.class.name}: #{target}"
  end
end
