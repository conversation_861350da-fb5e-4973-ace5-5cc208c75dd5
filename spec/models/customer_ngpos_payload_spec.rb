require 'rails_helper'

describe Customer do
  let(:customer) { build_stubbed(:customer) }

  let(:expected_payload) do
    {
      "customerType":     'Privato',
      "personalDataInfo": {
        "surname":                 customer.last_name,
        "name":                    customer.first_name,
        "birthDate":               "#{customer.birth_date.strftime('%FT%T.%LZ')}",
        "fiscalCode":              customer.cf,
        "vat":                     customer.vat,
        "birthPlace":              customer.birth_place,
        "birthProvince":           customer.birth_province.code,
        "birthNation":             customer.birth_country.posng_value,
        "gender":                  '<PERSON><PERSON><PERSON>',
        "addressUrbanDesignation": customer.address_street_type.ps,
        "addressName":             customer.address_street_name,
        "addressNumber":           customer.number,
        "addressPlace":            '',
        "addressCity":             customer.city.description,
        "addressZipCode":          customer.zip,
        "addressProvince":         customer.province.code,
        "addressNation":           'ITALIA',
        "tourist":                 customer.turista?
      },
      "contactInfo":      {
        "phoneNumber":       customer.mobile_phone,
        "secondPhoneNumber": '',
        "email":             customer.email
      },
      "documentInfo":     {
        "type":         "#{customer.identity_document_kind.posng_value}, #{customer.identity_document_kind.very_value}",
        "releasedBy":   '',
        "nationality":  customer.id_doc_country.posng_value,
        "released":     "#{customer.id_doc_date.strftime('%FT%T.%LZ')}",
        "number":       customer.id_doc_number,
        "municipality": '',
        "province":     ''
      }
    }
  end

  it { expect(customer.ngpos_payload).to eq expected_payload }

  context 'doc_issued_by is never passed' do
    before { customer.doc_issued_by = 'U.C.O' }

    it { expect(customer.ngpos_payload).to eq expected_payload }
  end

  context 'minor customer payload' do
    let(:expected_payload) do
      {
        'personalDataInfoUnderAge': {
          'surname':                 customer.last_name,
          'name':                    customer.first_name,
          'birthDate':               "#{customer.birth_date.strftime('%FT%T.%LZ')}",
          'fiscalCode':              customer.cf,
          "vat":                     customer.vat,
          'birthPlace':              customer.birth_place,
          'birthProvince':           customer.birth_province.code,
          'birthNation':             customer.birth_country.posng_value,
          'gender':                  'Maschio',
          'addressUrbanDesignation': customer.address_street_type.ps,
          'addressName':             customer.address_street_name,
          'addressNumber':           customer.number,
          'addressPlace':            '',
          'addressCity':             customer.city.description,
          'addressZipCode':          customer.zip,
          'addressProvince':         customer.province.code,
          'addressNation':           'ITALIA',
          'tourist':                 customer.turista?
        },
        'documentInfoUnderAge':     {
          'type':         "#{customer.identity_document_kind.posng_value}, #{customer.identity_document_kind.very_value}",
          'releasedBy':   '',
          'nationality':  customer.id_doc_country.posng_value,
          'released':     "#{customer.id_doc_date.strftime('%FT%T.%LZ')}",
          'number':       customer.id_doc_number,
          'municipality': '',
          'province':     ''
        }
      }
    end

    it { expect(customer.minor_ngpos_payload).to eq expected_payload }

    context 'without id_doc_date' do
      before { customer.id_doc_date = nil }

      it { expect(customer.minor_ngpos_payload[:documentInfoUnderAge][:released]).to be_nil }
    end

    context 'id_doc_date == empty string' do
      before { customer.id_doc_date = '' }

      it { expect(customer.minor_ngpos_payload[:documentInfoUnderAge][:released]).to be_nil }
    end
  end

  context 'foreign born customer' do
    let(:foreign_province) { build_stubbed(:province, :foreign) }
    let(:foreign_country) { build_stubbed(:country, :uk, posng_value: 'UK') }
    let(:expected_payload) do
      {
        'customerType':     'Privato',
        'personalDataInfo': {
          'surname':                 customer.last_name,
          'name':                    customer.first_name,
          'birthDate':               "#{customer.birth_date.strftime('%FT%T.%LZ')}",
          'fiscalCode':              customer.cf,
          "vat":                     customer.vat,
          'birthPlace':              'UK',
          'birthProvince':           customer.birth_province.code,
          'birthNation':             customer.birth_country.posng_value,
          'gender':                  'Maschio',
          'addressUrbanDesignation': customer.address_street_type.ps,
          'addressName':             customer.address_street_name,
          'addressNumber':           customer.number,
          'addressPlace':            '',
          'addressCity':             customer.city.description,
          'addressZipCode':          customer.zip,
          'addressProvince':         customer.province.code,
          'addressNation':           'ITALIA',
          'tourist':                 customer.turista?
        },
        'contactInfo':      {
          'phoneNumber':       customer.mobile_phone,
          'secondPhoneNumber': '',
          'email':             customer.email
        },
        'documentInfo':     {
          'type':         "#{customer.identity_document_kind.posng_value}, #{customer.identity_document_kind.very_value}",
          'releasedBy':   '',
          'nationality':  customer.id_doc_country.posng_value,
          'released':     "#{customer.id_doc_date.strftime('%FT%T.%LZ')}",
          'number':       customer.id_doc_number,
          'municipality': '',
          'province':     ''
        }
      }
    end

    before do
      customer.birth_province = foreign_province
      customer.birth_country = foreign_country
      customer.id_doc_country = foreign_country
    end

    it { expect(customer.ngpos_payload).to eq expected_payload }
  end

  context 'turista' do
    let(:customer_kind_tu) { build_stubbed(:customer_kind, :turista) }
    let(:foreign_province) { build_stubbed(:province, :foreign) }
    let(:foreign_country) { build_stubbed(:country, :uk, posng_value: 'UK') }
    let(:expected_payload) do
      {
        'customerType':     '',
        'personalDataInfo': {
          'surname':                 customer.last_name,
          'name':                    customer.first_name,
          'birthDate':               "#{customer.birth_date.strftime('%FT%T.%LZ')}",
          'fiscalCode':              customer.id_doc_number,
          "vat":                     customer.vat,
          'birthPlace':              'UK',
          'birthProvince':           customer.birth_province.code,
          'birthNation':             customer.birth_country.posng_value,
          'gender':                  'Maschio',
          'addressUrbanDesignation': customer.address_street_type.ps,
          'addressName':             'Dei Giardini',
          'addressNumber':           '11',
          'addressPlace':            '',
          'addressCity':             customer.city.description,
          'addressZipCode':          '20133',
          'addressProvince':         customer.province.code,
          'addressNation':           'ITALIA',
          'tourist':                 true
        },
        'contactInfo':      {
          'phoneNumber':       customer.mobile_phone,
          'secondPhoneNumber': '',
          'email':             customer.email
        },
        'documentInfo':     {
          'type':         "#{customer.identity_document_kind.posng_value}, #{customer.identity_document_kind.very_value}",
          'releasedBy':   '',
          'nationality':  customer.id_doc_country.posng_value,
          'released':     "#{customer.id_doc_date.strftime('%FT%T.%LZ')}",
          'number':       customer.id_doc_number,
          'municipality': '',
          'province':     ''
        }
      }
    end

    before do
      customer.customer_kind = customer_kind_tu
      customer.birth_province = foreign_province
      customer.birth_country = foreign_country
      customer.id_doc_country = foreign_country
      customer.id_doc_number = '1234'
      customer.address_street_name = 'Dei Giardini'
      customer.number = '11'
      customer.zip = '20133'
    end

    it { expect(customer.ngpos_payload).to eq expected_payload }
  end

  context 'ditta individuale' do
    let(:customer_kind_di) { build_stubbed(:customer_kind, :ditta_individuale) }

    before { customer.customer_kind = customer_kind_di }

    let(:expected_payload) do
      {
        "customerType":     'Ditta Individuale, Libero Professionista, Libero professionista',
        "personalDataInfo": {
          "surname":                 customer.last_name,
          "name":                    customer.first_name,
          "birthDate":               "#{customer.birth_date.strftime('%FT%T.%LZ')}",
          "fiscalCode":              customer.cf,
          "vat":                     customer.vat,
          "birthPlace":              customer.birth_place,
          "birthProvince":           customer.birth_province.code,
          "birthNation":             customer.birth_country.posng_value,
          "gender":                  'Maschio',
          "addressUrbanDesignation": customer.address_street_type.ps,
          "addressName":             customer.address_street_name,
          "addressNumber":           customer.number,
          "addressPlace":            '',
          "addressCity":             customer.city.description,
          "addressZipCode":          customer.zip,
          "addressProvince":         customer.province.code,
          "addressNation":           'ITALIA',
          "tourist":                 customer.turista?
        },
        "contactInfo":      {
          "phoneNumber":       customer.mobile_phone,
          "secondPhoneNumber": '',
          "email":             customer.email
        },
        "documentInfo":     {
          "type":         "#{customer.identity_document_kind.posng_value}, #{customer.identity_document_kind.very_value}",
          "releasedBy":   '',
          "nationality":  customer.id_doc_country.posng_value,
          "released":     "#{customer.id_doc_date.strftime('%FT%T.%LZ')}",
          "number":       customer.id_doc_number,
          "municipality": '',
          "province":     ''
        }
      }
    end

    it { expect(customer.ngpos_payload).to eq expected_payload }
  end

  context 'persona giuridica' do
    let(:customer) { build_stubbed(:customer, :persona_giuridica, company_kind: 'Srls') }

    let(:expected_payload) do
      {
        "customerType":        'Azienda',
        "companyData":         {
          "companyName": customer.company_name,
          "legalStatus": 'Società a respons. limitata',
          "vat":         customer.vat
        },
        "legalRepresentative": {
          "surname":                 customer.last_name,
          "name":                    customer.first_name,
          "birthDate":               "#{customer.birth_date.strftime('%FT%T.%LZ')}",
          "fiscalCode":              customer.cf,
          "vat":                     customer.vat,
          "birthPlace":              customer.birth_place,
          "birthProvince":           customer.birth_province.code,
          "birthNation":             customer.birth_country.posng_value,
          "gender":                  'Maschio',
          "addressUrbanDesignation": customer.headquarters_address_street_type.ps,
          "addressName":             customer.headquarters_address_street_name,
          "addressNumber":           customer.headquarters_number,
          "addressPlace":            '',
          "addressCity":             customer.headquarters_city.description,
          "addressZipCode":          customer.headquarters_zip,
          "addressProvince":         customer.headquarters_province.code,
          "addressNation":           'ITALIA',
          "tourist":                 customer.turista?
        },
        "contactInfo":         {
          "phoneNumber":       customer.mobile_phone,
          "secondPhoneNumber": '',
          "email":             customer.email
        },
        "documentInfo":        {
          "type":         "#{customer.identity_document_kind.posng_value}, #{customer.identity_document_kind.very_value}",
          "releasedBy":   '',
          "nationality":  customer.id_doc_country.posng_value,
          "released":     "#{customer.id_doc_date.strftime('%FT%T.%LZ')}",
          "number":       customer.id_doc_number,
          "municipality": '',
          "province":     ''
        }
      }
    end

    it { expect(customer.ngpos_payload).to eq expected_payload }
  end
end