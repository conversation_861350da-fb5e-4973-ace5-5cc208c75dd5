require 'rails_helper'

describe Document do
  after { FileUtils.rm_rf('spec/fixtures/tmp/phone_activations') }
  after { FileUtils.rm_rf('spec/fixtures/tmp/dealers') }

  context 'validate' do
    let(:document) { create(:document) }

    it 'expires_at' do
      expect(document.valid?).to be_truthy

      document.expires_at = Date.yesterday
      expect(document.valid?).to be_falsey

      document.expires_at = Date.tomorrow
      expect(document.valid?).to be_truthy

      document.update_columns(expires_at: Date.yesterday)
      document.notes = 'test'
      expect(document.valid?).to be_truthy

      document.expires_at = nil
      expect(document.valid?).to be_truthy
    end
  end

  context 'archived scope' do
    let(:phone_activation) { create(:phone_activation) }
    let!(:contract_document) { create(:document, :contract_file, documentable: phone_activation) }
    let!(:document) { create(:document) }

    it do
      expect(Document.count).to eq 2
      expect(Document.archived.count).to be_zero

      contract_document.update_columns(archived_at: Time.current)
      expect(Document.count).to eq 1
      expect(Document.archived.count).to eq 1
      expect(Document.contract_files.archived.count).to eq 1

      document.update_columns(archived_at: Time.current)
      expect(Document.count).to be_zero
      expect(Document.archived.count).to eq 2
      expect(Document.contract_files.archived.count).to eq 1
    end
  end

  context '#destroy' do
    let(:official_pda) { create(:official_pda, code: '123') }
    let(:document) { create(:document) }

    it do
      document.destroy
      expect(Document.count).to be_zero
    end

    it 'destroy PhoneActivation document without related_file and name' do
      document.update(documentable: create(:phone_activation), related_file: nil, name: nil)

      document.destroy
      expect(Document.count).to be_zero
    end

    it do
      document.update(documentable: official_pda)
      document.destroy
      expect(official_pda.code).to be_nil
    end
  end

  context 'PhoneActivation' do
    let(:phone_activation) { stub_model(PhoneActivation, hex_id: '12345d') }

    context '#documentable_type_for(context, documentable)' do
      it { expect(described_class.documentable_type_for('upload_serial_in_use', phone_activation)).to eq 'phone_activation_with_serial_in_use' }
    end

    context 'name_for(document_type, documentable, context, content = nil)' do
      let(:official_pda) { stub_model(OfficialPda, phone_activation: phone_activation) }
      let(:order) { stub_model(Order) }
      let(:wind_ngpos_activation) { stub_model(WindNgposActivation, id: '12') }
      let(:time) { Time.now }

      before { Timecop.freeze(time) }
      after { Timecop.return }

      it do
        expect(described_class.name_for('documents', phone_activation, 'upload_serial_in_use')).to eq 'CONTR12345d'

        expect(described_class.name_for('documents', order, nil)).to eq "BON_#{order.id}_#{Time.now.strftime('%d-%m-%Y_%H-%M')}"
        expect(described_class.name_for('documents', official_pda, nil)).to eq 'PDC_OP_12345d'
        expect(described_class.name_for('contract_signature_log', phone_activation, nil)).to eq 'CSL12345d'
        expect(described_class.name_for('documents', wind_ngpos_activation, nil)).to eq 'DOC12'
        expect(described_class.name_for('fiscal_code', phone_activation, nil)).to eq 'CF12345d'
        expect(described_class.name_for('sim_card', phone_activation, nil)).to eq 'SIM12345d'
        expect(described_class.name_for('different_owner_mnp_documents', phone_activation, 'upload_mnp_different_owner')).to eq 'DOCM12345d'
        expect(described_class.name_for('documents', phone_activation, 'unknow_context')).to eq 'DOC12345d'

        expect { described_class.name_for('different_owner_mnp_documents', phone_activation, 'unknown_upload_mnp_different_owner') }.to raise_error "UNKNOWN DOCUMENT CONTEXT unknown_upload_mnp_different_owner for different_owner_mnp_documents"
      end
    end
  end

  context 'SignedContract' do
    let(:signed_contract) { stub_model(SignedContract) }

    context 'name_for(document_type, documentable, context, content = nil)' do
      let(:date) { Date.new(2023, 7, 31) }

      before { Timecop.freeze(date) }
      after { Timecop.return }

      it do
        expect(described_class.name_for('signed_contracts', signed_contract, nil, 'contract')).to eq "Condizioni_Generali_Kolme_#{signed_contract.id}_31072023"
        expect(described_class.name_for('signed_contracts', signed_contract, nil, 'contract_signature_log')).to eq "Audit_Log_Firma_#{signed_contract.id}_31072023"
      end
    end
  end

  context '#self.extensions_for(context, documentable)' do
    let(:order) { build_stubbed(:order) }
    let(:dealer) { build_stubbed(:dealer) }

    it do
      expect(described_class.extensions_for('upload', order)).to match_array ['pdf', 'jpg', 'jpeg', 'png']
      expect(described_class.extensions_for('upload_mdv_document', dealer)).to match_array ['pdf', 'jpg', 'jpeg', 'png', 'xls', 'xlsx', 'xlsb']
      expect(described_class.extensions_for('upload_fwa_document', dealer)).to match_array ['pdf', 'jpg', 'jpeg', 'png']
    end
  end

  context '#create_tmp_files(document_type, document_content, documentable, context)' do
    let(:dealer_documentable) { stub_model(Dealer) }
    let(:phone_activation_documentable) { stub_model(PhoneActivation) }

    let(:pdf_document_content) { fixture_file_upload('spec/fixtures/files/pdf_test.pdf') }
    let(:png_document_content) { fixture_file_upload('spec/fixtures/files/test.png') }
    let(:xlsx_document_content) { fixture_file_upload('spec/fixtures/files/test.xlsx') }
    let(:txt_document_content) { fixture_file_upload('spec/fixtures/files/test.txt') }

    it do
      response = described_class.create_tmp_files(
        "FILE#{dealer_documentable.id}",
        'documents',
        [pdf_document_content, png_document_content],
        dealer_documentable,
        'upload'
      )
      expect(response.first.count).to eq 2
      expect(response.second.count).to eq 2
    end

    it do
      response = described_class.create_tmp_files(
        "FILE#{dealer_documentable.id}",
        'documents',
        [txt_document_content, pdf_document_content],
        dealer_documentable,
        'upload'
      )
      expect(response).to eq false
    end

    it do
      response = described_class.create_tmp_files(
        "FILE#{dealer_documentable.id}",
        'documents',
        [xlsx_document_content, pdf_document_content],
        dealer_documentable,
        'upload'
      )
      expect(response.first.count).to eq 2
      expect(response.second.count).to eq 2
    end

    it do
      response = described_class.create_tmp_files(
        "DOC#{phone_activation_documentable.hex_id}",
        'documents',
        [xlsx_document_content, pdf_document_content],
        phone_activation_documentable, 'upload'
      )
      expect(response).to eq false
    end
  end

  context '#name_no_ext' do
    let(:document) { stub_model(Document) }

    it do
      document.name = 'J.I.Global Cash_dichiarazione_di_trasparenza.pdf'
      expect(document.name_no_ext).to eq 'J.I.Global Cash_dichiarazione_di_trasparenza'

      document.name = 'FM Informatica_visura_camerale.pdf'
      expect(document.name_no_ext).to eq 'FM Informatica_visura_camerale'
    end
  end

  context '#expiring?' do
    let(:document) { build_stubbed(:document, status: Document::ACCEPTED) }

    it do
      expect(document.expiring?).to be_falsey

      document.expires_at = (Document::DAYS_EXPIRATION_NOTICE + 1).days.from_now.to_date
      expect(document.expiring?).to be_falsey

      document.expires_at = Document::DAYS_EXPIRATION_NOTICE.days.from_now.to_date
      expect(document.expiring?).to be_truthy

      document.expires_at = (Document::DAYS_EXPIRATION_NOTICE - 1).days.from_now.to_date
      expect(document.expiring?).to be_truthy

      document.status = nil
      expect(document.expiring?).to be_falsey
    end
  end

  context '#includes_fiscal_code?' do
    let(:document) { stub_model(Document, content: 'docs') }

    it do
      expect(document.includes_fiscal_code?).to be_falsey

      document.content = 'docs|minor_docs|minor_fiscal_code'
      expect(document.includes_fiscal_code?).to be_falsey

      document.content = 'docs|fiscal_code'
      expect(document.includes_fiscal_code?).to be_truthy
    end
  end

  context 'archivable document' do
    let(:dealer) { create(:dealer) }
    let!(:archivable_document) { create(:document, :fwa_kolme_installation_document_file, content: 'cciaa', documentable: dealer, status: Document::ACCEPTED) }
    let!(:not_accepted_document) { create(:document, :fwa_kolme_installation_document_file, content: 'identity_document', documentable: dealer) }
    let!(:not_archivable_document) { create(:document, :signed_contract_file, content: 'contract', documentable: dealer, status: Document::ACCEPTED) }

    it do
      # archive previous document
      new_archivable_document = create(:document, :fwa_kolme_installation_document_file, content: 'cciaa', documentable: dealer)
      expect(Document.count).to eq 3
      expect(Document.ids).to match_array [new_archivable_document.id, not_accepted_document.id, not_archivable_document.id]
      expect(Document.archived.count).to eq 1
      expect(Document.archived.ids).to eq [archivable_document.id]

      # does not archive previous document and remove previous document
      new_not_accepted_document = create(:document, :fwa_kolme_installation_document_file, content: 'identity_document', documentable: dealer)
      expect(Document.count).to eq 3
      expect(Document.ids).to match_array [new_not_accepted_document.id, new_archivable_document.id, not_archivable_document.id]
      expect(Document.archived.count).to eq 1
      expect(Document.archived.ids).to eq [archivable_document.id]

      # does not archive previous document
      new_not_archivable_document = create(:document, :signed_contract_file, content: 'contract', documentable: dealer)
      expect(Document.count).to eq 4
      expect(Document.ids).to match_array [new_not_archivable_document.id, new_not_accepted_document.id, new_archivable_document.id, not_archivable_document.id]
      expect(Document.archived.count).to eq 1
      expect(Document.archived.ids).to eq [archivable_document.id]
    end
  end
end
