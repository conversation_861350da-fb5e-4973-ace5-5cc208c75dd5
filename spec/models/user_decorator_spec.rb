require 'rails_helper'

describe UserDecorator do
  context 'agent_drop_pay_menu_visible?' do
    let(:agent) { create(:user, :agent) }
    let(:drop_pay_account_row) { create(:drop_pay_account_row) }
    let!(:warehouse) { create(:warehouse, agent_wind_tre: agent, dealer: drop_pay_account_row.drop_pay_account.dealer) }

    it do
      expect(agent.agent_drop_pay_menu_visible?).to be_truthy

      warehouse.update(agent_wind_tre: nil)
      expect(agent.agent_drop_pay_menu_visible?).to be_falsey
    end
  end

  describe '#show_only_my_own_data' do
    let(:dealer_contact) { create(:dealer_contact) }
    let(:dealer) { dealer_contact.dealer }
    let(:user) { dealer_contact.user }

    it do
      # #primary contact
      dealer_contact.update_columns(primary_contact: true)
      expect(user.show_only_my_own_data).to be_falsey

      # not primary contact but operator tls and team leader
      dealer_contact.update_columns(primary_contact: false, tls_primary_contact: true)
      dealer.update_columns(kind: 'operator_tls')
      expect(user.show_only_my_own_data).to be_falsey

      # primary contact, operator tls and team leader
      dealer_contact.update_columns(primary_contact: true, tls_primary_contact: true)
      dealer.update_columns(kind: 'operator_tls')
      expect(user.show_only_my_own_data).to be_falsey

      # not primary contact but franchising and team leader
      dealer_contact.update_columns(primary_contact: false, tls_primary_contact: true)
      dealer.update_columns(kind: 'franchising')
      expect(user.show_only_my_own_data).to be_falsey

      # primary contact, franchising and team leader
      dealer_contact.update_columns(primary_contact: true, tls_primary_contact: true)
      dealer.update_columns(kind: 'franchising')
      expect(user.show_only_my_own_data).to be_falsey

      # not primary contact but gallery_gd and team leader
      dealer_contact.update_columns(primary_contact: false, tls_primary_contact: true)
      dealer.update_columns(kind: 'gallery_gd')
      expect(user.show_only_my_own_data).to be_falsey

      # primary contact, gallery_gd and team leader
      dealer_contact.update_columns(primary_contact: true, tls_primary_contact: true)
      dealer.update_columns(kind: 'gallery_gd')
      expect(user.show_only_my_own_data).to be_falsey

      # not primary contact, not operator tls and team leader
      dealer_contact.update_columns(primary_contact: false, tls_primary_contact: true)
      dealer.update_columns(kind: 'generic')
      expect(user.show_only_my_own_data).to be_falsey

      # not primary contact, not operator tls not team leader
      dealer_contact.update_columns(primary_contact: false, tls_primary_contact: false)
      dealer.update_columns(kind: 'generic')
      expect(user.show_only_my_own_data).to be_truthy
    end
  end

  describe '#cacheable_prefix' do
    let(:user) { build_stubbed(:user, :agent) }

    it { expect(user.cacheable_prefix).to eq "#{user.id}-agent" }
  end

  describe '#provide_counter_cache_key' do
    context 'agent' do
      let(:user) { create(:user, :agent) }

      it 'fetch cache_key from user' do
        expect(user.reload.provide_counter_cache_key.cacheable_type).to eq 'User'
      end
    end

    context 'dealer user' do
      let(:dealer_contact) { create(:dealer_contact) }
      let(:dealer) { dealer_contact.dealer }
      let!(:dealer_user) { dealer_contact.user }

      it 'fetches cache_key from dealer' do
        expect(dealer_user.reload.provide_counter_cache_key.cacheable_type).to eq 'Dealer'
      end
    end
  end

  describe '#new_ast_cache_key' do
    let(:dealer_contact) { create(:dealer_contact) }
    let(:dealer) { dealer_contact.dealer }
    let!(:dealer_user) { dealer_contact.user }
    let!(:thread_kind) { create(:thread_kind, :edit_phone_activation) }

    it 'returns the cache_key for the new_ast' do
      expect(dealer_user.new_ast_cache_key).to eq "#{dealer.id}-#{dealer.role}-new_ast-#{Time.now.strftime('%d|%m|%y|%H|%M')}"
    end

    it 'returns a new cache_key for the new_ast' do
      thread_kind.update(generic_partner_enabled: true, pure_installer_fwa_enabled: true)

      expect(dealer.reload.counter_cache_key['new_ast']).to be_nil
      expect(dealer_user.new_ast_cache_key).to eq "#{dealer.id}-#{dealer.role}-new_ast-#{Time.now.strftime('%d|%m|%y|%H|%M')}"
      expect(dealer.reload.counter_cache_key['new_ast']).to eq dealer_user.new_ast_cache_key
    end
  end
end
