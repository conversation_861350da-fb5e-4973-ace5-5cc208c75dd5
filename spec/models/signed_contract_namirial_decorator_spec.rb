require 'rails_helper'

describe SignedContract, type: :module do
  let(:signed_contract) { create(:signed_contract) }
  let(:signature_data) do
    {
      'dealer' => { 'number' => '3334167742', 'email' => '<EMAIL>', 'first_name' => '<PERSON>abi<PERSON>', 'last_name' => 'Mazzon', 'update_number_otp' => 'false' }
    }
  end
  let(:prepare_body_json) { JSON.parse(File.read("#{Rails.root}/spec/fixtures/digital_signature/prepare_body_contract.json")) }

  it { expect(signed_contract.namirial_id).to eq signed_contract.id }
  it { expect(signed_contract.build_namirial_prepare_payload('123').fetch(:FileIds)).to eq ['123'] }
  it { expect(signed_contract.build_namirial_send_payload('123', prepare_body_json).fetch(:Documents)).to eq [{FileId: "123", DocumentNumber: 1}] }
end
