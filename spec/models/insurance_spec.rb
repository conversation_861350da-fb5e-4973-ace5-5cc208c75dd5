require 'rails_helper'

describe Insurance, type: :model do
  it { should validate_length_of(:voucher_code) }
  it { should validate_length_of(:product_serial) }

  it 'have a full_name method' do
    expect(subject).to receive(:full_name)
    subject.full_name
  end

  context 'validations' do
    describe 'validate price_ranges' do
      let!(:easycare_price_range) { create(:easycare_price_range) }
      let(:error_message) { 'Errore nel calcolo dei costi' }

      it do
        insurance = build_stubbed(:insurance, :azienda, :iphone, product_price: 10,
                                                                 origin:        'form',
                                                                 price:         easycare_price_range.customer_price,
                                                                 dealer_price:  easycare_price_range.dealer_price,
                                                                 kolme_price:   easycare_price_range.agent_profit,
                                                                 agent_profit:  easycare_price_range.agent_profit,
                                                                 gamma_code:    easycare_price_range.gamma_code)
        insurance.valid?
        expect(insurance.errors.full_messages).not_to include error_message

        [['price', 'customer_price'],
         ['dealer_price', 'dealer_price'],
         ['kolme_price', 'kolme_price'],
         ['agent_profit', 'agent_profit'],
         ['gamma_code', 'gamma_code']].each do |insurance_field, range_field|
          insurance.send("#{insurance_field}=", 100)
          insurance.valid?
          expect(insurance.errors.full_messages).to include error_message

          insurance.origin = nil
          insurance.valid?
          expect(insurance.errors.full_messages).not_to include error_message

          # restore valid value
          insurance.origin = 'form'
          insurance.send("#{insurance_field}=", easycare_price_range.send(range_field))
        end
      end
    end

    describe 'imei validation' do
      it '0 is not valid imei' do
        insurance = build_stubbed(:insurance, :azienda, :iphone, product_serial: '0')
        expect(insurance.valid?).to be_falsey
      end
      it 'aaaa is not numerically valid imei' do
        insurance = build_stubbed(:insurance, :azienda, :iphone, product_serial: 'aaaa')
        expect(insurance.valid?).to be_falsey
      end
      it '35920907518 is too short imei' do
        insurance = build_stubbed(:insurance, :azienda, :iphone, product_serial: '35920907518')
        expect(insurance.valid?).to be_falsey
      end
      it '359209075180624 is valid imei' do
        insurance = build_stubbed(:insurance, :azienda, :iphone, product_serial: '359209075180624')
        expect(insurance.valid?).to be_truthy
      end

      context 'uniqueness' do
        it 'if add with serial' do
          insurance = create(:insurance, :azienda, :iphone, product_serial: '359209075180624')
          same_imei_insurance = build_stubbed(:insurance, :azienda, :iphone, product_serial: '359209075180624')

          expect(same_imei_insurance.valid?).to be_falsey
        end

        it 'from phone activation can insert n insurances with empty imei' do
          phone_activation = create(:phone_activation, :eolo_casa)
          insurance = create(:insurance, :azienda, :iphone, product_serial: '', phone_activation: phone_activation)
          same_imei_insurance = build_stubbed(:insurance, :azienda, :iphone, product_serial: '', phone_activation: phone_activation)

          expect(same_imei_insurance.valid?).to be_truthy
        end
      end
    end
  end

  describe 'insurance for privato' do
    let(:insurance_privato) { create(:insurance, :privato, :iphone) }

    it 'have a full_address method and return' do
      expect(insurance_privato.full_name).to eq("#{insurance_privato.customer.first_name} #{insurance_privato.customer.last_name}")
    end
  end

  describe 'insurance for azienda' do
    let(:insurance_azienda) { create(:insurance, :azienda, :iphone) }

    it 'have a full_address method and return' do
      expect(insurance_azienda.full_name).to eq(insurance_azienda.customer.company_name)
    end
  end

  describe '#send_to_sap', delayed_job: true do
    let(:insurance) { create(:insurance, :azienda, :iphone) }

    it 'sent to sap' do
      insurance
      expect(insurance.sap_exported_at).to be_nil
      expect(Delayed::Job.last.handler.include?('Jobs::Sap::OdvExporter')).to be_truthy
    end
  end

  describe '#status' do
    let(:insurance) { create(:insurance, :privato, :iphone) }

    it ':to_be_confirmed for new added insurance ' do
      expect(insurance.status).to eq(:to_be_confirmed)
    end

    it ':sent for sent insurance' do
      insurance.sent         = true
      insurance.voucher_code = nil
      expect(insurance.status).to eq(:sent)
    end

    it ':used if sent and with voucher_code' do
      insurance.sent = true
      insurance.voucher_code = 'I1KLMAAAAAAAAAAAA'
      expect(insurance.status).to eq(:used)
    end

    it ':candeled if canceled' do
      insurance.canceled = true
      expect(insurance.status).to eq(:canceled)
    end
  end

  describe 'validate voucher code' do
    let(:insurance) { build(:insurance) }

    context 'Kolme' do
      context 'with voucher code not starting with I1KLM' do
        before { insurance.voucher_code = 'AAAAAAAAAAAAAAAAA' }
        before { insurance.valid? }
        it { expect(insurance.errors.attribute_names).to include(:voucher_code) }
      end

      context 'with voucher code with 16 digits' do
        before { insurance.voucher_code = 'I1KLMAAAAAAAAAAA' }
        before { insurance.valid? }
        it { expect(insurance.errors.attribute_names).to include(:voucher_code) }
      end

      context 'start with I1KLM and lenght is 17' do
        before { insurance.voucher_code = 'I1KLMAAAAAAAAAAAA' }

        before { insurance.valid? }
        it { expect(insurance.errors.attribute_names).not_to include(:voucher_code) }
      end
    end

    context 'saving already saved insurance' do
      let!(:insurance) { create(:insurance, :azienda, :iphone, product_serial: '359209075180624') }
      before { insurance.update_column(:voucher_code, 'AAAAAA') }

      it { expect(insurance).to be_valid }
    end
  end

  describe '#can_be_deleted' do
    let(:insurance) { build_stubbed(:insurance, :azienda, :iphone, product_serial: '35920907518') }
    let(:phone_activation) { double }
    it 'not post vendita' do
      allow(insurance).to receive(:sent).and_return false
      allow(insurance).to receive(:phone_activation).and_return nil

      expect(insurance.can_be_deleted?).to be_truthy
    end

    it 'post vendita' do
      allow(insurance).to receive(:sent).and_return false
      allow(insurance).to receive(:phone_activation).and_return phone_activation
      allow(phone_activation).to receive(:easycare_insurance).and_return false
      expect(insurance.can_be_deleted?).to be_truthy
    end

    it 'not post vendita' do
      allow(insurance).to receive(:sent).and_return false
      allow(insurance).to receive(:phone_activation).and_return phone_activation
      allow(phone_activation).to receive(:easycare_insurance).and_return true
      expect(insurance.can_be_deleted?).to be_falsey
    end
  end

  context '#decrement_dealer_remaining_fido' do
    let(:dealer) { create(:dealer, assigned_fido: 200) }
    let(:customer) { create(:customer) }
    let(:service_instance) { double }

    context 'without phone_activation' do
      let(:insurance_params) do
        {
          dealer:                 dealer,
          customer:               customer,
          warehouse_id:           1,
          dealer_price:           29.9,
          kolme_price:            9.9,
          product_type:           'smartphone',
          product_brand:          'apple',
          product_model:          '5s',
          product_serial:         '355873847928125',
          product_price:          18.0,
          product_invoice_type:   'fattura',
          product_invoice_number: '12',
          product_invoiced_at:    Date.today,
          price:                  30.0
        }
      end
      let(:insurance) { Insurance.new(insurance_params) }

      it do
        expect(DealerFidoService).to receive(:new).with(insurance).and_return(service_instance)
        expect(service_instance).to receive(:update_dealer_fido)
        insurance.save!
      end
    end

    context 'with phone_activation' do
      let(:phone_activation) { stub_model(PhoneActivation) }
      let(:insurance_params) do
        {
          dealer:                 dealer,
          customer:               customer,
          warehouse_id:           1,
          dealer_price:           29.9,
          kolme_price:            9.9,
          product_type:           'smartphone',
          product_brand:          'apple',
          product_model:          '5s',
          product_serial:         '355873847928125',
          product_price:          18.0,
          product_invoice_type:   'fattura',
          product_invoice_number: '12',
          product_invoiced_at:    Date.today,
          price:                  30.0,
          phone_activation:       phone_activation
        }
      end
      let(:insurance) { Insurance.new(insurance_params) }

      it do
        expect(DealerFidoService).not_to receive(:new)
        insurance.save!
      end
    end
  end
end
