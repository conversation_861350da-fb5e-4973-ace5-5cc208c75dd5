require "rails_helper"

describe CellphoneNumbers do
  let(:phone_activation) { build_stubbed(:phone_activation, cellphone_numbers: {"cellphone_number_1"=>"3272009987", "cellphone_number_2"=>"", "cellphone_number_3"=>""}) }

  it { expect(phone_activation.cellphone_numbers_obj.numbers).to eq ["3272009987"] }
  it { expect(phone_activation.cellphone_ngpos_payload("****************")).to eq({:name=>["Aggiungi i tuoi numeri mobili"], :params=>{ :"Codice Fiscale"=>"****************", :Sim1=>"3272009987"}}) }
end
