require 'rails_helper'

describe 'PhoneActivation::DigitalSignature' do
  let(:phone_activation) { build_stubbed(:phone_activation) }

  describe '#digital_signature_email' do
    let(:phone_activation) { build_stubbed(:phone_activation) }

    it do
      # signature present
      phone_activation.signature_data = { signature_kind:        'digital',
                                          customer:              { email: '<EMAIL>' },
                                          dealer:                { email: '<EMAIL>' },
                                          payment_accountholder: { email: '<EMAIL>' } }
      expect(phone_activation.digital_signature_email).to eq '<EMAIL>'

      # paper
      phone_activation.signature_data = { signature_kind: 'paper' }
      expect(phone_activation.digital_signature_email).to be_nil

      # signature data broken
      phone_activation.signature_data = { signature_kind: 'digital' }
      expect(phone_activation.digital_signature_email).to be_nil

      # signature broken
      phone_activation.signature_data = { signature_kind: 'digital_broken' }
      expect(phone_activation.digital_signature_email).to be_nil
    end
  end

  context 'initialize' do
    let(:phone_activation) { create(:phone_activation, :with_dealer_contact, :with_minimum_related_objects) }
    let(:dealer_contact) { phone_activation.dealer_contact }
    let(:customer) { phone_activation.customer }
    let(:user) { phone_activation.user }
    let(:full_signature_data) do
      {
        'customer': { first_name: customer.first_name, last_name: customer.last_name, area_code: '+39', number: "+39#{phone_activation.digital_signature_customer_number}", email: customer.email, number_readonly: false },
        'dealer':   { first_name: dealer_contact.first_name, last_name: dealer_contact.last_name, number: user.cellphone_otp, email: dealer_contact.email, update_number_otp: false, number_readonly: false },
      }
    end

    context 'standard' do
      before { phone_activation.digital_signature_initialize! }

      it do
        expect(phone_activation.signature_data).to eq full_signature_data.with_indifferent_access
        expect(phone_activation.app_signature_realtime_path).to eq "/app/digital_signature/#{phone_activation.id}"
      end
    end

    context 'with different owner mnp' do
      let(:number_portability_recipient) do
        { 'mnp_different_owner': { first_name: mnp.owner_first_name, last_name: mnp.owner_last_name, number: '', email: '', number_readonly: false } }
      end
      let(:contract_kind) { stub_model(ContractKind, code: "abbonamento") }
      let!(:mnp) { create(:number_portability_detail, :wind_fisso, :different_owner,
                          phone_activation: phone_activation,
                          contract_kind:    contract_kind) }

      before { phone_activation.digital_signature_initialize! }

      it { expect(phone_activation.signature_data).to eq full_signature_data.merge(number_portability_recipient).with_indifferent_access }

      context 'mobile phone activation' do
        before { phone_activation.plan.service_kind.update(code: ServiceKind::MOBILE) }
        before { phone_activation.digital_signature_initialize! }

        it do
          customer            = phone_activation.signature_obj.recipients.first
          dealer              = phone_activation.signature_obj.recipients.last
          mnp_different_owner = phone_activation.signature_obj.recipients.second

          expect(customer.number_readonly).to be_truthy
          expect(dealer.number_readonly).to be_falsey
          expect(mnp_different_owner.number_readonly).to be_falsey
        end

        context 'business phone activation' do
          before { phone_activation.customer.customer_kind.update(code: "pg") }
          before { phone_activation.digital_signature_initialize! }

          it do
            customer            = phone_activation.signature_obj.recipients.first
            dealer              = phone_activation.signature_obj.recipients.last
            mnp_different_owner = phone_activation.signature_obj.recipients.second

            expect(customer.number_readonly).to be_falsey
            expect(dealer.number_readonly).to be_falsey
            expect(mnp_different_owner.number_readonly).to be_falsey
          end
        end
      end
    end

    context 'with different account holder' do
      let(:payment_method_detail_recipient) do
        { 'payment_accountholder': { first_name: phone_activation.payment_method_detail.digital_signature_first_name, last_name: phone_activation.payment_method_detail.digital_signature_last_name, number: phone_activation.payment_method_detail.different_accountholder_phone_number, email: '', number_readonly: false } }
      end
      let!(:payment_method_detail) { create(:payment_method_detail, :with_different_accountholder,
                                            phone_activation: phone_activation) }

      before { phone_activation.digital_signature_initialize! }

      it do
        expect(phone_activation.signature_data).to eq full_signature_data.merge(payment_method_detail_recipient).with_indifferent_access
      end
    end
  end

  context 'full recipient list' do
    let(:full_signature_data) do
      {
        'customer':              { first_name: 'Customer first name', last_name: 'Customer Last Name', area_code: '+39', number: '**********', email: '<EMAIL>' },
        'payment_accountholder': { first_name: 'PM first name', last_name: 'PM Last Name', number: '**********', email: '<EMAIL>' },
        'mnp_different_owner':   { first_name: 'MNP first name', last_name: 'MNP Last Name', number: '**********', email: '<EMAIL>' },
        'dealer':                { first_name: 'Dealer first name', last_name: 'Dealer Last Name', number: '**********', email: '<EMAIL>', update_number_otp: false },
      }
    end

    before { phone_activation.signature_data = full_signature_data }

    it { expect(phone_activation.signature_obj.recipients.count).to eq 4 }
  end

  context 'partial list' do
    let(:partial_signature_data) do
      {
        'customer': { first_name: 'Customer first name', last_name: 'Customer Last Name', area_code: '+39', number: '**********', email: '<EMAIL>' },
        'dealer':   { first_name: 'Dealer first name', last_name: 'Dealer Last Name', number: '**********', email: '<EMAIL>', update_number_otp: false },
      }
    end

    before { phone_activation.signature_data = partial_signature_data }

    it { expect(phone_activation.signature_obj.recipients.count).to eq 2 }
  end

  context 'completed?' do
    let(:signature_data) do
      {
        'customer':              { first_name: 'Customer first name', last_name: 'Customer Last Name', area_code: '+39', number: '**********', email: '<EMAIL>' },
        'payment_accountholder': { first_name: 'PM first name', last_name: 'PM Last Name', number: '**********', email: '<EMAIL>' },
        'mnp_different_owner':   { first_name: 'MNP first name', last_name: 'MNP Last Name', number: '**********', email: '<EMAIL>' },
        'dealer':                { first_name: 'Dealer first name', last_name: 'Dealer Last Name', number: '**********', email: '<EMAIL>', update_number_otp: false },
      }
    end

    before { phone_activation.signature_data = signature_data }

    it { expect(phone_activation.signature_obj.completed?).to be_falsey }

    context 'InProgress' do
      let(:signature_data) do
        {
          'customer':              { first_name: 'Customer first name', last_name: 'Customer Last Name', area_code: '+39', number: '**********', email: '<EMAIL>' },
          'payment_accountholder': { first_name: 'Pinco', last_name: 'pallino', number: '**********', email: '<EMAIL>' },
          'mnp_different_owner':   { first_name: 'MNP first name', last_name: 'MNP Last Name', number: '**********', email: '<EMAIL>' },
          'dealer':                { first_name: 'Dealer first name', last_name: 'Dealer Last Name', number: '**********', email: '<EMAIL>', update_number_otp: false },
          steps:                   [{ 'document_id': '', 'full_name': 'Pinco pallino', 'redirection_url': 'https://first_step', 'status': 'InProgress' }]
        }
      end

      before { phone_activation.signature_data = signature_data }

      it do
        expect(phone_activation.signature_obj.completed?).to be_falsey
        expect(phone_activation.signature_obj.current_signer.full_name).to eq phone_activation.signature_obj.payment_accountholder.full_name
      end

      context '#current_signer two recipients with same name' do
        let(:signature_data) do
          {
            'customer':              { first_name: 'Pinco', last_name: 'pallino', area_code: '+39', number: '**********', email: '<EMAIL>' },
            'payment_accountholder': { first_name: 'Pinco', last_name: 'pallino', number: '**********', email: '<EMAIL>' },
            'mnp_different_owner':   { first_name: 'MNP first name', last_name: 'MNP Last Name', number: '**********', email: '<EMAIL>' },
            'dealer':                { first_name: 'Dealer first name', last_name: 'Dealer Last Name', number: '**********', email: '<EMAIL>', update_number_otp: false },
            steps:                   [{ 'document_id': '', 'full_name': 'Pinco pallino', 'redirection_url': 'https://first_step', 'status': 'InProgress' }]
          }
        end

        before { phone_activation.signature_data = signature_data }
        it { expect(phone_activation.signature_obj.current_signer.number).to eq '**********' }

        context 'previous step completed' do
          let(:signature_data) do
            {
              'customer':              { first_name: 'Pinco', last_name: 'pallino', area_code: '+39', number: '**********', email: '<EMAIL>' },
              'payment_accountholder': { first_name: 'Pinco', last_name: 'pallino', number: '**********', email: '<EMAIL>' },
              'mnp_different_owner':   { first_name: 'MNP first name', last_name: 'MNP Last Name', number: '**********', email: '<EMAIL>' },
              'dealer':                { first_name: 'Dealer first name', last_name: 'Dealer Last Name', number: '**********', email: '<EMAIL>', update_number_otp: false },
              steps:                   [{ 'document_id': '', 'full_name': 'Pinco pallino', 'redirection_url': 'https://first_step', 'status': 'InProgress' },
                                        { 'document_id': '', 'full_name': 'Pinco pallino', 'redirection_url': 'https://first_step', 'status': 'InProgress' }]
            }
          end
          before { phone_activation.signature_data = signature_data }

          it { expect(phone_activation.signature_obj.current_signer.number).to eq '**********' }
        end
      end
    end

    context 'Completed' do
      let(:signature_data) do
        {
          'customer':              { first_name: 'Customer first name', last_name: 'Customer Last Name', area_code: '+39', number: '**********', email: '<EMAIL>' },
          'payment_accountholder': { first_name: 'PM first name', last_name: 'PM Last Name', number: '**********', email: '<EMAIL>' },
          'mnp_different_owner':   { first_name: 'MNP first name', last_name: 'MNP Last Name', number: '**********', email: '<EMAIL>' },
          'dealer':                { first_name: 'Dealer first name', last_name: 'Dealer Last Name', number: '**********', email: '<EMAIL>', update_number_otp: false },
          steps:                   [{ 'document_id': '', 'full_name': 'Pinco pallino', 'redirection_url': 'https://first_step', 'status': 'InProgress' },
                                    { 'document_id': '721fbcbf-cea7-4100-9b43-755b6060198c', 'full_name': '', 'redirection_url': nil, 'status': 'Completed' }]
        }
      end

      before { phone_activation.signature_data = signature_data }

      it { expect(phone_activation.signature_obj.completed?).to be_falsey }

      context 'only processed' do
        let(:processed_signature_data) do
          {
            'processed':             true,
            'customer':              { first_name: 'Customer first name', last_name: 'Customer Last Name', area_code: '+39', number: '**********', email: '<EMAIL>' },
            'payment_accountholder': { first_name: 'PM first name', last_name: 'PM Last Name', number: '**********', email: '<EMAIL>' },
            'mnp_different_owner':   { first_name: 'MNP first name', last_name: 'MNP Last Name', number: '**********', email: '<EMAIL>' },
            'dealer':                { first_name: 'Dealer first name', last_name: 'Dealer Last Name', number: '**********', email: '<EMAIL>', update_number_otp: false },
            steps:                   [{ 'document_id': '', 'full_name': 'Pinco pallino', 'redirection_url': 'https://first_step', 'status': 'InProgress' },
                                      { 'document_id': '721fbcbf-cea7-4100-9b43-755b6060198c', 'full_name': '', 'redirection_url': nil, 'status': 'Completed' }]
          }
        end
        before { phone_activation.signature_data = processed_signature_data }

        it { expect(phone_activation.signature_obj.completed?).to be_truthy }
      end
    end
  end

  context '#strong_authorization_phone_number' do
    let(:signature_data) do
      {
        'customer': { first_name: 'Customer first name', last_name: 'Customer Last Name', area_code: '+39', number: '**********', email: '<EMAIL>' },
        'dealer':   { first_name: 'Dealer first name', last_name: 'Dealer Last Name', number: '***********', email: '<EMAIL>', update_number_otp: false },
      }
    end

    before { phone_activation.signature_data = signature_data }

    it { expect(phone_activation.signature_obj.strong_authorization_phone_number).to eq '**********' }

    context 'with different accountholder' do
      let(:signature_data) do
        {
          'customer':              { first_name: 'Customer first name', last_name: 'Customer Last Name', area_code: '+39', number: '**********', email: '<EMAIL>' },
          'payment_accountholder': { first_name: 'PM first name', last_name: 'PM Last Name', number: '**********', email: '<EMAIL>' },
          'dealer':                { first_name: 'Dealer first name', last_name: 'Dealer Last Name', number: '***********', email: '<EMAIL>', update_number_otp: false }
        }
      end

      it { expect(phone_activation.signature_obj.strong_authorization_phone_number).to eq '**********' }
    end
  end

  context '#digital?' do
    context 'kind not present' do
      let(:signature_data) do
        {
          'customer': { first_name: 'Customer first name', last_name: 'Customer Last Name', area_code: '+39', number: '**********', email: '<EMAIL>' },
          'dealer':   { first_name: 'Dealer first name', last_name: 'Dealer Last Name', number: '***********', email: '<EMAIL>', update_number_otp: false },
        }
      end

      before { phone_activation.signature_data = signature_data }

      it { expect(phone_activation.signature_obj.digital?).to be_truthy }
    end

    context 'kind present' do
      let(:signature_data) do
        {
          'customer':       { first_name: 'Customer first name', last_name: 'Customer Last Name', area_code: '+39', number: '**********', email: '<EMAIL>' },
          'dealer':         { first_name: 'Dealer first name', last_name: 'Dealer Last Name', number: '***********', email: '<EMAIL>', update_number_otp: false },
          'signature_kind': 'digital'
        }
      end

      before { phone_activation.signature_data = signature_data }

      it { expect(phone_activation.signature_obj.digital?).to be_truthy }
    end
  end

  context '#paper?' do
    context 'kind not present' do
      let(:signature_data) do
        {
          'customer': { first_name: 'Customer first name', last_name: 'Customer Last Name', area_code: '+39', number: '**********', email: '<EMAIL>' },
          'dealer':   { first_name: 'Dealer first name', last_name: 'Dealer Last Name', number: '***********', email: '<EMAIL>', update_number_otp: false },
        }
      end

      before { phone_activation.signature_data = signature_data }

      it { expect(phone_activation.signature_obj.paper?).to be_falsey }
    end

    context 'kind present' do
      let(:signature_data) do
        {
          'customer':       { first_name: 'Customer first name', last_name: 'Customer Last Name', area_code: '+39', number: '**********', email: '<EMAIL>' },
          'dealer':         { first_name: 'Dealer first name', last_name: 'Dealer Last Name', number: '***********', email: '<EMAIL>', update_number_otp: false },
          'signature_kind': 'paper'
        }
      end

      before { phone_activation.signature_data = signature_data }

      it { expect(phone_activation.signature_obj.paper?).to be_truthy }
    end
  end

  context '#document_id, #log_id' do
    let(:signature_data) do
      {
        'customer':              { first_name: 'Customer first name', last_name: 'Customer Last Name', area_code: '+39', number: '**********', email: '<EMAIL>' },
        'payment_accountholder': { first_name: 'PM first name', last_name: 'PM Last Name', number: '**********', email: '<EMAIL>' },
        'mnp_different_owner':   { first_name: 'MNP first name', last_name: 'MNP Last Name', number: '**********', email: '<EMAIL>' },
        'dealer':                { first_name: 'Dealer first name', last_name: 'Dealer Last Name', number: '**********', email: '<EMAIL>', update_number_otp: false },
        steps:                   [{ 'document_id': '', 'full_name': 'Pinco pallino', 'redirection_url': 'https://first_step', 'status': 'InProgress' },
                                  { 'document_id': '721fbcbf-cea7-4100-9b43-755b6060198c', 'log_id': '0a627142-5a11-4799-b39b-04be943d58b6', 'full_name': '', 'redirection_url': nil, 'status': 'Completed' }]
      }
    end

    before { phone_activation.signature_data = signature_data }

    it do
      expect(phone_activation.signature_obj.document_id).to eq '721fbcbf-cea7-4100-9b43-755b6060198c'
      expect(phone_activation.signature_obj.log_id).to eq '0a627142-5a11-4799-b39b-04be943d58b6'
    end
  end

  context '#download_attempts' do
    context 'attempts not present' do
      let(:signature_data) do
        {
          'customer':              { first_name: 'Customer first name', last_name: 'Customer Last Name', area_code: '+39', number: '**********', email: '<EMAIL>' },
          'payment_accountholder': { first_name: 'PM first name', last_name: 'PM Last Name', number: '**********', email: '<EMAIL>' },
          'mnp_different_owner':   { first_name: 'MNP first name', last_name: 'MNP Last Name', number: '**********', email: '<EMAIL>' },
          'dealer':                { first_name: 'Dealer first name', last_name: 'Dealer Last Name', number: '**********', email: '<EMAIL>', update_number_otp: false },
          steps:                   [{ 'document_id': '', 'full_name': 'Pinco pallino', 'redirection_url': 'https://first_step', 'status': 'InProgress' },
                                    { 'document_id': '721fbcbf-cea7-4100-9b43-755b6060198c', 'log_id': '0a627142-5a11-4799-b39b-04be943d58b6', 'full_name': '', 'redirection_url': nil, 'status': 'Completed' }]
        }
      end
      before { phone_activation.signature_data = signature_data }

      it { expect(phone_activation.signature_obj.download_attempts).to be_zero }
    end

    context 'attempts present' do
      let(:signature_data) do
        {
          'customer':              { first_name: 'Customer first name', last_name: 'Customer Last Name', area_code: '+39', number: '**********', email: '<EMAIL>' },
          'payment_accountholder': { first_name: 'PM first name', last_name: 'PM Last Name', number: '**********', email: '<EMAIL>' },
          'mnp_different_owner':   { first_name: 'MNP first name', last_name: 'MNP Last Name', number: '**********', email: '<EMAIL>' },
          'dealer':                { first_name: 'Dealer first name', last_name: 'Dealer Last Name', number: '**********', email: '<EMAIL>', update_number_otp: false },
          steps:                   [{ 'document_id': '', 'full_name': 'Pinco pallino', 'redirection_url': 'https://first_step', 'status': 'InProgress' },
                                    { 'document_id': '721fbcbf-cea7-4100-9b43-755b6060198c', 'log_id': '0a627142-5a11-4799-b39b-04be943d58b6', 'full_name': '', 'redirection_url': nil, 'status': 'Completed' }],
          download_attempts:       1
        }
      end

      before { phone_activation.signature_data = signature_data }

      it { expect(phone_activation.signature_obj.download_attempts).to eq 1 }
    end
  end

  context '#increment_download_attempts' do
    let(:phone_activation) { create(:phone_activation, :fissa_w3_no_product, signature_data: signature_data) }
    let(:signature_data) do
      {
        'customer':              { first_name: 'Customer first name', last_name: 'Customer Last Name', area_code: '+39', number: '**********', email: '<EMAIL>' },
        'payment_accountholder': { first_name: 'PM first name', last_name: 'PM Last Name', number: '**********', email: '<EMAIL>' },
        'mnp_different_owner':   { first_name: 'MNP first name', last_name: 'MNP Last Name', number: '**********', email: '<EMAIL>' },
        'dealer':                { first_name: 'Dealer first name', last_name: 'Dealer Last Name', number: '**********', email: '<EMAIL>', update_number_otp: false },
        steps:                   [{ 'document_id': '', 'full_name': 'Pinco pallino', 'redirection_url': 'https://first_step', 'status': 'InProgress' },
                                  { 'document_id': '721fbcbf-cea7-4100-9b43-755b6060198c', 'log_id': '0a627142-5a11-4799-b39b-04be943d58b6', 'full_name': '', 'redirection_url': nil, 'status': 'Completed' }]
      }
    end
    let(:phone_activation) { create(:phone_activation, :fissa_w3_no_product, signature_data: signature_data) }

    it do
      phone_activation.increment_download_attempts
      expect(phone_activation.reload.signature_obj.download_attempts).to eq 1
    end
  end

  describe '#broken_data_signature?' do
    let(:phone_activation) { build_stubbed(:phone_activation) }

    it { expect(phone_activation.broken_data_signature?).to be_truthy }
  end
end
