require 'rails_helper'

describe DealerSap do
  describe '#sap_json' do
    let(:dealer) { build_stubbed(:dealer, :with_sap_properties) }
    let(:payload) do
      {
        :BirthDate => nil,
        :BusinessPartner => "K#{dealer.id}",
        :BusinessPartnerCategory => '2',
        :BusinessPartnerFullName => '<PERSON>',
        :BusinessPartnerGrouping => 'BPAB',
        :BusinessPartnerName => '<PERSON>',
        :BusinessPartnerType => nil,
        :CorrespondenceLanguage => nil,
        :FirstName => nil,
        :IsFemale => nil,
        :IsMale => nil,
        :GenderCodeName => nil,
        :LastName => nil,
        :OrganizationBPName1 => dealer.ragione_sociale,
        :SearchTerm1 => nil,
        :BusinessPartnerBirthplaceName => nil,
        :TradingPartner => nil,
        :to_BusinessPartnerAddress => {
          :results => [{
            :BusinessPartner => "K#{dealer.id}",
            :AddressTimeZone => 'CET',
            :CityName => 'Milano',
            :Country => 'IT',
            :FullName => '<PERSON>',
            :HouseNumber => dealer.billing_address_number,
            :Language => 'IT',
            :POBoxIsWithoutNumber => false,
            :PostalCode => dealer.billing_address_postalcode,
            :Region => 'MI',
            :StreetName => dealer.billing_address_via,
            :to_EmailAddress => {
              :results => [{
                :OrdinalNumber => '1',
                :IsDefaultEmailAddress => true,
                :EmailAddress => '<EMAIL>',
                :AddressCommunicationRemarkText => ''
              }, {
                :OrdinalNumber => '2',
                :IsDefaultEmailAddress => false,
                :EmailAddress => '<EMAIL>',
                :AddressCommunicationRemarkText => 'PEC'
              }]
            },
            :to_PhoneNumber => {
              :results => [{
                :OrdinalNumber => '1',
                :IsDefaultPhoneNumber => true,
                :PhoneNumber => '0123456789'
              }]
            }
          }]
        },
        :to_BusinessPartnerRole => {
          :results => [{
            :BusinessPartner => "K#{dealer.id}",
            :BusinessPartnerRole => 'FLCU00'
          }, {
            :BusinessPartner => "K#{dealer.id}",
            :BusinessPartnerRole => 'FLCU01'
          }, {
            :BusinessPartner => "K#{dealer.id}",
            :BusinessPartnerRole => 'FLVN00'
          }, {
            :BusinessPartner => "K#{dealer.id}",
            :BusinessPartnerRole => 'FLVN01'
          }]
        },
        :to_BusinessPartnerTax => {
          :results => [{
            :BusinessPartner => "K#{dealer.id}",
            :BPTaxType => 'IT0',
            :BPTaxNumber => "IT#{dealer.partita_iva}"
          }, {
            :BusinessPartner => "K#{dealer.id}",
            :BPTaxType => 'IT1',
            :BPTaxNumber => dealer.codice_fiscale
          }, {
            :BusinessPartner => "K#{dealer.id}",
            :BPTaxType => 'IT4',
            :BPTaxNumber => dealer.sdi_forwarder_code
          }]
        },
        :to_BusinessPartnerBank => {
          :results => [{
            :BankAccount => '************',
            :BankControlKey => 'A',
            :BankCountryKey => 'IT',
#            :BankIdentification => '',
#            :BankName => '',
            :BankNumber => '**********',
            :BusinessPartner => "K#{dealer.id}",
            :CollectionAuthInd => false,
            :IBAN => '***************************',
#            :SWIFTCode => ''
          }]
        },
        :to_Customer => {
          :Customer => "K#{dealer.id}",
          :CustomerFullName => 'Mario Rossi',
          :BPCustomerFullName => 'Mario Rossi',
          :CustomerName => 'Mario Rossi',
          :BPCustomerName => 'Mario Rossi',
          :TaxNumber1 => nil,
          :TaxNumber4 => dealer.sdi_forwarder_code,
          :PostingIsBlocked => false,
          :VATRegistration => "IT#{dealer.partita_iva}",
          :DeletionIndicator => false,
          :to_CustomerCompany => {
            :results => [{
              :Customer => "K#{dealer.id}",
              :CompanyCode => '2310',
              :CustomerSupplierClearingIsUsed => false,
              :HouseBank => dealer.sap_internal_bank_institute.code,
              :LayoutSortingRule => '002',
              :PaymentMethodsList => dealer.sap_customer_payment_kind.code,
              :PaymentTerms => dealer.sap_customer_payment_term.code,
              :PaytAdviceIsSentbyEDI => false,
              :PhysicalInventoryBlockInd => false,
              :ReconciliationAccount => '********',
              :RecordPaymentHistoryIndicator => false,
              :DeletionIndicator => false,
              :CashPlanningGroup => 'E2',
              :to_CustomerDunning => {
                :results => [{
                  :Customer => "K#{dealer.id}",
                  :CompanyCode => '2310',
                  :DunningProcedure => 'K001',
                  :DunningClerk => ''
                }]
              }
            }]
          },
          :to_CustomerSalesArea => {
            :results => [{
              :Customer => "K#{dealer.id}",
              :SalesOrganization => '2310',
              :DistributionChannel => '10',
              :Division => '01',
              :CompleteDeliveryIsDefined => false,
              :Currency => 'EUR',
              :CustIsRlvtForSettlmtMgmt => false,
              :CustomerAccountAssignmentGroup => 'Z1',
              :CustomerGroup => 'Z1',
              :CustomerIsRebateRelevant => false,
              :CustomerPaymentTerms => 'SEP3',
              :CustomerPricingProcedure => '01',
              :IncotermsClassification => 'EXW',
              :IncotermsLocation1 => nil,
              :DeletionIndicator => false,
              :IncotermsTransferLocation => '.',
              :InspSbstHasNoTimeOrQuantity => false,
              :ItemOrderProbabilityInPercent => '100',
              :ManualInvoiceMaintIsRelevant => false,
              :OrderCombinationIsAllowed => true,
              :ShippingCondition => '01',
              :SlsDocIsRlvtForProofOfDeliv => false,
              :SlsUnlmtdOvrdelivIsAllwd => false,
              :SupplyingPlant => '2310',
              :to_SalesAreaTax => {
                :results => [{
                  :Customer => "K#{dealer.id}",
                  :SalesOrganization => '2310',
                  :DistributionChannel => '10',
                  :Division => '01',
                  :DepartureCountry => 'IT',
                  :CustomerTaxCategory => 'LCIT',
                  :CustomerTaxClassification => '1'
                }, {
                  :Customer => "K#{dealer.id}",
                  :SalesOrganization => '2310',
                  :DistributionChannel => '10',
                  :Division => '01',
                  :DepartureCountry => 'IT',
                  :CustomerTaxCategory => 'MTX1',
                  :CustomerTaxClassification => '0'
                }, {
                  :Customer => "K#{dealer.id}",
                  :SalesOrganization => '2310',
                  :DistributionChannel => '10',
                  :Division => '01',
                  :DepartureCountry => 'IT',
                  :CustomerTaxCategory => 'TTX1',
                  :CustomerTaxClassification => '1'
                }]
              }
            }]
          }
        },
        :to_Supplier => {
          :Supplier => "K#{dealer.id}",
          :PaymentIsBlockedForSupplier => false,
          :PostingIsBlocked => false,
          :PurchasingIsBlocked => false,
          :SupplierName => 'Mario Rossi',
          :VATRegistration => "IT#{dealer.partita_iva}",
          :DeletionIndicator => false,
          :TaxNumber1 => nil,
          :TaxNumber4 => dealer.sdi_forwarder_code,
          :BR_TaxIsSplit => false,
          :to_SupplierCompany => {
            :results => [{
              :Supplier => "K#{dealer.id}",
              :CompanyCode => '2310',
              :SupplierIsBlockedForPosting => false,
              :PaymentMethodsList => 'T',
              :PaymentTerms => 'FM00',
              :ClearCustomerSupplier => false,
              :IsToBeLocallyProcessed => nil,
              :ItemIsToBePaidSeparately => nil,
              :PaymentIsToBeSentByEDI => false,
              :HouseBank => dealer.sap_internal_bank_institute.code,
              :Currency => 'EUR',
              :ReconciliationAccount => '********',
              :LayoutSortingRule => '009',
              :DeletionIndicator => false,
              :CashPlanningGroup => 'A1',
              :IsToBeCheckedForDuplicates => true,
              :to_SupplierWithHoldingTax => {
                :results => [{
                  :Supplier => "K#{dealer.id}",
                  :CompanyCode => '2310',
                  :WithholdingTaxType => '1D',
                  :IsWithholdingTaxSubject => true,
                  :WithholdingTaxCode => '1Y'
                }]
              }
            }]
          },
          :to_SupplierPurchasingOrg => {
            :results => [{
              :Supplier => "K#{dealer.id}",
              :PurchasingOrganization => '2310',
              :AutomaticEvaluatedRcptSettlmt => false,
              :DeletionIndicator => false,
              :EvaldReceiptSettlementIsActive => false,
              :InvoiceIsGoodsReceiptBased => true,
              :InvoiceIsMMServiceEntryBased => false,
              :PaymentTerms => 'FM00',
              :PurOrdAutoGenerationIsAllowed => false,
              :PurchaseOrderCurrency => 'EUR',
              :PurchasingIsBlockedForSupplier => false,
              :SuplrDiscountInKindIsGranted => false,
              :SuplrInvcRevalIsAllowed => false,
              :SuplrIsRlvtForSettlmtMgmt => false,
              :SuplrPurgOrgIsRlvtForPriceDetn => false,
              :SupplierIsReturnsSupplier => false,
              :IsOrderAcknRqd => false,
              :to_PartnerFunction => {
                :results => [{
                  :Supplier => "K#{dealer.id}",
                  :PurchasingOrganization => '2310',
                  :PartnerFunction => 'OA',
                  :DefaultPartner => false
                }, {
                  :Supplier => "K#{dealer.id}",
                  :PurchasingOrganization => '2310',
                  :PartnerFunction => 'VN',
                  :DefaultPartner => false
                }, {
                  :Supplier => "K#{dealer.id}",
                  :PurchasingOrganization => '2310',
                  :PartnerFunction => 'PI',
                  :DefaultPartner => false
                }]
              }
            }]
          }
        }
      }
    end

    subject { dealer.sap_json }

    it { expect(subject).to eq(payload) }

    context 'to_BusinessPartnerTax for foreign_dealer' do
      before do
        dealer.country = build_stubbed(:country, :uk)
        dealer.sap_business_partner_taxes = [{ 'BPTaxType' => 'TR1', 'BPTaxNumber' => 'TR010101' }]
      end

      it do
        expect(subject[:to_BusinessPartnerTax][:results].map { |r| r[:BPTaxType] }).to eq ['TR1']
        expect(subject[:to_BusinessPartnerTax][:results].map { |r| r[:BPTaxNumber] }).to eq ['TR010101']
      end
    end

    context 'ditta_individuale' do
      before { dealer.assign_attributes(ditta_individuale: true, codice_fiscale: '****************') }

      it do
        expect(subject[:BirthDate]).to eq '/Date(504921600000)/'
        expect(subject[:to_BusinessPartnerTax][:results].map { |r| r[:BPTaxType] }).to eq ['IT0', 'IT1', 'IT4']
        expect(subject[:to_BusinessPartnerTax][:results].map { |r| r[:BPTaxNumber] }).to eq ["IT#{dealer.partita_iva}", '****************', 'AFDER45']
      end

      context 'business_partner_birthplace_name' do
        let!(:city) { create(:city, :milano) }

        it { expect(subject[:BusinessPartnerBirthplaceName]).to eq 'MI Milano' }
      end
    end

    context 'iban' do
      let!(:dealer) { create(:dealer, :with_sap_properties, iban: nil, sap_ids: { dummy: true }) }

      context 'without iban' do
        it { expect(subject[:to_BusinessPartnerBank]).to be_nil }
      end

      context 'when dealer will be updated with iban' do
        before { dealer.iban = '***************************' }

        it do
          expect(dealer.sap_json[:to_BusinessPartnerBank][:results][0].keys).not_to include(:BankIdentification)

          dealer.sap_ids['to_BusinessPartnerBank_BankIdentification'] = '0001'
          expect(dealer.sap_json[:to_BusinessPartnerBank][:results][0][:BankIdentification]).to eq '0001'
        end
      end
    end

    context 'kolme_agent dealer kind' do
      before { dealer.kind = 'kolme_agent' }

      it do
        expect(subject[:BusinessPartnerType]).to eq('9003')
        expect(subject[:to_Supplier][:to_SupplierCompany][:results].first[:to_SupplierWithHoldingTax]).to eq(results: [
          { :CompanyCode => "2310", :IsWithholdingTaxSubject => true, :Supplier => "K#{dealer.id}", :WithholdingTaxCode => "1Y", :WithholdingTaxType => "1D" },
          { :CompanyCode => "2310", :IsWithholdingTaxSubject => true, :Supplier => "K#{dealer.id}", :WithholdingTaxCode => "3A", :WithholdingTaxType => "1A" },
          { :CompanyCode => "2310", :IsWithholdingTaxSubject => true, :Supplier => "K#{dealer.id}", :WithholdingTaxCode => "3A", :WithholdingTaxType => "4A" }
        ])
      end

      context 'ditta_individuale' do
        let(:dealer) { build_stubbed(:dealer, :ditta_individuale, :with_sap_properties, codice_fiscale: '****************') }

        it do
          expect(subject[:to_Supplier][:to_SupplierCompany][:results].first[:to_SupplierWithHoldingTax]).to eq(results: [
            { :CompanyCode => "2310", :IsWithholdingTaxSubject => true, :Supplier => "K#{dealer.id}", :WithholdingTaxCode => "1Y", :WithholdingTaxType => "1D" },
            { :CompanyCode => "2310", :IsWithholdingTaxSubject => true, :Supplier => "K#{dealer.id}", :WithholdingTaxCode => "1K", :WithholdingTaxType => "1A" },
            { :CompanyCode => "2310", :IsWithholdingTaxSubject => true, :Supplier => "K#{dealer.id}", :WithholdingTaxCode => "1K", :WithholdingTaxType => "4A" }
          ])
        end
      end
    end

    context 'ReconciliationAccount' do
      it do
        dealer.id = 3066
        expect(subject[:to_Customer][:to_CustomerCompany][:results].first[:ReconciliationAccount]).to eq '********'
        expect(subject[:to_Supplier][:to_SupplierCompany][:results].first[:ReconciliationAccount]).to eq '********'
      end

      it do
        dealer.id = 6822
        expect(subject[:to_Customer][:to_CustomerCompany][:results].first[:ReconciliationAccount]).to eq '********'
        expect(subject[:to_Supplier][:to_SupplierCompany][:results].first[:ReconciliationAccount]).to eq '********'
      end

      it do
        dealer.country = build_stubbed(:country, :uk)
        dealer.sap_business_partner_taxes = [{ 'BPTaxType' => 'TR1', 'BPTaxNumber' => 'TR010101' }]
        expect(subject[:to_Customer][:to_CustomerCompany][:results].first[:ReconciliationAccount]).to eq '********'
        expect(subject[:to_Supplier][:to_SupplierCompany][:results].first[:ReconciliationAccount]).to eq '********'
      end
    end

    context 'CashPlanningGroup' do
      it do
        dealer.id = 3066
        expect(subject[:to_Customer][:to_CustomerCompany][:results].first[:CashPlanningGroup]).to eq 'E6'
        expect(subject[:to_Supplier][:to_SupplierCompany][:results].first[:CashPlanningGroup]).to eq 'A4'
      end

      it do
        dealer.id = 6822
        expect(subject[:to_Customer][:to_CustomerCompany][:results].first[:CashPlanningGroup]).to eq 'E6'
        expect(subject[:to_Supplier][:to_SupplierCompany][:results].first[:CashPlanningGroup]).to eq 'A4'
      end

      it do
        dealer.country = build_stubbed(:country, :uk)
        dealer.sap_business_partner_taxes = [{ 'BPTaxType' => 'TR1', 'BPTaxNumber' => 'TR010101' }]
        expect(subject[:to_Customer][:to_CustomerCompany][:results].first[:CashPlanningGroup]).to eq 'E3'
        expect(subject[:to_Supplier][:to_SupplierCompany][:results].first[:CashPlanningGroup]).to eq 'A2'
      end
    end

    context 'CustomerAccountAssignmentGroup and CustomerGroup' do
      let!(:sap_supplier_dealer_ids_app_setting) { create(:application_setting, :sap_supplier_dealer_ids, value: "1,#{dealer.id}") }

      it do
        expect(subject[:to_Customer][:to_CustomerSalesArea][:results].first[:CustomerAccountAssignmentGroup]).to eq 'Z2'
        expect(subject[:to_Customer][:to_CustomerSalesArea][:results].first[:CustomerGroup]).to eq 'Z2'
      end
    end

    context 'CustomerTaxClassification' do
      it 'san marino' do
        dealer.country.code = 'SM'
        dealer.sap_business_partner_taxes = [{ 'BPTaxType' => 'TR1', 'BPTaxNumber' => 'TR010101' }]
        expect(subject[:to_Customer][:to_CustomerSalesArea][:results].first[:to_SalesAreaTax][:results].third[:CustomerTaxClassification]).to eq 'X'
      end

      it 'Abilitato Reverse Charge 17RC' do
        dealer.reverse_charge_enabled = true
        expect(subject[:to_Customer][:to_CustomerSalesArea][:results].first[:to_SalesAreaTax][:results].third[:CustomerTaxClassification]).to eq '3'
      end

      it 'CEE' do
        dealer.country = build_stubbed(:country, :ro)
        dealer.sap_business_partner_taxes = [{ 'BPTaxType' => 'TR1', 'BPTaxNumber' => 'TR010101' }]
        expect(subject[:to_Customer][:to_CustomerSalesArea][:results].first[:to_SalesAreaTax][:results].third[:CustomerTaxClassification]).to eq 'Z'
      end

      it 'EXTRACEE' do
        dealer.country = build_stubbed(:country, :uk)
        dealer.sap_business_partner_taxes = [{ 'BPTaxType' => 'TR1', 'BPTaxNumber' => 'TR010101' }]
        expect(subject[:to_Customer][:to_CustomerSalesArea][:results].first[:to_SalesAreaTax][:results].third[:CustomerTaxClassification]).to eq 'Y'
      end
    end
  end

  describe '#sap_request_kind' do
    let(:dealer) { build_stubbed(:dealer) }

    it do
      expect(dealer.sap_request_kind).to eq 'create'

      dealer.sap_ids = { 'to_PartnerFunction_PartnerFunctions' => ['DUMMY'] }
      expect(dealer.sap_request_kind).to eq 'update'

      dealer.kind = 'kolme_agent'
      expect(dealer.sap_request_kind).to eq DealerSap::ADD_ES_ROLE_API_CALL

      dealer.sap_ids = { 'to_PartnerFunction_PartnerFunctions' => ['DUMMY', 'ES'] }
      expect(dealer.sap_request_kind).to eq 'update'
    end
  end

  describe '#export_to_sap' do
    let!(:enabled_sap_export_paths_app_setting) { create(:application_setting, :enabled_sap_export_paths, value: 'IFI206_BusinessPartner') }
    let(:dealer) { build(:dealer, :with_sap_properties) }
    let(:exporter_job) { double }

    it do
      expect(Jobs::Sap::DealerExporter).to receive(:new).and_return exporter_job
      expect(Delayed::Job).to receive(:enqueue).with(exporter_job, queue: 'sap_export')

      dealer.save
    end

    context 'disabled flag' do
      it 'does not export for disabled dealer' do
        expect(dealer).not_to receive(:export_to_sap)

        dealer.disabled = true
        dealer.save
      end

      context 'export when already exported disabled dealer will be enabled' do
        let!(:dealer) { create(:dealer, :with_sap_properties, disabled: true, sap_ids: {}) }

        it do
          expect(dealer).to receive(:export_to_sap)

          dealer.update(disabled: false)
        end
      end
    end

    it 'does not export for operator_tls dealer kid' do
      expect(dealer).not_to receive(:export_to_sap)

      dealer.kind = 'operator_tls'
      dealer.save
    end

    context 'dealer kind', vcr: true do
      context 'standard dealer' do
        let!(:dealer) { create(:dealer, :with_sap_properties, partita_iva: '***********', codice_fiscale: '***********') }
        let(:sap_ids) do
          {
            'to_BusinessPartnerAddress_AddressID' => "93704",
            'to_BusinessPartnerBank_BankIdentification' => "0001",
            'to_EmailAddress_1_AddressID' => "93704",
            'to_EmailAddress_1_Person' => "",
            'to_EmailAddress_2_AddressID' => "93704",
            'to_EmailAddress_2_Person' => "",
            'to_PhoneNumber_AddressID' => "93704",
            'to_PhoneNumber_Person' => "",
            'to_PartnerFunction_PartnerFunctions' => ["SP", "BP", "PY", "SH"]
          }
        end

        it 'run create only' do
          expect(SapApiCall.count).to eq 1
          expect(SapApiCall.first.http_method).to eq 'post'

          expect(dealer.reload.sap_ids).to eq(sap_ids)
          expect(dealer.audits.last.audited_changes).to eq({ 'sap_ids' => [nil, sap_ids] })
        end
      end

      context 'kolme_agent dealer' do
        let!(:dealer) { create(:dealer, :with_sap_properties, kind: 'kolme_agent', partita_iva: '***********', codice_fiscale: '***********') }

        it 'run create and add_es_role' do
          expect(SapApiCall.count).to eq 2
          expect(SapApiCall.first.http_method).to eq 'post'
          expect(SapApiCall.second.kind).to eq 'add_es_role'
        end
      end
    end
  end
end
