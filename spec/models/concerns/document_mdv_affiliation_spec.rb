require 'rails_helper'

describe DocumentMdvAffiliation do
  before do
    DocumentUploader.storage           = :file
    DocumentUploader.enable_processing = false

    Audited.auditing_enabled = true
  end
  after { Audited.auditing_enabled = false }

  let(:dealer) { create(:dealer, :with_warehouse) }
  let!(:dealer_contact) { create(:dealer_contact, dealer: dealer, primary_contact: true) }
  let(:identity_doc) { build(:document, :mdv_affiliation_document_file, documentable: dealer, content: 'identity_document') }
  let(:company_report_doc) { build(:document, :mdv_affiliation_document_file, documentable: dealer, content: 'company_registration_report') }
  let(:contract_doc) { build(:document, :mdv_affiliation_document_file, documentable: dealer, content: 'contract') }
  let(:contract_signature_log_doc) { build(:document, :mdv_affiliation_document_file, documentable: dealer, content: 'contract_signature_log') }
  let(:phone_activation_document) { build(:document, :fiscal_code_file, documentable: build_stubbed(:phone_activation)) }
  let!(:access_flag_activations) { create(:access_flag, :access_activations) }
  let!(:access_orders) { create(:access_flag, :access_orders) }
  let!(:access_recharges) { create(:access_flag, :access_recharges) }

  let!(:alert_mdv_affiliation_document_validated) { create(:alert_message, :mdv_affiliation_document_validated) }
  let!(:alert_mdv_affiliation_rejected_document) { create(:alert_message, :mdv_affiliation_rejected_document) }
  let!(:alert_mdv_affiliation_started) { create(:alert_message, :mdv_affiliation_started) }
  let!(:alert_mdv_affiliation_awaiting_validation) { create(:alert_message, :mdv_affiliation_awaiting_validation) }
  let!(:alert_mdv_affiliation_started_with_digital_signature) { create(:alert_message, :mdv_affiliation_started_with_digital_signature) }

  context 'dealer created as not md_very and updated after' do
    before { dealer.update(kind: 'md_very') }

    it do
      expect(dealer.mdv_affiliation_status).to eq 'awaiting_digital_signature'
      expect(dealer.mdv_affiliation_updated_at).not_to be_nil
      expect(Alert.visible.map(&:alert_message_id)).to match_array [alert_mdv_affiliation_started_with_digital_signature.id]

      #saving contract_doc
      contract_doc.save
      expect(contract_doc.status).to eq Document::ACCEPTED
      expect(dealer.reload.mdv_affiliation_status).to eq 'awaiting_digital_signature'

      #saving contract_signature_log_doc
      contract_signature_log_doc.save
      expect(contract_signature_log_doc.status).to eq Document::ACCEPTED
      expect(dealer.reload.mdv_affiliation_status).to eq 'affiliated'
      expect(dealer.primary_contact.access_flags.map(&:name)).to match_array ['Attivazioni', 'Ordini', 'Ricariche']
      expect(Alert.visible.map(&:alert_message_id)).to be_empty
    end
  end

  context 'dealer created as md_very' do
    let(:dealer) { create(:dealer, kind: 'md_very') }
    let!(:warehouse) { create :warehouse, dealer: dealer, name: Faker::Lorem.characters(number: 10) }
    let!(:first_alert) { create(:alert, alert_message: alert_mdv_affiliation_started, dealer: dealer.reload, warehouse: dealer.main_warehouse) }

    context 'callbacks' do
      it do
        expect(identity_doc).to receive(:process_mdv_affiliation_document)
        expect(identity_doc).to receive(:process_new_mdv_affiliation_document)
        expect(identity_doc).to receive(:update_mdv_affiliation_status)
        expect(identity_doc).not_to receive(:accept_mdv_affiliation_signature_documents)
        identity_doc.save

        expect(contract_doc).not_to receive(:process_mdv_affiliation_document)
        expect(contract_doc).not_to receive(:process_new_mdv_affiliation_document)
        expect(contract_doc).to receive(:update_mdv_affiliation_status)
        expect(contract_doc).to receive(:accept_mdv_affiliation_signature_documents)
        contract_doc.save

        expect(phone_activation_document).not_to receive(:process_mdv_affiliation_document)
        expect(phone_activation_document).not_to receive(:process_new_mdv_affiliation_document)
        expect(phone_activation_document).not_to receive(:update_mdv_affiliation_status)
        expect(phone_activation_document).not_to receive(:accept_mdv_affiliation_signature_documents)
        phone_activation_document.save
      end
    end

    it do
      expect(dealer.mdv_affiliation_status).to eq 'awaiting_documents'
      expect(dealer.mdv_affiliation_updated_at).not_to be_nil
      expect(Alert.visible.map(&:alert_message_id)).to match_array [alert_mdv_affiliation_started.id]

      #upload identity_doc
      Timecop.freeze(DateTime.new(2023, 1, 31, 10, 0))
      identity_doc.save
      expect(identity_doc.status).to be_nil
      expect(dealer.reload.mdv_affiliation_status).to eq 'awaiting_validation_documents'
      expect(dealer.mdv_affiliation_updated_at).to eq DateTime.new(2023, 1, 31, 10, 0)
      expect(Alert.visible.map(&:alert_message_id)).to match_array [alert_mdv_affiliation_started.id, alert_mdv_affiliation_awaiting_validation.id]

      #upload company_report_doc
      Timecop.freeze(DateTime.new(2023, 1, 31, 10, 1))
      company_report_doc.save
      expect(company_report_doc.status).to be_nil
      expect(dealer.reload.mdv_affiliation_status).to eq 'awaiting_validation_documents'
      expect(dealer.mdv_affiliation_updated_at).to eq DateTime.new(2023, 1, 31, 10, 1)
      expect(Alert.visible.map(&:alert_message_id)).to match_array [alert_mdv_affiliation_awaiting_validation.id]

      #accept identity_doc
      Timecop.freeze(DateTime.new(2023, 1, 31, 10, 2))
      identity_doc.update(status: Document::ACCEPTED)
      expect(dealer.reload.mdv_affiliation_status).to eq 'awaiting_validation_documents'
      expect(dealer.mdv_affiliation_updated_at).to eq DateTime.new(2023, 1, 31, 10, 2)
      expect(Alert.visible.map(&:alert_message_id)).to match_array []

      #reject company_report_doc
      Timecop.freeze(DateTime.new(2023, 1, 31, 10, 3))
      company_report_doc.update(status: Document::REJECTED, notes: 'notes')
      expect(dealer.reload.mdv_affiliation_status).to eq 'awaiting_documents'
      expect(dealer.mdv_affiliation_updated_at).to eq DateTime.new(2023, 1, 31, 10, 3)
      expect(company_report_doc.related_file.present?).to be_falsey
      expect(Alert.where(document_id: company_report_doc.id).count).to eq(1)

      #upload company_report_doc
      Timecop.freeze(DateTime.new(2023, 1, 31, 10, 4))
      new_company_report_doc = create(:document, :mdv_affiliation_document_file, documentable: dealer, content: 'company_registration_report')
      expect(dealer.reload.mdv_affiliation_status).to eq 'awaiting_validation_documents'
      expect(dealer.mdv_affiliation_updated_at).to eq DateTime.new(2023, 1, 31, 10, 4)
      expect(Document.find_by_id(company_report_doc.id)).to be_nil
      expect(Alert.visible.map(&:alert_message_id)).to match_array [alert_mdv_affiliation_awaiting_validation.id]
      expect(Alert.where(document_id: company_report_doc.id).first.archived).to be_truthy

      #accept new_company_report_doc
      Timecop.freeze(DateTime.new(2023, 1, 31, 10, 5))
      new_company_report_doc.update(status: Document::ACCEPTED)
      expect(dealer.reload.mdv_affiliation_status).to eq 'awaiting_digital_signature'
      expect(dealer.mdv_affiliation_updated_at).to eq DateTime.new(2023, 1, 31, 10, 5)
      expect(Alert.visible.map(&:alert_message_id)).to match_array [alert_mdv_affiliation_document_validated.id]

      #saving contract_doc
      Timecop.freeze(DateTime.new(2023, 1, 31, 10, 6))
      contract_doc.save
      expect(contract_doc.status).to eq Document::ACCEPTED
      expect(dealer.reload.mdv_affiliation_status).to eq 'awaiting_digital_signature'

      #saving contract_signature_log_doc
      contract_signature_log_doc.save
      expect(contract_signature_log_doc.status).to eq Document::ACCEPTED
      expect(dealer.reload.mdv_affiliation_status).to eq 'affiliated'
      expect(dealer.mdv_affiliation_updated_at).to eq DateTime.new(2023, 1, 31, 10, 6)
      expect(dealer.primary_contact.access_flags.map(&:name)).to match_array ['Attivazioni', 'Ordini', 'Ricariche']
      expect(Alert.visible.map(&:alert_message_id)).to be_empty
    end

    it 'awaiting validation alert created only on upload' do
      identity_doc.save
      company_report_doc.save
      dealer.alerts.where(alert_message_id: alert_mdv_affiliation_awaiting_validation.id).each(&:archive!)

      identity_doc.update(status: Document::REJECTED, notes: 'notes')
      expect(Alert.visible.map(&:alert_message_id)).not_to include alert_mdv_affiliation_awaiting_validation.id
    end
  end
end
