require 'rails_helper'

describe Abilities::UserAbility do
  let(:user) { build(:user, :role_user) }
  let(:internal_user_detail) { build_stubbed(:internal_user_detail) }

  subject { AbilityFactory.build_ability_for(user) }

  it { should not_have_abilities([:see_competence_field], Product) }
  it do
    should have_abilities(
      %i[autocomplete_non_bundle_product_name financeable show non_sim_for_activation sim_for_activation
         autocomplete_product_name image_for_activation image_url_for_imei_reservation see_images_button
         see_all_prices reload_warehouse_change add_to_cart store_index change_color_to_selected_product],
      Product
    )
  end
  it { should_not have_abilities([:index, :create, :destroy], WhitelistedIp) }
  it { should_not have_abilities([:create, :destroy], AppVersion) }
  it { should have_abilities(:manage, PhoneActivation) }
  it { should not_have_abilities([:view_print_label], PhoneActivation) }
  it do
    should not_have_abilities(%i[
      edit_compenso
      filter_phone_activation_for_dealers
      upload_ftp_documents
      lock
      unlock
      destroy
      remove_item
      reset_sent_to_robot
      send_already_enqueued_to_robot
      ], PhoneActivation)

    should have_abilities(:replace_documents, PhoneActivation)
  end

  it do
    should not_have_abilities(:replace_documents, WindNgposActivation)
    should not_have_abilities(%i[reimport reimport_last_month filter_by_with_import_errors], PhoneCall)
    should have_abilities(%i[see_dealer_name add_note], PhoneCall)
    should have_abilities([:see_vote_username], Vote)
    should have_abilities(%i[index edit update create cancel], Insurance)
    should not_have_abilities(%i[destroy list audits force_cancel], Insurance)
    should have_abilities(:customer_price, EasycarePriceRange)
    should not_have_abilities([:lock_item], Item)
    should have_abilities(%i[filter_order_by_origin show_trading_marker force_total_discount_with_auto_invoice], Order)
    should not_have_abilities([:destroy], Order)
    should have_abilities([:add_discount], ShoppingCartItem)
    should have_abilities(%i[find_province update], FixedLineDetail)
    should have_abilities(:autocomplete_option_name, Option)
    should have_abilities(:show_access_floa_financings, DealerContact)
    should have_abilities(:can_access_insert_recharges, DealerContact)
    should not_have_abilities(:audit, AfterSaleThread)
    should not_have_abilities(:destroy, AfterSaleThread)
    should have_abilities(:cancel, AfterSaleThread)
    should have_abilities(:insert_message_for, AfterSaleThread)
    should not_have_abilities(:destroy, ThreadMessage)
    should not_have_abilities(:index, ItemEvent)
    should have_abilities(:for_operation_and_status, KoReason)
    should have_abilities(:edit_avatar, User)
    should have_abilities(:last_activity, User)
    should have_abilities(:list_subagents_for, User)
    should not_have_abilities(%i[create_internal_user set_temporary_session], User)
    should not_have_abilities(%i[create edit index], GammaPaymentMethod)
    should not_have_abilities(:manually_confirm, GammaCall)
    should not_have_abilities([:destroy], PaymentRequest)
    should not_have_abilities([:index], RechargeSize)
    should not_have_abilities(%i[index show destroy], Page)
    should have_abilities(%i[internal_news], Page)
    should have_abilities([:create], ContractRebindingDetail)
    should have_abilities(%i[create update], TlsCustomerDetail)
    should not_have_abilities(:destroy, NumberPortabilityDetail)
    should have_abilities(%i[send_otp validate_otp], NumberPortabilityDetail)
    should have_abilities(:update, NumberPortabilityDetail)
    should have_abilities(:create, NumberRebindingDetail)
    should not_have_abilities(%i[pay reimport_data], AccountingDocument)
    should have_abilities([:update_sdi_forwarder_code], Customer)
    should not_have_abilities(%i[create], Notice)

    should not_have_abilities(:destroy, Reports::Generic)
    should not_have_abilities(:delayed_job, Reports::Generic)
    should not_have_abilities(:import_wind_tre_datas_from_csv, Reports::Generic)
    should not_have_abilities(:import_wind_tre_cb_datas_from_csv, Reports::Generic)
    should not_have_abilities(:import_db_energy_contracts_from_csv, Reports::Generic)
    should not_have_abilities(:import_wind_tre_fixed_datas_from_csv, Reports::Generic)
    should not_have_abilities(:import_tender_thresholds_from_csv, Reports::Generic)
    should not_have_abilities(:verify_tender_thresholds, Reports::Generic)

    should have_abilities(:show_access_debug, InternalUserDetail)
    should not_have_abilities(:manage, OfferInstance)
    should not_have_abilities(:rebuild, OfferInstance)
    should not_have_abilities(:import_details, Item)
    should have_abilities(:manage, WindNgposActivation)
    should not_have_abilities(:update_warehouse, WindNgposActivation)
    should not_have_abilities(:debug, WindNgposActivation)
    should not_have_abilities(:destroy, Fulfill)

    should have_abilities(:manage, Shipment)
    should have_abilities(:post_to_gsped, Shipment)
    should have_abilities(%i[index show full_search listen see_dealer_name add_note see_import_details], PhoneCall)
    should have_abilities(%i[create show], PhoneCallReport)
    should not_have_abilities(%i[import vote destroy], PhoneCall)
    should have_abilities(:for_aggregated_operator, RechargeSize)
    should have_abilities(%i[index show], ChatTranscription)
    should not_have_abilities(:delete, ChatTranscription)
    should have_abilities(:manage, ImeiReservation)
    should not_have_abilities(:delete, ImeiReservation)
    should not_have_abilities(:confirm, ImeiReservation)
    should not_have_abilities(:show_pending, ImeiReservation)
    should not_have_abilities(:manage, Operator)
    should not_have_abilities(:manage, RechargeSize)
    should not_have_abilities(:manage, Forwarder)
    should not_have_abilities(:manage, PhoneActivationKindOperation)
    should not_have_abilities(:manage, KoReason)
    should not_have_abilities(:manage, PdaKind)
    should not_have_abilities(:manage, PortabilityOperator)
    should not_have_abilities(:manage, PaymentMethod)
    should not_have_abilities(:manage, GspedCall)
    should not_have_abilities(:manage, DealerCategory)
    should not_have_abilities(:manage, DealerSegmentation)
    should not_have_abilities(:manage, GammaPaymentMethod)
    should not_have_abilities(:manage, H3gAgent)
    should not_have_abilities(:manage, ThreadKind)
    should not_have_abilities(:manage, EasycarePriceRange)
    should not_have_abilities(:manage, Financing)
    should not_have_abilities(:manage, FinancingStructure)
    should not_have_abilities(:destroy, CompassBranch)
    should have_abilities(:autocomplete_compass_branch_name, CompassBranch)
    should not_have_abilities(:edit_tester_flag, DealerContact)
    should have_abilities(:show_tester_flag, DealerContact)
    should have_abilities(:edit_tls_primary_contact, DealerContact)
    should have_abilities(:create, OperationOutcome)
    should not_have_abilities(:edit_administrative_fields, Item)
    should have_abilities(:reload_different_accountholder_container, PaymentMethodDetail)
    should have_abilities([:index, :show], DealerContractSignature)
    should have_abilities([:show], CmsPost)

    should not_have_abilities(:manage, Product)
    should not_have_abilities(:edit, Product)
    should not_have_abilities(:update, Product)
    should not_have_abilities(:import_tags, Product)
    should not_have_abilities(:import_promo_device, Product)

    should have_abilities(%i[show youtube_video], CmsPost)
    should not_have_abilities(%i[register_click create edit update destroy], CmsPost)
    should not_have_abilities(%i[show index update secret_settings_dont_tell_anyone], ApplicationSetting)
    should not_have_abilities(:manage, BlacklistedEmail)
    should have_abilities(:pdf_preview, ShoppingCart)
    should have_abilities(:manage, Device)
    should not_have_abilities(:unlock, Device)
    should not_have_abilities(:manually_confirm, ImeiReservation)
    should have_abilities(%i[index open_transactions_list], DropPayAccountRow)
    should not_have_abilities(%i[create close charge destroy], DropPayAccountRow)
    should not_have_abilities(:manage, AggregatedOperatorCategory)
    should not_have_abilities(:manage, AggregatedOperator)
    should have_abilities(:display_logo, AggregatedOperator)
    should have_abilities(%i[autocomplete_dealer_for_imei_reservation autocomplete_with_recharges_credit autocomplete_dealer_available_as_installation_bookmarked_dealers], Dealer)
    should have_abilities(:skip_autofulfill, Order)
    should not_have_abilities(:delete_preassigned_items, Order)
    should not_have_abilities(:manage, PriceListImage)
    should have_abilities(%i[edit_formation_fields see_formation_fields], Warehouse)
    should not_have_abilities(%i[edit_ad_hoc], Warehouse)
    should have_abilities(:for_activation, PortabilityOperator)
    should not_have_abilities(%i[create edit index show update], CostMatrix)
    should not_have_abilities(%i[create edit index show update], Plan)
    should have_abilities(%i[options_for_activation for_activation autocomplete_plan_name description_for_activation], Plan)
    should not_have_abilities(%i[create edit index show update], Offer)
    should have_abilities(:for_activation, Offer)
    should not_have_abilities(%i[create edit index show update], Option)
    should have_abilities(%i[autocomplete_option_name for_activation description_for_activation], Option)
    should not_have_abilities(:manage, Page)
    should have_abilities(:download, Document)
    should not_have_abilities(:manage, IiRow)
    should have_abilities(:index, IiRow)
    should not_have_abilities(:manage, AttivaBrand)
    should have_abilities(:manage, Affiliation)
    should not_have_abilities(:destroy, Affiliation)
    should have_abilities(:see_agent_column, Affiliation)
    should have_abilities(:manage, AffiliationOutcome)
    should not_have_abilities(:manage, ZoomLink)
    should have_abilities(:set_cluster, Warehouse)
    should have_abilities(:home_md_very, Page)
    should have_abilities(:home_page, Page)
    should not_have_abilities(:manage, FwaInstallationFee)
    should have_abilities(:for_warehouse_installation_areas, City)
    should have_abilities(:read, PaymentMethod)
    should have_abilities(:read, PdaKind)
    should have_abilities(:read, Province)
    should not_have_abilities(:third_party_installations_enabled, :assign_fido, :unlock_recharges_pin_disabled, :unlock, Dealer)
    should have_abilities(%i[new create], :trade_in)
    should not_have_abilities(%i[debug manage], SapApiCall)
    should have_abilities(:access, :coverage_check)
    should not_have_abilities(:show_archived_fwa_kolme_documents, Dealer)
    should have_abilities(:login_ai, User)
  end

  context 'password reset' do
    it 'can be performed on self' do
      should have_abilities(:reset_password, user)
    end

    it 'cannot be performed on other internal users' do
      other_user = create(:user, :role_user, email: '<EMAIL>')
      should not_have_abilities(:reset_password, other_user)
    end

    it 'can be performed on dealer contacts' do
      other_user = create(:user, :dealer_user)
      should have_abilities(:reset_password, other_user)
    end
  end

  context 'welcome email sending' do
    it 'can be performed only manually on other internal users' do
      other_user = create(:user, :role_user)
      should have_abilities(:send_welcome, other_user)
      should not_have_abilities(:auto_send_welcome, other_user)
    end

    it 'cannot be performed on other internal users who already changed their passwords' do
      other_user = create(:user, :role_user,
                          created_at:          2.days.ago,
                          password_changed_at: 1.day.ago)

      should not_have_abilities(:send_welcome, other_user)
      should not_have_abilities(:auto_send_welcome, other_user)
    end

    it 'can be performed only manually on dealer contacts' do
      other_user = create(:user, :dealer_user)
      should have_abilities(:send_welcome, other_user)
      should not_have_abilities(:auto_send_welcome, other_user)
    end

    it 'cannot be performed on other internal users who already changed their passwords' do
      other_user = create(:user, :dealer_user,
                          created_at:          2.days.ago,
                          password_changed_at: 1.day.ago)
      should not_have_abilities(:send_welcome, other_user)
      should not_have_abilities(:auto_send_welcome, other_user)
    end

    it 'can be performed only manually on new internal users' do
      other_user = build(:user, :role_user)
      should have_abilities(:send_welcome, other_user)
      should not_have_abilities(:auto_send_welcome, other_user)
    end

    it 'can be performed only manually on new dealer contact' do
      other_user = build(:user, :dealer_user)
      should have_abilities(:send_welcome, other_user)
      should not_have_abilities(:auto_send_welcome, other_user)
    end

    it 'can be performed only manually on new users with given created_at' do
      other_user = build(:user, :role_user, email: '<EMAIL>', created_at: 1.day.ago)
      should have_abilities(:send_welcome, other_user)
      should not_have_abilities(:auto_send_welcome, other_user)
    end

    it 'can be performed only manually on new users with given password_changed_at  ' do
      other_user = build(:user, :role_user, email: '<EMAIL>', password_changed_at: 1.day.ago)
      should have_abilities(:send_welcome, other_user)
      should not_have_abilities(:auto_send_welcome, other_user)
    end
  end

  describe 'AppointmentDetail' do
    it {
      should have_abilities(%i[index show new create
                               import export accept_fwa_installation_request manage_assignment manage_assignment_modal
                               reject_fwa_installation_request reassign_installation reassign_installation_modal], AppointmentDetail)
    }

    context 'update, update_date' do
      let(:appointment_detail) { build_stubbed(:appointment_detail, status: 'set', appointment_date: Date.today) }

      context 'time limit' do
        after { Timecop.return }

        it do
          should have_abilities(:update, appointment_detail)
          should have_abilities(:update_date, appointment_detail)

          Timecop.freeze Date.tomorrow
          should have_abilities(%i[update_date], appointment_detail)
          should have_abilities(%i[update], appointment_detail)
        end
      end

      context 'appointment statuses' do
        it do
          ['modified', 'set', 'confirmed', 'reschedule'].each do |status|
            appointment_detail.status = status
            should have_abilities(:update, appointment_detail)
            should have_abilities(:update_date, appointment_detail)
          end
          ['void', 'unset', 'completed', 'ko', 'suspend'].each do |status|
            appointment_detail.status = status
            should not_have_abilities(%i[update update_date], appointment_detail)
          end
        end

        context 'waiting status' do
          before { appointment_detail.status = 'waiting' }

          it do
            should have_abilities(:update, appointment_detail)
            should have_abilities(:update_date, appointment_detail)
          end

          context 'third_party_installation' do
            before { appointment_detail.third_party_installation = true }

            it do
              should have_abilities(:update, appointment_detail)
              should not_have_abilities(:update_date, appointment_detail)
            end
          end
        end

        context 'waiting_appointment status' do
          before do
            appointment_detail.status = 'waiting_appointment'
            appointment_detail.third_party_installation = true
          end

          it do
            should have_abilities(:update, appointment_detail)
            should not_have_abilities(:update_date, appointment_detail)
            appointment_detail.appointment_date = nil
            should have_abilities(:update, appointment_detail)
            should have_abilities(:update_date, appointment_detail)
          end
        end

        context 'draft status' do
          before { appointment_detail.status = 'draft' }

          it do
            should have_abilities(:update, appointment_detail)
            should not_have_abilities(:update_date, appointment_detail)
          end
        end

        context 'completed status' do
          it do
            appointment_detail.status = 'completed'
            allow(appointment_detail).to receive(:support_kind_not_user_available?).and_return true
            should not_have_abilities(%i[update update_date], appointment_detail)
          end
        end
      end
    end

    context 'operation_kind, update_operation_kind' do
      let(:appointment_detail) { build_stubbed(:appointment_detail, status: 'confirmed') }

      it do
        AppointmentDetail::OPERATION_KIND_EDITABLE_STATES.each do |status|
          appointment_detail.status = status
          should have_abilities([:operation_kind, :update_operation_kind], appointment_detail)
        end
      end

      context 'parent_np_x_order_code present' do
        it do
          appointment_detail.operation_kind = 'wall'
          appointment_detail.parent_np_x_order_code = 'XXXXX'

          should_not have_abilities([:operation_kind, :update_operation_kind], appointment_detail)
        end
      end

      context 'already present' do
        it do
          appointment_detail.operation_kind = 'wall'
          should not_have_abilities([:operation_kind, :update_operation_kind], appointment_detail)
        end
      end

      context 'invalid appointment states' do
        it do
          (AppointmentDetail.statuses.keys - AppointmentDetail::OPERATION_KIND_EDITABLE_STATES).each do |status|
            appointment_detail.status = status
            should not_have_abilities([:operation_kind, :update_operation_kind], appointment_detail)
          end
        end
      end

      context 'date roles' do
        let(:appointment_date) { Date.new(2023, 10, 16) }

        before { appointment_detail.appointment_date = appointment_date }
        after { Timecop.return }

        it do
          Timecop.freeze(appointment_date)
          should have_abilities(%i[operation_kind update_operation_kind], appointment_detail)

          Timecop.freeze(Date.new(2023, 11, 4))
          should have_abilities(%i[operation_kind update_operation_kind], appointment_detail)

          Timecop.freeze(Date.new(2023, 11, 5))
          should not_have_abilities(%i[operation_kind update_operation_kind], appointment_detail)
        end
      end
    end

    context 'complete_support' do
      let(:appointment_detail) { build_stubbed(:appointment_detail, status: 'confirmed', appointment_date: Date.today) }
      it do
        should not_have_abilities(:complete_support, appointment_detail)

        appointment_detail.support_kind = 'fix'
        appointment_detail.status = 'set'

        should not_have_abilities(:complete_support, appointment_detail)

        appointment_detail.support_kind = 'fix'
        appointment_detail.status = 'confirmed'

        should have_abilities(:complete_support, appointment_detail)
      end
    end

    context 'create assurance and post_delivery' do
      it { should have_abilities(%i(autocomplete_appointment_detail_np_x_order_code check_np_x_order_code close_support duplicate_npx_order edit_support_kind open_new_form_modal), AppointmentDetail) }
    end

    context 'reassign_installation_modal reassign_installation' do
      let(:appointment_detail) { build_stubbed(:appointment_detail) }

      it do
        %w[draft waiting waiting_appointment set confirmed modified reschedule dummy_set suspend].each do |status|
          appointment_detail.status = status
          should have_abilities(%i[reassign_installation_modal reassign_installation], appointment_detail)
        end

        %w[unset void completed ko customer_cancel].each do |status|
          appointment_detail.status = status
          should not_have_abilities(%i[reassign_installation_modal reassign_installation], appointment_detail)
        end
      end
    end
  end

  describe 'TlsUpfront' do
    let(:tls_upfront) { build_stubbed(:tls_upfront, status: 'waiting', status_updated_at: Time.now) }

    it do
      should have_abilities(:index, TlsUpfront)
      should not_have_abilities(%i[cancel refund], TlsUpfront)

      should have_abilities(:pay, tls_upfront)

      tls_upfront.status = 'paid'
      should not_have_abilities(:pay, tls_upfront)
    end

    context 'rebuild_payment' do
      it do
        should not_have_abilities(:rebuild_payment, tls_upfront)

        tls_upfront.status = 'failed'
        should have_abilities(:rebuild_payment, tls_upfront)
      end
    end

    context 'send_payment_notification' do
      it do
        should not_have_abilities(:send_payment_notification, tls_upfront)

        tls_upfront.payment_notification_sent_at = Time.now
        should not_have_abilities(:send_payment_notification, tls_upfront)

        tls_upfront.payment_notification_sent_at = 60.seconds.ago
        should have_abilities(:send_payment_notification, tls_upfront)

        tls_upfront.status = 'paid'
        should not_have_abilities(:send_payment_notification, tls_upfront)
      end
    end

    context 'send_welcome_mail' do
      before { tls_upfront.phone_activation.activated_at = 29.days.ago }

      it 'max time limit' do
        should have_abilities(:send_welcome_mail, tls_upfront)

        tls_upfront.phone_activation.activated_at = 30.days.ago
        should not_have_abilities(:send_welcome_mail, tls_upfront)

        tls_upfront.phone_activation.activated_at = nil
        should not_have_abilities(:send_welcome_mail, tls_upfront)
      end

      it 'min time limit' do
        tls_upfront.welcome_mail_sent_at = Time.now
        should not_have_abilities(:send_welcome_mail, tls_upfront)

        tls_upfront.welcome_mail_sent_at = 400.seconds.ago
        should have_abilities(:send_welcome_mail, tls_upfront)
      end
    end
  end

  describe 'PromoDevice' do
    let(:promo_device_in_progress) { build_stubbed(:promo_device, state: :in_progress) }
    let(:promo_device_activated) { build_stubbed(:promo_device, state: :activated) }

    context 'autosend enabled' do
      before { create(:application_setting, :autosend_activations_to_robot, value: 1) }

      it do
        should have_abilities(:manage, PromoDevice)
        should not_have_abilities(:destroy, PromoDevice)
        should not_have_abilities(:resend_to_robot, promo_device_activated)
        should have_abilities(:resend_to_robot, promo_device_in_progress)
      end
    end

    context 'autosend disabled' do
      before { create(:application_setting, :autosend_activations_to_robot, value: 0) }

      it do
        should have_abilities(:manage, PromoDevice)
        should not_have_abilities(:destroy, PromoDevice)
        should not_have_abilities(:resend_to_robot, promo_device_activated)
        should not_have_abilities(:resend_to_robot, promo_device_in_progress)
      end
    end
  end

  describe 'PhoneActivation::ROBOT_ACTIONS' do
    context 'autosend enabled' do
      before { create(:application_setting, :autosend_activations_to_robot, value: 1) }

      it do
        PhoneActivation::ROBOT_ACTIONS.each do |action|
          should have_abilities(action.to_sym, PhoneActivation)
        end
      end
    end

    context 'autosend disabled' do
      before { create(:application_setting, :autosend_activations_to_robot, value: 0) }

      it do
        PhoneActivation::ROBOT_ACTIONS.each do |action|
          should not_have_abilities(action.to_sym, build_stubbed(:phone_activation))
        end
      end
    end
  end

  context 'listen, PhoneCall' do
    let(:allowed_phone_call) do
      stub_model(PhoneCall, internal_user_first_name: user.internal_user_detail.first_name,
                            internal_user_last_name:  user.internal_user_detail.last_name)
    end
    let(:disallowed_phone_call) { stub_model(PhoneCall, internal_user_first_name: 'FirstName', internal_user_last_name: 'LastName') }

    before { allow(user).to receive(:internal_user_detail).and_return internal_user_detail }
    it do
      should have_abilities(:listen, allowed_phone_call)
      should not_have_abilities(:listen, disallowed_phone_call)
    end

    context 'with access flag' do
      before { allow(user).to receive_message_chain(:internal_user_detail, :can_access_phone_calls?).and_return true }

      it do
        should have_abilities(:listen, allowed_phone_call)
        should have_abilities(:listen, disallowed_phone_call)
      end
    end
  end

  context 'accessing customers' do
    let(:user_ability) { AbilityFactory.build_ability_for(user) }

    it 'can be seen when internal user have access flag' do
      access_flag = create(:access_flag, :access_customers)

      user.internal_user_detail.access_flags << access_flag

      should have_abilities(:manage, Customer)
      should have_abilities(:access_customers, Customer)
      should have_abilities(:customer_details, PhoneActivation)
      should have_abilities(:download_pda, PhoneActivation)
    end

    it 'cant be seen when internal user not have access flag' do
      should have_abilities(:manage, Customer)

      should not_have_abilities(:access_customers, Customer)
      should not_have_abilities(:customer_details, PhoneActivation)
      should not_have_abilities(:download_pda, PhoneActivation)
    end
  end

  context 'Order' do
    it { should have_abilities(%i[filter_order_by_origin show_trading_marker], Order) }
    it { should not_have_abilities([:destroy], Order) }

    let(:order) { stub_model(Order) }

    context 'update, Order' do
      it 'should not be able to update an order in states' do
        [:cancelled, :paid, :awaiting_cc_confirmation, :partially_processed, :processed, :void, :waiting_for_dhl].each do |state|
          order.aasm_state = state
          should not_have_abilities(:update, order)
        end
      end

      it 'should be able to update an order in to_pay_credit_card state' do
        order.aasm_state = :to_pay_credit_card
        should have_abilities(:update, order)
      end
    end
  end

  context 'change_status' do
    let(:item) { stub_model(Item) }

    context 'without connected phone activations, orders or IMEI reservations' do
      it 'item sold' do
        item.state = 'sold'
        should not_have_abilities([:change_status], item)
      end

      it 'item not sold' do
        item.state = 'instock'
        should have_abilities([:change_status], item)
      end
    end

    context 'with a connected phone activation' do
      let(:phone_activation) { double }

      before do
        allow(item).to receive(:phone_activation).and_return phone_activation
      end

      it 'item instock' do
        item.state = 'instock'
        should have_abilities([:change_status], item)
      end

      it 'item not instock' do
        item.state = 'sold'
        should not_have_abilities([:change_status], item)
      end
    end

    context 'with a connected order' do
      let(:order) { double }

      before do
        allow(item).to receive(:order).and_return order
      end

      it 'item instock' do
        item.state = 'instock'
        should have_abilities([:change_status], item)
      end

      it 'item not instock' do
        item.state = 'sold'
        should not_have_abilities([:change_status], item)
      end
    end

    context 'with a connected requested imei_reservation' do
      let(:requested_imei_reservation) { double }

      before do
        allow(item).to receive(:requested_imei_reservation).and_return requested_imei_reservation
      end

      it 'item instock' do
        item.state = 'instock'
        should have_abilities([:change_status], item)
      end

      it 'item not instock' do
        item.state = 'sold'
        should not_have_abilities([:change_status], item)
      end
    end

    context 'with a connected requested imei_reservation' do
      let(:confirmed_imei_reservation) { double }

      before do
        allow(item).to receive(:confirmed_imei_reservation).and_return confirmed_imei_reservation
      end

      it 'item instock' do
        item.state = 'instock'
        should have_abilities([:change_status], item)
      end

      it 'item not instock' do
        item.state = 'sold'
        should not_have_abilities([:change_status], item)
      end
    end

    context 'update_ko_reason_error_type OperationOutcome' do
      let(:operation_outcome) { build_stubbed(:operation_outcome) }

      it { should not_have_abilities(:update_ko_reason_error_type, operation_outcome) }
      context 'operation_outcome status_ko_r' do
        before { operation_outcome.status = 'ko_r' }

        it { should not_have_abilities(:update_ko_reason_error_type, operation_outcome) }

        context 'ko_reason present' do
          let(:ko_reason) { build_stubbed(:ko_reason) }
          before { operation_outcome.ko_reason = ko_reason }

          it { should not_have_abilities(:update_ko_reason_error_type, operation_outcome) }

          context 'with details' do
            let(:ko_reason) { build_stubbed(:ko_reason, details: ['first detail', 'second detail']) }
            before { operation_outcome.ko_reason = ko_reason }

            it { should have_abilities(:update_ko_reason_error_type, operation_outcome) }
          end
        end
      end
    end
  end

  context 'Recharge' do
    context 'non pin recharge' do
      let(:recharge) { create(:recharge, :manual) }

      it 'status OK' do
        recharge.update(recharge_status: Recharge::SUCCESS)

        should have_abilities(:show_receipt, recharge)
      end

      it '!status OK' do
        recharge.update(recharge_status: Recharge::FAILED)

        should have_abilities(:show_receipt, recharge)
      end
    end

    context 'pin recharge' do
      let(:recharge) { create(:recharge, :pin_vodafone) }

      it 'status OK' do
        recharge.update(recharge_status: Recharge::SUCCESS)

        should not_have_abilities(:show_receipt, recharge)
      end

      it '!status OK' do
        recharge.update(recharge_status: Recharge::FAILED)

        should have_abilities(:show_receipt, recharge)
      end
    end
  end

  describe 'DropPayAccount' do
    let(:auth_required_drop_pay_account) { build_stubbed(:drop_pay_account, recursive_charges_authorization: :required) }
    let(:auth_not_required_drop_pay_account) { build_stubbed(:drop_pay_account, :dealer, state: :in_activation, recursive_charges_authorization: :not_required, enabled: true) }
    let(:not_enabled_drop_pay_account) { create(:drop_pay_account, state: :not_enabled) }
    let(:active_drop_pay_account) { create(:drop_pay_account, :dealer) }

    let(:dealer) { build_stubbed(:dealer) }
    let(:warehouse) { build_stubbed(:warehouse, dealer: dealer) }

    it { should have_abilities(:edit, auth_required_drop_pay_account) }

    context 'require_connect_authorization' do
      let(:auth_required_drop_pay_account) { build_stubbed(:drop_pay_account, state: :in_activation) }
      let(:auth_not_required_drop_pay_account) { build_stubbed(:drop_pay_account, :dealer, state: :deactivated) }

      it do
        should_not have_abilities(:require_connect_authorization, auth_required_drop_pay_account)
        should not_have_abilities(:require_connect_authorization, auth_not_required_drop_pay_account)

        auth_not_required_drop_pay_account.enabled = true
        should have_abilities(:require_connect_authorization, auth_not_required_drop_pay_account)
      end
    end

    context 'require_recursive_charge_authorization' do
      let(:auth_required_drop_pay_account) { build_stubbed(:drop_pay_account, state: :active, recursive_charges_authorization: :required) }
      let(:auth_not_required_drop_pay_account) { build_stubbed(:drop_pay_account, :dealer, state: :active, recursive_charges_authorization: :not_required) }

      it do
        should not_have_abilities(:require_recursive_charge_authorization, auth_required_drop_pay_account)
        should not_have_abilities(:require_recursive_charge_authorization, auth_not_required_drop_pay_account)

        auth_not_required_drop_pay_account.enabled = true
        should have_abilities(:require_recursive_charge_authorization, auth_not_required_drop_pay_account)

        auth_not_required_drop_pay_account.state = :in_activation
        should not_have_abilities(:require_recursive_charge_authorization, auth_not_required_drop_pay_account)
      end
    end

    it 'show section on dealer form' do
      should_not have_abilities(:show_drop_pay_section, dealer)
      dealer.drop_pay_account = not_enabled_drop_pay_account
      should_not have_abilities(:show_drop_pay_section, dealer)
      dealer.drop_pay_account = active_drop_pay_account
      should not_have_abilities(:show_drop_pay_section, dealer)
      active_drop_pay_account.enabled = true
      should have_abilities(:show_drop_pay_section, dealer)
    end

    it 'show section on warehouse form' do
      should_not have_abilities(:show_drop_pay_pos_section, warehouse)
      warehouse.dealer = dealer
      should_not have_abilities(:show_drop_pay_pos_section, warehouse)
      warehouse.dealer.drop_pay_account = not_enabled_drop_pay_account
      should_not have_abilities(:show_drop_pay_pos_section, warehouse)
      warehouse.dealer.drop_pay_account = active_drop_pay_account
      should not_have_abilities(:show_drop_pay_pos_section, warehouse)
      active_drop_pay_account.enabled = true
      should have_abilities(:show_drop_pay_pos_section, warehouse)
    end
  end

  describe 'index, User' do
    let(:empty_params) { {} }
    let(:dealer_contact_params) { { 'kind' => 'dealer_contact' } }

    it { expect(AbilityFactory.build_ability_for(user, empty_params)).not_to have_abilities(:index, User) }
    it { expect(AbilityFactory.build_ability_for(user, dealer_contact_params)).to have_abilities(:index, User) }
  end

  describe 'edit, Affiliation' do
    let(:affiliation) { build_stubbed(:affiliation) }

    it { should have_abilities(:edit, affiliation) }

    context 'affiliation not in_progress' do
      it do
        affiliation.status = 'pre_approved'
        should not_have_abilities(:edit, affiliation)

        affiliation.status = 'rejected'
        should not_have_abilities(:edit, affiliation)

        affiliation.status = 'completed'
        should not_have_abilities(:edit, affiliation)
      end
    end
  end

  describe 'iframe_redirect, ZoomLink' do
    let(:zoom_link) { ZoomLink.create(url_alias: 'alias', meeting_url: 'meeting', active: true) }

    it 'active link' do
      should have_abilities(:iframe_redirect, zoom_link)
    end

    it 'inactive link' do
      zoom_link.update(active: false)
      should not_have_abilities(:iframe_redirect, zoom_link)
    end
  end

  context 'mdv_documents' do
    let(:dealer) { build_stubbed(:dealer) }
    let(:md_very_dealer) { build_stubbed(:dealer, kind: 'md_very') }
    let(:tls_dealer) { build_stubbed(:dealer, kind: 'operator_tls') }

    context 'dealer created as md_very' do
      before { allow(md_very_dealer).to receive(:created_as_md_very?).and_return true }

      it do
        should not_have_abilities(%i[show_mdv_affiliation_documents upload_mdv_affiliation_document_form update_mdv_affiliation_document], dealer)
        should have_abilities(%i[show_mdv_affiliation_documents upload_mdv_affiliation_document_form update_mdv_affiliation_document], md_very_dealer)
        should not_have_abilities(%i[show_mdv_affiliation_documents upload_mdv_affiliation_document_form update_mdv_affiliation_document], tls_dealer)

        should not_have_abilities(%i[do_sign_with_digital_signature next_digital_sign_step], Dealer)
      end

      context 'when all_mdv_affiliation_documents_accepted' do
        before do
          allow(dealer).to receive(:all_mdv_affiliation_documents_accepted?).and_return true
          allow(md_very_dealer).to receive(:all_mdv_affiliation_documents_accepted?).and_return true
          allow(tls_dealer).to receive(:all_mdv_affiliation_documents_accepted?).and_return true
        end

        it do
          should have_abilities(%i[show_mdv_affiliation_documents], dealer)
          should not_have_abilities(%i[upload_mdv_affiliation_document_form update_mdv_affiliation_document], dealer)
          should have_abilities(%i[show_mdv_affiliation_documents upload_mdv_affiliation_document_form update_mdv_affiliation_document], md_very_dealer)
          should not_have_abilities(%i[show_mdv_affiliation_documents upload_mdv_affiliation_document_form update_mdv_affiliation_document], tls_dealer)

          should not_have_abilities(%i[do_sign_with_digital_signature next_digital_sign_step], Dealer)
        end
      end

      context 'when affiliated_from_contract_terms' do
        before do
          allow(dealer).to receive(:affiliated_from_contract_terms?).and_return true
          allow(md_very_dealer).to receive(:affiliated_from_contract_terms?).and_return true
          allow(tls_dealer).to receive(:affiliated_from_contract_terms?).and_return true
        end

        it do
          should not_have_abilities(%i[show_mdv_affiliation_documents upload_mdv_affiliation_document_form update_mdv_affiliation_document], dealer)
          should not_have_abilities(%i[show_mdv_affiliation_documents upload_mdv_affiliation_document_form update_mdv_affiliation_document], md_very_dealer)
          should not_have_abilities(%i[show_mdv_affiliation_documents upload_mdv_affiliation_document_form update_mdv_affiliation_document], tls_dealer)

          should not_have_abilities(%i[do_sign_with_digital_signature next_digital_sign_step], Dealer)
        end
      end
    end

    context 'dealer created as standard and updated to md_very' do
      it { should not_have_abilities(%i[show_mdv_affiliation_documents upload_mdv_affiliation_document_form update_mdv_affiliation_document], md_very_dealer) }

      context 'when all_mdv_affiliation_documents_accepted' do
        before { allow(md_very_dealer).to receive(:all_mdv_affiliation_documents_accepted?).and_return true }

        it do
          should have_abilities(:show_mdv_affiliation_documents, md_very_dealer)
          should not_have_abilities(%i[upload_mdv_affiliation_document_form update_mdv_affiliation_document], md_very_dealer)
        end
      end

      context 'when affiliated_from_contract_terms' do
        before { allow(md_very_dealer).to receive(:affiliated_from_contract_terms?).and_return true }

        it { should not_have_abilities(%i[show_mdv_affiliation_documents upload_mdv_affiliation_document_form update_mdv_affiliation_document], md_very_dealer) }
      end
    end
  end

  context 'fwa_documents' do
    let(:dealer) { create(:dealer) }
    let(:fwa_dealer) { create(:dealer, :fwa_installer) }

    it do
      should not_have_abilities(:upload_fwa_installation_document_form, dealer)
      should have_abilities(:upload_fwa_installation_document_form, fwa_dealer)
    end
  end

  context 'Document update_expires_at' do
    let(:document) { build(:document) }

    it do
      should not_have_abilities(:update_expires_at, document)

      document.save!
      should not_have_abilities(:update_expires_at, document)

      document.update(status: Document::ACCEPTED)
      should have_abilities(:update_expires_at, document)

      document.update(status: Document::EXPIRED)
      should not_have_abilities(:update_expires_at, document)
    end
  end

  context 'ContractTerm' do
    let(:contract_term) { build_stubbed(:contract_term) }

    it do
      should have_abilities(:disable_contract_term, contract_term)

      contract_term.kind = ContractTerm::REQUIRED_KIND
      should not_have_abilities(:disable_contract_term, contract_term)
    end
  end

  describe 'SignedContract' do
    it do
      should have_abilities(:sign_contract, Dealer)
      should have_abilities(:document, SignedContract)
      should not_have_abilities(%i[destroy do_sign_with_digital_signature next_digital_sign_step], SignedContract)
    end
  end

  describe 'FloaFinancing' do
    let(:floa_financing) { build_stubbed(:floa_financing) }

    it do
      should have_abilities(:manage, FloaFinancing)

      should have_abilities(:destroy, floa_financing)
      floa_financing.status = 'waiting'
      should have_abilities(:destroy, floa_financing)

      floa_financing.status = 'accepted'
      should not_have_abilities(:destroy, floa_financing)
    end

    context 'send_sms' do
      it do
        should not_have_abilities(:send_sms, floa_financing)

        floa_financing.sms_sent_at = 4.minutes.ago
        should not_have_abilities(:send_sms, floa_financing)

        floa_financing.status = 'waiting'
        should have_abilities(:send_sms, floa_financing)

        floa_financing.sms_sent_at = 2.minutes.ago
        should not_have_abilities(:send_sms, floa_financing)
      end
    end
  end

  describe 'ImeiReservation' do
    let(:imei_reservation) { build_stubbed(:imei_reservation) }

    context 'dealer_gallery_gd' do
      before { allow(imei_reservation).to receive(:dealer_gallery_gd?).and_return(true) }
      it { should not_have_abilities(:download_credit_transfer, imei_reservation) }

      context 'with wind_ngpos_activations' do
        let(:wind_ngpos_activation) { build_stubbed(:wind_ngpos_activation) }
        before { imei_reservation.wind_ngpos_activation = wind_ngpos_activation }

        it { should have_abilities(:download_credit_transfer, imei_reservation) }
      end
      context 'with canceled imei_reservation' do
        before { imei_reservation.state = 'canceled' }
        before { imei_reservation.wind_ngpos_activation = build_stubbed(:wind_ngpos_activation) }

        it { should_not have_abilities(:download_credit_transfer, imei_reservation) }
      end
    end
  end

  describe 'EnergyContract' do
    let(:dealer) { build_stubbed(:dealer) }
    let(:energy_contract) { create(:energy_contract, dealer: dealer) }

    it { should have_abilities(:manage, EnergyContract) }

    context 'edit_rewards, update_rewards' do
      it do
        should_not have_abilities(%i[edit_rewards update_rewards], energy_contract)

        dealer.kind = 'kolme_master'
        should have_abilities(%i[edit_rewards update_rewards], energy_contract)
      end
    end

    context 'destroy' do
      it do
        should have_abilities(:destroy, energy_contract)

        create(:energy_contract_api_call, :send_request, energy_contract: energy_contract)
        should not_have_abilities(:destroy, energy_contract.reload)
      end
    end
  end

  describe 'ProductMacroCategory' do
    let(:product_macro_category) { create(:product_macro_category) }

    it { should not_have_abilities(:destroy, product_macro_category) }
  end

  describe 'Warehouse' do
    let(:warehouse) { build_stubbed(:warehouse) }

    it { should not_have_abilities(:open_dealer_bookmark_modal, warehouse) }

    context 'bookmarkable warehouse' do
      before { allow(warehouse).to receive(:bookmarkable?).and_return(true) }

      it { should have_abilities(:open_dealer_bookmark_modal, warehouse) }
    end
  end

  describe 'WarehouseBookmarkedDealer' do
    let(:warehouse) { build_stubbed(:warehouse) }
    let(:warehouse_bookmarked_dealer) { build_stubbed(:warehouse_bookmarked_dealer, warehouse: warehouse) }

    it { should have_abilities(:create, warehouse_bookmarked_dealer) }
    it { should have_abilities(:destroy, warehouse_bookmarked_dealer) }
  end
end
