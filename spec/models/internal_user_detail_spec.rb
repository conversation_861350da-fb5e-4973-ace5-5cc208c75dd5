require 'rails_helper'

describe InternalUserDetail do
  describe 'create' do
    let!(:internal_user_detail) { create(:internal_user_detail, user_id: 1) }

    it { expect(InternalUserDetail.count).to eq 1 }
    it 'cannot duplicate' do
      expect { create(:internal_user_detail, user_id: 1) }.to raise_error(ActiveRecord::RecordNotUnique)
    end
  end

  context 'validations' do
    let(:internal_user_detail) { build(:internal_user_detail, user_id: 1) }

    it 'is valid with a phone number starting with 3 and 9 or 10 digits long' do
      expect(internal_user_detail).to be_valid

      internal_user_detail.phone = '329195088'
      expect(internal_user_detail).to be_valid
    end

    it 'is invalid without a phone number' do
      internal_user_detail.phone = nil
      internal_user_detail.valid?
      expect(internal_user_detail.errors.details[:phone].first[:error]).to eq :blank
    end

    it 'is invalid with a phone number not starting with 3' do
      internal_user_detail.phone = '4291950880'
      internal_user_detail.valid?
      expect(internal_user_detail.errors.details[:phone].first[:error]).to eq :invalid
    end

    it 'is invalid with a too short phone number' do
      internal_user_detail.phone = '4291950'
      internal_user_detail.valid?
      expect(internal_user_detail.errors.details[:phone].first[:error]).to eq :invalid
    end

    it 'is invalid with a too long phone number' do
      internal_user_detail.phone = '42919508801'
      internal_user_detail.valid?
      expect(internal_user_detail.errors.details[:phone].first[:error]).to eq :invalid
    end
  end

  describe '#can_access_insert_recharges?' do
    let(:internal_user_detail) { build(:internal_user_detail) }
    let(:access_insert_recharges_flag) { build(:access_flag, :access_insert_recharges) }

    it { expect(internal_user_detail.can_access_insert_recharges?).not_to be_nil }

    context 'with user' do
      let(:user_internal_user_detail) { build(:internal_user_detail, :user) }
      before { user_internal_user_detail.access_flags << access_insert_recharges_flag }

      it { expect(user_internal_user_detail.can_access_insert_recharges?).to be true }
    end
    context 'with super user' do
      let(:super_user_internal_user_detail) { build(:internal_user_detail, :super_user) }
      before { super_user_internal_user_detail.access_flags << access_insert_recharges_flag }

      it { expect(super_user_internal_user_detail.can_access_insert_recharges?).to be true }
    end
    context 'with admin' do
      let(:admin_internal_user_detail) { build(:internal_user_detail, :admin) }
      before { admin_internal_user_detail.access_flags << access_insert_recharges_flag }

      it { expect(admin_internal_user_detail.can_access_insert_recharges?).to be true }
    end
    context 'with agent' do
      let(:agent_internal_user_detail) { build(:internal_user_detail, :agent) }

      it { expect(agent_internal_user_detail.can_access_insert_recharges?).to be false }
    end
  end

  describe 'user#set_child_agents_access_level' do
    let(:agent_wind_tre) { create(:user, :agent, email: '<EMAIL>') }
    let(:sub_agent) { create(:user, :sub_agent_wind_tre, parent_agent: agent_wind_tre, email: '<EMAIL>') }

    before do
      create(:internal_user_detail, user: agent_wind_tre, access_level: AccessLevel::ONLY_WIND_TRE)
      create(:internal_user_detail, user: sub_agent, access_level: AccessLevel::ONLY_WIND_TRE)
    end

    it do
      agent_wind_tre.reload.internal_user_detail.update(access_level: AccessLevel::ONLY_MD)

      expect(sub_agent.internal_user_detail.reload.access_level).to eq(AccessLevel::ONLY_MD)
    end
  end
end
