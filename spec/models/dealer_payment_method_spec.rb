require "rails_helper"

describe DealerPaymentMethod do
  let!(:default_payment_method)     { create(:payment_method, :credit_card) }
  let!(:non_default_payment_method) { create(:payment_method, :pinpad_fastweb) }
  let(:dealer)                      { create(:dealer) }

  it "associates default payment methods after creating dealer" do
    expect(dealer.payment_methods).to include(default_payment_method)
    expect(dealer.payment_methods).not_to include(non_default_payment_method)
  end

  it "doesnt add duplicates" do
    expect { dealer.send(:associate_default_payment_methods) }.not_to change { dealer.payment_methods.size }
  end
end
