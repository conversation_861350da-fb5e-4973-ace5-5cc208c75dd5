require 'rails_helper'

describe NumberPortabilityDetail do
  let(:source_operator_tim) { build_stubbed(:portability_operator, :mobile, label: 'Tim', group_value: 'TIM') }
  let(:source_operator_vodafone) { build_stubbed(:portability_operator, :mobile, label: 'Vodafone', group_value: 'Vodafone') }
  let(:source_operator_altro) { create(:portability_operator, :altro) }
  let(:source_operator_tim_fisso) { create(:portability_operator, :tim_fisso) }

  it { expect(NumberPortabilityDetail::MAX_ASSOCIATED_NUMBERS).to eq (1..3) }

  context 'create' do
    let(:phone_activation) { create(:phone_activation) }
    let(:number_portability_detail) { NumberPortabilityDetail.new(phone_activation: phone_activation) }

    it do
      number_portability_detail.save(validate: false)
      expect(number_portability_detail.required_sim_document).to be_truthy
    end
  end

  context 'is_voce?' do
    let(:mobile_phone_activation) { create(:phone_activation, :abbonamento_solo_sim) }
    let(:number_portability_detail) { NumberPortabilityDetail.new(phone_activation: mobile_phone_activation) }
    before { allow(number_portability_detail).to receive(:is_voce?).and_return true }

    it { expect(number_portability_detail.valid?).to be_falsey }

    context 'with right values' do
      before do
        number_portability_detail.phone_number     = '3334323343'
        number_portability_detail.source_operator  = source_operator_tim
        number_portability_detail.sim_serial       = Faker::Number.number(digits: 21)
        number_portability_detail.contract_kind_id = 1
      end
      it { expect(number_portability_detail.valid?).to be_truthy }

      context 'with sim lost or stolen' do
        before { number_portability_detail.sim_lost_or_stolen = true }

        context 'with tim operator' do
          it 'does not validate sim_serial length' do
            number_portability_detail.sim_serial = '1234'
            expect(number_portability_detail.valid?).to be_truthy
          end
        end

        context 'with not tim operator' do
          before { number_portability_detail.source_operator = source_operator_vodafone }

          it 'does not validate sim_serial numericality' do
            number_portability_detail.sim_serial = '123456789012345678a'
            expect(number_portability_detail.valid?).to be_truthy
          end

          it 'does not validate sim_serial length' do
            number_portability_detail.sim_serial = '1234'
            expect(number_portability_detail.valid?).to be_truthy
          end
        end
      end

      context 'with not tim operator' do
        before do
          number_portability_detail.source_operator = source_operator_vodafone
          number_portability_detail.sim_serial      = Faker::Number.number(digits: 18)
        end
        it do
          expect(number_portability_detail.valid?).to be_falsey
        end
        it do
          number_portability_detail.sim_serial = '0231313123121231239'
          expect(number_portability_detail.valid?).to be_truthy
        end
        it 'accept only numbers' do
          number_portability_detail.sim_serial = Faker::Lorem.characters(number: 19)
          expect(number_portability_detail.valid?).to be_falsey
        end
      end

      context 'with tim operator' do
        it 'sim_serial minimum length 19' do
          number_portability_detail.sim_serial = Faker::Number.number(digits: 18)
          expect(number_portability_detail.valid?).to be_falsey
        end
        it 'sim_serial maximum length 30' do
          number_portability_detail.sim_serial = Faker::Number.number(digits: 40)
          expect(number_portability_detail.valid?).to be_falsey
        end
        it 'sim_serial accept numbers and chars' do
          number_portability_detail.sim_serial = "#{Faker::Number.number(digits: 10)}#{Faker::Lorem.characters(number: 15)}"
          expect(number_portability_detail.valid?).to be_truthy
        end
        it 'sim_serial accept numbers and chars no symbols or space' do
          number_portability_detail.sim_serial = "$#{Faker::Number.number(digits: 9)} #{Faker::Lorem.characters(number: 14)}"
          expect(number_portability_detail.valid?).to be_falsey
        end
      end
    end

    context 'check different owner is not same as customer' do
      let(:error_message) { 'Attenzione, il diverso intestatario della sim coincide con il cliente dell\'attivazione. Per proseguire rimuovi il flag "Diverso intestatario"' }

      it do
        number_portability_detail.different_owner = true
        number_portability_detail.owner_cf = number_portability_detail.phone_activation.customer.cf

        number_portability_detail.owner_kind = 'pf'
        number_portability_detail.valid?
        expect(number_portability_detail.errors.messages[:base]).to include error_message

        number_portability_detail.owner_kind = 'di'
        number_portability_detail.valid?
        expect(number_portability_detail.errors.messages[:base]).not_to include error_message
      end
    end

    context 'OTP' do
      context 'with otp disabled' do
        it do
          number_portability_detail.valid?
          expect(number_portability_detail.errors.attribute_names).not_to include :otp
        end
      end

      context 'with otp enabled' do
        before { create(:application_setting, :portability_otp) }

        it do
          number_portability_detail.valid?
          expect(number_portability_detail.errors.attribute_names).to include :otp

          number_portability_detail.otp_status == 'verified'
          number_portability_detail.valid?
          expect(number_portability_detail.errors.attribute_names).to include :otp
        end
      end
    end

    context 'very mobile phone activation' do
      let(:operator_very) { create(:operator, :very_mobile) }
      let(:source_operator_windtre) { create(:portability_operator, :mobile, :windtre) }

      before { mobile_phone_activation.update_column(:operator_id, operator_very.id) }

      it 'source_operator windtre' do
        number_portability_detail.source_operator = source_operator_windtre

        number_portability_detail.sim_serial = ''
        number_portability_detail.valid?
        expect(number_portability_detail.errors.attribute_names).to include :sim_serial

        number_portability_detail.sim_serial = '12345'
        number_portability_detail.valid?
        expect(number_portability_detail.errors.attribute_names).to include :sim_serial

        number_portability_detail.sim_serial = '1234512345123451234'
        number_portability_detail.valid?
        expect(number_portability_detail.errors.attribute_names).not_to include :sim_serial
      end

      it 'source_operator != windtre' do
        number_portability_detail.source_operator = source_operator_vodafone

        number_portability_detail.sim_serial = ''
        number_portability_detail.valid?
        expect(number_portability_detail.errors.attribute_names).not_to include :sim_serial

        number_portability_detail.sim_serial = '12345'
        number_portability_detail.valid?
        expect(number_portability_detail.errors.attribute_names).not_to include :sim_serial
      end
    end
  end

  context 'is_fissa?' do
    let(:source_operator_altro) { create(:portability_operator, :altro) }

    let(:number_portability_detail) { build_stubbed(:number_portability_detail, :fissa, source_operator: source_operator_altro) }
    before { allow(number_portability_detail).to receive(:is_fissa?).and_return true }
    let(:phone_activation_with_fissa) { create(:phone_activation, :wind_fisso) }

    before do
      number_portability_detail.phone_activation_id  = phone_activation_with_fissa.id
      number_portability_detail.phone_number         = '327327273'
      number_portability_detail.voice_migration_code = '000000000000000001A'
      number_portability_detail.contract_kind_id     = 1
      number_portability_detail.prefix               = '02'
    end

    it { expect(number_portability_detail.valid?).to be_truthy }

    it 'with not tim operator' do
      number_portability_detail.source_operator = source_operator_vodafone
      expect(number_portability_detail.valid?).to be_truthy
    end

    it 'without phone number' do
      number_portability_detail.phone_number = nil
      expect(number_portability_detail.valid?).to be_falsey
    end

    it 'without prefix' do
      number_portability_detail.prefix = nil
      expect(number_portability_detail.valid?).to be_falsey
    end

    it 'with prefix not starting with 0' do
      number_portability_detail.prefix = '22'
      expect(number_portability_detail.valid?).to be_falsey
    end

    it 'with prefix too long' do
      number_portability_detail.prefix = '022222'
      expect(number_portability_detail.valid?).to be_falsey
    end

    it 'with prefix too short' do
      number_portability_detail.prefix = '0'
      expect(number_portability_detail.valid?).to be_falsey
    end

    it 'without source_operator' do
      number_portability_detail.source_operator = nil
      expect(number_portability_detail.valid?).to be_falsey
    end

    context 'additional_associated_numbers' do
      context 'has_additional_associated_numbers == true' do
        before { number_portability_detail.has_additional_associated_numbers = '1' }

        it 'without any numbers' do
          number_portability_detail.additional_associated_numbers = {}
          expect(number_portability_detail.valid?).to be_falsey
          expect(number_portability_detail.errors.attribute_names).to include(:additional_associated_numbers_1)
          expect(number_portability_detail.errors.full_messages).to eq(['Numero 1 inserisci almeno una numerazione associata'])
        end

        it 'with wrong format numbers' do
          number_portability_detail.additional_associated_numbers = { "1": '0123456', "2": '1234' }
          expect(number_portability_detail.valid?).to be_falsey
          expect(number_portability_detail.errors.attribute_names).to include(:additional_associated_numbers_2)
          expect(number_portability_detail.errors.full_messages).to eq(['Numero 2 deve iniziare per 0 ed essere di almeno 6 cifre'])
        end

        it 'not numeric' do
          number_portability_detail.additional_associated_numbers = { "1": '0123456', "2": '1234AA' }
          expect(number_portability_detail.valid?).to be_falsey
          expect(number_portability_detail.errors.attribute_names).to include(:additional_associated_numbers_2)
          expect(number_portability_detail.errors.full_messages).to eq(['Numero 2 deve contenere solo numeri e deve iniziare per 0 ed essere di almeno 6 cifre'])
        end

        it 'correct number format' do
          number_portability_detail.additional_associated_numbers = { "1": '0123456' }
          expect(number_portability_detail.valid?).to be_truthy
        end
      end

      context 'has_additional_associated_numbers == false' do
        it 'without any numbers' do
          expect(number_portability_detail.valid?).to be_truthy
        end

        it 'with wrong format numbers' do
          number_portability_detail.additional_associated_numbers = { "1": '0123456', "2": '1234' }
          expect(number_portability_detail.valid?).to be_falsey
          expect(number_portability_detail.errors.attribute_names).to include(:additional_associated_numbers_2)
          expect(number_portability_detail.errors.full_messages).to eq(['Numero 2 deve iniziare per 0 ed essere di almeno 6 cifre'])
        end

        it 'not numeric' do
          number_portability_detail.additional_associated_numbers = { "1": '0123456', "2": '1234AA' }
          expect(number_portability_detail.valid?).to be_falsey
          expect(number_portability_detail.errors.attribute_names).to include(:additional_associated_numbers_2)
          expect(number_portability_detail.errors.full_messages).to eq(['Numero 2 deve contenere solo numeri e deve iniziare per 0 ed essere di almeno 6 cifre'])
        end

        it 'correct number format' do
          number_portability_detail.additional_associated_numbers = { "1": '0123456' }
          expect(number_portability_detail.valid?).to be_truthy
        end
      end
    end
  end

  context 'different_owner' do
    let(:number_portability_detail) do
      NumberPortabilityDetail.new(different_owner:     true,
                                  phone_activation_id: 1,
                                  phone_number:        '3334323343',
                                  source_operator:     source_operator_tim,
                                  sim_serial:          Faker::Number.number(digits: 21),
                                  contract_kind_id:    1)
    end

    context 'validates presence of owner_cf' do
      it 'without owner_cf' do
        expect(number_portability_detail.valid?).to be_falsey
        expect(number_portability_detail.errors.attribute_names).to include :owner_cf
      end

      it 'with owner_cf' do
        number_portability_detail.owner_cf = '****************'
        number_portability_detail.valid?
        expect(number_portability_detail.errors.attribute_names).not_to include :owner_cf
      end
    end

    context 'validates format of owner_cf' do
      context 'cf' do
        it do
          number_portability_detail.owner_cf = '****************'
          number_portability_detail.valid?
          expect(number_portability_detail.errors.attribute_names).not_to include :owner_cf
        end

        it do
          number_portability_detail.owner_cf = 'CSTLBT88T66M'
          expect(number_portability_detail.valid?).to be_falsey
        end
      end

      context 'vat' do
        it do
          number_portability_detail.owner_cf = '01234567890'
          number_portability_detail.valid?
          expect(number_portability_detail.errors.attribute_names).not_to include :owner_cf
        end

        it do
          number_portability_detail.owner_cf = '0123456789082639T'
          expect(number_portability_detail.valid?).to be_falsey
        end
      end
    end
  end

  context 'different_owner_is_same_as_customer?' do
    let(:phone_activation) { build_stubbed(:phone_activation) }
    let(:number_portability_detail) { build_stubbed(:number_portability_detail, :different_owner, phone_activation: phone_activation) }

    it do
      expect(number_portability_detail.different_owner_is_same_as_customer?).to be_falsey

      number_portability_detail.owner_cf = phone_activation.customer.cf
      number_portability_detail.owner_kind = 'pf'
      phone_activation.customer.customer_kind.code = 'pf'
      expect(number_portability_detail.different_owner_is_same_as_customer?).to be_truthy

      number_portability_detail.owner_kind = 'pg'
      phone_activation.customer.customer_kind.code = 'pg'
      expect(number_portability_detail.different_owner_is_same_as_customer?).to be_falsey

      number_portability_detail.owner_kind = 'pg'
      phone_activation.customer.customer_kind.code = 'pg'
      phone_activation.customer.vat = number_portability_detail.owner_vat
      expect(number_portability_detail.different_owner_is_same_as_customer?).to be_truthy

      number_portability_detail.owner_kind = 'di'
      phone_activation.customer.customer_kind.code = 'di'
      expect(number_portability_detail.different_owner_is_same_as_customer?).to be_truthy
    end
  end

  context 'before_save #normalize_adsl_migration_code and #normalize_activation_line_path' do
    let(:source_operator_tim) { create(:portability_operator, :mobile, label: 'Tim', group_value: 'TIM') }
    let(:phone_activation_with_fissa) { create(:phone_activation, :wind_fisso) }
    let(:number_portability_detail_fissa) do
      build(:number_portability_detail,
            :wind_fisso,
            source_operator:      source_operator_tim,
            phone_activation:     phone_activation_with_fissa,
            voice_migration_code: '5555550000000001B')
    end
    context 'create' do
      before { number_portability_detail_fissa.save }

      it { expect(number_portability_detail_fissa.reload.adsl_migration_code).to eq '5555550000000001B' }
      it { expect(number_portability_detail_fissa.reload.activation_line_path).to eq 'LA' }
    end

    context 'create#different_adsl_migration_code' do
      before { number_portability_detail_fissa.update(adsl_migration_code: '555555000000000001A', different_adsl_migration_code: true) }

      it { expect(number_portability_detail_fissa.reload.adsl_migration_code).to eq '555555000000000001A' }
      it { expect(number_portability_detail_fissa.reload.activation_line_path).to eq 'LA' }
    end

    context 'before_save #upcase_migration_codes' do
      before do
        number_portability_detail_fissa.update(voice_migration_code:          'bbbbbb000000000001b',
                                               adsl_migration_code:           'aaaaaa000000000001a',
                                               different_adsl_migration_code: true)
      end

      it { expect(number_portability_detail_fissa.reload.voice_migration_code).to eq 'BBBBBB000000000001B' }
      it { expect(number_portability_detail_fissa.reload.adsl_migration_code).to eq 'AAAAAA000000000001A' }
    end

    context 'cos' do
      let(:number_portability_detail) { build_stubbed(:number_portability_detail, voice_migration_code: 'BBBBBB000000000001B') }

      it { expect(number_portability_detail.cos).to eq '001' }

      context 'length < 19' do
        before { number_portability_detail.voice_migration_code = 'BBBBBB00000001B' }

        it { expect(number_portability_detail.cos).to eq '001' }
      end
    end

    context 'it save LA if source operator is TIM / TELECOM' do
      before do
        number_portability_detail_fissa.activation_line_path = nil
        number_portability_detail_fissa.source_operator      = source_operator_tim
        number_portability_detail_fissa.save
      end

      it { expect(number_portability_detail_fissa.activation_line_path).to eq 'LA' }
    end

    context 'cos only chars' do
      let(:source_operator_w3) { create(:portability_operator, :mobile, label: 'WindTre', group_value: 'W3') }
      let(:number_portability_detail) do
        build(:number_portability_detail,
              :wind_fisso,
              source_operator:      source_operator_w3,
              phone_activation:     phone_activation_with_fissa,
              voice_migration_code: '5555550000000AAAB')
      end
      before { number_portability_detail.save }

      it { expect(number_portability_detail.cos).to eq 'AAA' }
      it { expect(number_portability_detail.activation_line_path).to eq 'LA' }
    end

    context 'before_save #cleanup_unneeded' do
      let(:phone_activation) { create(:phone_activation) }
      let(:customer_doc) { create(:document, :document_file, documentable: phone_activation) }
      let(:different_owner_doc) { create(:document, :different_owner_document_file, documentable: phone_activation) }
      let(:sim_doc) { create(:document, :sim_card_file, documentable: phone_activation) }
      let(:number_portability_detail) { create(:number_portability_detail, :different_owner, phone_activation: phone_activation) }

      before do
        customer_doc
        different_owner_doc
        sim_doc
      end

      it do
        number_portability_detail.update(different_owner: false)

        expect(number_portability_detail.owner_company_name).to be_nil
        expect(number_portability_detail.owner_company_kind).to be_nil
        expect(number_portability_detail.owner_vat).to be_nil
        expect(number_portability_detail.owner_gender).to be_nil
        expect(number_portability_detail.owner_birth_date).to be_nil
        expect(number_portability_detail.owner_birth_place).to be_nil
        expect(number_portability_detail.owner_birth_province_id).to be_nil
        expect(number_portability_detail.location_address_street_type_id).to be_nil
        expect(number_portability_detail.location_address).to be_nil
        expect(number_portability_detail.location_number).to be_nil
        expect(number_portability_detail.location_zip).to be_nil
        expect(number_portability_detail.location_city).to be_nil
        expect(number_portability_detail.location_province_id).to be_nil
        expect(number_portability_detail.owner_cf).to be_nil
        expect(number_portability_detail.owner_first_name).to be_nil
        expect(number_portability_detail.owner_last_name).to be_nil
        expect(number_portability_detail.owner_document_kind).to be_nil
        expect(number_portability_detail.owner_identity_document_kind_id).to be_nil
        expect(number_portability_detail.owner_document_number).to be_nil
        expect(number_portability_detail.owner_document_date).to be_nil
        expect(number_portability_detail.owner_document_expiry).to be_nil

        expect(phone_activation.documents).to eq [customer_doc, sim_doc]
      end
    end
  end
end
