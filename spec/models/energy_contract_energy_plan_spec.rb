require 'rails_helper'

describe EnergyContractEnergyPlan do
  let(:energy_contract) { create(:energy_contract) }
  let(:energy_plan) { energy_contract_energy_plan.energy_plan }
  let(:energy_contract_energy_plan) { energy_contract.energy_contract_energy_plans.first }

  describe 'associations' do
    it { should belong_to(:energy_contract) }
    it { should belong_to(:energy_plan) }
  end

  describe 'callbacks' do
    context 'before create' do
      let(:gas_energy_plan) { create(:energy_plan, :gas) }

      it 'sets plan_stored_data' do
        new_energy_contract_energy_plan = build(:energy_contract_energy_plan, energy_contract: energy_contract, energy_plan: gas_energy_plan)
        expect(new_energy_contract_energy_plan).to receive(:set_plan_stored_data)
        new_energy_contract_energy_plan.save
      end
    end

    context 'before update' do
      let!(:report_call) { create(:energy_contract_api_call, :report, energy_contract: energy_contract, response: { "requests" => [{ "podPdr" => "IT012E00143575", "actDateAsset" => "2024-07-24" }] }) }

      it 'sets activated_at' do
        energy_contract_energy_plan.update(state: 'check_in_progress')
        expect(energy_contract_energy_plan.activated_at).to be_nil

        energy_contract_energy_plan.update(state: 'activated')
        expect(energy_contract_energy_plan.activated_at).to eq Date.new(2024, 7, 24)
      end
    end
  end

  describe 'AASM states' do
    xit 'transitions to :to_recover' do
      energy_contract_energy_plan.state = 'cancelled'
      expect(energy_contract_energy_plan).to transition_from(:cancelled).to(:to_recover).on_event(:recover)
    end

    it 'transitions to :cancelled' do
      energy_contract_energy_plan.state = 'to_recover'
      expect(energy_contract_energy_plan).to transition_from(:to_recover).to(:cancelled).on_event(:cancel)
    end
  end

  it { expect(energy_contract_energy_plan.commodity_value).to eq 'ELE' }

  describe '.energy' do
    subject { described_class.energy }

    let!(:energy_contract_plan) do
      create(:energy_contract_energy_plan, energy_contract: energy_contract, energy_plan: create(:energy_plan, kind, position: 2))
    end

    context 'with energy contract plan gas kind' do
      let(:kind) { :gas }

      it { is_expected.not_to include(energy_contract_plan) }
    end

    context 'with energy contract plan energy kind' do
      let(:kind) { :energy }

      it { is_expected.to include(energy_contract_plan) }
    end
  end

  describe '.gas' do
    subject { described_class.gas }

    let!(:energy_contract_plan) do
      create(:energy_contract_energy_plan, energy_contract: energy_contract, energy_plan: create(:energy_plan, kind))
    end

    context 'with energy contract plan gas kind' do
      let(:kind) { :gas }

      it { is_expected.to include(energy_contract_plan) }
    end

    context 'with energy contract plan energy kind' do
      let(:kind) { :energy }

      it { is_expected.not_to include(energy_contract_plan) }
    end
  end

  describe '#service_point' do
    let(:energy_contract_energy_plan_gas) { create(:energy_contract_energy_plan, :gas, energy_contract: energy_contract) }
    let!(:check_punto_cliente_call) { create(:energy_contract_api_call, :check_punto_cliente, origin: energy_contract_energy_plan_gas) }
    let!(:normalized_address_by_fields_call) { create(:energy_contract_api_call, :normalized_address_by_fields, origin: energy_contract_energy_plan_gas) }
    let!(:energy_supply_detail) { create(:energy_supply_detail, energy_contract: energy_contract_energy_plan_gas.energy_contract) }

    before { allow(energy_contract_energy_plan_gas).to receive(:check_punto_cliente_call).and_return check_punto_cliente_call }

    context 'CP_CB' do
      before { allow(check_punto_cliente_call).to receive(:check_punto_cliente_code).and_return 'CP_CB' }

      it do
        expect(energy_contract_energy_plan_gas.service_point).to eq({ commodity: 'GAS', podPdr: '12345678901234', operation: 'CP_CB', immediateActivation: true, vas: false, vulnerable: false, consumer: true })
      end
    end

    context 'CP' do
      before { allow(check_punto_cliente_call).to receive(:check_punto_cliente_code).and_return 'CP' }

      it do
        expect(energy_contract_energy_plan_gas.service_point).to eq({ commodity: 'GAS', podPdr: '12345678901234', operation: 'CP', immediateActivation: true, vas: false, vulnerable: false, consumer: true })
      end
    end

    context 'SW' do
      before { allow(check_punto_cliente_call).to receive(:check_punto_cliente_code).and_return 'SW' }

      it do
        expect(energy_contract_energy_plan_gas.service_point).to eq({ commodity:               'GAS',
                                                                      podPdr:                  '12345678901234',
                                                                      operation:               'SW',
                                                                      tensioneDiAlimentazione: '220V',
                                                                      categoriaTISG:           'Category A',
                                                                      classePrelievo:          'Class A',
                                                                      matricolaMisuratore:     '111111111',
                                                                      uso:                     'Destination A',
                                                                      categoriaUso:            'Category A',
                                                                      prelievoAnnuoPrevisto:   '1000kWh',
                                                                      ateco:                   '01.19',
                                                                      atecoDescription:        '01.19 - Floricoltura e coltivazione di altre colture non permanenti',
                                                                      address:                 { streetPrefix: 'Via', streetName: 'Gian Carlo Sismondi', streetNumberType: 'N°', streetNumber: '61', streetNumberExtension: 'A', postalCode: '20133', town: 'Milano', provinceIsoCode: 'MI', pointcadastralcode: 'F205', istatCode: '015146' },
                                                                      distributor:             'UNARETI SPA',
                                                                      distributorVatNumber:    '12883450152',
                                                                      remiCode:                '34403700',
                                                                      immediateActivation:     true,
                                                                      vas:                     false,
                                                                      consumer:                true })
      end
    end
  end
end
