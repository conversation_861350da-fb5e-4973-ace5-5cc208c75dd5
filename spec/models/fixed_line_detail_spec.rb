require 'rails_helper'

describe FixedLineDetail, type: :model do
  let(:fixed_line_detail) { create(:fixed_line_detail, location_number: '12    /  a ') }

  it { expect(fixed_line_detail.reload.location_number).to eq '12/a' }
  describe 'create' do
    let(:fixed_line_detail) { create(:fixed_line_detail, location_province: nil) }

    it do
      expect(fixed_line_detail.posng_value).to eq fixed_line_detail.city.province.posng_value
      expect(fixed_line_detail.location_province).to eq fixed_line_detail.city.province
    end
  end

  describe 'update' do
    let(:appointment_detail) { create(:appointment_detail) }
    let(:fixed_line_detail) { appointment_detail.fixed_line_detail }

    around do |example|
      Audited.auditing_enabled = true
      example.run
      Audited.auditing_enabled = false
    end

    context 'on appointment detail fields changed' do
      before { fixed_line_detail.update(location_address_street_name: 'Test address changed') }

      it do
        expect(appointment_detail.reload.location_address_street_name).to eq 'Test address changed'
        expect(appointment_detail.audits.count).to eq 2
        expect(appointment_detail.audits.last.audited_changes.has_key?('location_address_street_name')).to be_truthy
      end
    end

    context 'only on appointment detail fields changed' do
      it { expect{fixed_line_detail.update(coverage_status: 2)}.not_to change{ appointment_detail.audits.count } }
    end
  end
end
