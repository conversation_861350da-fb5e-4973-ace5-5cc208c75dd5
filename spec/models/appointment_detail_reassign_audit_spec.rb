require 'rails_helper'

RSpec.describe AppointmentDetailReassignAudit, type: :model do
  let(:appointment_detail) { create(:appointment_detail, status: 'waiting') }
  let(:from_dealer) { create(:dealer, name: 'Original Dealer') }
  let(:to_dealer) { create(:dealer, name: 'New Dealer') }
  let(:user) { create(:user, :admin) }

  describe 'associations' do
    it { is_expected.to belong_to(:appointment_detail) }
    it { is_expected.to belong_to(:from_dealer).class_name('Dealer') }
    it { is_expected.to belong_to(:to_dealer).class_name('Dealer') }
    it { is_expected.to belong_to(:user).optional }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:reassigned_at) }
  end


  describe '#changes_summary' do
    let(:audit) do
      create(:appointment_detail_reassign_audit,
             appointment_detail: appointment_detail,
             from_dealer:        from_dealer,
             to_dealer:          to_dealer,
             user:               user,
             status_before:      'waiting',
             status_after:       'set',
             reassigned_at:      Time.current)
    end

    it 'returns a summary of all changes' do
      summary = audit.changes_summary

      expect(summary).to include("Status changed from 'waiting' to 'set'")
      expect(summary).to include("Reassigned from #{from_dealer.name} to #{to_dealer.name}")
      expect(summary).to include("Reassigned by #{user.full_name}")
      expect(summary).to include("Reassigned at #{audit.reassigned_at.strftime('%Y-%m-%d %H:%M')}")
    end
  end
end