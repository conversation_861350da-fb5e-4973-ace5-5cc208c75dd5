require 'rails_helper'

describe PhoneActivation, type: :model do
  let(:phone_activation) { create(:phone_activation, :abbonamento_ricaricabile) }
  it do
    expect(phone_activation.previous_group_completed?(Operation::INSERITA_DA_PARTNER)).to be_truthy
    expect(phone_activation.previous_group_completed?(Operation::UPLOAD_DOCUMENTI)).to be_truthy

    #UPLOAD_DOCUMENTI ko
    upload_documenti_oo = create(:operation_outcome, operation_id: Operation::UPLOAD_DOCUMENTI, phone_activation: phone_activation, status: 'ko_r')
    expect(phone_activation.previous_group_completed?(Operation::CONTROLLO_DOCUMENTI)).to be_falsey

    upload_documenti_oo.update(status: 'ok')
    expect(phone_activation.previous_group_completed?(Operation::CONTROLLO_DOCUMENTI)).to be_truthy

    [Operation::INSERITA_DA_PARTNER, Operation::CONTROLLO_DOCUMENTI].each do |operation_id|
      create(:operation_outcome, operation_id: operation_id, phone_activation: phone_activation, status: 'ok')
    end

    expect(phone_activation.previous_group_completed?(Operation::ATTIVAZIONE_COMPLETATA)).to be_truthy
  end

  context 'without CONTROLLO DOCUMENTI' do
    let(:operation_controllo_amministrativo) { create(:operation, :controllo_amministrativo) }
    let(:phone_activation_kind) { phone_activation.phone_activation_kind }
    let(:operation_ids) { [Operation::INSERITA_DA_PARTNER, Operation::UPLOAD_DOCUMENTI, Operation::CONTROLLO_AMMINISTRATIVO, Operation::ATTIVAZIONE_COMPLETATA].map(&:to_s) }
    before do
      phone_activation.operator.update(operation_ids: [Operation::INSERITA_DA_PARTNER, Operation::UPLOAD_DOCUMENTI, Operation::CONTROLLO_AMMINISTRATIVO, Operation::ATTIVAZIONE_COMPLETATA])
      PhoneActivationKind.generate_or_update({ phone_activation_kind_id: phone_activation_kind.id, operation_ids: operation_ids })
      phone_activation.update(required_operations_ids: phone_activation.required_operations_exec.ids)
    end

    it 'CONTROLLO_AMMINISTRATIVO' do
      [Operation::INSERITA_DA_PARTNER, Operation::CONTROLLO_AMMINISTRATIVO].each do |operation_id|
        create(:operation_outcome, operation_id: operation_id, phone_activation: phone_activation, status: 'ok')
      end

      expect(phone_activation.previous_group_completed?(Operation::CONTROLLO_AMMINISTRATIVO)).to be_falsey

      create(:operation_outcome, operation_id: Operation::UPLOAD_DOCUMENTI, phone_activation: phone_activation, status: 'ok')

      expect(phone_activation.previous_group_completed?(Operation::CONTROLLO_AMMINISTRATIVO)).to be_truthy
    end

    context 'ATTIVAZIONE_COMPLETATA' do
      before do
        [Operation::INSERITA_DA_PARTNER, Operation::CONTROLLO_AMMINISTRATIVO].each do |operation_id|
          create(:operation_outcome, operation_id: operation_id, phone_activation: phone_activation, status: 'ok')
        end
      end

      it { expect(phone_activation.previous_group_completed?(Operation::ATTIVAZIONE_COMPLETATA)).to be_falsey }

      context 'with duplicated operation_outcome' do
        before do
          create(:operation_outcome, operation_id: Operation::CONTROLLO_AMMINISTRATIVO, phone_activation: phone_activation, status: 'ok')
        end

        it { expect(phone_activation.previous_group_completed?(Operation::ATTIVAZIONE_COMPLETATA)).to be_falsey }
      end
    end
  end

  context '#previous_group_for(operation_id)' do
    it do
      expect(phone_activation.previous_group_for(Operation::INSERITA_DA_PARTNER)).to be_zero
      expect(phone_activation.previous_group_for(Operation::ATTIVAZIONE_COMPLETATA)).to eq 1
    end
  end
end