require "rails_helper"

describe ItemOrder do
  let(:fulfill) { stub_model(<PERSON><PERSON>ill, sent_at: DateTime.parse("25/12/2019")) }
  let(:shopping_cart) { stub_model(ShoppingCart) }
  let(:shopping_cart_item) { stub_model(ShoppingCartItem, shopping_cart: shopping_cart, sci_price: 10, discount: 0, vat_type: "22") }
  let(:item_order) { stub_model(ItemOrder, fulfill: fulfill, shopping_cart_item: shopping_cart_item) }

  it { expect(item_order.price_with_vat).to eq 12.2 }
  it { expect(item_order.fulfilled_at).to eq DateTime.parse("25/12/2019") }
end
