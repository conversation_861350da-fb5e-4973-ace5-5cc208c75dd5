require "rails_helper"

describe AttivaBrand do
  describe "#toggle_visibility!" do
    let(:attiva_category)     { create(:attiva_category) }
    let(:attiva_subcategory)  { create(:attiva_subcategory) }
    let(:company_brand)       { create(:company_brand) }

    context "when visible" do
      let!(:product) do
        create(:product, :attiva_imported, can_be_requested: true, attiva_brand: attiva_brand, attiva_category: attiva_category,
                                           attiva_subcategory: attiva_subcategory, company_brand: company_brand)
      end
      let(:attiva_brand) { create(:attiva_brand, visible: true) }
      subject            { attiva_brand.toggle_visibility! }

      it "@visible become false" do
        expect(subject).to be_truthy
        expect(attiva_brand.visible).to be_falsey
      end

      it "associated products cannot be requested" do
        expect(subject).to be_truthy
        product.reload
        expect(product.can_be_requested).to be_falsey
      end
    end

    context "when not visible" do
      let!(:product) do
        create(:product, :attiva_imported, can_be_requested: false, attiva_brand: attiva_brand, attiva_category: attiva_category,
                                           attiva_subcategory: attiva_subcategory, company_brand: company_brand)
      end
      let(:attiva_brand) { create(:attiva_brand, visible: false) }
      subject            { attiva_brand.toggle_visibility! }

      it "@visible become false" do
        expect(subject).to be_truthy
        expect(attiva_brand.visible).to be_truthy
      end

      it "associated products still not requestable" do
        expect(subject).to be_truthy
        expect(product.can_be_requested).to be_falsey
      end
    end
  end
end
