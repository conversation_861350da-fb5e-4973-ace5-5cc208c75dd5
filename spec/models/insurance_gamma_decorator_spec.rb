require 'rails_helper'

describe InsuranceGammaDecorator do
  let(:insurance) do 
    create(:insurance,
            :privato,
            :galaxy,
            created_at: Date.yesterday,
            gamma_code: 'EASYCARE1000',
            product_brand: 'Samsung',
            product_model: 'S12',
            dealer_price: 229.90 )
  end

  before do 
    insurance.warehouse.update(gamma_code_dest_cli: '09876')
    insurance.dealer.update(gamma_code_cli: '12345')
  end

  context 'gamma_csv_row' do
    it do
      expect(insurance.gamma_csv_row).to eq [
        'EC',
        "#{insurance.created_at.strftime('%d/%m/%Y')}", 
        insurance.id, 
        '12345', 
        '09876', 
        '', 
        '', 
        '', 
        '', 
        "EC#{insurance.id}", 
        'Articolo', 
        "#{insurance.gamma_code}", 
        'EasyCare+ - Samsung S12',
        1, 
        '229.90', 
        '', 
        '', 
        '', 
        '', 
        '', 
        '', 
        '', 
        '', 
        "#{insurance.product_serial}"
      ]
    end
  end
end