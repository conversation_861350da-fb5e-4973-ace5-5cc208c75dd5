require 'rails_helper'

describe GammaCall do
  let(:gamma_call) { stub_model(GammaCall) }

  let(:regular_response) { File.read('spec/fixtures/gamma_calls/fulfill_to_gamma/one_document_response.xml') }
  let(:gamma_error_response) { File.read('spec/fixtures/gamma_calls/fulfill_to_gamma/gamma_error_response.xml') }
  let(:uncaught_error_response) { File.read('spec/fixtures/gamma_calls/fulfill_to_gamma/anomalous_response.xml') }

  context '#response_to_json' do
    it 'converts correctly a well formed response_xml into json' do
      gamma_call.response_xml = '<foo>bar</foo>'
      expect(gamma_call.response_to_json).to eq({ foo: 'bar' }.with_indifferent_access)
    end
  end

  context '#response' do
    let(:may_2020_export_product_response) { File.read('spec/fixtures/gamma_calls/export_to_gamma/products/from_may_2020_response.xml') }

    it 'returns ko_r for a GammaCall with no response_xml' do
      expect(gamma_call.response).to eq 'ko_r'
    end

    it 'returns ok for a GammaCall with no response_xml if the call type is compatible' do
      gamma_call.name = '_new_gamma_export_product'
      expect(gamma_call.response).to eq 'ok'

      gamma_call.name = '_new_gamma_export_dealer_anagrafica'
      expect(gamma_call.response).to eq 'ok'

      gamma_call.name = '_new_gamma_export_dealer_cliente'
      expect(gamma_call.response).to eq 'ok'

      gamma_call.name = '_new_gamma_export_dealer_fornitore'
      expect(gamma_call.response).to eq 'ok'

      gamma_call.name = '_new_gamma_export_dealer_destinazione_cliente'
      expect(gamma_call.response).to eq 'ok'

      gamma_call.name = '_new_gamma_export_dealer_destinazione_fornitore'
      expect(gamma_call.response).to eq 'ok'
    end

    context 'Trivial responses' do
      let(:may_2020_export_product_response) { File.read('spec/fixtures/gamma_calls/export_to_gamma/products/from_may_2020_response.xml') }
      let(:create_general_anagraphics_response_response) do
        File.read('spec/fixtures/gamma_calls/export_to_gamma/dealers/create_general_anagraphics_response.xml')
      end
      let(:create_client_response) do
        File.read('spec/fixtures/gamma_calls/export_to_gamma/dealers/create_client_response.xml')
      end
      let(:create_supplier_response) do
        File.read('spec/fixtures/gamma_calls/export_to_gamma/dealers/create_supplier_response.xml')
      end
      let(:create_destination_response) do
        File.read('spec/fixtures/gamma_calls/export_to_gamma/dealers/create_destination_response.xml')
      end

      it 'returns ok for a GammaCall with trivial response_xml if the call type is compatible' do
        gamma_call.name = '_new_gamma_export_product'
        gamma_call.response_xml = may_2020_export_product_response
        expect(gamma_call.response).to eq 'ok'

        gamma_call.name = '_new_gamma_export_dealer_anagrafica'
        gamma_call.response_xml = create_general_anagraphics_response_response
        expect(gamma_call.response).to eq 'ok'

        gamma_call.name = '_new_gamma_export_dealer_cliente'
        gamma_call.response_xml = create_client_response
        expect(gamma_call.response).to eq 'ok'

        gamma_call.name = '_new_gamma_export_dealer_fornitore'
        gamma_call.response_xml = create_supplier_response
        expect(gamma_call.response).to eq 'ok'

        gamma_call.name = '_new_gamma_export_dealer_destinazione_cliente'
        gamma_call.response_xml = create_destination_response
        expect(gamma_call.response).to eq 'ok'

        gamma_call.name = '_new_gamma_export_dealer_destinazione_fornitore'
        gamma_call.response_xml = create_destination_response
        expect(gamma_call.response).to eq 'ok'
      end
    end

    it 'returns ko_r for a GammaCall in timeout' do
      gamma_call.response_xml = '<serious_errors>Request Timeout</serious_errors>'
      expect(gamma_call.response).to eq 'ko_r'
    end

    it 'returns ko_r for a GammaCall responding with an error from Gamma' do
      gamma_call.response_xml = gamma_error_response
      expect(gamma_call.response).to eq 'ko_r'
    end

    it 'returns ko_r for a GammaCall responding with an error uncaught by Gamma' do
      gamma_call.response_xml = uncaught_error_response
      expect(gamma_call.response).to eq 'ko_r'
    end

    it 'returns ok for a successful GammaCall' do
      gamma_call.response_xml = regular_response
      expect(gamma_call.response).to eq 'ok'
    end

    it 'returns ok for a manually confirmed GammaCall' do
      gamma_call.manually_confirmed_at = 10.minutes.ago
      expect(gamma_call.response).to eq 'ok'
    end
  end

  context '#manually_confirmable?' do
    let(:phone_activation) { stub_model(PhoneActivation) }
    let(:order) { stub_model(Order) }
    before { allow(gamma_call).to receive(:response).and_return 'ko_r' }

    it 'returns trure when caller is a PhoneActivation' do
      gamma_call.update_attribute(:caller, phone_activation)
      expect(gamma_call.manually_confirmable?).to be_truthy
    end

    it 'returns false for a successful GammaCall' do
      gamma_call.update_attribute(:caller, phone_activation)
      allow(gamma_call).to receive(:response).and_return 'ok'
      expect(gamma_call.manually_confirmable?).to be_falsey
    end

    it 'returns false when caller is not a PhoneActivation' do
      gamma_call.update_attribute(:caller, order)
      expect(gamma_call.manually_confirmable?).to be_falsey
    end
  end

  context '#relaunchable' do
    it 'returns true for a runnable unsuccessful GammaCall not yet relaunched' do
      allow(gamma_call).to receive(:runnable?).and_return(true)
      allow(gamma_call).to receive(:response).and_return 'ko_r'
      expect(gamma_call.relaunchable?).to be_truthy
    end

    it 'returns false for a non-runnable GammaCall' do
      allow(gamma_call).to receive(:runnable?).and_return(false)
      allow(gamma_call).to receive(:response).and_return 'ko_r'
      expect(gamma_call.relaunchable?).to be_falsey
    end

    it 'returns false for a successful GammaCall' do
      allow(gamma_call).to receive(:runnable?).and_return(true)
      allow(gamma_call).to receive(:response).and_return 'ok'
      expect(gamma_call.relaunchable?).to be_falsey
    end

    it 'returns false for an already relaunched GammaCall' do
      allow(gamma_call).to receive(:runnable?).and_return(true)
      allow(gamma_call).to receive(:response).and_return 'ko_r'
      gamma_call.relaunched_at = 10.minutes.ago
      expect(gamma_call.relaunchable?).to be_falsey
    end
  end

  context '#outcome' do
    it 'returns ko for a GammaCall returning ko_r to #response' do
      allow(gamma_call).to receive(:response).and_return 'ko_r'
      expect(gamma_call.outcome).to eq 'ko'
    end

    it 'returns ok for a GammaCall returning ok to #response' do
      allow(gamma_call).to receive(:response).and_return 'ok'
      expect(gamma_call.outcome).to eq 'ok'
    end
  end
end