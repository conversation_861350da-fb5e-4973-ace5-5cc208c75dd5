require "rails_helper"

describe 'number_portability_details/new.html.erb', type: :view do
  context "mobile" do
    let(:phone_activation) { create(:phone_activation, :abbonamento_ricaricabile) }
    let(:number_portability_detail) { build_stubbed(:number_portability_detail, phone_activation: phone_activation) }
    let(:cost_matrix) { build_stubbed(:cost_matrix) }

    before { allow(CostMatrix).to receive(:for).and_return cost_matrix }
    before{ assign(:number_portability_detail, number_portability_detail) }
    before{ assign(:phone_activation, phone_activation) }

    before { render }


    it { expect(rendered).not_to be_nil }
    it { expect(rendered).not_to include("Altre numerazioni associate (esclusa la seconda linea)") }
    it { expect(rendered).to include("Operatore di provenienza") }
  end
end