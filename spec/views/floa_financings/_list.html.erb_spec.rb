require 'rails_helper'

describe 'views/floa_financings/_list', type: :view do
  let(:dealer_user) { create(:user, :dealer_user, :with_dealer_contact) }
  let(:admin_user) { create(:user, :admin, :with_internal_user_detail) }
  let(:agent_user) { build_stubbed(:user, :agent) }
  let(:floa_financing) { build_stubbed(:floa_financing, id: '1234') }

  before { allow(view).to receive(:can?).and_return true }

  subject { render partial: 'floa_financings/list', locals: { type: 'pending', floa_financings: [floa_financing] } }

  context 'dealer user' do
    before { allow(view).to receive(:current_user).and_return dealer_user }

    it 'without admin access_flag' do
      subject
      expect(rendered).to include '#'
      expect(rendered).to include '1234'
      expect(rendered).not_to include 'Partner'
      expect(rendered).not_to include 'Margine'

      expect(rendered).not_to include "href=\"/customers/"
      expect(rendered).not_to include "href=\"/dealers/"
    end

    it 'with admin access_flag' do
      allow(dealer_user.dealer_contact).to receive(:can_access_admin?).and_return true
      subject
      expect(rendered).to include '#'
      expect(rendered).to include 'Margine'

      expect(rendered).not_to include "href=\"/customers/"
      expect(rendered).not_to include "href=\"/dealers/"
    end
  end

  context 'admin user' do
    before { allow(view).to receive(:current_user).and_return admin_user }

    it do
      subject
      expect(rendered).to include '#'
      expect(rendered).to include '1234'
      expect(rendered).to include 'Partner'
      expect(rendered).to include floa_financing.dealer.name

      expect(rendered).not_to include "href=\"/customers/"
      expect(rendered).to include "href=\"/dealers/"
    end

    it 'with customer access_flag' do
      allow(admin_user.internal_user_detail).to receive(:can_access_customers?).and_return true
      subject
      expect(rendered).to include "href=\"/customers/"
      expect(rendered).to include "href=\"/dealers/"
    end
  end

  it 'agent user' do
    allow(view).to receive(:current_user).and_return agent_user
    subject
    expect(rendered).not_to include "href=\"/customers/"
    expect(rendered).to include "href=\"/dealers/"
  end
end

