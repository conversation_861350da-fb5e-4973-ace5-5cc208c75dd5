require 'rails_helper'

describe 'application_settings/edit.html.erb', type: :view do
  let(:admin) { build_stubbed(:user, :admin) }

  context 'promo_device_8mld' do
    let!(:promo_device_8mld) { create(:application_setting, :promo_device_8mld) }
    before do
      allow(view).to receive(:current_user) { admin }
      allow(view).to receive(:can?).and_return true
      assign(:application_setting, promo_device_8mld)
    end

    before { render }

    it 'render promo_device_8mld' do
      expect(rendered).to have_content(promo_device_8mld.description)
      expect(rendered).not_to include('textarea maxlength="160"')
    end
  end

  context 'promo_device_very_text' do
    let!(:promo_device_very_text) { create(:application_setting, :promo_device_very_text) }
    before do
      allow(view).to receive(:current_user) { admin }
      allow(view).to receive(:can?).and_return true
      assign(:application_setting, promo_device_very_text)
    end

    before { render }

    it 'render promo device very text with 160 chars limit ' do
      expect(rendered).to have_content(promo_device_very_text.description)
      expect(rendered).to include('textarea maxlength="160"')
    end
  end
end
