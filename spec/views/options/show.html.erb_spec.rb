require "rails_helper"

describe "show", type: :view do

  let(:admin) { stub_model(User, role: "admin") }

  before { allow(view).to receive(:can?).and_return true }
  before { allow(view).to receive(:current_user).and_return admin }
  before { assign(:option, stub_model(Option, rpa_ready: true)) }
  before { render template: "options/show" }


  it do
    expect(rendered.include?("RPA ready")).to be_truthy
    expect(rendered.include?("Contestuale")).to be_truthy
  end
end