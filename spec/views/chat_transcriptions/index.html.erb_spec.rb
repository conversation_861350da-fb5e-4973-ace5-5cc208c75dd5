require "rails_helper"
require "will_paginate/array"

describe "/chat_transcriptions/index.html.erb", type: :view do
  let(:admin) { stub_model(User, role: "admin") }
  let(:user) { build_stubbed(:user, :role_user) }

  let(:chat_transcription) do
    build_stubbed(:chat_transcription,
           created_at: Time.parse("24/02/2020 12:00"),
           duration:   15,
           dealer_ip:  "123.123.123",
           messages:   "Test Account: How may i help you today? [01-01-2020 22:31:31]\r\n\
                       Test Account:     test5 [09-01-2020     22:31:32]\r\n\
                       Test Account: hi [09-01-2020 22:31:33]\r\n")
  end

  let(:minimal_chat_transcription) do
    build_stubbed(
      :chat_transcription,
      user:              user,
      chat_session_id:   nil,
      dealer_contact_id: nil,
      user_id:           nil,
      site_id:           nil,
      dealer_ip:         nil,
      language:          nil,
      messages:          nil,
      date:              nil,
      duration:          nil,
      total_duration:    nil,
      returned_value:    nil,
      question:          nil,
      dealer_id:         nil
    )
  end

  before do
    assign(:chat_transcriptions, [chat_transcription, minimal_chat_transcription].paginate)
    allow(view).to receive(:can?).and_return true
    allow(view).to receive(:page_entries_info) { "" }
    render
  end

  it do
    expect(rendered).to have_text "ID"
    expect(rendered).to have_text chat_transcription.id.to_s
    expect(rendered).to have_text minimal_chat_transcription.id.to_s
    expect(rendered).to have_text "Data"
    expect(rendered).to have_text "24/02/2020"
    expect(rendered).to have_text "Partner"
    expect(rendered).to have_text chat_transcription.dealer_name
    expect(rendered).to have_text "Contatto"
    expect(rendered).to have_text chat_transcription.dealer_contact_full_name
    expect(rendered).to have_text "Operatore"
    expect(rendered).to have_text chat_transcription.internal_user_full_name
    expect(rendered).to have_text "Durata"
    expect(rendered).to have_text "15"
  end
end