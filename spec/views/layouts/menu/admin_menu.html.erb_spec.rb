require 'rails_helper'

describe 'layouts/menu/_admin_menu', type: :view do
  context 'admin_menu' do
    let(:admin) { build_stubbed(:user, :admin) }

    before do
      stub_authentication admin

      allow(view).to receive(:can?).and_return true
      allow(view).to receive(:action_name).and_return 'action_name'

      setup_application_settings
      render
    end

    it do
      expect(rendered).to include 'Scheda Partner'
      expect(rendered).to include 'Cessioni Credito'
      expect(rendered).not_to include 'Prenotazioni'
      expect(rendered).to include 'Lok-me'
      expect(rendered).not_to include 'Finanziamento'
      expect(rendered).to include 'Resolvo'
      expect(rendered).to include 'Firme contrattuali'
      expect(rendered).to include 'Ordini Fornitore'
      expect(rendered).to include 'Promo Device'
      expect(rendered).to include 'Affiliazioni'
    end
  end
end

