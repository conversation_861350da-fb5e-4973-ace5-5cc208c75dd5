require 'rails_helper'

describe '/recharge_sizes/_form.html.erb', type: :view do
  before { assign(:recharge_size, RechargeSize.new) }
  before { render }

  it { expect(rendered).to have_text('Operatore aggregato') }
  it { expect(rendered).to have_text('Nome taglio di ricarica') }
  it { expect(rendered).to have_text('Visibile nel modulo ricariche') }
  it { expect(rendered).to have_text('Operatore ricarica') }
  it { expect(rendered).to have_text('Tipologia IVA') }
  it { expect(rendered).to have_text('Note') }
end