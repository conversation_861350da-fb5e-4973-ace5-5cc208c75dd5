require 'rails_helper'

describe 'dealers/_dealer_summary_anagrafica', type: :view do
  let!(:km_9mld_area1) { create(:application_setting, :km_9mld_area1) }
  let(:dealer) { build_stubbed(:dealer) }

  before do
    allow(dealer).to receive(:warehouses).and_return([build_stubbed(:warehouse, tre_code: '1234545')])
    assign(:dealer, dealer)
  end

  context 'with basic dealer information' do
    before do
      dealer.ragione_sociale = 'Test Dealer'
      dealer.kind = 'generic'
      render
    end

    it 'displays the dealer name' do
      expect(rendered).to have_text('Test Dealer')
      expect(rendered).to have_text('1234545')
      expect(rendered).not_to have_text(km_9mld_area1.value)
    end

    context 'kolme master' do
      before do
        dealer.kind = 'kolme_master'
        render
      end

      it { expect(rendered).to have_text('1234545') }
      it { expect(rendered).to have_text(km_9mld_area1.value) }
    end

    it 'displays the dealer kind' do
      expect(rendered).to have_text(Dealer.human_attribute_name('dealer'))
    end
  end
end