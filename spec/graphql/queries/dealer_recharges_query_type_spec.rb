require 'rails_helper'

describe Types::QueryType do
  let(:dealer_user) { create(:user, :dealer_contact_access_recharges) }
  let(:query) do
    %(query {
      recharges {
        id
        status
        phoneNumber
        rechargeSize
        dateTimeUtc
        notes
        receiptBody
      }
    })
  end

  subject(:result) do
    SpazioSchema.execute(query, context: context).as_json
  end

  context 'When not logged in' do
    let(:context) { { current_user: nil } }

    it 'returns a not authorized error' do
      expect(result['errors'].first['message']).to eq 'Non sei autorizzato ad accedere a questa funzionalità'
      expect(result['errors'].first['extensions']['code']).to eq 'AUTHENTICATION_ERROR'
      expect(result.dig('data', 'recharges')).to be_nil
    end
  end

  context 'When not authorized' do
    let(:context) { { current_user: dealer_user } }

    before { dealer_user.dealer_contact.access_flags.delete_all }

    it 'returns a not authorized error' do
      expect(result['errors'].first['message']).to eq 'Non sei autorizzato ad accedere a questa funzionalità'
      expect(result['errors'].first.dig('extensions', 'code')).to eq 'AUTHORIZATION_ERROR'
      expect(result.dig('data', 'recharges')).to be_nil
    end
  end

  context 'When logged in and authorized' do
    let(:context) { { current_user: dealer_user } }
    let(:repo) { double }
    let(:wind_creation_time) { Time.new(2020, 7, 14, 15, 38) }
    let(:tre_creation_time) { Time.new(2020, 6, 29, 11, 14) }
    let(:pin_creation_time) { Time.new(2020, 5, 20, 14, 7) }
    let(:paymat_operator) { build_stubbed(:paymat_operator, :tim) }
    let(:wind_recharge) { build_stubbed(:recharge, :manual_wind, created_at: wind_creation_time) }
    let(:tre_recharge) { build_stubbed(:recharge, :automatic_tre, created_at: tre_creation_time) }
    let(:pin_recharge) { build_stubbed(:recharge, :pin_vodafone, created_at: pin_creation_time) }
    let!(:wind_euronet_transaction) { create(:euronet_transaction, :check_request, recharge: wind_recharge, expiration: 1.day.from_now) }
    let!(:tre_euronet_transaction) { create(:euronet_transaction, :check_request, recharge: tre_recharge, expiration: 1.day.from_now) }
    let!(:pin_euronet_transaction) { create(:euronet_transaction, :check_request, recharge: pin_recharge, expiration: 1.day.from_now) }
    let(:renderer) { ApplicationController.render_in_api_for(dealer_user) }

    before do
      pin_recharge.recharge_size.paymat_operator = paymat_operator
      allow(Repositories::RechargeRepository).to receive(:new).and_return repo
      allow(repo).to receive_message_chain(:find_for, :results).and_return [wind_recharge, tre_recharge, pin_recharge]
    end

    context 'get the last 50 recharges for the current user' do
      before do
        expect(Repositories::RechargeRepository).to receive(:new).with(current_user: dealer_user)
        expect(repo).to receive(:find_for).with({ per_page: 50 })
      end

      it do        
        expect(result['errors']).to be_nil
        expect(result['data']['recharges'].first['id']).to eq wind_recharge.id
        expect(result['data']['recharges'].first['status']).to eq wind_recharge.present_status
        expect(result['data']['recharges'].first['phoneNumber']).to eq wind_recharge.phone_number
        expect(result['data']['recharges'].first['rechargeSize']).to eq wind_recharge.recharge_size.name
        expect(result['data']['recharges'].first['dateTimeUtc']).to eq wind_creation_time.utc.strftime('%Y-%m-%dT%H:%M:%S')
        expect(result['data']['recharges'].first['notes']).to eq wind_recharge.notes
        expect(result['data']['recharges'].first['receiptBody']).to be_nil

        expect(result['data']['recharges'].second['id']).to eq tre_recharge.id
        expect(result['data']['recharges'].second['status']).to eq tre_recharge.present_status
        expect(result['data']['recharges'].second['phoneNumber']).to eq tre_recharge.phone_number
        expect(result['data']['recharges'].second['rechargeSize']).to eq tre_recharge.recharge_size.name
        expect(result['data']['recharges'].second['dateTimeUtc']).to eq tre_creation_time.utc.strftime('%Y-%m-%dT%H:%M:%S')
        expect(result['data']['recharges'].second['notes']).to eq tre_recharge.notes
        expect(result['data']['recharges'].second['receiptBody']).to be_nil

        expect(result['data']['recharges'].third['id']).to eq pin_recharge.id
        expect(result['data']['recharges'].third['status']).to eq pin_recharge.present_status
        expect(result['data']['recharges'].third['phoneNumber']).to eq 'Ricarica a PIN'
        expect(result['data']['recharges'].third['rechargeSize']).to eq pin_recharge.recharge_size.name
        expect(result['data']['recharges'].third['dateTimeUtc']).to eq pin_creation_time.utc.strftime('%Y-%m-%dT%H:%M:%S')
        expect(result['data']['recharges'].third['notes']).to eq pin_recharge.notes
        expect(result['data']['recharges'].third['receiptBody']).not_to be_nil
      end
    end

    context 'get the last 50 recharges to a given number for the current user' do
      let(:query) do
        %(query {
          recharges(phoneNumber: "3335226207") {
            id
            status
            phoneNumber
            rechargeSize
            dateTimeUtc
          }
        })
      end

      before do
        expect(Repositories::RechargeRepository).to receive(:new).with(current_user: dealer_user)
        expect(repo).to receive(:find_for).with({
                                                  search:   { phone_number: '3335226207' },
                                                  per_page: 50
                                                })
      end

      it do
        expect(result['errors']).to be_nil
        expect(result.dig('data', 'recharges'))
          .to match_array [
            {
              'id'           => wind_recharge.id,
              'status'       => wind_recharge.present_status,
              'phoneNumber'  => wind_recharge.phone_number,
              'rechargeSize' => wind_recharge.recharge_size.name,
              'dateTimeUtc'  => wind_creation_time.utc.strftime('%Y-%m-%dT%H:%M:%S')
            },
            {
              'id'           => tre_recharge.id,
              'status'       => tre_recharge.present_status,
              'phoneNumber'  => tre_recharge.phone_number,
              'rechargeSize' => tre_recharge.recharge_size.name,
              'dateTimeUtc'  => tre_creation_time.utc.strftime('%Y-%m-%dT%H:%M:%S')
            },
            {
              'id'           => pin_recharge.id,
              'status'       => pin_recharge.present_status,
              'phoneNumber'  => 'Ricarica a PIN',
              'rechargeSize' => pin_recharge.recharge_size.name,
              'dateTimeUtc'  => pin_creation_time.utc.strftime('%Y-%m-%dT%H:%M:%S')
            }
          ]
      end
    end

    context 'get the last N recharges for the current user' do
      let(:query) do
        %(query {
          recharges(returnedRechargesNumber: 35) {
            id
            status
            phoneNumber
            rechargeSize
            dateTimeUtc
          }
        })
      end

      before do
        expect(Repositories::RechargeRepository).to receive(:new).with(current_user: dealer_user)
        expect(repo).to receive(:find_for).with({ per_page: 35 })
      end

      it do
        expect(result['errors']).to be_nil
        expect(result.dig('data', 'recharges'))
          .to match_array [
            {
              'id'           => wind_recharge.id,
              'status'       => wind_recharge.present_status,
              'phoneNumber'  => wind_recharge.phone_number,
              'rechargeSize' => wind_recharge.recharge_size.name,
              'dateTimeUtc'  => wind_creation_time.utc.strftime('%Y-%m-%dT%H:%M:%S')
            },
            {
              'id'           => tre_recharge.id,
              'status'       => tre_recharge.present_status,
              'phoneNumber'  => tre_recharge.phone_number,
              'rechargeSize' => tre_recharge.recharge_size.name,
              'dateTimeUtc'  => tre_creation_time.utc.strftime('%Y-%m-%dT%H:%M:%S')
            },
            {
              'id'           => pin_recharge.id,
              'status'       => pin_recharge.present_status,
              'phoneNumber'  => 'Ricarica a PIN',
              'rechargeSize' => pin_recharge.recharge_size.name,
              'dateTimeUtc'  => pin_creation_time.utc.strftime('%Y-%m-%dT%H:%M:%S')
            }
          ]
      end
    end
  end
end