require "rails_helper"

describe Types::QueryType do
  let(:dealer_user) { create(:user, :dealer_contact_access_activations, :dealer_contact_access_orders) }
  let!(:reservable_product) { create(:product, :lumia_dhl) }
  let!(:curtailed_reservable_product) do
    create(:product, :lumia_dhl,
           code:                       "#{reservable_product.code} curtailed",
           company_brand:              reservable_product.company_brand,
           imei_reservation_curtailed: true,
           uuid:                       "96554495-e9f2-4c0b-8ba3-4271085437a5")
  end
  let!(:non_reservable_product) { create(:product, :lumia_dhl_outside_pigeon_house) }

  let(:context) do
    { current_user: dealer_user }
  end

  subject(:result) do
    SpazioSchema.execute(query, context: context).as_json
  end

  context "retrieves all reservable products" do
    let(:query) do
      %(query {
        lokmeProducts {
          id
          nameForImeiReservation
          imeiReservationCurtailed
          brand {
            id
            description
          }
        }
      })
    end

    it do
      expect(result["data"]["lokmeProducts"].size).to eq 2
      expect(result["data"]["lokmeProducts"].first["id"]).to eq reservable_product.id.to_s
      expect(result["data"]["lokmeProducts"].first["nameForImeiReservation"]).to eq reservable_product.imei_reservations_full_name.to_s
      expect(result["data"]["lokmeProducts"].first["imeiReservationCurtailed"]).to eq false
      expect(result["data"]["lokmeProducts"].first["brand"]["id"]).to eq reservable_product.company_brand.id.to_s
      expect(result["data"]["lokmeProducts"].first["brand"]["description"]).to eq reservable_product.company_brand.description.to_s
      expect(result["data"]["lokmeProducts"].second["imeiReservationCurtailed"]).to eq true
    end
  end

  context "retrieves reservable products filtered by brand" do
    let(:query) do
      %(query {
        lokmeProducts(brandId: "#{reservable_product.company_brand.id}") {
          id
          nameForImeiReservation
          imeiReservationCurtailed
          brand {
            id
            description
          }
        }
      })
    end

    it do
      expect(result["data"]["lokmeProducts"].size).to eq 2
      expect(result["data"]["lokmeProducts"].first["id"]).to eq reservable_product.id.to_s
      expect(result["data"]["lokmeProducts"].first["nameForImeiReservation"]).to eq reservable_product.imei_reservations_full_name.to_s
      expect(result["data"]["lokmeProducts"].first["brand"]["id"]).to eq reservable_product.company_brand.id.to_s
      expect(result["data"]["lokmeProducts"].first["brand"]["description"]).to eq reservable_product.company_brand.description.to_s
      expect(result["data"]["lokmeProducts"].second["imeiReservationCurtailed"]).to eq true
    end
  end
end