require 'rails_helper'

describe Types::QueryType do
  before do
    create(:city, :milano)
    create(:city, :sesto_san_giovanni)
    create(:city, :genova)
    create(:city, :barcellona_pozzo_digotto)
    create(:city, :borgarello)
  end

  let(:context) { { current_user: current_user } }

  shared_examples_for 'a cities query' do |description|
    let(:query) do
      %(query {
        cities(description: "#{description}") {
          id
          code
          description
          createdAt
          updatedAt
          population
          disadvantagedLocation
          zipCode
          province {
            id
            code
            description
            createdAt
            updatedAt
            regionId
            areaCode
            h3gAreaCode
            position
            googleDescription
            peoplesoftCode
          }
        }
      })
    end

    subject(:result) { SpazioSchema.execute(query, context: context).as_json }

    it 'returns filtered cities' do
      expect(result['data']['cities']).to match_array(
        City.where('LOWER(description) LIKE ?', "%#{description.downcase}%").map do |city|
          {
            'id' => city.id.to_s,
            'code' => city.code,
            'description' => city.description,
            'createdAt' => city.created_at.iso8601,
            'updatedAt' => city.updated_at.iso8601,
            'population' => city.population,
            'disadvantagedLocation' => city.disadvantaged_location,
            'zipCode' => city.zip_code,
            'province' => {
              'id' => city.province.id.to_s,
              'code' => city.province.code,
              'description' => city.province.description,
              'createdAt' => city.province.created_at.iso8601,
              'updatedAt' => city.province.updated_at.iso8601,
              'regionId' => city.province.region_id,
              'areaCode' => city.province.area_code,
              'h3gAreaCode' => city.province.h3g_area_code,
              'position' => city.province.position,
              'googleDescription' => city.province.google_description,
              'peoplesoftCode' => city.province.peoplesoft_code
            }
          }
        end
      )
    end
  end

  context 'When not logged in' do
    let(:current_user) { nil }

    subject(:result) { SpazioSchema.execute(%(query { cities { id } }), context: context).as_json }

    it 'returns a not authorized error' do
      expect(result['errors'].first['message']).to eq 'Non sei autorizzato ad accedere a questa funzionalità'
      expect(result['errors'].first['extensions']['code']).to eq 'AUTHENTICATION_ERROR'
      expect(result.dig('data', 'cities')).to be_nil
    end
  end

  context 'When logged in' do
    let(:current_user) { create(:user, :admin) }

    context 'when no filters are passed' do
      let(:query) do
        %(query {
          cities {
            id
            code
            description
            createdAt
            updatedAt
            population
            disadvantagedLocation
            zipCode
            province {
              id
              code
              description
              createdAt
              updatedAt
              regionId
              areaCode
              h3gAreaCode
              position
              googleDescription
              peoplesoftCode
            }
          }
        })
      end

      subject(:result) { SpazioSchema.execute(query, context: context).as_json }

      it 'returns all cities' do
        expect(result['data']['cities']).to match_array(
          City.find_each.map do |city|
            {
              'id' => city.id.to_s,
              'code' => city.code,
              'description' => city.description,
              'createdAt' => city.created_at.iso8601,
              'updatedAt' => city.updated_at.iso8601,
              'population' => city.population,
              'disadvantagedLocation' => city.disadvantaged_location,
              'zipCode' => city.zip_code,
              'province' => {
                'id' => city.province.id.to_s,
                'code' => city.province.code,
                'description' => city.province.description,
                'createdAt' => city.province.created_at.iso8601,
                'updatedAt' => city.province.updated_at.iso8601,
                'regionId' => city.province.region_id,
                'areaCode' => city.province.area_code,
                'h3gAreaCode' => city.province.h3g_area_code,
                'position' => city.province.position,
                'googleDescription' => city.province.google_description,
                'peoplesoftCode' => city.province.peoplesoft_code
              }
            }
          end
        )
      end
    end

    context 'when filters are passed' do
      ['Milano', 'milano'].each do |description|
        include_examples 'a cities query', description
      end
    end
  end
end
