require 'rails_helper'

describe Types::QueryType do
  let(:dealer_user) { create(:user, :dealer_user) }
  let(:order) { create(:order, aasm_state: 'awaiting_confirmation') }
  let(:alert) do
    create(:alert, :with_dealer_and_warehouse, :order_payment_upload, todo: true, alertable: order)
  end

  let(:context) do
    { current_user: dealer_user }
  end

  let(:query) do
    %(query {
      alertDetail(id: "#{alert.id}") {
        id
        actionData {
          modelType
          modelId
          modelData {
            ... on AlertOrderData {
              koOperatorNote
              toPayAmount
              uploadData {
                uploadContext
                documentTypes
              }
            }
          }
        }
      }
    })
  end

  subject(:result) do
    SpazioSchema.execute(query, context: context).as_json
  end

  before { allow(alert).to receive(:alertable).and_return order }
  before { allow(order).to receive(:to_pay_amount).and_return 112.89 }

  context 'Success' do
    before { allow(Alert).to receive(:find).and_return alert }

    it 'asks to upload the payment proof' do
      expect(result['data']['alertDetail']['id']).to eq alert.id.to_s

      action_data = result['data']['alertDetail']['actionData']
      expect(action_data['modelType']).to eq 'Order'
      expect(action_data['modelId']).to eq order.id.to_s

      order_data = action_data['modelData']
      expect(order_data['koOperatorNote']).to be_nil
      expect(order_data['toPayAmount']).to eq 112.89

      upload_data = order_data['uploadData']
      expect(upload_data['uploadContext']).to eq Settings.document_uploader.orders.contexts.keys.first.to_s
      expect(upload_data['documentTypes']).to eq Settings.document_uploader.orders.contexts.upload.fields
    end
  end

  context 'When not logged in' do
    let(:context) do
      { current_user: nil }
    end

    it 'returns a not authorized error' do
      expect(result['errors'].first['message']).to eq 'Non sei autorizzato ad accedere a questa funzionalità'
      expect(result['errors'].first['extensions']['code']).to eq 'AUTHENTICATION_ERROR'
      expect(result.dig('data', 'user')).to be_nil
    end
  end

  context 'when the alert is already archived' do
    before { alert.update_columns(archived: true) }

    it 'returns an error' do
      expect(result.dig('data')).to be_nil
      expect(result.dig('errors').first).to include('message' => 'Il caricamento è già stato effettuato.')
    end
  end

  context 'unknown id' do
    let(:query) do
      %(query {
        alertDetail(id: "#{alert.id + 1}") {
          id
        }
      })
    end

    it do
      expect(result['errors'].first['message']).to eq "Couldn't find Alert with 'id'=#{alert.id + 1}"
    end
  end

  context 'with previously rejected payment proof' do
    before do
      allow(Alert).to receive(:find).and_return alert
      expect(Alerts::OrderAlertMessage).to receive_message_chain(:new, :insert_message)
      order.reject_bank_transfer_file!
      order.upload_audits.create(user_id: 1, motivation: 'file illeggibile')
    end

    it 'send rejecting motivation' do
      expect(result['data']['alertDetail']['id']).to eq alert.id.to_s

      action_data = result['data']['alertDetail']['actionData']
      expect(action_data['modelType']).to eq 'Order'
      expect(action_data['modelId']).to eq order.id.to_s

      order_data = action_data['modelData']
      expect(order_data['koOperatorNote']).to eq 'file illeggibile'
      expect(order_data['toPayAmount']).to eq 112.89

      upload_data = order_data['uploadData']
      expect(upload_data['uploadContext']).to eq Settings.document_uploader.orders.contexts.keys.first.to_s
      expect(upload_data['documentTypes']).to eq Settings.document_uploader.orders.contexts.upload.fields
    end
  end
end