require 'rails_helper'

describe Types::QueryType do
  let!(:application_setting_otp) { create(:application_setting, :portability_otp, value: '0') }
  let(:dealer_user) { create(:user, :dealer_user) }
  let(:phone_activation) { create(:phone_activation, :abbonamento_ricaricabile) }
  let!(:eta_cluster_option) { create(:cluster_option, :age) }
  let!(:estera_generica_cluster_option) { create(:cluster_option, :generic_nationality) }
  let!(:estera_specifica_cluster_option) { create(:cluster_option, :specific_nationality) }
  let!(:eta_e_nazionalita_generic_cluster_option) { create(:cluster_option, :age_and_nationality_generic_nationality) }
  let!(:eta_e_nazionalita_specific_cluster_option) { create(:cluster_option, :age_and_nationality_specific_nationality) }
  let!(:plan_cluster_option) { create(:plan_cluster_option, plan: phone_activation.plan) }

  let(:context) do
    { current_user: dealer_user }
  end

  before do
    phone_activation.customer.update_column(:state, 'draft')
  end

  let(:query) do
    %(query {
      phoneActivation(id: "#{phone_activation.id}") {
        id
        hexId
        inInstallments
        customer {
          firstName
        }
        numberPortabilityDetail {
          phoneNumber
          locationAddressStreetTypeId
          locationAddress
          locationNumber
          locationZip
          locationCity
          locationProvinceId
        }
        requestedDocuments {
          docDescriptor
          documentName
          acceptedDocuments
          additionalDocuments {
            documentName
            requiredIf
            enableTextInput
            frontOnly
            documentType
          }
          pictureOnly
          conditions
          validationRules
        }
        lastKoReason {
          id
          category
        }
      }
    })
  end

  subject(:result) { SpazioSchema.execute(query, context: context).as_json }

  it 'returns no requested documents for non-app ready plan' do
    expect(result.dig('data')).to be_nil
    expect(result.dig('errors').first).to include('message' => "L'attivazione per cui è stata inviata la notifica non può essere gestita da Blink.")
  end

  context 'App-ready plan' do
    before { phone_activation.plan.update_column(:app_ready, true) }

    it 'raises an error if no locked devices with matching acitvation id are found' do
      expect(result.dig('data')).to be_nil
      expect(result.dig('errors').first).to include('message' => "L'acquisizione è stata interrotta da Spazio oppure è già stata completata.")
    end

    context 'with locked devices with matching phone_activation_id' do
      let(:push_notification_data) do
        {
          'data' => {
            'phone_activation_id' => phone_activation.id.to_s,
            auth_token:           'XXXX'
          }
        }
      end
      let!(:device) { create(:device, push_notification_data: push_notification_data, locked_at: 1.minute.ago) }

      it 'raises an error if the auth_token is not matching' do
        expect(result.dig('data')).to be_nil
        expect(result.dig('errors').first).to include('message' => "L'acquisizione è stata presa in carico su un altro dispositivo.")
      end

      context 'and matching auth_token' do
        before { context.merge! authorization_token: 'XXXX' }

        let(:contract_owner_document_hash) do
          result.dig('data', 'phoneActivation', 'requestedDocuments').first
        end

        let(:actual_user_document_hash) do
          result.dig('data', 'phoneActivation', 'requestedDocuments').second
        end

        context 'Activation at the customer adding step' do
          context 'operation outcome' do
            let(:user) { create(:user, :dealer_user) }
            let(:operation_outcome) do
              create(:operation_outcome, :kolme_recoverable,
                     phone_activation: phone_activation,
                     operation_id: OperationConstants::UPLOAD_DOCUMENTI,
                     user_id: user.id)
            end
            let!(:archived_alert) do
              create(:alert, :with_dealer_and_warehouse, alertable: phone_activation, ko_reason: operation_outcome.ko_reason, archived: true)
            end
            let!(:current_alert) do
              create(:alert, :with_dealer_and_warehouse,
                     alertable: phone_activation,
                     ko_reason: operation_outcome.ko_reason)
            end

            it 'returns the last ko reason' do
              expect(result.dig('data', 'phoneActivation', 'lastKoReason')).to include('id' => "#{current_alert.id}")
              expect(result.dig('data', 'phoneActivation', 'lastKoReason')).to include('category' => current_alert.label_title)
            end
          end

          context 'for a plan without doppia_anagrafica' do
            it 'do not require extra customers' do
              expect(actual_user_document_hash).to be_nil
            end

            it 'requires a customer id document' do
              expect(contract_owner_document_hash['docDescriptor']).to eq 'customerDocument'
              expect(contract_owner_document_hash['documentName']).to eq "Documento dell'intestatario"
              expect(contract_owner_document_hash['pictureOnly']).to eq false
              expect(contract_owner_document_hash['conditions']).to eq []
              expect(contract_owner_document_hash['validationRules']).to eq ['planMinage 18']
            end

            it 'accepts IT id cards and driving licenses, IT passports, non-EU passports' do
              expect(contract_owner_document_hash['acceptedDocuments']).to eq [
                                                                                'IT identity card',
                                                                                'IT driving license',
                                                                                'IT passport',
                                                                                'nonEu passport'
                                                                              ]
            end

            it "requires a stay permit to non-EU customers if needed" do
              stay_permit_hash = contract_owner_document_hash['additionalDocuments']
                                   .detect { |additional_document_hash| additional_document_hash['documentName'] == 'Permesso di soggiorno' }
              expect(stay_permit_hash).not_to be_nil
              expect(stay_permit_hash['requiredIf']).to eq 'nonEu'
            end

            it 'requires a fiscal code document to customers if their ID does not contain the fiscal code' do
              fiscal_document_hash = contract_owner_document_hash['additionalDocuments']
                                       .detect { |additional_document_hash| additional_document_hash['documentName'] == 'Codice fiscale / tessera sanitaria' }
              expect(fiscal_document_hash).not_to be_nil
              expect(fiscal_document_hash['requiredIf']).to eq 'notRetrievedBefore'
              expect(fiscal_document_hash['enableTextInput']).to eq true
              expect(fiscal_document_hash['frontOnly']).to eq true
            end

            context 'Plans for businesses' do
              let(:customer_kind) { create(:customer_kind, :persona_giuridica) }

              before do
                phone_activation.plan.customer_kinds << customer_kind
              end

              it 'does not require partita_iva input if the customer is not business' do
                partita_iva_hash = contract_owner_document_hash['additionalDocuments']
                                     .detect { |additional_document_hash| additional_document_hash['documentName'] == 'Partita IVA' }
                expect(partita_iva_hash).to be_nil
              end

              context 'if the customer is business-type' do
                before do
                  phone_activation.customer.update_columns(customer_kind_id: customer_kind.id)
                end

                it 'require also a partita_iva input for the contract owner' do
                  partita_iva_hash = contract_owner_document_hash['additionalDocuments']
                                       .detect { |additional_document_hash| additional_document_hash['documentName'] == 'Partita IVA' }
                  expect(partita_iva_hash).not_to be_nil
                  expect(partita_iva_hash['requiredIf']).to be_nil
                  expect(partita_iva_hash['enableTextInput']).to be_truthy
                  expect(partita_iva_hash['frontOnly']).to be_nil
                end
              end
            end

            context 'PhoneActivationKinds::Rechargeable activation' do
              let(:ricaricabile) { create(:contract_kind, :ricaricabile) }

              before do
                phone_activation.plan.update_columns(contract_kind_id: ricaricabile.id)
              end

              it 'accepts IT stay permit, EU ID card, any passport or IT driving license regardless of customer nationality' do
                expect(contract_owner_document_hash['acceptedDocuments']).to eq [
                                                                                  'EU identity card',
                                                                                  'IT driving license',
                                                                                  'passport',
                                                                                  'IT stay permit'
                                                                                ]

                customer_stay_permit_hash = contract_owner_document_hash['additionalDocuments']
                                              .detect do |additional_document_hash|
                  additional_document_hash['documentName'] == 'Permesso di soggiorno'
                end
                expect(customer_stay_permit_hash).to be_nil
              end
            end

            context 'Minor-only plan' do
              before do
                phone_activation.plan.update_columns(minimum_age: 14)
                plan_cluster_option.update(cluster_option: eta_cluster_option, value: { min_age: 14, max_age: 16 })
              end

              it 'accepts fiscal code as ID for contract owner' do
                expect(contract_owner_document_hash['documentName']).to eq "Documento dell'intestatario"
                expect(contract_owner_document_hash['acceptedDocuments']).to include 'IT fiscal code'
                expect(contract_owner_document_hash['validationRules']).to eq ['planMinage 14', 'clusterAgerange 14-16']
              end
            end

            context 'Tourist customer' do
              let(:turista) { create(:customer, :turista, state: 'draft') }

              before { phone_activation.update(customer: turista) }

              it 'only accepts EU id card or passport' do
                expect(contract_owner_document_hash['acceptedDocuments']).to eq ['EU identity card', 'passport']
              end

              it 'does not require a fiscal code document to customers' do
                fiscal_document_hash = contract_owner_document_hash['additionalDocuments']
                                         .detect { |additional_document_hash| additional_document_hash['documentName'] == 'Codice fiscale / tessera sanitaria' }
                expect(fiscal_document_hash).to be_nil
              end
            end

            context 'country cluster option' do
              context 'estera generica' do
                before { plan_cluster_option.update(cluster_option: estera_generica_cluster_option) }

                it do
                  expect(contract_owner_document_hash['validationRules']).to eq ['planMinage 18', 'nationalityCheck Z']
                end
              end

              context 'estera specifica' do
                before { plan_cluster_option.update(cluster_option: estera_specifica_cluster_option, value: { country_code: 'Z123' }) }

                it do
                  expect(contract_owner_document_hash['validationRules']).to eq ['planMinage 18', 'nationalityCheck Z123']
                end
              end
            end

            context 'età e nazionalità cluster option' do
              before { phone_activation.plan.update_columns(minimum_age: 14) }

              context 'estera generica' do
                before { plan_cluster_option.update(cluster_option: eta_e_nazionalita_generic_cluster_option, value: { min_age: 14, max_age: 16 }) }

                it { expect(contract_owner_document_hash['validationRules']).to eq ['planMinage 14', 'clusterAgerange 14-16', 'nationalityCheck Z'] }
              end

              context 'estera specifica' do
                before { plan_cluster_option.update(cluster_option: eta_e_nazionalita_specific_cluster_option, value: { min_age: 14, max_age: 16, country_code: 'Z123' }) }

                it { expect(contract_owner_document_hash['validationRules']).to eq ['planMinage 14', 'clusterAgerange 14-16', 'nationalityCheck Z123'] }
              end
            end
          end

          context 'for a plan with doppia_anagrafica' do
            before { phone_activation.plan.update_column(:doppia_anagrafica, true) }

            it 'does require extra customers' do
              expect(actual_user_document_hash).not_to be_nil
            end

            it 'requires a customer id document' do
              expect(contract_owner_document_hash['docDescriptor']).to eq 'customerDocument'
              expect(contract_owner_document_hash['documentName']).to eq "Documento dell'intestatario principale"
              expect(contract_owner_document_hash['pictureOnly']).to eq false
              expect(contract_owner_document_hash['conditions']).to eq []
              expect(contract_owner_document_hash['validationRules']).to eq ['planMinage 18']
            end

            it 'requires an adult id document if the first one scanned belongs to a minor' do
              expect(actual_user_document_hash['docDescriptor']).to eq 'contractOwnerDocument'
              expect(actual_user_document_hash['documentName']).to eq 'Documento del reale utilizzatore'
              expect(actual_user_document_hash['pictureOnly']).to eq false
              expect(actual_user_document_hash['conditions']).to eq []
              expect(actual_user_document_hash['validationRules']).to eq []
            end

            it 'accepts IT id cards and driving licenses, IT passports, non-EU passports' do
              expect(contract_owner_document_hash['acceptedDocuments']).to eq [
                                                                                'IT identity card',
                                                                                'IT driving license',
                                                                                'IT passport',
                                                                                'nonEu passport'
                                                                              ]

              expect(actual_user_document_hash['acceptedDocuments']).to eq [
                                                                             'IT identity card',
                                                                             'IT passport',
                                                                             'IT health card'
                                                                           ]
            end

            it 'requires a stay permit to non-EU customers if needed' do
              stay_permit_hash = contract_owner_document_hash['additionalDocuments']
                                   .detect { |additional_document_hash| additional_document_hash['documentName'] == 'Permesso di soggiorno' }
              expect(stay_permit_hash).not_to be_nil
              expect(stay_permit_hash['requiredIf']).to eq 'nonEu'
            end

            it 'requires a fiscal code document to customers if their ID does not contain the fiscal code' do
              fiscal_document_hash = contract_owner_document_hash['additionalDocuments']
                                       .detect { |additional_document_hash| additional_document_hash['documentName'] == 'Codice fiscale / tessera sanitaria' }
              expect(fiscal_document_hash).not_to be_nil
              expect(fiscal_document_hash['requiredIf']).to eq 'notRetrievedBefore'
              expect(fiscal_document_hash['enableTextInput']).to eq true
              expect(fiscal_document_hash['frontOnly']).to eq true
            end

            it 'requires a fiscal code document to contract owners if their ID does not contain the fiscal code' do
              fiscal_document_hash = actual_user_document_hash['additionalDocuments']
                                       .detect { |additional_document_hash| additional_document_hash['documentName'] == 'Codice fiscale / tessera sanitaria' }
              expect(fiscal_document_hash).not_to be_nil
              expect(fiscal_document_hash['requiredIf']).to eq 'notRetrievedBefore'
              expect(fiscal_document_hash['enableTextInput']).to eq true
              expect(fiscal_document_hash['frontOnly']).to eq true
            end

            context 'PhoneActivationKinds::Rechargeable activation' do
              let(:ricaricabile) { create(:contract_kind, :ricaricabile) }

              before do
                phone_activation.plan.update_columns(contract_kind_id: ricaricabile.id, doppia_anagrafica: true)
              end

              it 'accepts IT stay permit, EU ID card, any passport or IT driving license regardless of customer nationality' do
                expect(contract_owner_document_hash['acceptedDocuments']).to eq [
                                                                                  'EU identity card',
                                                                                  'IT driving license',
                                                                                  'passport',
                                                                                  'IT stay permit'
                                                                                ]

                customer_stay_permit_hash = contract_owner_document_hash['additionalDocuments'].detect do |additional_document_hash|
                  additional_document_hash['documentName'] == 'Permesso di soggiorno'
                end

                expect(customer_stay_permit_hash).to be_nil
                expect(actual_user_document_hash['acceptedDocuments']).to eq [
                                                                               'IT identity card',
                                                                               'IT passport',
                                                                               'IT health card'
                                                                             ]
              end
            end

            context 'Tourist customer' do
              let(:turista) { create(:customer, :turista, state: 'draft') }

              before { phone_activation.update(customer: turista) }

              it 'only accepts EU id card or passport' do
                expect(contract_owner_document_hash['acceptedDocuments']).to eq ['EU identity card', 'passport']
              end

              it 'does not require a fiscal code document to customers' do
                fiscal_document_hash = contract_owner_document_hash['additionalDocuments']
                                         .detect { |additional_document_hash| additional_document_hash['documentName'] == 'Codice fiscale / tessera sanitaria' }
                expect(fiscal_document_hash).to be_nil
              end

              it 'does not require a fiscal code document to contract owners' do
                fiscal_document_hash = actual_user_document_hash['additionalDocuments']
                                         .detect { |additional_document_hash| additional_document_hash['documentName'] == 'Codice fiscale / tessera sanitaria' }
                expect(fiscal_document_hash).to be_nil
              end
            end

            context 'Minor-only plan' do
              before do
                phone_activation.plan.update_columns(minimum_age: 14)
                plan_cluster_option.update(cluster_option: eta_cluster_option, value: { min_age: 14, max_age: 16 })
              end

              it 'mandates two documents and accepts fiscal code as ID for contract owner' do
                expect(contract_owner_document_hash['documentName']).to eq "Documento dell'intestatario principale"
                expect(contract_owner_document_hash['acceptedDocuments']).not_to include 'IT fiscal code'
                expect(actual_user_document_hash['documentName']).to eq 'Documento del reale utilizzatore'
                expect(actual_user_document_hash['conditions']).to be_empty
                expect(contract_owner_document_hash['validationRules']).to eq ['planMinage 14']
                expect(actual_user_document_hash['validationRules']).to eq ['clusterAgerange 14-16']
              end
            end

            context 'country cluster option' do
              context 'estera generica' do
                before { plan_cluster_option.update(cluster_option: estera_generica_cluster_option) }

                it do
                  expect(contract_owner_document_hash['validationRules']).to eq ['planMinage 18']
                  expect(actual_user_document_hash['validationRules']).to eq ['nationalityCheck Z']
                end
              end

              context 'estera specifica' do
                before { plan_cluster_option.update(cluster_option: estera_specifica_cluster_option, value: { country_code: 'Z123' }) }

                it do
                  expect(contract_owner_document_hash['validationRules']).to eq ['planMinage 18']
                  expect(actual_user_document_hash['validationRules']).to eq ['nationalityCheck Z123']
                end
              end
            end

            context 'età e nazionalità cluster option' do
              before { phone_activation.plan.update_columns(minimum_age: 14) }

              context 'estera generica' do
                before { plan_cluster_option.update(cluster_option: eta_e_nazionalita_generic_cluster_option, value: { min_age: 14, max_age: 16 }) }

                it do
                  expect(contract_owner_document_hash['validationRules']).to eq ['planMinage 14']
                  expect(actual_user_document_hash['validationRules']).to eq ['clusterAgerange 14-16', 'nationalityCheck Z']
                end
              end

              context 'estera specifica' do
                before { plan_cluster_option.update(cluster_option: eta_e_nazionalita_specific_cluster_option, value: { min_age: 14, max_age: 16, country_code: 'Z123' }) }

                it do
                  expect(contract_owner_document_hash['validationRules']).to eq ['planMinage 14']
                  expect(actual_user_document_hash['validationRules']).to eq ['clusterAgerange 14-16', 'nationalityCheck Z123']
                end
              end
            end
          end
        end

        context 'Activation at the payment method detail adding step' do
          before do
            phone_activation.customer.update_column(:state, 'added_from_app')
            create(:payment_method_detail, phone_activation: phone_activation, state: 'draft')
          end

          it 'requires an account holder id document from an adult person' do
            expect(contract_owner_document_hash['docDescriptor']).to eq 'differentPayer'
            expect(contract_owner_document_hash['documentName']).to eq "Documento dell'intestatario"
            expect(contract_owner_document_hash['pictureOnly']).to eq false
            expect(contract_owner_document_hash['conditions']).to eq []
            expect(contract_owner_document_hash['validationRules']).to eq ["planMinage 18;L'intestatario del metodo di pagamento deve essere maggiorenne."]
          end

          it 'accepts IT id cards and driving licenses, IT passports, non-EU passports' do
            expect(contract_owner_document_hash['acceptedDocuments']).to eq [
                                                                              'IT identity card',
                                                                              'IT driving license',
                                                                              'IT passport',
                                                                              'nonEu passport'
                                                                            ]
          end

          it 'does not require a second document' do
            expect(actual_user_document_hash).to be_nil
          end

          it "requires a stay permit to non-EU account holders if needed" do
            stay_permit_hash = contract_owner_document_hash['additionalDocuments']
                                 .detect { |additional_document_hash| additional_document_hash['documentName'] == 'Permesso di soggiorno' }
            expect(stay_permit_hash).not_to be_nil
            expect(stay_permit_hash['requiredIf']).to eq 'nonEu'
          end

          it 'requires a fiscal code document to account holders if their ID does not contain the fiscal code' do
            fiscal_document_hash = contract_owner_document_hash['additionalDocuments']
                                     .detect { |additional_document_hash| additional_document_hash['documentName'] == 'Codice fiscale / tessera sanitaria' }
            expect(fiscal_document_hash).not_to be_nil
            expect(fiscal_document_hash['requiredIf']).to eq 'notRetrievedBefore'
            expect(fiscal_document_hash['enableTextInput']).to eq false
            expect(fiscal_document_hash['frontOnly']).to eq false
          end

          context 'Plans for businesses' do
            let(:customer_kind) { create(:customer_kind, :persona_giuridica) }

            before do
              phone_activation.plan.customer_kinds << customer_kind
            end

            it 'does not require business certificate (visura camerale) for the account holder' do
              visura_camerale_hash = contract_owner_document_hash['additionalDocuments']
                                       .detect { |additional_document_hash| additional_document_hash['documentName'] == 'Visura camerale' }
              expect(visura_camerale_hash).to be_nil
            end
          end

          context 'PhoneActivationKinds::Rechargeable activation' do
            let(:ricaricabile) { create(:contract_kind, :ricaricabile) }

            before do
              phone_activation.plan.update_columns(contract_kind_id: ricaricabile.id)
            end

            it 'accepts IT stay permit, EU ID card, any passport or IT driving license regardless of customer nationality' do
              expect(contract_owner_document_hash['acceptedDocuments']).to eq [
                                                                                'EU identity card',
                                                                                'IT driving license',
                                                                                'passport',
                                                                                'IT stay permit'
                                                                              ]

              customer_stay_permit_hash = contract_owner_document_hash['additionalDocuments']
                                            .detect do |additional_document_hash|
                additional_document_hash['documentName'] == 'Permesso di soggiorno'
              end
              expect(customer_stay_permit_hash).to be_nil
            end

            context 'for a Fissa contract kind' do
              let(:fissa) { create(:product_category, :fissa) }

              before do
                phone_activation.plan.update_columns(product_category_id: fissa.id)
              end

              it 'requires an account holder id document from an adult person' do
                expect(contract_owner_document_hash['docDescriptor']).to eq 'differentPayer'
                expect(contract_owner_document_hash['documentName']).to eq "Documento dell'intestatario"
                expect(contract_owner_document_hash['pictureOnly']).to eq false
                expect(contract_owner_document_hash['conditions']).to eq []
                expect(contract_owner_document_hash['validationRules']).to eq ["planMinage 18;L'intestatario del metodo di pagamento deve essere maggiorenne."]
              end

              it 'accepts IT id cards and driving licenses, IT passports, non-EU passports' do
                expect(contract_owner_document_hash['acceptedDocuments']).to eq [
                                                                                  'IT identity card',
                                                                                  'IT driving license',
                                                                                  'passport'
                                                                                ]
              end

              it 'does not require a second document' do
                expect(actual_user_document_hash).to be_nil
              end

              it "requires a stay permit to non-EU account holders even if the plan a 'pure rechargeable'" do
                stay_permit_hash = contract_owner_document_hash['additionalDocuments']
                                     .detect { |additional_document_hash| additional_document_hash['documentName'] == 'Permesso di soggiorno' }
                expect(stay_permit_hash).not_to be_nil
                expect(stay_permit_hash['requiredIf']).to eq 'nonEu'
              end

              it 'requires a fiscal code document to account holders if their ID does not contain the fiscal code' do
                fiscal_document_hash = contract_owner_document_hash['additionalDocuments']
                                         .detect { |additional_document_hash| additional_document_hash['documentName'] == 'Codice fiscale / tessera sanitaria' }
                expect(fiscal_document_hash).not_to be_nil
                expect(fiscal_document_hash['requiredIf']).to eq 'notRetrievedBefore'
                expect(fiscal_document_hash['enableTextInput']).to eq false
                expect(fiscal_document_hash['frontOnly']).to eq false
              end
            end
          end

          context 'Minor-only plan' do
            before do
              phone_activation.plan.update_columns(minimum_age: 14)
              plan_cluster_option.update(cluster_option: eta_cluster_option, value: { min_age: 14, max_age: 16 })
            end

            it 'requires an account holder id document from an adult person' do
              expect(contract_owner_document_hash['docDescriptor']).to eq 'differentPayer'
              expect(contract_owner_document_hash['documentName']).to eq "Documento dell'intestatario"
              expect(contract_owner_document_hash['pictureOnly']).to eq false
              expect(contract_owner_document_hash['conditions']).to eq []
              expect(contract_owner_document_hash['validationRules']).to eq ["planMinage 18;L'intestatario del metodo di pagamento deve essere maggiorenne."]
            end
          end

          context 'Tourist customer' do
            let(:turista) { create(:customer, :turista) }

            before { phone_activation.update(customer: turista) }

            it 'does require a fiscal code document to account holders' do
              fiscal_document_hash = contract_owner_document_hash['additionalDocuments']
                                       .detect { |additional_document_hash| additional_document_hash['documentName'] == 'Codice fiscale / tessera sanitaria' }
              expect(fiscal_document_hash).not_to be_nil
            end

            context 'Minor-only plan' do
              before do
                phone_activation.plan.update_columns(minimum_age: 14)
                plan_cluster_option.update(cluster_option: eta_cluster_option, value: { min_age: 14, max_age: 16 })
              end

              it 'does not accept fiscal code as ID for account holder' do
                expect(contract_owner_document_hash['acceptedDocuments']).not_to include 'IT fiscal code'
              end
            end
          end
        end

        context 'with number_portability_detail' do
          let(:source_operator) { create(:portability_operator, :tim_fisso) }
          let!(:number_portability_detail) { create(:number_portability_detail, :different_owner, phone_activation: phone_activation, source_operator: source_operator) }

          it do
            expect(result.dig('data', 'phoneActivation', 'numberPortabilityDetail')).not_to be_nil
            expect(result.dig('data', 'phoneActivation', 'numberPortabilityDetail', 'phoneNumber')).to eq '**********'
          end
        end
      end
    end
  end
end
