require 'rails_helper'
include GraphQL::TestHelpers

describe Mutations::RecoverKoWithNote, type: :mutation do
  let!(:user) { create(:user, :dealer_contact_access_activations) }
  let(:alert) do
    create(:alert, :with_dealer_and_warehouse, :ko_recoverable, todo: true)
  end

  def mutation_string
    <<~GQL
      mutation recoverKoWithNote($alertId: ID!, $note: String!) {
        recoverKoWithNote(input: { alertId: $alertId, note: $note }) {
          alert {
            id
          }
          success
          errors
       }
      }
    GQL
  end

  context 'Success' do
    let(:ko_reason) { create(:ko_reason, :with_upload_documents) }

    let(:time) { Time.now.round }

    before { Timecop.freeze(time) }
    after { Timecop.return }

    before { allow(PrivatePub).to receive(:publish_to) }

    context 'with a phone activation' do
      let(:phone_activation) { create(:phone_activation, :abbonamento_ricaricabile) }

      let!(:operation_outcome) do
        create(:operation_outcome, :controllo_documenti, status: 'ko_r',
          phone_activation: phone_activation, ko_reason: ko_reason)
      end

      before { allow(Alert).to receive(:find).and_return alert }
      before { allow(alert).to receive(:alertable).and_return phone_activation }
      before { allow(alert).to receive(:alertable_type).and_return phone_activation.class.name }
      before { allow(alert).to receive(:alertable_id).and_return phone_activation.id }

      subject do
        mutation mutation_string,
                 variables: { alert_id: alert.id, note: 'Ok, will do' },
                 context:   { current_user: user }
      end

      it 'recovers the last KoR outcome, calls complete_todo on the activation and refreshes the pages' do
        expect(Alert).to receive(:find).with(alert.id.to_s)
        expect(phone_activation).to receive(:complete_todo).with(
          operation_id: operation_outcome.operation_id,
          user_id:      user.id
        )
        expect(phone_activation).to receive(:index)
        expect(phone_activation).to receive(:refresh_views_after_operation)
          .with(operation_outcome.operation_id, true)

        subject

        expect(operation_outcome.reload.recovered_by_dealer).to be_truthy
        expect(operation_outcome.recovery_note).to eq "Recuperata - #{I18n.l time} - #{user.full_name} <br> Ok, will do"
        expect(operation_outcome.recovered_at).to eq time

        expect(gql_response.data['recoverKoWithNote']['success']).to be_truthy
        expect(gql_response.errors).to be_nil
        expect(gql_response.data['recoverKoWithNote']['alert']['id']).to eq alert.id.to_s
      end
    end

    context 'with an NG POS activation' do
      let(:activation) { create(:wind_ngpos_activation) }

      let!(:operation_outcome) do
        create(:ngpos_operation_outcome, :controllo_documenti, status: 'ko_r',
              ko_reason: ko_reason, wind_ngpos_activation: activation)
      end

      before { allow(Alert).to receive(:find).and_return alert }
      before { allow(alert).to receive(:alertable).and_return activation }
      before { allow(alert).to receive(:alertable_type).and_return activation.class.name }

      subject do
        mutation mutation_string,
                 variables: { alert_id: alert.id, note: 'Ok, will do' },
                 context:   { current_user: user }
      end

      it 'recovers the last KoR outcome, calls complete_todo on the activation' do
        expect(Alert).to receive(:find).with(alert.id.to_s)
        expect(activation).to receive(:complete_todo).with(
          ngpos_operation_id: operation_outcome.ngpos_operation_id,
          user_id:            user.id
        )
        expect(activation).to receive(:index)

        subject

        expect(operation_outcome.reload.recovered_by_dealer).to be_truthy
        expect(operation_outcome.recovery_note).to eq "Recuperata - #{I18n.l time} - #{user.full_name} <br> Ok, will do"
        expect(operation_outcome.recovered_at).to eq time

        expect(gql_response.data['recoverKoWithNote']['success']).to be_truthy
        expect(gql_response.errors).to be_nil
        expect(gql_response.data['recoverKoWithNote']['alert']['id']).to eq alert.id.to_s
      end
    end
  end

  context 'Some error' do
    let(:error) { StandardError.new('Nasty error') }

    before do
      allow(Alert).to receive(:find).and_raise error
    end

    subject do
      mutation mutation_string,
               variables: { alert_id: alert.id, note: 'Ok, will do' },
               context:   { current_user: user }
    end

    it 'logs the error' do
      expect(KolmeLogger).to receive(:error).with error
      subject

      expect(gql_response.errors.first['message']).to eq 'Nasty error'
      expect(gql_response.data['recoverKoWithNote']).to be_nil
    end
  end

  context 'unauthorized user' do
    subject do
      mutation mutation_string, variables: { alert_id: alert.id, note: 'Ok, will do' }
    end

    it 'does not log the error' do
      expect(KolmeLogger).not_to receive(:error)

      subject

      expect(gql_response.errors.first['message']).to eq 'Non sei autorizzato ad accedere a questa funzionalità'
      expect(gql_response.errors.first['extensions']['code']).to eq 'AUTHENTICATION_ERROR'
      expect(gql_response.data['archiveAlert']).to be_nil
      expect(alert.reload.archived?).to be_falsey
    end
  end
end
