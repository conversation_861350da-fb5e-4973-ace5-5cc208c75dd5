require "rails_helper"
include GraphQL::TestHelpers

describe Mutations::CreateImeiReservation, type: :mutation do
  let!(:user) { create(:user, :dealer_contact_access_imei_reservations) }
  let!(:warehouse) { create(:warehouse, dealer: user.dealer) }
  let!(:product) { create(:product, :lumia_dhl) }
  let(:variables) { { product_id: product.id, warehouse_id: warehouse.id } }

  def mutation_string
    <<~GQL
      mutation createImeiReservation($productId: ID!, $warehouseId: ID!) {
        createImeiReservation(input: { productId: $productId, warehouseId: $warehouseId }) {
          imeiReservation {
            id
            createdAt
            expirationOffsetMinutes
            item {
              serial
              barcode
            }
            privatePubSubscription {
              channel
              timestamp
              signature
            }
          }
          success
          errors
          message
        }
      }
    GQL
  end

  context "unauthenticated user" do
    before do
      mutation mutation_string, variables: variables
    end

    it do
      expect(gql_response.errors.first["message"]).to eq "Non sei autorizzato ad accedere a questa funzionalità"
      expect(gql_response.errors.first["extensions"]["code"]).to eq "AUTHENTICATION_ERROR"
      expect(gql_response.data["createImeiReservation"]).to be_nil
    end
  end

  context "unauthorized user" do
    before { user.dealer_contact.access_flags.delete_all }

    before do
      mutation mutation_string, variables: variables, context: { current_user: user }
    end

    it do
      expect(gql_response.errors.first["message"]).to eq "Non sei autorizzato ad accedere a questa funzionalità"
      expect(gql_response.errors.first["extensions"]["code"]).to eq "AUTHORIZATION_ERROR"
      expect(gql_response.data["createImeiReservation"]).to be_nil
    end
  end

  context "product unavailable" do
    it do
      mutation mutation_string, variables: variables, context: { current_user: user }

      expect(gql_response.data["createImeiReservation"]["success"]).to be_truthy
      expect(gql_response.data["createImeiReservation"]["message"])
        .to eq "Grazie per la tua richiesta, riceverai una notifica quando il prodotto tornerà disponibile. Ricordati di controllare che il tuo fido copra l'importo del prodotto quando sarà disponibile."
      expect(gql_response.errors).to be_nil
      expect(ImeiReservation.count).to eq 1
      expect(ImeiReservation.last.from_app?).to be_truthy
      expect(gql_response.data["createImeiReservation"]["imeiReservation"]).to be_nil
    end

    context "with sufficient fido" do
      before do
        user.dealer.update_columns(assigned_fido: 10000)
      end

      it do
        mutation mutation_string, variables: variables, context: { current_user: user }

        expect(gql_response.data["createImeiReservation"]["success"]).to be_truthy
        expect(gql_response.data["createImeiReservation"]["message"])
          .to eq "Grazie per la tua richiesta, riceverai una notifica quando il prodotto tornerà disponibile."
        expect(gql_response.data["createImeiReservation"]["errors"]).to be_truthy
        expect(gql_response.errors).to be_nil
        expect(ImeiReservation.count).to eq 1
        expect(ImeiReservation.last.from_app?).to be_truthy
        expect(gql_response.data["createImeiReservation"]["imeiReservation"]).to be_nil
      end
    end

    context "and already wished for today" do
      before do
        create(:imei_reservation, :wished, warehouse: warehouse)
      end

      it do
        mutation mutation_string, variables: variables, context: { current_user: user }

        expect(gql_response.data["createImeiReservation"]["success"]).to be_truthy
        expect(gql_response.data["createImeiReservation"]["message"])
          .to eq "Grazie per la tua richiesta, riceverai una notifica quando il prodotto tornerà disponibile. Ricordati di controllare che il tuo fido copra l'importo del prodotto quando sarà disponibile."
        expect(gql_response.data["createImeiReservation"]["errors"]).to be_truthy
        expect(gql_response.errors).to be_nil
        expect(ImeiReservation.count).to eq 1
        expect(gql_response.data["createImeiReservation"]["imeiReservation"]).to be_nil
      end
    end
  end

  context "product available" do
    let!(:item) { create(:item, :lumia_dhl_item) }
    let(:time) { Time.now }
    let(:channel) { "/notify_imei_reservation_autoconfirmation_to_app/#{ImeiReservation.last.id}" }
    let(:timestamp) { (time.to_f * 1000).round.to_s }
    let(:signature) do
      Digest::SHA1.hexdigest([PrivatePub.config[:secret_token], channel, timestamp].join)
    end

    before do
      Timecop.freeze(time)
      user.dealer.update_columns(assigned_fido: 10000)
    end
    after do
      Timecop.return
    end

    before do
      user.dealer.update_columns(assigned_fido: 10000)
      product.update_attribute(:bookable_quantity, 1)
      mutation mutation_string, variables: variables, context: { current_user: user }
    end

    it do
      expect(gql_response.data["createImeiReservation"]["success"]).to be_truthy
      expect(gql_response.errors).to be_nil
      expect(ImeiReservation.count).to eq 1
      expect(ImeiReservation.last.from_app?).to be_truthy
      expect(gql_response.data["createImeiReservation"]["imeiReservation"]["id"]).to eq ImeiReservation.last.id.to_s
      expect(gql_response.data["createImeiReservation"]["imeiReservation"]["createdAt"]).to eq ImeiReservation.last.created_at.to_s
      expect(gql_response.data["createImeiReservation"]["imeiReservation"]["expirationOffsetMinutes"]).to eq "45"
      expect(gql_response.data["createImeiReservation"]["imeiReservation"]["item"]["serial"]).to eq item.serial
      expect(gql_response.data["createImeiReservation"]["imeiReservation"]["item"]["barcode"]).to eq Base64.encode64(item.barcode.to_png)
      expect(gql_response.data["createImeiReservation"]["imeiReservation"]["privatePubSubscription"]["channel"]).to eq channel
      expect(gql_response.data["createImeiReservation"]["imeiReservation"]["privatePubSubscription"]["timestamp"]).to eq timestamp
      expect(gql_response.data["createImeiReservation"]["imeiReservation"]["privatePubSubscription"]["signature"]).to eq signature
    end
  end
end
