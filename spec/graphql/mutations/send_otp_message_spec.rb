# frozen_string_literal: true
require 'rails_helper'

include GraphQL::TestHelpers

RSpec.describe Mutations::SendOtpMessage, type: :mutation do
  let(:current_user) { create(:user, :dealer_contact_access_activations) }
  let(:phone_activation) { create(:phone_activation, :abbonamento_ricaricabile) }
  let(:source_operator) { create(:portability_operator, :tim_fisso) }

  def mutation_string
    <<~GQL
      mutation sendOtpMessage($phoneActivationId: ID!, $numberPortabilityDetailInput: NumberPortabilityDetailInput!) {
        sendOtpMessage(input: { phoneActivationId: $phoneActivationId, numberPortabilityDetailInput: $numberPortabilityDetailInput }) {
          responseCode
          success
          errors
        }
      }
    GQL
  end

  let(:context) do
    {
      current_user: current_user
    }
  end

  subject { mutation mutation_string, variables: variables, context: context }

  before do
    stub_request(:post, 'https://ml-in-2.apps.windtre.it/test/otp-sender/send-message')
      .to_return(status: 200, body: '{"responseCode":"OK"}', headers: {})

    subject
  end

  describe 'with valid input' do
    let(:variables) do
      {
        phoneActivationId: phone_activation.id,
        numberPortabilityDetailInput: {
          sourceOperatorId: source_operator.id,
          phoneNumber: '1234567890'
        }
      }
    end

    it 'creates a record' do
      expect(NumberPortabilityOtpRequest.send_message.count).to eq 1
    end

    it 'returns a response_code' do
      expect(gql_response.data['sendOtpMessage']['responseCode']).to eq 'OK'
    end

    it 'returns a success' do
      expect(gql_response.data['sendOtpMessage']['success']).to eq true
    end

    it 'returns no errors' do
      expect(gql_response.data['sendOtpMessage']['errors']).to eq []
    end
  end

  describe 'with invalid input' do
    let(:variables) do
      {
        phoneActivationId: 'invalid',
        numberPortabilityDetailInput: {
          sourceOperatorId: source_operator.id,
          phoneNumber: '1234567890'
        }
      }
    end

    it 'does not create a record' do
      expect(NumberPortabilityOtpRequest.count).to eq 0
    end

    it 'returns a response_code' do
      expect(gql_response.data['sendOtpMessage']['responseCode']).to eq 'KO'
    end

    it 'returns a success false' do
      expect(gql_response.data['sendOtpMessage']['success']).to eq false
    end

    it 'returns an error' do
      expect(gql_response.data['sendOtpMessage']['errors']).to eq ["Couldn't find PhoneActivation with 'id'=invalid"]
    end
  end
end
