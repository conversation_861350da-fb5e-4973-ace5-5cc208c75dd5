require "rails_helper"
include GraphQL::TestHelpers

describe Mutations::CreateNumberRebindingDetail, type: :mutation do
  let!(:user) { create(:user, :dealer_contact_access_activations) }
  let(:phone_activation) { create(:phone_activation, :abbonamento_ricaricabile, dealer: user.dealer) }
  let!(:operation) { create(:operation, :segnalazione_numero_attivo) }

  def mutation_string
    <<~GQL
      mutation createNumberRebindingDetail($phoneActivationId: ID!, $phoneNumber: String!) {
        createNumberRebindingDetail(input: { phoneActivationId: $phoneActivationId, phoneNumber: $phoneNumber }) {
        numberRebindingDetail {
            id
            phoneActivationId
            phoneNumber
          }
          success
          errors
       }
      }
    GQL
  end

  context "Success" do
    before do
      expect(Operations::OperationsHandler).to receive(:process)
      mutation mutation_string,
               variables: { phone_activation_id: phone_activation.id, phone_number: "3331122333" },
               context:   { current_user: user }
    end

    it do
      expect(gql_response.data["createNumberRebindingDetail"]["success"]).to be_truthy
      expect(gql_response.errors).to be_nil
      expect(NumberRebindingDetail.count).to eq 1
      expect(OperationOutcome.count).to eq 1
      expect(OperationOutcome.last.phone_activation_id).to eq phone_activation.id
      expect(OperationOutcome.last.operation_id).to eq OperationConstants::SEGNALAZIONE_NUMERO_ATTIVO
      expect(gql_response.data["createNumberRebindingDetail"]["numberRebindingDetail"]["phoneActivationId"]).to eq phone_activation.id.to_s
      expect(gql_response.data["createNumberRebindingDetail"]["numberRebindingDetail"]["phoneNumber"]).to eq "3331122333"
    end
  end


  context "unauthorized user" do
    before do
      mutation mutation_string, variables: { phone_activation_id: phone_activation.id, phone_number: "3331122333" }
    end

    it do
      expect(gql_response.errors.first["message"]).to eq "Non sei autorizzato ad accedere a questa funzionalità"
      expect(gql_response.errors.first["extensions"]["code"]).to eq "AUTHENTICATION_ERROR"
      expect(gql_response.data["createNumberRebindingDetail"]).to be_nil
      expect(NumberRebindingDetail.count).to be_zero
    end
  end

  context "Invalid phone number" do
    let(:number_rebinding_detail) { stub_model(NumberRebindingDetail) }

    before do
      mutation mutation_string, variables: { phone_activation_id: phone_activation.id, phone_number: "12345" }, 
                                context: { current_user: user }
    end

    it do
      expect(gql_response.data["createNumberRebindingDetail"]).to be_nil
      expect(gql_response.errors.first["message"]).to eq "Numero di telefono non è valido"
      expect(NumberRebindingDetail.count).to eq 0
    end
  end
end
