require 'rails_helper'
include GraphQL::TestHelpers

describe Mutations::EnableRechargesAccess, type: :mutation do
  let(:user) { create(:user, :dealer_contact_access_recharges) }
  let(:device) { create(:device, user: user) }

  def mutation_string
    <<~GQL
      mutation enableRechargesAccess($deviceId: ID!, $otp: String!) { 
        enableRechargesAccess(input: { deviceId: $deviceId, otp: $otp }) {
          success  
          message 
          errors
        }
      }
    GQL
  end

  context 'Success' do
    before { device.update_columns(direct_otp: '1234', otp_blocked_at: 1.week.ago) }

    subject do
      mutation mutation_string,
               variables: { device_id: device.id, otp: '1234' },
               context: { current_user: user }
    end

    it do
      subject
      
      expect(device.reload.otp_trusted).to be_truthy
      expect(device.reload.direct_otp).to be_nil
      expect(device.reload.otp_blocked_at).to be_nil
      expect(gql_response.data['enableRechargesAccess']['success']).to be_truthy
    end
  end

  context 'Failure' do

    context 'wrong otp' do
      before { device.update_columns(direct_otp: '1234', second_factor_attempts_count: 1) }

      subject do
        mutation mutation_string,
                 variables: { device_id: device.id, otp: '4123' },
                 context: { current_user: user }
      end

      it do
        subject

        expect(device.reload.direct_otp).to eq '1234'
        expect(device.reload.otp_trusted).to be_falsey
        expect(device.reload.second_factor_attempts_count).to eq 2
        expect(gql_response.data['enableRechargesAccess']['success']).to be_falsey
        expect(gql_response.data['enableRechargesAccess']['errors']).to eq ['invalidOtp']
      end
    end

    context 'max attempts count reached' do
      before { device.update_columns(direct_otp: '1234', second_factor_attempts_count: 4) }

      subject do
        mutation mutation_string,
                 variables: { device_id: device.id, otp: '1234' },
                 context: { current_user: user }
      end

      it do
        subject

        expect(device.reload.direct_otp).to be_nil
        expect(device.reload.second_factor_attempts_count).to be_zero
        expect(device.reload.otp_blocked_at).not_to be_nil
        expect(gql_response.data['enableRechargesAccess']['success']).to be_falsey
        expect(gql_response.data['enableRechargesAccess']['errors']).to eq ['attemptsExhausted']
      end
    end

    context 'device blocked' do
      before { device.update_columns(direct_otp: '1234', otp_blocked_at: 20.minute.ago) }

      subject do
        mutation mutation_string,
                 variables: { device_id: device.id, otp: '1234' },
                 context: { current_user: user }
      end

      it do
        subject

        expect(device.reload.direct_otp).to be_nil
        expect(device.reload.second_factor_attempts_count).to be_zero
        expect(device.reload.otp_blocked_at).not_to be_nil
        expect(gql_response.data['enableRechargesAccess']['success']).to be_falsey
        expect(gql_response.data['enableRechargesAccess']['errors']).to eq ['attemptsExhausted']
      end
    end
  end
end