# encoding: utf-8

FactoryBot.define do
  factory :city do
    trait :milano do
      code { "F205" }
      description { "Milano" }
      association :province, :milano
    end

    trait :sesto_san_giovanni do
      code { "I690" }
      description { "Sesto San Giovanni" }
      association :province, :milano
    end

    trait :genova do
      code { "D969" }
      description { "Genova" }
      association :province, :genova
    end

    trait :barcellona_pozzo_digotto do
      code { "A638" }
      description { "Barcellona Pozzo Digotto" }
      association :province, :messina
    end

    trait :borgarello do
      code { "A989" }
      description { "Borgarello" }
      association :province, :pavia
    end

    trait :amblar_don do
      code { "M351" }
      description { "Amblar-Don" }
      association :province, :trento
    end

    initialize_with { City.find_or_create_by(code: code) }
  end
end
