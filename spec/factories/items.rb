FactoryBot.define do
  factory :item do
    initialize_with { Item.find_or_initialize_by(serial: serial) }
    serial { Faker::Number.number(digits: 15) }
    ddt { 'INIZIO' }

    trait :iphone_dealer_item_instock do
      state { 'instock' }
      association :product, factory: %i[product iphone]
      association :warehouse
    end

    trait :galaxy_dealer_item_not_instock do
      state { :activated }
      association :product, factory: %i[product galaxy]
      association :warehouse
    end

    trait :lumia_system_owner_item do
      association :product, factory: %i[product lumia]
      association :warehouse, factory: %i[warehouse system_owner_warehouse]
    end

    trait :lumia_dhl_item do
      association :product, factory: %i[product lumia_dhl]
      association :warehouse, factory: %i[warehouse kolme_dhl]
    end

    trait :lumia_dhl_item_outside_pigeon_house do
      association :product, factory: %i[product lumia_dhl_outside_pigeon_house]
      association :warehouse, factory: %i[warehouse kolme_dhl]
    end

    trait :lumia_dhl_item_booked do
      state { 'booked' }
      association :product, factory: %i[product lumia_dhl]
      association :warehouse, factory: %i[warehouse kolme_dhl]
    end

    trait :lumia_dhl_item_booked_curtailed do
      state { 'booked' }
      association :product, factory: %i[product lumia_dhl_curtailed]
      association :warehouse, factory: %i[warehouse kolme_dhl]
    end

    trait :lumia_dhl_item_quarantined do
      state { 'quarantined' }
      association :product, factory: %i[product lumia_dhl]
      association :warehouse, factory: %i[warehouse kolme_dhl]
    end

    trait :callback_skipping do
      after(:build) do
        Item.skip_callback(:save, :after, :update_amounts_in_warehouses, raise: false)
      end
    end

    trait :router_wimax_dealer_item_instock do
      state { 'instock' }
      association :product, factory: %i[product router_wimax]
      association :warehouse
    end

    trait :sim_tre_dealer_item_instock do
      serial { '8939123456789012346' } # first 4 digits must be 8939 and serial length must be equal to 19 for sims
      state { 'instock' }
      phone_number { '3280547672' }
      association :product, factory: %i[sim sim_tre]
      association :warehouse
    end

    trait :sim_tre_instock do
      serial { '8939123456789012346' } # first 4 digits must be 8939 and serial length must be equal to 19 for sim s
      state { 'instock' }
      phone_number { '3280547671' }
      association :product, factory: %i[sim sim_tre]
      association :warehouse, factory: %i[warehouse system_owner_warehouse]
    end

    trait :sim_tre_dhl_instock do
      serial { '8939123456789012346' } # first 4 digits must be 8939 and serial length must be equal to 19 for sim s
      state { 'instock' }
      phone_number { '3280547671' }
      association :product, factory: %i[sim sim_tre_dhl]
      association :warehouse, factory: %i[warehouse kolme_dhl]
    end

    trait :sim_tre_micro_instock do
      serial { '8939123456789012349' } # first 4 digits must be 8939 and serial length must be equal to 19 for sim s
      state { 'instock' }
      phone_number { '3280547675' }
      association :product, factory: %i[sim sim_tre_micro]
      association :warehouse, factory: %i[warehouse system_owner_warehouse]
    end

    trait :sim_tre_dealer_item_activating do
      serial { '8939123456789012347' } # first 4 digits must be 8939 and serial length must be equal to 19 for sims
      state { 'activating' }
      phone_number { '3280547673' }
      association :product, factory: %i[sim sim_tre]
      association :warehouse
    end

    trait :sim_tre_dealer_item_activated do
      serial { '8939123456789012350' } # first 4 digits must be 8939 and serial length must be equal to 19 for sims
      state { 'activated' }
      phone_number { '3280547673' }
      association :product, factory: %i[sim sim_tre_micro]
      association :warehouse
    end

    trait :sim_tre_micro_dealer_item_instock do
      serial { '8939123456789012349' } # first 4 digits must be 8939 and serial length must be equal to 19 for sims
      state { 'instock' }
      phone_number { '3280547675' }
      association :product, factory: %i[sim sim_tre_micro]
      association :warehouse
    end

    trait :sim_tre_nano_dealer_item_instock do
      serial { '8939123456789012348' } # first 4 digits must be 8939 and serial length must be equal to 19 for sims
      state { 'instock' }
      phone_number { '3280547674' }
      association :product, factory: %i[sim sim_tre_nano]
      association :warehouse
    end

    trait :sim_fastweb_dealer_item_instock do
      serial { '8939123456789012345' } # first 4 digits must be 8939 and serial length must be equal to 19 for sims
      state { 'instock' }
      phone_number { '3280547672' }
      association :product, factory: %i[sim sim_fastweb]
      association :warehouse
    end

    trait :disabled_sim_fastweb_dealer_item_instock do
      serial { '8939123456789012345' } # first 4 digits must be 8939 and serial length must be equal to 19 for sims
      state { 'instock' }
      phone_number { '3285447672' }
      association :product, factory: %i[sim disabled_sim_fastweb]
      association :warehouse
    end

    trait :sim_tiscali_without_phone_number do
      serial { '8939123456789012345' }
      state { 'instock' }
      association :product, factory: %i[sim sim_tiscali_without_phone_number]
      association :warehouse
    end

    trait :in_system_owner_warehouse do
      association :warehouse, factory: %i[warehouse system_owner_warehouse]
    end

    trait :in_system_owner_operations do
      association :warehouse, factory: %i[warehouse system_owner_operation]
    end

    trait :cable do
      state { 'instock' }
      association :product, factory: %i[product cable]
      association :warehouse
    end

    trait :in_dhl_warehouse do
      association :warehouse, factory: %i[warehouse kolme_dhl]
    end

    trait :router_kolme_warehouse do
      state { 'instock' }
      association :product, factory: %i[product router_wimax]
      association :warehouse, :kolme_dhl
    end
  end
end
