# Read about factories at https://github.com/thoughtbot/factory_bot

FactoryBot.define do
  factory :statistic_group do
    code { "MyString" }
    description { "MyString" }
  end

  factory :statistic_group1, parent: :statistic_group, class: "StatisticGroup1" do
    code { "ARIA" }
    description { "Operatore ARIA & TERRA" }

    trait :italia do
      code { "IT" }
      description { "Italia" }
    end

    initialize_with { StatisticGroup1.find_or_initialize_by(code: code) }
  end

  factory :statistic_group2, parent: :statistic_group, class: "StatisticGroup2" do
    code { "ACC" }
    description { "Accessori" }

    trait :smartphone do
      code { 'SP' }
      description { "Smartphone" }
    end

    trait :altro do
      description { "Altro" }
    end

    trait :complementi_e_accessori do
      code { "C&A" }
      description { "Complementi e accessori" }
    end

    initialize_with { StatisticGroup2.find_or_initialize_by(code: code) }
  end

  factory :statistic_group3, parent: :statistic_group, class: "StatisticGroup3" do
    code { "ACC" }
    description { "Accessori" }
    initialize_with { StatisticGroup3.find_or_initialize_by(code: code) }
  end

  factory :statistic_group4, parent: :statistic_group, class: "StatisticGroup4" do
    code { "ACC" }
    description { "Accessori" }
    initialize_with { StatisticGroup4.find_or_initialize_by(code: code) }
  end
end
