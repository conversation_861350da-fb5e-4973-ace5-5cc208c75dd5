FactoryBot.define do
  factory :wind_tre_data do
    brand { 'H3G' }
    canale_vendita { 'W3SP' }
    codice_area_wind_tre { '1' }
    codice_dealer { '8000000100' }
    codice_venditore { '9000000202' }
    codice_venditore_aggregato { '9000000202' }
    comune { "CASSANO D'ADDA" }
    dealer_name { 'F.LLI ROSSI' }
    finanziamento { '0' }
    funzionario { 'RIGANO EROS' }
    handset { 'NO HS' }
    attivazione_mdp { '*ND' }
    mnp { 'Y' }
    modello { 'USIM' }
    nazionalita { 'Straniera' }
    offerta_terminale_listino_biz { 'Abbonamento Biz' }
    operatore_aggregato { 'MVNO' }
    opzione_1 { 'PLAY Power 9-99' }
    opzione_2 { '0' }
    opzione_3 { '0' }
    opzione_4 { '0' }
    piano_tariffario_macro_listino { 'Power 29' }
    plans { 'UNTIED' }
    plans_2 { 'National Offer' }
    provenienza_ricarica { 'SCRATCH-ATM' }
    provincia { 'MI' }
    ricaricato_contestuale { 'Y' }
    vendor { 'USIM' }
    voce_dati { 'VOCE' }
    wind_cluster_offerta_2 { 'National' }
    wind_cluster_offerta_4 { 'National' }
    importo_lordo { 200 }
    numero_portfoli_lordo { 1 }
    numero_portfoli_netto { 1 }
    quantita_ricariche { 1 }

    association :warehouse

    trait :april_first do
      data_attivazione_portfolio { Date.new(2019, 4, 1) }
    end

    trait :february_last do
      data_attivazione_portfolio { Date.new(2019, 2, 28) }
    end

    trait :fixed do
      plans { WindTreData::FIXED_PLANS.sample }
    end

    trait :may do
      data_attivazione_portfolio { Date.new(2019, 5, 10) }
    end

    trait :march do
      data_attivazione_portfolio { Date.new(2019, 3, 10) }
    end

    trait :mobile do
      plans { 'UNITED' }
    end

    trait :march_first do
      data_attivazione_portfolio { Date.new(2019, 3, 1) }
    end
  end
end
