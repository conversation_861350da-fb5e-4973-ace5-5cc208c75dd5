FactoryBot.define do
  factory :plan do
    ad_hoc { 0 }
    app_ready { false }
    reward { '40.0' }
    pda_kind_id { '2' }
    sorting { '1040' }
    visibility { 'y' }
    voice_minutes_threshold { 0 }
    sms_threshold { 0 }
    mb_threshold { 20000 }
    data_time_threshold { 0 }
    threshold_kind { 's' }
    minutes_vs_kind { 'fm' }
    no_voice { 'false' }
    no_over_threshold { 'false' }
    target { 'true' }
    target_rule_id { 1 }
    plan_description { '<p style="text-align: center;"><strong style="background-color: initial;"><span style="font-size: 20px;">3 </span></strong><span style="font-family: Arial, Helvetica, Verdana, Tahoma, sans-serif; background-color: initial;">GB LTE</span></p><p style="text-align: center;"><span style="font-family: Arial, Helvetica, Verdana, Tahoma, sans-serif; font-size: 12px;">100 MB GPRS</span></p>' }
    target_type { 'target_type_m' }
    kolme_reward { '' }
    kolme_avg_extra_reward { '' }
    plan_kind_list { 'Abb3VoceTOP' }
    pda_text { 'pda_text' }

    customer_kinds { build_list :customer_kind, 1, :cliente_privato }

    trait :eolo_casa do
      name { 'Eolo casa' }
      alternative_name { 'Eolo casa' }
      association :operator, factory: %i[operator eolo]
      association :service_kind, factory: %i[service_kind fisso]
      association :product_category, :fissa
      association :pda_kind, :eolo
      contract_kind_id { '1' }
      tariffa_cb { false }
      coverage_typology { 'ULL/FTTC/NGA' }
      posng_summary_key { 'test summary' }
    end

    trait :top_infinito do
      name { 'TOP Infinito' }
      alternative_name { 'TOP Infinito' }
      system_name_operator { 'TRE' }
      association :operator, factory: %i[operator tre]
      association :service_kind, factory: %i[service_kind mobile]
      association :product_category, :voce
      association :contract_kind, :abbonamento
      association :pda_kind, :tre_abbonamento
    end

    trait :top_400 do
      name { 'TOP 400' }
      alternative_name { 'TOP 400' }
      association :operator, factory: %i[operator tre]
      service_kind_id { '2' }
      product_category_id { '1' }
      contract_kind_id { '1' }
    end

    trait :top_800 do
      name { 'TOP 800' }
      alternative_name { 'TOP 800' }
      association :operator, factory: %i[operator tre]
      service_kind_id { '2' }
      product_category_id { '1' }
      contract_kind_id { '1' }
    end

    trait :tre_power_ricaricabile do
      name { '3 Power 10 Ricaricabile' }
      alternative_name { 'Power 10 Ricaricabile' }
      association :operator, factory: %i[operator tre]
      association :contract_kind, factory: %i[contract_kind ricaricabile]
      association :service_kind, factory: %i[service_kind mobile]
      association :product_category, factory: %i[product_category voce]
      tariffa_cb { true }
    end

    trait :joy do
      name { 'Joy' }
      alternative_name { 'Joy' }
      association :operator, factory: %i[operator fastweb]
      coverage_typology {'FTTH'}
      service_kind_id { '1' }
      product_category_id { '3' }
      contract_kind_id { '1' }
    end

    trait :jet do
      name { 'Jet' }
      alternative_name { 'Jet' }
      association :operator, factory: %i[operator fastweb]
      service_kind_id { '1' }
      association :product_category, factory: %i[product_category fissa]
      contract_kind_id { '1' }
      coverage_typology { 'ULL/FTTC/NGA' }
      posng_summary_key { 'test summary' }
    end

    trait :aria_smart_no_problem do
      name { 'Aria Smart No Problem' }
      alternative_name { 'Aria Smart No Problem' }
      association :operator, factory: %i[operator aria]
      service_kind_id { '1' }
      association :product_category, factory: %i[product_category fissa]
      contract_kind_id { '1' }
      coverage_typology { 'ULL/FTTC/NGA' }
      posng_summary_key { 'test summary' }
    end

    trait :special_plan_int do
      name { 'Special Plan Int' }
      alternative_name { 'Special Plan Int' }
      association :operator, factory: %i[operator tre]
      service_kind_id { '2' }
      product_category_id { '1' }
      contract_kind_id { '1' }
      visibility { 'i' }
    end

    trait :special_plan_invisible do
      name { 'Special Plan Invisible' }
      alternative_name { 'Special Plan Invisible' }
      association :operator, factory: %i[operator tre]
      service_kind_id { '2' }
      product_category_id { '1' }
      contract_kind_id { '1' }
      visibility { 'n' }
    end

    trait :super_internet_30_gb do
      name { 'Super Internet 30GB' }
      alternative_name { 'Super Internet 30GB' }
      association :operator, factory: %i[operator wind_business]
      service_kind_id { '2' }
      product_category_id { '1' }
      contract_kind_id { '1' }
      visibility { 'y' }
    end

    trait :very_ricaricabile do
      name { 'Very Ricaricabile' }
      alternative_name { 'Very Ricaricabile' }
      association :operator, factory: %i[operator very_mobile]
      association :contract_kind, factory: %i[contract_kind ricaricabile]
      association :service_kind, factory: %i[service_kind mobile]
      association :product_category, factory: %i[product_category voce]
      tariffa_cb { false }
      coverage_typology { 'ULL/FTTC/NGA' }
      posng_summary_key { 'test summary' }
      app_highlights { 'Lorem ipsum' }
      app_info { 'Lorem ipsum' }
    end

    trait :without_threshold do
      voice_minutes_threshold { 0 }
      sms_threshold { 0 }
      mb_threshold { 0 }
      data_time_threshold { 0 }
    end

    trait :tre_casa do
      name { 'Tre casa' }
      alternative_name { 'Tre casa' }
      association :operator, factory: %i[operator tre]
      association :service_kind, factory: %i[service_kind fisso]
      coverage_typology { 'ULL/FTTC/NGA' }
      posng_summary_key { 'test summary' }
    end

    trait :w3_ricaricabile do
      name { 'W3 Ricaricabile' }
      alternative_name { 'W3 Ricaricabile' }
      system_name_operator { 'WINDTRE' }
      association :operator, factory: %i[operator w3]
      association :contract_kind, factory: %i[contract_kind ricaricabile]
      association :service_kind, factory: %i[service_kind mobile]
      association :product_category, factory: %i[product_category voce]
      tariffa_cb { false }
      rpa_ready { true }
      app_ready { true }
      association :pda_kind, :tre_ricaricabile
      coverage_typology { 'ULL/FTTC/NGA' }
      posng_summary_key { 'test summary' }
    end

    trait :abbonamento_w3_rpa_ready do
      name { 'W3 Abbonamento' }
      alternative_name { 'W3 Abbonamento' }
      system_name_operator { 'WINDTRE' }
      association :operator, factory: %i[operator w3]
      association :service_kind, factory: %i[service_kind mobile]
      association :product_category, :voce
      association :contract_kind, :abbonamento
      association :pda_kind, :tre_abbonamento
      rpa_ready { true }
      app_ready { true }
      coverage_typology { 'ULL/FTTC/NGA' }
      posng_summary_key { 'test summary' }
    end

    trait :w3_landline do
      name { 'W3 Fissa' }
      alternative_name { 'W3 Fissa' }
      system_name_operator { 'WINDTRE' }
      association :operator, factory: %i[operator w3]
      association :service_kind, factory: %i[service_kind fisso]
      association :product_category, :fissa
      association :pda_kind, :eolo
      contract_kind_id { '1' }
      tariffa_cb { false }
      rpa_ready { true }
      app_ready { true }
      coverage_typology { 'ULL/FTTC/NGA' }
      posng_summary_key { 'test summary' }
    end

    trait :wind_fisso do
      name { 'wind casa' }
      alternative_name { 'wind casa' }
      system_name_operator { 'WINDTRE' }
      association :operator, factory: %i[operator wind]
      association :service_kind, factory: %i[service_kind fisso]
      association :product_category, :fissa
      association :pda_kind, :eolo
      contract_kind_id { '1' }
      tariffa_cb { false }
      request_mnp { true }
      coverage_typology { 'ULL/FTTC/NGA' }
      posng_summary_key { 'test summary' }
      quality_parameter { 'FTTC' }
    end

    comparator_category do
      ComparatorCategory.where(name: 'Abbonamento').first || create(:comparator_category, :abbonamento)
    end
  end
end
