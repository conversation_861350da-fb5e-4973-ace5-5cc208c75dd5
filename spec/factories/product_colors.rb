FactoryBot.define do
  factory :product_color do
    name {'random_color'}
    code {'random_color_code'}

    trait :white do
      name { 'Bianco' }
      code { '#FFFFFF' }
    end

    trait :gray do
      name { '<PERSON><PERSON><PERSON>' }
      code { '#808080' }
    end

    trait :black do
      name { 'Nero' }
      code { '#000000' }
    end

    initialize_with { ProductColor.find_or_initialize_by(name: name) }
  end
end