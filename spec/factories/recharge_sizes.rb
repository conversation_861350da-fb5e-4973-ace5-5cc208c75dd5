FactoryBot.define do
  factory :recharge_size do
    vat_type { '22' }
    commercial_approach_type { 'Margine' }
    association :aggregated_operator, factory: :aggregated_operator

    trait :gold_bet do
      name { 'Gold Bet' }
      amount { 50 }
      amount_charged { 50 }
      pin_srv { 'PINGLDBT' }
      offer_code { 'PINGLDBT' }
      sorting { 1 }
      pin { true }
      provider { 'manual' }
      association :aggregated_operator, factory: [:aggregated_operator, :gaming]
    end

    trait :lottomatica do
      name { 'Lottomatica' }
      amount { 50 }
      amount_charged { 50 }
      pin_srv { 'PINGLTM' }
      sorting { 1 }
      pin { true }
    end

    trait :optima_10 do
      name { 'optima_10' }
      amount { 10 }
      amount_charged { 10 }
      pin_srv { 'RicaricheOptima' }
      sorting { 1 }
      pin { false }
    end

    trait :optima_20 do
      name { 'optima_20' }
      amount { 20 }
      amount_charged { 20 }
      pin_srv { 'RicaricheOptima' }
      sorting { 1 }
      pin { false }
    end

    trait :optima_50 do
      name { 'optima_50' }
      amount { 50 }
      amount_charged { 50 }
      pin_srv { 'RicaricheOptima' }
      sorting { 1 }
      pin { false }
    end

    trait :bet_10 do
      name { 'bet_10' }
      amount { 10 }
      amount_charged { 5.5 }
      offer_code { 'BET10' }
      pin_srv { 'BETPIN' }
      sorting { 2 }
      provider { 'automatic' }
      pin { true }
    end

    trait :digimobil_5 do
      name { 'Digi Mobil Euro 5' }
      amount { 5 }
      amount_charged { 4.5 }
      offer_code { 'PINDGM5' }
      pin_srv { 'PINDGM' }
      sorting { 3 }
      provider { 'automatic' }
      pin { true }
    end

    trait :fastweb_euro_10 do
      name { 'Fastweb - Euro 10' }
      amount { 10 }
      amount_charged { 10 }
      sorting { 210 }
      provider { 'manual' }
      pin { false }
      association :operator, factory: %i[operator fastweb]
    end

    trait :poste_mobile_5 do
      name { 'PosteMobile - Euro 5' }
      amount { 5 }
      amount_charged { 5 }
      pin { false }
      provider { 'automatic' }
    end

    trait :tre_euro_10 do
      name { '3 - Euro 10' }
      amount { 10 }
      amount_charged { 5.5 }
      sorting { 1 }
      pin { false }
      provider { 'automatic' }
      euronet_quantity { 1 }
      euronet_base_size { 10 }
      association :operator, factory: %i[operator tre]
    end

    trait :tre_euro_10_contextual do
      name { '3 - Euro 10 contestuale' }
      amount { 10 }
      amount_charged { 5.5 }
      sorting { 1 }
      pin { false }
      provider { 'automatic' }
      euronet_quantity { 1 }
      euronet_base_size { 10 }
      association :operator, factory: %i[operator tre]
    end

    trait :tre_euro_15 do
      name { '3 - Euro 15' }
      amount { 15 }
      code { '305' }
      activation_visible { false }
      post_sale_visible { false }
      association :operator, factory: %i[operator tre]
      paymat_quantity { 3 }
      dealer_reward { 0 }
      provider { 'automatic' }
      euronet_quantity { 3 }
      euronet_base_size { 5 }
      note { 'ASS74 - Taglio di ricarica non più disponibile presso H3G.' }
      pin { false }
      amount_charged { 15 }
    end

    trait :tre_euro_25 do
      name { '3 - Euro 25' }
      amount { 25 }
      code { '307' }
      activation_visible { true }
      post_sale_visible { true }
      association :operator, factory: %i[operator tre]
      paymat_quantity { 5 }
      dealer_reward { 0 }
      provider { 'automatic' }
      euronet_quantity { 5 }
      euronet_base_size { 5 }
      pin { false }
      amount_charged { 25 }
    end

    trait :tre_euro_5 do
      name { '3 - Euro 5' }
      amount { 5 }
      code { '306' }
      activation_visible { false }
      post_sale_visible { false }
      association :operator, factory: %i[operator tre]
      paymat_quantity { 1 }
      dealer_reward { 0 }
      provider { 'automatic' }
      euronet_quantity { 1 }
      euronet_base_size { 5 }
      note { 'ASS74 - Taglio di ricarica non più disponibile presso H3G.' }
      pin { false }
      amount_charged { 5 }
    end

    trait :wind_euro_5 do
      name { 'Wind - Euro 5' }
      amount { 5 }
      association :operator, factory: %i[operator wind]
      euronet_quantity { 1 }
      euronet_base_size { 5 }
      pin { false }
      amount_charged { 5 }
    end

    trait :tnw_5 do
      name { 'Telecom Internazionale Welcome Euro 5' }
      amount { 5 }
      amount_charged { 4.5 }
      offer_code { 'TNW5' }
      pin_srv { 'PINTCC' }
      sorting { 944 }
      provider { 'automatic' }
      pin { true }
    end

    trait :tnw_10 do
      name { 'Telecom Internazionale Welcome Euro 10' }
      amount { 10 }
      amount_charged { 4.5 }
      offer_code { 'TNW10' }
      pin_srv { 'PINTCC' }
      sorting { 944 }
      provider { 'automatic' }
      pin { true }
    end

    trait :vodafone_carte_5 do
      name { 'Vodafone Carte servizi Euro 5' }
      amount { 5 }
      amount_charged { 4.5 }
      offer_code { 'VFSRV5' }
      pin_srv { 'VFPINSRV' }
      sorting { 3 }
      provider { 'automatic' }
      pin { true }
    end

    trait :w3_euro_5 do
      name { 'W3 - Euro 5' }
      amount { 5 }
      association :operator, factory: %i[operator w3]
      euronet_quantity { 1 }
      euronet_base_size { 5 }
      pin { false }
      amount_charged { 5 }
    end

    trait :w3_euro_100 do
      name { 'W3 - Euro 100' }
      amount { 100 }
      association :operator, factory: %i[operator w3]
      euronet_quantity { 1 }
      euronet_base_size { 5 }
      pin { false }
      amount_charged { 100 }
    end

    trait :retriable do
      retry_on_ko { true }
    end

    trait :with_paymat_operator do
      association :paymat_operator, factory: [:paymat_operator, :h3g]
    end

    initialize_with { RechargeSize.find_or_initialize_by(name: name) }
  end
end