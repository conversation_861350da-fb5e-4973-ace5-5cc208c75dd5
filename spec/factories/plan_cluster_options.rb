FactoryBot.define do
  factory :plan_cluster_option do
    association :plan, factory: %i[plan top_infinito]
    association :cluster_option, factory: %i[cluster_option standard]

    trait :age do
      association :cluster_option, :age

      value { { min_age: "10", max_age: "18" } }
    end

    trait :age_double_cluster do
      association :cluster_option, :age_double_cluster

      value { { min_age: '30', max_age: '60', outward_range: '1' } }
    end
  end
end