FactoryBot.define do
  factory :affiliation do
    source { 'agent' }
    additional_data { { 'dealer_operators' => 'very_mobile', 'dealer_type' => 'standard',
                        'dealer'           => { 'bank' => 'bnl', 'bank_branch' => 'milano', 'bank_address' => 'via tortona', 'company_kind' => 'spa', 'email' => '<EMAIL>', 'billing_street_type' => 'Via' },
                        'dealer_contact'   => { 'birth_province_id' => '1', 'birth_city' => 'pavia', 'role' => 'ceo', 'street_type' => 'Via', 'address' => 'tortona', 'number' => '37', 'zip' => '20124', 'city_id' => '1', 'email' => '<EMAIL>' },
                        'warehouse'        => { 'street_type' => 'Via' } } }
    stored_serialized { { "dealer_attributes" => { "pec"                        => "<EMAIL>", "iban" => "***************************", "swift_code" => "FEBIITM2XXX", "name" => "999", "role" => "dealer", "uuid" => "43298f45-098a-4a19-a44f-381707c4af41", "shield" => 0, "protested" => false, "tipo_gara" => "Standard", "collection" => false, "country_id" => 1, "tax_regime" => "regular", "partita_iva" => "***********", "prejudicial" => false, "assigned_fido" => "0.0", "codice_fiscale" => "***********", "remaining_fido" => "0.0", "ragione_sociale" => "Dealer One", "available_credit" => "0.0", "daily_operations" => "0.0", "enabled_for_chat" => true, "sap_withholding_tax_id" => 1, "ditta_individuale" => false, "intermediate_code" => "0", "dealer_category_id" => 1, "sdi_forwarder_code" => "9999999", "billing_address_via" => "via", "price_list_category" => "a", "sdd_payments_amount" => "0.0", "email_amministrativa" => "<EMAIL>", "iva_invito_fatturare" => "22", "last_balance_revenue" => "0.0", "payments_with_credit" => true, "billing_address_number" => "2", "reverse_charge_enabled" => true, "unpaid_invoices_amount" => "0.0", "billing_address_city_id" => 1, "gamma_payment_method_id" => 7, "telefono_amministrativo" => "0123456789", "billing_address_postalcode" => "20142", "recharges_credit_available" => "0.0", "second_last_balance_revenue" => "0.0",
                                                   "dealer_contacts_attributes" => [{ "cf" => "****************", "uuid" => "48f36bf0-2cf9-4bf9-be83-5f84edc10c4c", "last_name" => "g", "first_name" => "mik", "cellphone_1" => "3332112233", "date_of_birth" => "1979-09-14", "portal_enabled" => true, "mailup_attempts" => 0, "primary_contact" => false, "legal_representative" => false, "can_activate_simulation_plans" => false }],
                                                   "warehouses_attributes"      => [{ "cluster" => "main_w3sp_warehouse", 'fwa_installer' => false, "telefono1" => "021234", "zip" => "20142", "name" => "999", "uuid" => "2d3f4929-5b47-49dd-9fdf-666464cdd774", "place" => "mi", "ensign" => "nome insegna", "number" => "2", "address" => "via", "city_id" => 1, "disabled" => false, "country_id" => 1, "has_pinpad" => false, "kolme_code" => "SK202207001", "acea_enabled" => false, "has_facebook" => false, "has_whatsapp" => false, "opening_hours" => "10.00 - 12.00", "affiliation_opening_hours" => { before_noon: { from: "9:00", to: "12:00" }, after_noon: { from: "15:00", to: "19:00" } }, "weekly_closing_day" => "nessuno", "ad_hoc_enabled" => [], "compass_enabled" => "no", "findomestic_enabled" => "no", "warehouse_brands_warehouses_attributes" => [{ "warehouse_brand_id" => 1, "partnership_accepted" => "y" }] }] } } }
    created_at { 1.minutes.ago }

    association :agent, factory: %i(user agent with_internal_user_detail)
    association :creator_user, factory: %i[user admin]

    trait :pre_approved do
      status { 'pre_approved' }
      association :dealer, factory: %i(dealer with_dealer_contact with_warehouse)

      after(:build) do |affiliation|
        affiliation.stored_serialized = affiliation.stored_serialized.merge({
                                                                              "#{AffiliationSerializerConcern::DEALER_CONTACT_FIELD}": affiliation.dealer.dealer_contacts.first.id,
                                                                              "#{AffiliationSerializerConcern::WAREHOUSE_FIELD}":      affiliation.dealer.warehouses.first.id })
      end
    end
    
    trait :kolme_master_nexus do
      affiliation_type { "kolme_master_nexus" }
      
      stored_serialized { { "dealer_attributes" => { "pec"                        => "<EMAIL>", 
                                                     "iban"                       => "***************************", 
                                                     "swift_code"                 => "FEBIITM2XXX", 
                                                     "name"                       => "KMN Partner", 
                                                     "role"                       => "dealer", 
                                                     "uuid"                       => "43298f45-098a-4a19-a44f-381707c4af42",
                                                     "shield"                     => 0, 
                                                     "protested"                  => false, 
                                                     "tipo_gara"                  => "Standard", 
                                                     "collection"                 => false, 
                                                     "country_id"                 => 1, 
                                                     "tax_regime"                 => "regular", 
                                                     "partita_iva"                => "22222222222", 
                                                     "prejudicial"                => false, 
                                                     "assigned_fido"              => "0.0", 
                                                     "codice_fiscale"             => "88888888888", 
                                                     "remaining_fido"             => "0.0", 
                                                     "ragione_sociale"            => "Kolme Master Nexus Partner", 
                                                     "available_credit"           => "0.0", 
                                                     "daily_operations"           => "0.0", 
                                                     "enabled_for_chat"           => true, 
                                                     "sap_withholding_tax_id"     => 1, 
                                                     "ditta_individuale"          => false, 
                                                     "intermediate_code"          => "0", 
                                                     "dealer_category_id"         => 1, 
                                                     "sdi_forwarder_code"         => "8888888", 
                                                     "billing_address_via"        => "via", 
                                                     "price_list_category"        => "a", 
                                                     "sdd_payments_amount"        => "0.0", 
                                                     "email_amministrativa"       => "<EMAIL>", 
                                                     "iva_invito_fatturare"       => "22", 
                                                     "last_balance_revenue"       => "0.0", 
                                                     "payments_with_credit"       => true, 
                                                     "billing_address_number"     => "2", 
                                                     "reverse_charge_enabled"     => true, 
                                                     "unpaid_invoices_amount"     => "0.0", 
                                                     "billing_address_city_id"    => 1, 
                                                     "gamma_payment_method_id"    => 7, 
                                                     "telefono_amministrativo"    => "0123456789", 
                                                     "billing_address_postalcode" => "20142", 
                                                     "recharges_credit_available" => "0.0", 
                                                     "second_last_balance_revenue" => "0.0",
                                                   "dealer_contacts_attributes"   => [{ 
                                                     "cf"                         => "****************", 
                                                     "uuid"                       => "48f36bf0-2cf9-4bf9-be83-5f84edc10c4d", 
                                                     "last_name"                  => "Rossi", 
                                                     "first_name"                 => "Mario", 
                                                     "cellphone_1"                => "3332112244", 
                                                     "date_of_birth"              => "1980-10-15", 
                                                     "portal_enabled"             => true, 
                                                     "mailup_attempts"            => 0, 
                                                     "primary_contact"            => false, 
                                                     "legal_representative"       => false, 
                                                     "can_activate_simulation_plans" => false 
                                                   }],
                                                   "warehouses_attributes"        => [{ 
                                                     "cluster"                    => "main_w3sp_warehouse", 
                                                     "fwa_installer"              => false, 
                                                     "telefono1"                  => "021234", 
                                                     "zip"                        => "20142", 
                                                     "name"                       => "KMN Store", 
                                                     "uuid"                       => "2d3f4929-5b47-49dd-9fdf-666464cdd775", 
                                                     "place"                      => "mi", 
                                                     "ensign"                     => "Kolme Master Store", 
                                                     "number"                     => "2", 
                                                     "address"                    => "via", 
                                                     "city_id"                    => 1, 
                                                     "disabled"                   => false, 
                                                     "country_id"                 => 1, 
                                                     "has_pinpad"                 => false, 
                                                     "kolme_code"                 => "SKMN202504001", 
                                                     "acea_enabled"               => false, 
                                                     "has_facebook"               => false, 
                                                     "has_whatsapp"               => false, 
                                                     "opening_hours"              => "10.00 - 12.00", 
                                                     "affiliation_opening_hours"  => { 
                                                       before_noon: { from: "9:00", to: "12:00" }, 
                                                       after_noon: { from: "15:00", to: "19:00" } 
                                                     }, 
                                                     "weekly_closing_day"         => "nessuno", 
                                                     "ad_hoc_enabled"             => [], 
                                                     "compass_enabled"            => "no", 
                                                     "findomestic_enabled"        => "no", 
                                                     "warehouse_brands_warehouses_attributes" => [{ 
                                                       "warehouse_brand_id"       => 1, 
                                                       "partnership_accepted"     => "y" 
                                                     }] 
                                                   }] 
                                                 } 
                        } 
      }
      
      additional_data { { 
        'dealer_operators' => 'very_mobile', 
        'dealer_type' => 'standard',
        'company_name_change' => false,
        'dealer' => { 
          'bank' => 'bnl', 
          'bank_branch' => 'milano', 
          'bank_address' => 'via tortona', 
          'company_kind' => 'spa', 
          'email' => '<EMAIL>', 
          'billing_street_type' => 'Via' 
        },
        'dealer_contact' => { 
          'email' => '<EMAIL>' 
        },
        'warehouse' => { 
          'street_type' => 'Via' 
        }
      } }
    end

    before(:create) do |affiliation|
      street_type                                                     = create(:street_type, :via)
      affiliation.additional_data['dealer_contact']['street_type_id'] = street_type.id
      affiliation.additional_data['warehouse']['street_type_id']      = street_type.id

      city                                                                                      = create(:city, :milano)
      affiliation.stored_serialized['dealer_attributes']['billing_address_city_id']             = city.id
      affiliation.stored_serialized['dealer_attributes']['warehouses_attributes'][0]['city_id'] = city.id

      country                                                                                      = create(:country, :italy)
      affiliation.stored_serialized['dealer_attributes']['country_id']                             = country.id
      affiliation.stored_serialized['dealer_attributes']['warehouses_attributes'][0]['country_id'] = country.id

      create(:gamma_payment_method, id: 7)
      create(:operator, :w3)
      create(:operator, :very_mobile)
    end
  end
end
