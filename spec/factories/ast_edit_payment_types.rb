FactoryBot.define do
  factory :ast_edit_payment_type do
    phone_number { "MyString" }
    payment_method { "credit_card" }
    iban { "MyString" }
    credit_card_type { "visa" }
    credit_card_number { "1234567890123456" }
    credit_card_cvv { "123" }
    credit_card_expire_month { 1 }
    credit_card_expire_year { Time.now.year + 10 }
    notes { "MyText" }
    association :operator, factory: %i[operator eolo]
    association :customer
  end
end
