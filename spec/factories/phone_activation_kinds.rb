FactoryBot.define do
  factory :phone_activation_kind do
    final_operations_group { 2 }

    trait :rmd do
      id { 1 }
      code { "RMD" }
      description { "Ricaricabile da Mag. Dealer" }
      priority { 3 }
      final_operations_group { 2 }
    end
    trait :rmdr do
      id { 2 }
      code { "RMDR" }
      description { "Ricaricabile da Mag. Dealer + Ricarica Contestuale" }
      code { "RMD+3" }
    end
    trait :amd do
      id { 3 }
      code { "AMD" }
      description { "Abbonamento da Mag. Dealer" }
    end
    trait :amk do
      code { "AMK" }
      id { 4 }
      description { "Abbonamento da Mag. Kolme" }
    end
    trait :armd do
      id { 5 }
      code { "ARMD" }
      description { "Abbonamento Rateale da Mag. Kolme" }
      final_operations_group { 2 }
    end
    trait :armk do
      id { 6 }
      description { "Abbonamento Rateale da Mag. Kolme" }
      code { "ARMK" }
    end
    trait :armktiga do
      id { 29 }
      description { "Telefono Incluso GA da Mag. Kolme" }
      code { "ARMKTIGA" }
    end
    trait :armdtiga do
      id { 30 }
      description { "Telefono Incluso GA da Mag. Dealer" }
      code { "ARMDTIGA" }
    end
    trait :apdmd do
      id { 32 }
      code { "APDMD" }
      description { "Abbonamento Promo Device da Mag. Dealer" }
      final_operations_group { 2 }
      after(:create) do |phone_activation_kind, _evaluator|
        create(:phone_activation_kind_operation, :inserita_da_partner, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :upload_documenti, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :assegnazione_seriale, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :controllo_anagrafica, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :controllo_mdp, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :controllo_documenti, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :controllo_amministrativo, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :trasformazione_in_differita, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :verifica_imei, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :attivazione_completata, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :spedizione_effettuata, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :terminale_ricevuto, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :fattura_cliente_finale, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :fattura_anticipo, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :nota_di_credito_immediato, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :originali_cartacei_ricevuti, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :fattura_easycare, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :chiusura_convergenza, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :verifica_metodo_di_pagamento, phone_activation_kind: phone_activation_kind)
      end
    end
    trait :armd do
      id { 5 }
      code { "ARMD" }
      description { "Abbonamento Rateale da Mag. Kolme" }
      final_operations_group { 2 }
      after(:create) do |phone_activation_kind, _evaluator|
        create(:phone_activation_kind_operation, :inserita_da_partner, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :upload_documenti, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :assegnazione_seriale, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :controllo_anagrafica, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :controllo_mdp, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :controllo_documenti, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :controllo_amministrativo, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :trasformazione_in_differita, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :verifica_imei, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :attivazione_completata, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :spedizione_effettuata, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :terminale_ricevuto, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :fattura_cliente_finale, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :fattura_anticipo, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :nota_di_credito_immediato, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :originali_cartacei_ricevuti, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :fattura_easycare, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :chiusura_convergenza, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :verifica_metodo_di_pagamento, phone_activation_kind: phone_activation_kind)
      end
    end

    trait :fissa do
      id { 11 }
      code { "F" }
      description { "Fissa" }
    end
    trait :ad do
      id { 9 }
      code { "AD" }
      description { "Abbonamento Differito" }
    end
    trait :fpmd do
      id { 39 }
      code { "FPMD" }
      description { "Fissa con Prodotto da Mag. Dealer" }
      final_operations_group { 2 }
      after(:create) do |phone_activation_kind, _evaluator|
        create(:phone_activation_kind_operation, :inserita_da_partner, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :upload_documenti, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :controllo_anagrafica, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :controllo_mdp, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :controllo_documenti, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :controllo_amministrativo, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :trasformazione_in_differita, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :verifica_imei, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :attivazione_completata, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :spedizione_effettuata, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :terminale_ricevuto, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :fattura_cliente_finale, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :fattura_anticipo, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :nota_di_credito_immediato, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :originali_cartacei_ricevuti, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :fattura_easycare, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :chiusura_convergenza, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :verifica_metodo_di_pagamento, phone_activation_kind: phone_activation_kind)
        create(:phone_activation_kind_operation, :verifica_convergenza, phone_activation_kind: phone_activation_kind)
      end
    end
    trait :fpmk do
      code { "FPMK" }
      description { "Fissa con Prodotto da Mag. Kolme" }
    end

    trait :fcpsmk do
      code { "FCPSMK" }
      description { "Fissa con prenotazione seriale" }
    end

    trait :armdtiw3 do
      code { "ARMDTIW3" }
      description { "W3 Telefono Incluso GA da Mag. Dealer" }
    end

    trait :armktiw3 do
      code { "ARMKTIW3" }
      description { "W3 Telefono Incluso GA da Mag. Kolme" }
    end

    trait :rpmdw3 do
      id { 37 }
      code { "RPMDW3" }
      description { "W3 Ricaricabile pura GA da Mag. Dealer" }
    end

    trait :ssmdw3 do
      code { "SSMDW3" }
      id { 36 }
      description { "W3 Solo SIM GA da Mag. Dealer" }
    end

    trait :rpmdv do
      id { 38 }
      code { 'RPMDV' }
      description { 'Very Ricaricabile pura GA da Mag. Dealer' }
    end

    trait :w3frmk do
      id { 39 }
      code { "W3FRMK" }
      description { "W3 Fissa Rateale da Mag. Kolme" }
    end

    trait :w3frmd do
      id { 40 }
      code { "W3FRMD" }
      description { "W3 Fissa Rateale da Mag. Dealer" }
    end

    trait :w3f do
      id { 41 }
      code { "W3F" }
      description { "W3 Rete Fissa" }
    end

    trait :armkcbw3 do
      id { 42 }
      code { "ARMKCBW3" }
      description { "W3 Telefono Incluso CB da Mag. Kolme" }
    end

    initialize_with { PhoneActivationKind.find_or_initialize_by(code: code) }
  end
end
