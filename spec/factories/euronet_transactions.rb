FactoryBot.define do
  factory :euronet_transaction do
    trid { "119021018" }

    trait :check_request do
      call_type { "check_request" }
    end
    trait :confirm_request do
      call_type { "confirm_request" }
    end

    trait :check_request_recharge_pin do
      call_type { "check_request" }
      origid { "2941464505" }
      trid { "149282176" }
      rc { "000" }
      status { 20 }
      auth { "704376" }
      amt { 500 }
      rrn { "EPAC45086414651" }
      serial { "003300710331" }
      pin_code { "7869328983473" }
    end
  end
end
