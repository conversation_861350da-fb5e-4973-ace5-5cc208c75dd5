FactoryBot.define do
  factory :region do
    trait :estero do
      # id 4
      code { "23" }
      description { "Estero" }
    end
    
    trait :lombardia do
      # id 4
      code { "4" }
      description { "Lombardia" }
    end

    trait :liguria do
      # id 4
      code { "3" }
      description { "Liguria" }
    end

    trait :sicilia do
      code { "19" }
      description { "Sicilia" }
    end

    trait :trentino do
      code { "6" }
      description { "Trentino Alto Adige" }
    end

    initialize_with { Region.find_or_initialize_by(code: code) }
  end
end
