require 'rails_helper'

describe CustomerValidatorFactory do
  let(:validate_insurance) { stub_model(Customer, origin: 'insurance') }
  let(:validate_financing) { stub_model(Customer, origin: 'financing') }
  let(:validate_phone_activation) { stub_model(Customer, origin: 'phone_activation') }

  it { expect(described_class.for(validate_insurance.origin)).to eq CustomerPartialValidator }
  it { expect(described_class.for(validate_financing.origin)).to eq CustomerFinancingValidator }
  it { expect(described_class.for(validate_phone_activation.origin)).to eq CustomerPartialValidator }
end