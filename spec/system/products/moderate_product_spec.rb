# encoding: utf-8

require "rails_helper"
include ProductFormHelper

shared_examples "moderator for" do
  describe "imported products" do
    let!(:italy_purchase) { create(:italy_purchase) }
    let!(:italy_purchase_return) { create(:italy_purchase_return) }
    let!(:italy_sales) { create(:italy_sales) }
    let!(:italy_sales_return) { create(:italy_sales_return) }
    let!(:statistic_group1) { create(:statistic_group1) }
    let!(:statistic_group2) { create(:statistic_group2) }
    let!(:family_product) { create(:family_product) }
    let(:attiva_brand) { create(:attiva_brand) }
    let(:attiva_category) { create(:attiva_category) }
    let(:attiva_subcategory) { create(:attiva_subcategory) }
    let(:company_brand) { create(:company_brand) }
    let!(:sap_product_kind) { create(:sap_product_kind, code: 'TP3') }
    let!(:sap_distribution_chain) { create(:sap_distribution_chain, code: 'Z5') }
    let(:product) do
      create(:product, :attiva_imported,
             attiva_brand:       attiva_brand, attiva_category: attiva_category,
             attiva_subcategory: attiva_subcategory,
             company_brand: company_brand,
             sap_product_kind: sap_product_kind,
             sap_distribution_chain: sap_distribution_chain)
    end

    before do
      moderator
      login_as moderator
    end

    context "new product" do
      scenario do
        #has kolme mandatory tags
        visit edit_moderate_products_path(product)
        expect(page).to have_css("#product_store_tag_list_cnt")
        expect(page).not_to have_css("#product_archived")

        #has gamma mandatory tag inputs
        visit edit_moderate_products_path(product)

        expect(page).to have_css(".gamma_section")
        expect(page.find(".gamma_section")).to have_css("select#product_company_brand_id")
        expect(page.find(".gamma_section")).to have_css("select#product_statistic_group1_id")
        expect(page.find(".gamma_section")).to have_css("select#product_statistic_group3_id")
        expect(page.find(".gamma_section")).to have_css("select#product_family_product_id")
        expect(page.find(".gamma_section")).not_to have_css("select#product_italy_purchase_return_id")
        expect(page.find(".gamma_section")).not_to have_css("select#product_italy_purchase_id")
        expect(page.find(".gamma_section")).not_to have_css("select#product_italy_sales_return_id")
        expect(page.find(".gamma_section")).not_to have_css("select#product_italy_sales_id")
        expect(page.find("#current_availability")).to have_content(product.current_availability)

        create(:attiva_product, product_id: product.id)
        visit edit_moderate_products_path(product)
        expect(page).not_to have_xpath('//div[starts-with(@id, "diff-panel-")]')
        visit products_path
        expect(page).to have_content("PRODOTTI / LISTA")
        expect(page).not_to have_content(product.name)
      end
    end

    # 12. check of can_be_requested false after toggle visibility in /products/moderate/:id/edit
    context "when attiva_brand is visible" do
      describe "and becomes not visible", solr: true do
        before do
          attiva_brand.update_attribute(:visible, false) && product.index
          Sunspot.commit(true)
        end

        scenario "product is no more visible in store index" do
          visit store_products_path
          expect(page).to have_content("ORDINI / CATALOGO")
          expect(page).not_to have_content(product.name)
        end

        scenario "product is no more visible in index" do
          visit products_path
          expect(page).to have_content("PRODOTTI / LISTA")
          expect(page).not_to have_content(product.name)
        end

        scenario "product is no more visible in moderate products index" do
          visit moderate_products_path
          expect(page).to have_text("PRODOTTI / LISTA MODERAZIONI")
          expect(page).not_to have_content(product.name)
        end
      end
    end

    context "attiva_brand is not visible and product cannot be requested" do
      describe "when attiva_brand become visible", solr: true do
        let(:attiva_brand) { create(:attiva_brand, visible: false) }
        let(:product) do
          create(:product, :attiva_imported,
                 can_be_requested:   false, moderated: true,
                 attiva_brand:       attiva_brand, attiva_category: attiva_category,
                 attiva_subcategory: attiva_subcategory, company_brand: company_brand,
                 sap_product_kind: sap_product_kind,
                 sap_distribution_chain: sap_distribution_chain)
        end

        before do
          attiva_brand.toggle_visibility!
        end

        scenario "moderated product still not visible in store product list" do
          visit store_products_path
          expect(page).to have_content("ORDINI / CATALOGO")
          expect(page).not_to have_content(product.name)
        end

        scenario "moderated product still not visible in product list" do
          visit products_path
          expect(page).to have_content("PRODOTTI / LISTA")
          expect(page).not_to have_content(product.name)
        end

        scenario "moderated product still not visible in moderate list" do
          visit moderate_products_path
          expect(page).to have_text("PRODOTTI / LISTA MODERAZIONI")
          expect(page).not_to have_content(product.name)
        end
      end
    end

    context "when listing products" do
      before do
        product.archived = true
        product.save
      end

      it "exist checkbox for archived display" do
        visit moderate_products_path
        expect(page).to have_css("#search_archived_eq")
      end

      it "product archived is not visibile in list by default" do
        visit moderate_products_path
        expect(page).not_to have_content(product.name)
      end
      it "product archived have red background" do
        visit moderate_products_path
        check "search_archived_eq"
        click_button "Cerca"
        expect(page).to have_css(".enable")
        expect(page).to have_css(".deleted")
        expect(page).to have_content(product.name)
        expect(page).not_to have_css(".fa-flask")
      end
    end
  end
end

context "user with role super_user", vcr: true do
  it_behaves_like "moderator for" do
    let(:moderator) { create(:user, :super_user) }
  end
end

context "user with role admin", vcr: true do
  it_behaves_like "moderator for" do
    let(:moderator) { create(:user, :admin) }
  end
end
