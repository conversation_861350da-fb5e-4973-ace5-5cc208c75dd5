# encoding: utf-8

require "rails_helper"
require "config"

describe "Number portability form" do
  include IntegrationHelpers

  let!(:portability_operator_fixed) { create(:portability_operator, :tim_fisso) }

  let(:admin)                    { create(:user, :admin) }
  let(:phone_activation)         { create(:phone_activation, :wind_fisso) }
  let(:number_portability_route) { "/phone_activations/#{phone_activation.id}/number_portability_detail/new" }

  before :all do
    setup_default_enviroment
  end

  before do
    phone_activation.plan.update_attribute(:coverage_typology, "ULL/FTTC/NGA")
  end

  describe "As an admin" do
    scenario "I can use a preset portability operator" do
      expect(phone_activation.portability_operators).not_to be_empty
      expect(NumberPortabilityDetail.required_for?(phone_activation.product_category)).to be_truthy
      login_as(admin)
      visit number_portability_route
      expect(page).to have_content("Effettuare portabilità")
      select("Sì", from: "phone_activation_do_number_portability")
      expect(page).to have_content("Numero di telefono (senza prefisso)")
      fill_in "Prefisso", with: "02"
      fill_in "Numero di telefono (senza prefisso)", with: "3234567890"
      select("TIM (Telecom Italia)", from: "source_operator_id_for_fixed_line")
      fill_in "Codice migrazione Voce", with: "01A"
      click_button "Salva e continua"
      expect(page).to have_content("Controllo copertura")
      expect(phone_activation.reload.number_portability_detail.prefix).to eq "02"
    end
  end
end
