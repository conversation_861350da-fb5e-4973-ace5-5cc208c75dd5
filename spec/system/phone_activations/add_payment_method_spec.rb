require "rails_helper"
require "config"

describe "Add Payment method" do
  include IntegrationHelpers

  let(:admin) { create(:user, :admin) }

  let(:operator_wind) { create(:operator, :wind) }
  let(:payment_method_sdd) { create(:payment_method, code: "rid", description: "SDD") }
  let(:phone_activation) { create(:phone_activation, :abbonamento_ricaricabile) }

  let(:add_payment_method_url) { "/phone_activations/#{phone_activation.id}/payment_method_detail/new" }

  # scenario "visit page add payment_method_detail credit_card and operator 3" do
  #   login_as(admin)
  #   visit add_payment_method_url
  #
  #   expect(page).to have_content("Metodo di pagamento scelto: Carta di Credito")
  #   expect(page).to have_content("Dove trovo il codice di sicurezza?")
  #   expect(page).to have_selector("#payment_method_detail_cc_cvv")
  #   expect(page).to have_content("Diverso intestatario")
  #   expect(page).to have_content("Utilizzo PinPad")
  #   find(:css, "#pinpad_code_toggle").set(true)
  #
  #   # click_on "Utilizzo PinPad"
  #   expect(page).to have_content("Codice autorizzativo PinPad")
  #
  #   #visit page add payment_method_detail with operator wind and credit card method
  #   phone_activation.update_column(:operator_id, operator_wind.id)
  #
  #   visit add_payment_method_url
  #   have_default_expectations
  #   click_different_accountholder
  #   have_credit_card_fields
  #   does_not_have_cvv_field
  # end

  def click_different_accountholder
    find("#payment_method_detail_different_accountholder").click
  end

  def does_not_have_cvv_field
    expect(page).not_to have_content("Dove trovo il codice di sicurezza?")
    expect(page).not_to have_selector("#payment_method_detail_cc_cvv")
  end

  def have_credit_card_tre_fields
    expect(page).to have_selector("#payment_method_detail_different_accountholder_first_name")
    expect(page).to have_selector("#payment_method_detail_different_accountholder_last_name")
    expect(page).to have_selector("#payment_method_detail_different_accountholder_birth_date")
    expect(page).to have_selector("#payment_method_detail_different_accountholder_birth_place")
    expect(page).to have_selector("#payment_method_detail_different_accountholder_cf_or_vat")
    expect(page).to have_selector("#payment_method_detail_different_accountholder_address")
    expect(page).to have_selector("#payment_method_detail_different_accountholder_number")
    expect(page).to have_selector("#payment_method_detail_different_accountholder_zip")
    expect(page).to have_selector("#payment_method_detail_different_accountholder_city")
    expect(page).to have_selector("#payment_method_detail_different_accountholder_province_id")
  end

  def have_credit_card_fields
    expect(page).to have_selector("#payment_method_detail_different_accountholder_cc_name")
  end

  def have_default_expectations
    #expect(page.status_code).to eq 200
    expect(page).to have_content("Diverso intestatario")
  end

  def have_sdd_fields
    expect(page).to have_selector("#payment_method_detail_different_accountholder_first_name")
    expect(page).to have_selector("#payment_method_detail_different_accountholder_last_name")
    expect(page).to have_selector("#payment_method_detail_accountholder_name_or_company_name")
    expect(page).to have_selector("#payment_method_detail_accountholder_cf_or_vat")
  end
end
