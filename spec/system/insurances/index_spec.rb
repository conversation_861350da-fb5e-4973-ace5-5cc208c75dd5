# encoding: utf-8

require "rails_helper"
require "config"

describe "index Easycare" do
  include IntegrationHelpers

  let(:admin) { create(:user, :admin) }
  let(:dealer) { create(:dealer, :generic_dealer, :with_warehouse) }
  let(:dealer_second) { create(:dealer, :generic_dealer, :with_warehouse, name: "<PERSON>") }
  let(:primary_contact) { create(:user, :dealer_user) }
  let(:primary_dealer_contact) { create(:dealer_contact, dealer: dealer, user: primary_contact, primary_contact: true) }
  let!(:operation_inserita_da_partner) { create(:operation, :inserita_da_partner) }

  let(:phone_activation) { create(:phone_activation, :abbonamento_ricaricabile, easycare_insurance: true, activated_at: DateTime.now) }
  let!(:insurance_attivazione) { create(:insurance, :privato, :iphone, product_invoice_type: "OperatorContract", dealer: dealer, phone_activation: phone_activation, sent: true) }

  let!(:insurance_fattura) { create(:insurance, :azienda, :iphone, product_invoice_type: "fattura", dealer: dealer, product_serial: "511787662592306") }
  let!(:insurance_scontrino) { create(:insurance, :privato, :iphone, product_invoice_type: "scontrino", dealer: dealer_second, product_serial: "524910840745118") }

  before do
    setup_default_enviroment
    create(:access_flag, :access_recharges)
    create(:access_flag, :access_orders)
    primary_dealer_contact
    primary_contact.reload
    set_access_flags(access_recharges: false, access_orders: true, collection: false)
  end

  # scenario "dealer user cannot see force_cancel button" do
  #   login_as(primary_contact)
  #   visit "/easycares"
  #   expect(page).not_to have_selector("#status_")
  #   expect(page).to_not have_selector("#collapse_insurance_heading")
  #   expect(page).to have_selector("#insurance_list")
  #   expect(page).to have_selector("#insurance_list tr", count: 3)
  #   expect(page).to have_selector("a.force_cancel", count: 0)
  # end

  scenario "Admin user can see force_cancel button" do
    login_as(admin)
    visit "/easycares"
    expect(page).to have_selector("#status_")
    expect(page).to have_selector("#insurance_list")
    expect(page).to have_selector("#insurance_list tr", count: 4)
    expect(page).to have_selector("a.force_cancel", count: 1)

    #Admin user can see force_cancel button for sent insurances
    insurance_fattura.update_column(:sent, true)
    insurance_scontrino.update_column(:sent, true)
    visit "/easycares"
    expect(page).to have_selector("#insurance_list")
    expect(page).to have_selector("#insurance_list tr", count: 4)
    expect(page).to have_selector("a.force_cancel", count: 3)

    #Admin user can see force_cancel button for unsent insurances with phone activation
    insurance_fattura.update_column(:sent, true)
    insurance_scontrino.update_column(:sent, true)
    insurance_attivazione.update_column(:sent, false)
    visit "/easycares"
    expect(page).to have_selector("#insurance_list")
    expect(page).to have_selector("#insurance_list tr", count: 4)
    expect(page).to have_selector("a.force_cancel", count: 3)
  end


  private

  def set_access_flags(access_recharges:, access_orders:, collection:)
    dealer.update_column(:collection, collection)
    primary_dealer_contact.access_flags << AccessFlag.find_by(name: AccessFlag::RECHARGES) if access_recharges
    primary_dealer_contact.access_flags << AccessFlag.find_by(name: AccessFlag::ORDERS) if access_orders
  end
end
