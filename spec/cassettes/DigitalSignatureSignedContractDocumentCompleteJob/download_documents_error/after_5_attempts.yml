---
http_interactions:
- request:
    method: get
    uri: https://demo.esignanywhere.net/Api/v6/envelope/wrong
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept:
      - "*/*"
      Accept-Encoding:
      - gzip, deflate
      User-Agent:
      - rest-client/2.0.2 (darwin22.5.0 arm) ruby/2.5.3p105
      Authorization:
      - Bearer r8wphqrw64j9928bwvg2pzkj5bwh05j1s4t6e1jjq2l248i2wvz3gysjmjht8gnl
  response:
    status:
      code: 400
      message: Bad Request
    headers:
      Date:
      - Tue, 25 Jul 2023 12:06:26 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '211'
      Connection:
      - keep-alive
      Cache-Control:
      - no-cache,no-cache, no-store, must-revalidate
      Pragma:
      - no-cache
      Expires:
      - "-1"
      Server:
      - ''
      Flow-Log-Trace-Id:
      - caeefc24-84e7-4767-be7a-26753591f7df
      Api-Supported-Versions:
      - 3, 4, 5, 6
      X-Powered-By:
      - ''
      Strict-Transport-Security:
      - max-age=31536000
      X-Content-Type-Options:
      - nosniff
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Permissions-Policy:
      - geolocation=*
    body:
      encoding: UTF-8
      string: '{"ErrorId":"ERR0011","Message":"ERR0011 - Parameter is invalid. - [ApiInvalidParameterLocation:
        EnvelopeId] [ApiInvalidParameterReason: Unable to parse as GUID]","TraceId":"caeefc24-84e7-4767-be7a-26753591f7df"}'
    http_version:
  recorded_at: Tue, 25 Jul 2023 12:06:26 GMT
- request:
    method: get
    uri: https://demo.esignanywhere.net/Api/v6/file/
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept:
      - "*/*"
      Accept-Encoding:
      - gzip, deflate
      User-Agent:
      - rest-client/2.0.2 (darwin22.5.0 arm) ruby/2.5.3p105
      Authorization:
      - Bearer r8wphqrw64j9928bwvg2pzkj5bwh05j1s4t6e1jjq2l248i2wvz3gysjmjht8gnl
  response:
    status:
      code: 404
      message: Not Found
    headers:
      Date:
      - Tue, 25 Jul 2023 12:06:26 GMT
      Content-Length:
      - '0'
      Connection:
      - keep-alive
      Cache-Control:
      - no-cache, no-store, must-revalidate
      Server:
      - ''
      Flow-Log-Trace-Id:
      - 683fc5f9-5fef-495e-bea2-fab6f32e47ab
      X-Powered-By:
      - ''
      Strict-Transport-Security:
      - max-age=31536000
      X-Content-Type-Options:
      - nosniff
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Permissions-Policy:
      - geolocation=*
    body:
      encoding: UTF-8
      string: ''
    http_version:
  recorded_at: Tue, 25 Jul 2023 12:06:26 GMT
- request:
    method: get
    uri: https://demo.esignanywhere.net/Api/v6/file/
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept:
      - "*/*"
      Accept-Encoding:
      - gzip, deflate
      User-Agent:
      - rest-client/2.0.2 (darwin22.5.0 arm) ruby/2.5.3p105
      Authorization:
      - Bearer r8wphqrw64j9928bwvg2pzkj5bwh05j1s4t6e1jjq2l248i2wvz3gysjmjht8gnl
  response:
    status:
      code: 404
      message: Not Found
    headers:
      Date:
      - Tue, 25 Jul 2023 12:06:27 GMT
      Content-Length:
      - '0'
      Connection:
      - keep-alive
      Cache-Control:
      - no-cache, no-store, must-revalidate
      Server:
      - ''
      Flow-Log-Trace-Id:
      - c79ac1e2-89ac-4516-827c-6e4bf8222584
      X-Powered-By:
      - ''
      Strict-Transport-Security:
      - max-age=31536000
      X-Content-Type-Options:
      - nosniff
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Permissions-Policy:
      - geolocation=*
    body:
      encoding: UTF-8
      string: ''
    http_version:
  recorded_at: Tue, 25 Jul 2023 12:06:27 GMT
recorded_with: VCR 5.0.0
