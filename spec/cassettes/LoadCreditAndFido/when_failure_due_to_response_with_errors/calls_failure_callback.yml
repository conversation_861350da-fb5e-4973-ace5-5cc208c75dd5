---
http_interactions:
- request:
    method: post
    uri: http://localhost:8084/
    body:
      encoding: UTF-8
      string: ! "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n<WebServiceRequest>\n\t<SessionCookie>5f6869ba-3aa8-41fd-8847-a7cae6164a7b</SessionCookie>\n\t<RequestAction>REGANAG</RequestAction>\n\t<RequestParameters>\n
        \       <parameter name=\"Operatore\">kolme.0002</parameter>\n        <parameter
        name=\"Terminale\">PC01</parameter>\n        <parameter name=\"DataOraInizio\">2017-04-05
        10:39:17 +0200</parameter>\n        <parameter name=\"DataOraFine\">2017-04-05
        10:39:17 +0200</parameter>\n        <parameter name=\"IncludiDebug\">True</parameter>\n
        \   </RequestParameters>\n\t<RequestData Rows=\"1\">\n\t\t<Anagrafica>\n\t\t
        \   <GUID>c414a5e0-4b02-4b01-9eaf-7eb2912a4faa</GUID>\n\t\t\t<Codice>84</Codice>\n
        \           <Tipo>CF</Tipo>\n            <CodIntermedio>4</CodIntermedio>\n
        \           <PrivatoAzienda>A</PrivatoAzienda>\n            <RagioneSociale><![CDATA[Brigitta
        Marian]]></RagioneSociale>\n            <Cognome><![CDATA[]]></Cognome>\n
        \           <Nome><![CDATA[]]></Nome>\n            <PartitaIVA>61628168584</PartitaIVA>\n
        \           <CodiceFiscale></CodiceFiscale>\n            <Indirizzo><![CDATA[Rotonda
        Caputo, 2]]></Indirizzo>\n            <CAP>23504</CAP>\n            <Citta><![CDATA[San
        Arcibaldo salentino]]></Citta>\n            <Provincia>BS</Provincia>\n            <CodCatastale></CodCatastale>\n
        \           <Nazione>IT</Nazione>\n\t\t\t<Telefono></Telefono>\n\t\t\t<Cellulare></Cellulare>\n\t\t\t<Fax></Fax>\n
        \           <Email></Email>\n            <Web></Web>\n            <CodPagamento>BFM</CodPagamento>\n
        \           <IBAN></IBAN>\n            <ExtraFido>0</ExtraFido>\n            <CodIva>1</CodIva>\n
        \           <CodRitenuta>0</CodRitenuta>\n            <DataAcquisizione>2017-04-05</DataAcquisizione>\n
        \           <Dismissione><![CDATA[]]></Dismissione>\n            <Catena><![CDATA[]]></Catena>\n
        \           <Note><![CDATA[anagrafica di test]]></Note>\n            \n\t\t</Anagrafica>\n\t</RequestData>\n</WebServiceRequest>\n\n\n"
    headers:
      Accept:
      - application/xml
      Accept-Encoding:
      - gzip, deflate
      Content-Type:
      - text/xml; charset=utf-8
      Content-Length:
      - '1886'
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private
      Content-Length:
      - '1606'
      Content-Type:
      - text/xml; Charset=ISO-8859-1
      Expires:
      - Wed, 05 Apr 2017 08:39:17 GMT
      Server:
      - Microsoft-IIS/8.5
      Set-Cookie:
      - ASPSESSIONIDCCSSDASB=HAIBCGFCELCMLMOCNPBEFJDJ; path=/
      X-Powered-By:
      - ASP.NET
      Date:
      - Wed, 05 Apr 2017 08:39:17 GMT
    body:
      encoding: US-ASCII
      string: ! "<?xml version=\"1.0\" encoding=\"ISO-8859-1\" standalone=\"no\"?>\r\n<WebServiceResponse>\r\n\t<SessionCookie>5f6869ba-3aa8-41fd-8847-a7cae6164a7b</SessionCookie>\r\n\t<RequestAction>REGANAG</RequestAction>\r\n\t<ActionResult>0</ActionResult>\r\n\t<Results
        Count=\"1\" Size=\"133 bytes\">\r\n\t\t<Anagrafica GUID=\"c414a5e0-4b02-4b01-9eaf-7eb2912a4faa\">\r\n\t\t\t<Result>0</Result>\r\n\t\t</Anagrafica>\r\n\t</Results>\r\n\t<UserMessages
        Count=\"2\" ErrorCount=\"0\">\r\n\t\t<Message ID=\"\" Group=\"clsRegAnagrafiche.ExecuteRequest\"
        Field=\"c414a5e0-4b02-4b01-9eaf-7eb2912a4faa\" Type=\"info\" TypeUI=\"\"><![CDATA[Elaboro
        Anagrafica 1/1:]]></Message>\r\n\t\t<Message ID=\"\" Group=\"clsRegAnagrafiche.ExecuteRequest\"
        Field=\"c414a5e0-4b02-4b01-9eaf-7eb2912a4faa\" Type=\"warning\" TypeUI=\"\"><![CDATA[
        ==&gt; ERRORE!]]></Message>\r\n\t</UserMessages>\r\n\t<DebugMessages Count=\"5\"
        ErrorCount=\"2\">\r\n\t\t<Message ID=\"\" Group=\"\" Field=\"\" Type=\"info\"
        TypeUI=\"\"><![CDATA[[0,00 ms | 0,00 ms]: inizio sessione]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[15,00 ms
        | 15,00 ms]: Eseguita funzione: [Valida_ParametriStandard]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"error\" TypeUI=\"\"><![CDATA[[250,00
        ms | 265,00 ms]: [c414a5e0-4b02-4b01-9eaf-7eb2912a4faa - Brigitta Marian]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"ValidaContenutiAnagrafica\" Field=\"CodIva\" Type=\"error\"
        TypeUI=\"\"><![CDATA[Codice IVA non esistente in GAMMA: [1]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[0,00 ms
        | 265,00 ms]: fine sessione]]></Message>\r\n\t</DebugMessages>\r\n\t<ProcessingTime>281,00ms</ProcessingTime>\r\n</WebServiceResponse>\r\n\r\n"
    http_version: 
  recorded_at: Wed, 05 Apr 2017 08:39:18 GMT
- request:
    method: post
    uri: http://localhost:8084/
    body:
      encoding: UTF-8
      string: ! "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n<WebServiceRequest>\n\t<SessionCookie>4185f88a-de05-4b76-ad6a-742a5030fd84</SessionCookie>\n\t<RequestAction>REGANAG</RequestAction>\n\t<RequestParameters>\n
        \       <parameter name=\"Operatore\">kolme.0002</parameter>\n        <parameter
        name=\"Terminale\">PC01</parameter>\n        <parameter name=\"DataOraInizio\">2017-04-05
        10:39:18 +0200</parameter>\n        <parameter name=\"DataOraFine\">2017-04-05
        10:39:18 +0200</parameter>\n        <parameter name=\"IncludiDebug\">True</parameter>\n
        \   </RequestParameters>\n\t<RequestData Rows=\"1\">\n\t\t<Anagrafica>\n\t\t
        \   <GUID>c414a5e0-4b02-4b01-9eaf-7eb2912a4faa</GUID>\n\t\t\t<Codice>84</Codice>\n
        \           <Tipo>CF</Tipo>\n            <CodIntermedio>4</CodIntermedio>\n
        \           <PrivatoAzienda>A</PrivatoAzienda>\n            <RagioneSociale><![CDATA[Brigitta
        Marian]]></RagioneSociale>\n            <Cognome><![CDATA[]]></Cognome>\n
        \           <Nome><![CDATA[]]></Nome>\n            <PartitaIVA>61628168584</PartitaIVA>\n
        \           <CodiceFiscale></CodiceFiscale>\n            <Indirizzo><![CDATA[Rotonda
        Caputo, 2]]></Indirizzo>\n            <CAP>23504</CAP>\n            <Citta><![CDATA[San
        Arcibaldo salentino]]></Citta>\n            <Provincia>BS</Provincia>\n            <CodCatastale></CodCatastale>\n
        \           <Nazione>IT</Nazione>\n\t\t\t<Telefono></Telefono>\n\t\t\t<Cellulare></Cellulare>\n\t\t\t<Fax></Fax>\n
        \           <Email></Email>\n            <Web></Web>\n            <CodPagamento>BFM</CodPagamento>\n
        \           <IBAN></IBAN>\n            <ExtraFido>0</ExtraFido>\n            <CodIva>1</CodIva>\n
        \           <CodRitenuta>0</CodRitenuta>\n            <DataAcquisizione>2017-04-05</DataAcquisizione>\n
        \           <Dismissione><![CDATA[]]></Dismissione>\n            <Catena><![CDATA[]]></Catena>\n
        \           <Note><![CDATA[anagrafica di test]]></Note>\n            \n                <Destinazione>\n
        \                   <GUID>1775728f-a3b4-4bca-9bed-d33f86648ebd</GUID>\n                    <Tipo>R</Tipo>\n
        \                   <RagioneSociale><![CDATA[Hardware & Servizi]]></RagioneSociale>\n
        \                   <Contatto><![CDATA[]]></Contatto>\n                    <Indirizzo>Via
        Alessandro Maragliano, 10</Indirizzo>\n                    <CAP>27058</CAP>\n
        \                   <Citta><![CDATA[Milano]]></Citta>\n                    <Provincia>MI</Provincia>\n
        \                   <Nazione>IT</Nazione>\n                    <Telefono><![CDATA[]]></Telefono>\n
        \                   <Cellulare><![CDATA[]]></Cellulare>\n                    <Fax><![CDATA[]]></Fax>\n
        \                   <Email><![CDATA[]]></Email>\n                    <Note></Note>\n
        \               </Destinazione>\n            \n\t\t</Anagrafica>\n\t</RequestData>\n</WebServiceRequest>\n\n\n"
    headers:
      Accept:
      - application/xml
      Accept-Encoding:
      - gzip, deflate
      Content-Type:
      - text/xml; charset=utf-8
      Content-Length:
      - '2693'
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private
      Content-Length:
      - '1606'
      Content-Type:
      - text/xml; Charset=ISO-8859-1
      Expires:
      - Wed, 05 Apr 2017 08:39:17 GMT
      Server:
      - Microsoft-IIS/8.5
      Set-Cookie:
      - ASPSESSIONIDCCSSDASB=IAIBCGFCKNLJAGABAOAKFKEG; path=/
      X-Powered-By:
      - ASP.NET
      Date:
      - Wed, 05 Apr 2017 08:39:17 GMT
    body:
      encoding: US-ASCII
      string: ! "<?xml version=\"1.0\" encoding=\"ISO-8859-1\" standalone=\"no\"?>\r\n<WebServiceResponse>\r\n\t<SessionCookie>4185f88a-de05-4b76-ad6a-742a5030fd84</SessionCookie>\r\n\t<RequestAction>REGANAG</RequestAction>\r\n\t<ActionResult>0</ActionResult>\r\n\t<Results
        Count=\"1\" Size=\"133 bytes\">\r\n\t\t<Anagrafica GUID=\"c414a5e0-4b02-4b01-9eaf-7eb2912a4faa\">\r\n\t\t\t<Result>0</Result>\r\n\t\t</Anagrafica>\r\n\t</Results>\r\n\t<UserMessages
        Count=\"2\" ErrorCount=\"0\">\r\n\t\t<Message ID=\"\" Group=\"clsRegAnagrafiche.ExecuteRequest\"
        Field=\"c414a5e0-4b02-4b01-9eaf-7eb2912a4faa\" Type=\"info\" TypeUI=\"\"><![CDATA[Elaboro
        Anagrafica 1/1:]]></Message>\r\n\t\t<Message ID=\"\" Group=\"clsRegAnagrafiche.ExecuteRequest\"
        Field=\"c414a5e0-4b02-4b01-9eaf-7eb2912a4faa\" Type=\"warning\" TypeUI=\"\"><![CDATA[
        ==&gt; ERRORE!]]></Message>\r\n\t</UserMessages>\r\n\t<DebugMessages Count=\"5\"
        ErrorCount=\"2\">\r\n\t\t<Message ID=\"\" Group=\"\" Field=\"\" Type=\"info\"
        TypeUI=\"\"><![CDATA[[0,00 ms | 0,00 ms]: inizio sessione]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[16,00 ms
        | 16,00 ms]: Eseguita funzione: [Valida_ParametriStandard]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"error\" TypeUI=\"\"><![CDATA[[125,00
        ms | 141,00 ms]: [c414a5e0-4b02-4b01-9eaf-7eb2912a4faa - Brigitta Marian]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"ValidaContenutiAnagrafica\" Field=\"CodIva\" Type=\"error\"
        TypeUI=\"\"><![CDATA[Codice IVA non esistente in GAMMA: [1]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[0,00 ms
        | 141,00 ms]: fine sessione]]></Message>\r\n\t</DebugMessages>\r\n\t<ProcessingTime>141,00ms</ProcessingTime>\r\n</WebServiceResponse>\r\n\r\n"
    http_version: 
  recorded_at: Wed, 05 Apr 2017 08:39:18 GMT
recorded_with: VCR 3.0.3
