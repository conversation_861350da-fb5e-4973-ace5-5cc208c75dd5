---
http_interactions:
- request:
    method: post
    uri: http://localhost:8084/
    body:
      encoding: UTF-8
      string: ! "<?xml version=\"1.0\" encoding=\"ISO-8859-1\" standalone=\"no\"?>\n<WebServiceRequest>\n
        \ <SessionCookie>545d1820-91f0-4093-9afe-3ecf7413dfe5</SessionCookie>\n  <RequestAction>REPLICAARTICOLIGAMMA</RequestAction>\n
        \ <RequestParameters>\n    <parameter name=\"Operatore\">Account Attiva</parameter>\n
        \   <parameter name=\"Terminale\">WEB</parameter>\n    <parameter name=\"CallerURL\">spazio.kolme.it</parameter>\n
        \   <parameter name=\"ClientIPAddress\">************</parameter>\n    <parameter
        name=\"DataOraRichiesta\">******** 10:23:17.000</parameter>\n    <parameter
        name=\"IncludiDebug\">false</parameter>\n  </RequestParameters>\n  <RequestData>\n
        \   <Articolo>\n      <Codifica>\n        <Marchio>\n          <Codice x-type=\"str\"><![CDATA[ZTE46]]></Codice>\n
        \         <Descrizione x-type=\"str\"><![CDATA[ZTE46]]></Descrizione>\n        </Marchio>\n
        \       <Gs1>\n          <Codice x-type=\"str\"><![CDATA[ARIA]]></Codice>\n
        \         <Descrizione x-type=\"str\"><![CDATA[Operatore ARIA & TERRA]]></Descrizione>\n
        \       </Gs1>\n        <Gs2>\n          <Codice x-type=\"str\"><![CDATA[ACC]]></Codice>\n
        \         <Descrizione x-type=\"str\"><![CDATA[Accessori]]></Descrizione>\n
        \       </Gs2>\n        <Gs3>\n          <Codice x-type=\"str\"><![CDATA[]]></Codice>\n
        \         <Descrizione x-type=\"str\"><![CDATA[]]></Descrizione>\n        </Gs3>\n
        \       <Gs4>\n          <Codice x-type=\"str\"><![CDATA[]]></Codice>\n          <Descrizione
        x-type=\"str\"><![CDATA[]]></Descrizione>\n        </Gs4>\n        <Famiglia>\n
        \         <Codice x-type=\"str\"><![CDATA[TEL]]></Codice>\n          <Descrizione
        x-type=\"str\"><![CDATA[Telefonia]]></Descrizione>\n        </Famiglia>\n
        \       <SottoFamiglia>\n          <Codice x-type=\"str\"></Codice>\n          <Descrizione
        x-type=\"str\"></Descrizione>\n        </SottoFamiglia>\n        <CodArt x-type=\"str\">BLK0147A35</CodArt>\n
        \       <CodEan x-type=\"str\">0722868762844</CodEan>\n        <Descrizione
        x-type=\"str\"><![CDATA[CAVO MONITOR VGA/VGA MASCHIO MASCHIO 3 M NERO]]></Descrizione>\n
        \       <DescrizioneWeb x-type=\"str\"><![CDATA[CAVO MONITOR VGA/VGA MASCHIO
        MASCHIO 3 M NERO - VERY LONG CABLE]]></DescrizioneWeb>\n      </Codifica>\n
        \     <Commerciale>\n        <Iva>\n          <Codice x-type=\"str\">22</Codice>\n
        \         <Descrizione x-type=\"str\">IVA 22%</Descrizione>\n          <Aliquota
        x-type=\"lng\">22</Aliquota>\n        </Iva>\n        <Acquisto>\n          <Valuta
        x-type=\"str\">EUR</Valuta>\n          <Prezzo x-type=\"cur\">19.99</Prezzo>\n
        \       </Acquisto>\n        <PrezzoRivenditoreA x-type=\"cur\">11.47</PrezzoRivenditoreA>\n
        \       <PrezzoRivenditoreB x-type=\"cur\">11.47</PrezzoRivenditoreB>\n        <PrezzoRivenditoreC
        x-type=\"cur\">11.47</PrezzoRivenditoreC>\n      </Commerciale>\n      <Logistica>\n
        \       <Dimensioni>\n          <UM x-type=\"str\">mm</UM>\n          <Altezza
        x-type=\"\">220.98</Altezza>\n          <Larghezza x-type=\"\">240.03</Larghezza>\n
        \         <Profondita x-type=\"\">220.98</Profondita>\n        </Dimensioni>\n
        \       <Peso>\n          <UM x-type=\"str\">g</UM>\n          <Netto x-type=\"\">1.66</Netto>\n
        \         <Lordo x-type=\"\">1.66</Lordo>\n        </Peso>\n        <Fittizio
        x-type=\"bol\">true</Fittizio>\n      </Logistica>\n      <Contabilita>\n
        \       <CpVendita>\n          <IT x-type=\"str\">7028100907</IT>\n          <ResoIT
        x-type=\"str\">7028100907</ResoIT>\n        </CpVendita>\n        <CpAcquisto>\n
        \         <IT x-type=\"str\">9038609570</IT>\n          <ResoIT x-type=\"str\">9038609570</ResoIT>\n
        \       </CpAcquisto>\n      </Contabilita>\n      <Stato>\n        <Inserimento>\n
        \         <DataOra x-type=\"dtm\">2017-03-15T10:23:17+01:00</DataOra>\n          <UtenteID
        x-type=\"lng\"></UtenteID>\n          <UtenteDescrizione x-type=\"str\"></UtenteDescrizione>\n
        \       </Inserimento>\n        <Modifica>\n          <DataOra x-type=\"dtm\">2017-03-15T10:23:17+01:00</DataOra>\n
        \         <UtenteID x-type=\"lng\">1</UtenteID>\n          <UtenteDescrizione
        x-type=\"str\">Mario Mariotti</UtenteDescrizione>\n        </Modifica>\n        <Codice
        x-type=\"lng\">50</Codice>\n        <Descrizione x-type=\"str\">In uso</Descrizione>\n
        \     </Stato>\n    </Articolo>\n  </RequestData>\n</WebServiceRequest>\n"
    headers:
      Accept:
      - application/xml
      Accept-Encoding:
      - gzip, deflate
      Content-Type:
      - text/xml; charset=utf-8
      Content-Length:
      - '4046'
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private
      Content-Length:
      - '1258'
      Content-Type:
      - text/xml; Charset=ISO-8859-1
      Expires:
      - Wed, 15 Mar 2017 09:23:17 GMT
      Server:
      - Microsoft-IIS/8.5
      Set-Cookie:
      - ASPSESSIONIDSASCDSRC=IHBMEBHAEPMCMOLCLIOBICJP; path=/
      X-Powered-By:
      - ASP.NET
      Date:
      - Wed, 15 Mar 2017 09:23:17 GMT
    body:
      encoding: US-ASCII
      string: ! "<?xml version=\"1.0\" encoding=\"ISO-8859-1\" standalone=\"no\"?>\r\n<WebServiceResponse>\r\n\t<SessionCookie>545d1820-91f0-4093-9afe-3ecf7413dfe5</SessionCookie>\r\n\t<RequestAction>REPLICAARTICOLIGAMMA</RequestAction>\r\n\t<ActionResult>1</ActionResult>\r\n\t<Results
        Count=\"1\" Size=\"113 bytes\">\r\n\t\t<Articolo>\r\n\t\t\t<CodAtv/>\r\n\t\t\t<CodEan>0722868762844</CodEan>\r\n\t\t</Articolo>\r\n\t</Results>\r\n\t<UserMessages
        Count=\"2\" ErrorCount=\"0\">\r\n\t\t<Message ID=\"\" Group=\"clsRegArticoli.ExecuteRequest\"
        Field=\"1\" Type=\"info\" TypeUI=\"\"><![CDATA[Elaboro Articolo 1/1]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"clsRegArticoli.ExecuteRequest\" Field=\"1\" Type=\"confirm\"
        TypeUI=\"\"><![CDATA[Articolo [CAVO MONITOR VGA/VGA MASCHIO MASCHIO 3 M NERO]
        ==&gt; OK!]]></Message>\r\n\t</UserMessages>\r\n\t<DebugMessages Count=\"3\"
        ErrorCount=\"0\">\r\n\t\t<Message ID=\"\" Group=\"\" Field=\"\" Type=\"info\"
        TypeUI=\"\"><![CDATA[[0,00 ms | 0,00 ms]: inizio sessione]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[15,00 ms
        | 15,00 ms]: Eseguita funzione: [Valida_ParametriStandard]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[328,00 ms
        | 343,00 ms]: fine sessione]]></Message>\r\n\t</DebugMessages>\r\n\t<ProcessingTime>359,00ms</ProcessingTime>\r\n</WebServiceResponse>\r\n\r\n"
    http_version: 
  recorded_at: Wed, 15 Mar 2017 09:23:18 GMT
recorded_with: VCR 3.0.3
