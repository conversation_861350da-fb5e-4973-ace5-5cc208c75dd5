---
http_interactions:
- request:
    method: post
    uri: https://collector.newrelic.com/agent_listener/invoke_raw_method?license_key=&marshal_format=json&method=preconnect&protocol_version=15
    body:
      encoding: UTF-8
      string: "[]"
    headers:
      Content-Encoding:
      - identity
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - NewRelic-RubyAgent/5.0.0.342 (ruby 2.3.1 x86_64-linux) zlib/1.2.11
      Content-Type:
      - application/octet-stream
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Length:
      - '133'
      Content-Type:
      - text/plain;charset=utf-8
      Date:
      - Thu, 03 May 2018 13:36:14 GMT
      X-Application-Context:
      - application
    body:
      encoding: UTF-8
      string: '{"exception":{"message":"Invalid license key, <NAME_EMAIL>","error_type":"NewRelic::Agent::LicenseException"}}'
    http_version: 
  recorded_at: Thu, 03 May 2018 13:36:14 GMT
recorded_with: VCR 4.0.0
