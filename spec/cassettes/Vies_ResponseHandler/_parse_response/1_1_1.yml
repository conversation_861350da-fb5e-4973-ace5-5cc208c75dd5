---
http_interactions:
- request:
    method: get
    uri: https://maps.googleapis.com/maps/api/geocode/json?address=VIA%20ANTONIO%20TOLOMEO%20TRIVULZIO%201%20%0A20146%20MILANO%20MI%0A&key=AIzaSyCa8wUQ0yENUeS4LeFNb84_4rxU1HVN1uE&language=it&sensor=false
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Wed, 18 May 2022 14:29:12 GMT
      Pragma:
      - no-cache
      Expires:
      - Fri, 01 Jan 1990 00:00:00 GMT
      Cache-Control:
      - no-cache, must-revalidate
      Access-Control-Allow-Origin:
      - "*"
      X-Goog-Maps-Metro-Area:
      - Milan
      Server:
      - mafe
      X-Xss-Protection:
      - '0'
      X-Frame-Options:
      - SAMEORIGIN
      Server-Timing:
      - gfet4t7; dur=12
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443";
        ma=2592000,h3-Q043=":443"; ma=2592000,quic=":443"; ma=2592000; v="46,43"
      Transfer-Encoding:
      - chunked
    body:
      encoding: ASCII-8BIT
      string: !binary |-
        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
    http_version: 
  recorded_at: Wed, 18 May 2022 14:29:12 GMT
recorded_with: VCR 5.0.0
