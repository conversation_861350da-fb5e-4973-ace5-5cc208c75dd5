---
http_interactions:
- request:
    method: post
    uri: http://localhost:9292/faye
    body:
      encoding: US-ASCII
      string: message=%7B%22channel%22%3A%22%2Fthread_messages%2F1%22%2C%22data%22%3A%7B%22channel%22%3A%22%2Fthread_messages%2F1%22%2C%22eval%22%3A%22window.App.afterSaleThreadStore.setState%28%5C%22charge_kolme%5C%22%29%22%7D%2C%22ext%22%3A%7B%22private_pub_token%22%3A%22secret%22%7D%7D
    headers:
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
      Content-Type:
      - application/x-www-form-urlencoded
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json; charset=utf-8
      Cache-Control:
      - no-cache, no-store
      X-Content-Type-Options:
      - nosniff
      Content-Length:
      - '52'
      Connection:
      - keep-alive
      Server:
      - thin 1.5.0 codename Knife
    body:
      encoding: UTF-8
      string: '[{"channel":"/thread_messages/1","successful":true}]'
    http_version: 
  recorded_at: Thu, 09 Nov 2017 09:26:19 GMT
recorded_with: VCR 3.0.3
