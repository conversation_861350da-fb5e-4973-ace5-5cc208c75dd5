---
http_interactions:
- request:
    method: post
    uri: http://localhost:9292/faye
    body:
      encoding: US-ASCII
      string: message=%7B%22channel%22%3A%22%2Fbroadcast_alerts_295%22%2C%22data%22%3A%7B%22channel%22%3A%22%2Fbroadcast_alerts_295%22%2C%22eval%22%3A%22update_notifications_count%28178%2C1%29%22%7D%2C%22ext%22%3A%7B%22private_pub_token%22%3A%22secret%22%7D%7D
    headers:
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
      Content-Type:
      - application/x-www-form-urlencoded
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json; charset=utf-8
      Cache-Control:
      - no-cache, no-store
      X-Content-Type-Options:
      - nosniff
      Content-Length:
      - '55'
      Connection:
      - keep-alive
      Server:
      - thin
    body:
      encoding: UTF-8
      string: '[{"channel":"/broadcast_alerts_295","successful":true}]'
    http_version:
  recorded_at: Wed, 02 Aug 2023 14:43:18 GMT
- request:
    method: post
    uri: http://localhost:9292/faye
    body:
      encoding: US-ASCII
      string: message=%7B%22channel%22%3A%22%2Fapp_dashboard_refresh%2F295%22%2C%22data%22%3A%7B%22channel%22%3A%22%2Fapp_dashboard_refresh%2F295%22%2C%22eval%22%3A%22activeAlertsCount%22%7D%2C%22ext%22%3A%7B%22private_pub_token%22%3A%22secret%22%7D%7D
    headers:
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
      Content-Type:
      - application/x-www-form-urlencoded
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json; charset=utf-8
      Cache-Control:
      - no-cache, no-store
      X-Content-Type-Options:
      - nosniff
      Content-Length:
      - '60'
      Connection:
      - keep-alive
      Server:
      - thin
    body:
      encoding: UTF-8
      string: '[{"channel":"/app_dashboard_refresh/295","successful":true}]'
    http_version:
  recorded_at: Wed, 02 Aug 2023 14:43:18 GMT
recorded_with: VCR 5.0.0
