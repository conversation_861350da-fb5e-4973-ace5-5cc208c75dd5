---
http_interactions:
- request:
    method: post
    uri: http://localhost:8084/
    body:
      encoding: UTF-8
      string: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n<WebServiceRequest>\n\t<SessionCookie>96392bf3-8c22-44ca-9502-779cc03f2243</SessionCookie>\n\t<RequestAction>REGANAG</RequestAction>\n\t<RequestParameters>\n
        \       <parameter name=\"Operatore\">kolme.0002</parameter>\n        <parameter
        name=\"Terminale\">PC01</parameter>\n        <parameter name=\"DataOraInizio\">2018-01-30
        12:59:20 +0100</parameter>\n        <parameter name=\"DataOraFine\">2018-01-30
        12:59:20 +0100</parameter>\n        <parameter name=\"IncludiDebug\">True</parameter>\n
        \   </RequestParameters>\n\t<RequestData Rows=\"1\">\n\t\t<Anagrafica>\n\t\t
        \   <GUID>bfca5e42-3d59-414b-a5b0-ca7ded3c9d37</GUID>\n\t\t\t<Codice>1595</Codice>\n
        \           <Tipo>CF</Tipo>\n            <CodIntermedio>4</CodIntermedio>\n
        \           <PrivatoAzienda>A</PrivatoAzienda>\n            <RagioneSociale><![CDATA[Mattia
        Russo]]></RagioneSociale>\n            <Cognome><![CDATA[]]></Cognome>\n            <Nome><![CDATA[]]></Nome>\n
        \           <PartitaIVA>87960870643</PartitaIVA>\n            <CodiceFiscale></CodiceFiscale>\n
        \           <Indirizzo><![CDATA[Borgo Palmieri, 69]]></Indirizzo>\n            <CAP>37954</CAP>\n
        \           <Citta><![CDATA[San Isira]]></Citta>\n            <Provincia>CA</Provincia>\n
        \           <CodCatastale></CodCatastale>\n            <Nazione>IT</Nazione>\n\t\t\t<Telefono></Telefono>\n\t\t\t<Cellulare></Cellulare>\n\t\t\t<Fax></Fax>\n
        \           <Email></Email>\n            <Web></Web>\n            <CodPagamento>BFM</CodPagamento>\n
        \           <IBAN></IBAN>\n            <ExtraFido></ExtraFido>\n            <CodIva>22</CodIva>\n
        \           <CodRitenuta>0</CodRitenuta>\n            <DataAcquisizione>2018-01-30</DataAcquisizione>\n
        \           <Dismissione><![CDATA[]]></Dismissione>\n            <Catena><![CDATA[]]></Catena>\n
        \           <Note><![CDATA[anagrafica di test]]></Note>\n            \n\t\t</Anagrafica>\n\t</RequestData>\n</WebServiceRequest>\n"
    headers:
      Accept:
      - application/xml
      Accept-Encoding:
      - gzip, deflate
      User-Agent:
      - rest-client/2.0.2 (darwin16.7.0 x86_64) ruby/2.3.1p112
      Content-Type:
      - text/xml; charset=utf-8
      Content-Length:
      - '1870'
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private
      Content-Type:
      - text/xml; Charset=ISO-8859-1
      Expires:
      - Tue, 30 Jan 2018 11:59:20 GMT
      Server:
      - Microsoft-IIS/8.5
      Set-Cookie:
      - ASPSESSIONIDSCQCRCDQ=MBENAPCAJMNMAEAKDFCFCMJO; path=/
      X-Powered-By:
      - ASP.NET
      Date:
      - Tue, 30 Jan 2018 11:59:26 GMT
      Content-Length:
      - '1425'
    body:
      encoding: UTF-8
      string: "<?xml version=\"1.0\" encoding=\"ISO-8859-1\" standalone=\"no\"?>\r\n<WebServiceResponse>\r\n\t<SessionCookie>96392bf3-8c22-44ca-9502-779cc03f2243</SessionCookie>\r\n\t<RequestAction>REGANAG</RequestAction>\r\n\t<ActionResult>1</ActionResult>\r\n\t<Results
        Count=\"1\" Size=\"232 bytes\">\r\n\t\t<Anagrafica GUID=\"bfca5e42-3d59-414b-a5b0-ca7ded3c9d37\">\r\n\t\t\t<Result>1</Result>\r\n\t\t\t<CodAnagrafico>44270</CodAnagrafico>\r\n\t\t\t<CodCliente>43638</CodCliente>\r\n\t\t\t<CodFornitore>5745</CodFornitore>\r\n\t\t</Anagrafica>\r\n\t</Results>\r\n\t<UserMessages
        Count=\"2\" ErrorCount=\"0\">\r\n\t\t<Message ID=\"\" Group=\"clsRegAnagrafiche.ExecuteRequest\"
        Field=\"bfca5e42-3d59-414b-a5b0-ca7ded3c9d37\" Type=\"info\" TypeUI=\"\"><![CDATA[Elaboro
        Anagrafica 1/1:]]></Message>\r\n\t\t<Message ID=\"\" Group=\"clsRegAnagrafiche.ExecuteRequest\"
        Field=\"bfca5e42-3d59-414b-a5b0-ca7ded3c9d37\" Type=\"confirm\" TypeUI=\"\"><![CDATA[Anagrafica
        [Mattia Russo] ==&gt; OK!]]></Message>\r\n\t</UserMessages>\r\n\t<DebugMessages
        Count=\"3\" ErrorCount=\"0\">\r\n\t\t<Message ID=\"\" Group=\"\" Field=\"\"
        Type=\"info\" TypeUI=\"\"><![CDATA[[0,00 ms | 0,00 ms]: inizio sessione]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[63,00 ms
        | 63,00 ms]: Eseguita funzione: [Valida_ParametriStandard]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[5,14 s |
        5,20 s]: fine sessione]]></Message>\r\n\t</DebugMessages>\r\n\t<ProcessingTime>5.204,00ms</ProcessingTime>\r\n</WebServiceResponse>\r\n\r\n"
    http_version: 
  recorded_at: Tue, 30 Jan 2018 11:59:26 GMT
recorded_with: VCR 4.0.0
