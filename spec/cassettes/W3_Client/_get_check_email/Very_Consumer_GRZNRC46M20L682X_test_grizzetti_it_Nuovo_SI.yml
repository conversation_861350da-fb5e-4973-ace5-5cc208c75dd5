---
http_interactions:
- request:
    method: post
    uri: https://ml-in-2.apps.windtre.it:8080/test/dp/getCheckEmail
    body:
      encoding: UTF-8
      string: '{"EMAIL":"<EMAIL>","IDENTIFICATIVO":"GRZNRC46M20L682X","BRAND":"VERY","MERCATO":"CONSUMER"}'
    headers:
      Accept:
      - "*/*"
      Accept-Encoding:
      - gzip, deflate
      User-Agent:
      - rest-client/2.0.2 (darwin22 x86_64) ruby/3.0.5p211
      X-Api-Key:
      - djdygy72x3gdq77etarydp7m
      X-Trace-Token:
      - '1726048578'
      X-Client-Name:
      - w3-kolme
      Content-Length:
      - '101'
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Mashery-Responder:
      - tm-deploy-1-684b45c67b-ccwqj
      X-Mashery-Message-Id:
      - 3f91b352-d9fa-422c-b1be-f34aec012a4a
      X-Planmethod-Qps-Allotted:
      - '2'
      X-Planmethod-Qps-Current:
      - '1'
      X-Planmethod-Quota-Allotted:
      - '5000'
      X-Planmethod-Quota-Current:
      - '4'
      X-Method-Quota-Reset:
      - Thursday, September 12, 2024 at 12:00:00 AM Greenwich Mean Time
      X-Packagekey-Qps-Allotted:
      - Unlimited
      X-Packagekey-Quota-Allotted:
      - Unlimited
      Date:
      - Wed, 11 Sep 2024 09:56:18 GMT
      Content-Type:
      - application/json
      Server:
      - envoy
      X-Envoy-Upstream-Service-Time:
      - '194'
      Content-Length:
      - '43'
      X-Igp-Cloud:
      - GCP
      Via:
      - 1.1 google
    body:
      encoding: UTF-8
      string: '{"FLAG_CHECKEMAIL":"N","COUNTER_EMAIL":"0"}'
  recorded_at: Wed, 11 Sep 2024 09:56:19 GMT
recorded_with: VCR 6.2.0
