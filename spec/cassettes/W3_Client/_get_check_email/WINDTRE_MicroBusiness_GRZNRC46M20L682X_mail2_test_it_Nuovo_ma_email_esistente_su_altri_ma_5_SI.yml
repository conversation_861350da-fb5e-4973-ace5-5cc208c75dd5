---
http_interactions:
- request:
    method: post
    uri: https://ml-in-2.apps.windtre.it:8080/test/dp/getCheckEmail
    body:
      encoding: UTF-8
      string: '{"EMAIL":"<EMAIL>","IDENTIFICATIVO":"GRZNRC46M20L682X","BRAND":"WINDTRE","MERCATO":"MICROBUSINESS"}'
    headers:
      Accept:
      - "*/*"
      Accept-Encoding:
      - gzip, deflate
      User-Agent:
      - rest-client/2.0.2 (darwin22 x86_64) ruby/3.0.5p211
      X-Api-Key:
      - djdygy72x3gdq77etarydp7m
      X-Trace-Token:
      - '1726048475'
      X-Client-Name:
      - w3-kolme
      Content-Length:
      - '105'
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Mashery-Responder:
      - tm-deploy-0-74c5bd8f59-zpb7b
      X-Mashery-Message-Id:
      - 1f1add60-c166-4aaa-bc59-d18aec98b324
      X-Planmethod-Qps-Allotted:
      - '2'
      X-Planmethod-Qps-Current:
      - '1'
      X-Planmethod-Quota-Allotted:
      - '5000'
      X-Planmethod-Quota-Current:
      - '2'
      X-Method-Quota-Reset:
      - Thursday, September 12, 2024 at 12:00:00 AM Greenwich Mean Time
      X-Packagekey-Qps-Allotted:
      - Unlimited
      X-Packagekey-Quota-Allotted:
      - Unlimited
      Date:
      - Wed, 11 Sep 2024 09:54:35 GMT
      Content-Type:
      - application/json
      Server:
      - envoy
      X-Envoy-Upstream-Service-Time:
      - '186'
      Content-Length:
      - '43'
      X-Igp-Cloud:
      - GCP
      Via:
      - 1.1 google
    body:
      encoding: UTF-8
      string: '{"FLAG_CHECKEMAIL":"N","COUNTER_EMAIL":"4"}'
  recorded_at: Wed, 11 Sep 2024 09:54:35 GMT
recorded_with: VCR 6.2.0
