---
http_interactions:
- request:
    method: post
    uri: https://api-np.windtre.it:4443/authentication
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept:
      - "*/*"
      Accept-Encoding:
      - gzip, deflate
      User-Agent:
      - rest-client/2.0.2 (darwin23 x86_64) ruby/3.0.5p211
      Content-Type:
      - application/x-www-form-urlencoded
      Grant-Type:
      - client_credentials
      Authorization:
      - Basic Q0I3eTMyZHpzWEF4MW9meTRUT0FHUjhYSENBRE1NZGUwQVJWSU94SFl3Q3pIQUozOmFsQW9FZDZ3bGRBU1FSaHhSdkY1UGp2Nk1ybmhmUFdKT0lEQTFmazFONzg1NDlZNGM5d1R5MnRPMlUwZTk5RlQ=
      Content-Length:
      - '0'
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json
      X-Request-Id:
      - 787626c2-71c4-4d66-8fc0-200170730c20
      Content-Length:
      - '558'
      Date:
      - Mon, 03 Mar 2025 12:26:50 GMT
      Via:
      - 1.1 google
    body:
      encoding: UTF-8
      string: |-
        {
          "refresh_token_expires_in": "0",
          "api_product_list": "[Api Product Kolme]",
          "api_product_list_json": [
            "Api Product Kolme"
          ],
          "organization_name": "w3-channel-n-prj-ddf2",
          "developer.email": "<EMAIL>",
          "token_type": "BearerToken",
          "issued_at": "1741004810577",
          "client_id": "CB7y32dzsXAx1ofy4TOAGR8XHCADMMde0ARVIOxHYwCzHAJ3",
          "access_token": "fBBPtsUkxo7w9rsHZOjDSzSGByBz",
          "application_name": "95be5928-be75-4c41-812f-f807d2ff185c",
          "scope": "",
          "expires_in": "1799",
          "refresh_count": "0",
          "status": "approved"
        }
  recorded_at: Mon, 03 Mar 2025 12:26:50 GMT
recorded_with: VCR 6.2.0
