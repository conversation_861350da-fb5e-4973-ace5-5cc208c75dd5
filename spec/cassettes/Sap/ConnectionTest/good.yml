---
http_interactions:
- request:
    method: get
    uri: https://kolmedev.it-cpi023-rt.cfapps.eu20-001.hana.ondemand.com/http/DEV/ConnectionTest
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept:
      - application/json
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      User-Agent:
      - Ruby
      Authorization:
      - Basic c2ItZWE4OWMyYWYtOThiOS00NDg5LWFiZGEtZWM4NTIzNjViMjEyIWIzMzIyNHxpdC1ydC1rb2xtZWRldiFiMTg2MzE6YTA1OGQ5ZjktNDI1Mi00ZTA2LWFmZTgtMzE0M2U3OWYwYjI5JFRXR012T1RONVo2VEtuczN2dWNseS01YlpURnB1dWVSRjh3NXlMV2dBY3M9
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - no-cache, no-store, max-age=0, must-revalidate
      Content-Type:
      - application/json
      Date:
      - Thu, 10 Oct 2024 18:15:32 GMT
      Expires:
      - '0'
      Pragma:
      - no-cache
      Sap-Messageprocessinglogid:
      - AGcIGcQ31K7GGI5H0fE2MlrDyYAJ
      Server:
      - SAP
      X-Content-Type-Options:
      - nosniff
      X-Correlationid:
      - 50cccb83-7b60-4264-5fd6-e39d3ef61359
      X-Frame-Options:
      - DENY
      X-Vcap-Request-Id:
      - 50cccb83-7b60-4264-5fd6-e39d3ef61359
      X-Xss-Protection:
      - 1; mode=block
      Transfer-Encoding:
      - chunked
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains; preload;
    body:
      encoding: UTF-8
      string: "{\n\t\"ConnectionTest\": \"OK\"\n}"
  recorded_at: Thu, 10 Oct 2024 18:15:32 GMT
recorded_with: VCR 6.2.0
