---
http_interactions:
- request:
    method: post
    uri: https://api-stage.drop-pay.io/oa2/v1/cc/token
    body:
      encoding: UTF-8
      string: '{"grant_type":"client_credentials","client_id":"zvEYOgYZJ8UZIkVAf8SyS68b7bW3tlb9","client_secret":"KvFirQLDOR0zr9FIzuKiuTVlllW5HK04","scope":"app"}'
    headers:
      Accept:
      - "*/*"
      Accept-Encoding:
      - gzip, deflate
      User-Agent:
      - rest-client/2.0.2 (darwin20.6.0 x86_64) ruby/2.5.3p105
      Content-Type:
      - application/json
      Content-Length:
      - '147'
  response:
    status:
      code: 200
      message: OK
    headers:
      Date:
      - Wed, 21 Sep 2022 15:38:38 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '91'
      Connection:
      - keep-alive
      Cache-Control:
      - no-store
      Pragma:
      - no-cache
      Access-Control-Allow-Origin:
      - "*"
      Server:
      - kong/1.0.2
    body:
      encoding: UTF-8
      string: '{"token_type":"bearer","access_token":"YrOVjYdCifB2ue7UQCj1Kj33LjM5IB0g","expires_in":7200}'
    http_version: 
  recorded_at: Wed, 21 Sep 2022 15:38:38 GMT
recorded_with: VCR 5.0.0
