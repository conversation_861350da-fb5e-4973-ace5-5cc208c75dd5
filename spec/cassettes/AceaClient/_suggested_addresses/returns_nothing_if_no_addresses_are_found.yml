---
http_interactions:
- request:
    method: post
    uri: https://accounts.eu1.gigya.com/accounts.getJWT
    body:
      encoding: US-ASCII
      string: apiKey=3_SF-TKzU9b87TpkMNzw1Limm2VitYI3hcl_N1r0qjhmVVjB6LsJJzDkPERVXC1vVM&secret=5XtDjycFTzhZTmhPwMAKim2m09WUrfBl&userKey=ALqFLcHJxgSV&targetUID=24660300749c43e0b6a50955e6eeeeeb&expiration=3600
    headers:
      Content-Type:
      - application/x-www-form-urlencoded
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Length:
      - '796'
      Content-Type:
      - text/javascript; charset=utf-8
      Date:
      - Thu, 23 May 2024 09:16:16 GMT
      Cache-Control:
      - private
      Vary:
      - Accept-Encoding
      P3p:
      - CP="IDC COR PSA DEV ADM OUR IND ONL"
      X-Error-Code:
      - '0'
      X-Soa:
      - true, Gator
      X-Server:
      - eu1a-nomad-t24
      X-Callid:
      - 9646764abc7046e3946a571ed8e61d71
      X-Robots-Tag:
      - none
    body:
      encoding: ASCII-8BIT
      string: "{\r\n  \"callId\": \"9646764abc7046e3946a571ed8e61d71\",\r\n  \"errorCode\":
        0,\r\n  \"apiVersion\": 2,\r\n  \"statusCode\": 200,\r\n  \"statusReason\":
        \"OK\",\r\n  \"time\": \"2024-05-23T09:16:16.441Z\",\r\n  \"id_token\": \"***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\"\r\n}"
  recorded_at: Thu, 23 May 2024 09:16:16 GMT
- request:
    method: get
    uri: https://qa-selfservice.aceaspa.it/RB/funnel/suggestedAddresses?address=abcd
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Authorization:
      - Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 404
      message: Not Found
    headers:
      Cache-Control:
      - no-cache, no-store, max-age=0, must-revalidate
      Content-Type:
      - application/json
      Date:
      - Thu, 23 May 2024 09:16:16 GMT
      Expires:
      - '0'
      Pragma:
      - no-cache
      Server:
      - envoy
      Strict-Transport-Security:
      - max-age=31536000 ; includeSubDomains
      Vary:
      - Origin,Access-Control-Request-Method,Access-Control-Request-Headers
      X-Content-Type-Options:
      - nosniff
      X-Envoy-Decorator-Operation:
      - ingress GetSuggestedAddressesUsingGET
      X-Frame-Options:
      - DENY
      X-Xss-Protection:
      - 1; mode=block
      Transfer-Encoding:
      - chunked
      Via:
      - 1.1 google
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
    body:
      encoding: UTF-8
      string: '{"result":"KO","message":"ADDRESS_NOT_FOUND","body":{"errorCode":"ADDRESS_NOT_FOUND","errorClass":"PORTAL","errorMessage":"Suggestion
        for [abcd] not found"}}'
  recorded_at: Thu, 23 May 2024 09:16:16 GMT
recorded_with: VCR 6.2.0
