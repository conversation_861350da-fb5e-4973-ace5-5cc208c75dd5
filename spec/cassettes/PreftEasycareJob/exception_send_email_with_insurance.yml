---
http_interactions:
- request:
    method: post
    uri: http://localhost:8084/
    body:
      encoding: UTF-8
      string: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n<WebServiceRequest>\n\t<SessionCookie>37286c0b-4f31-48d2-bdde-de01db76ab9c</SessionCookie>\n\t<RequestAction>REGANAG</RequestAction>\n\t<RequestParameters>\n
        \       <parameter name=\"Operatore\">kolme.0002</parameter>\n        <parameter
        name=\"Terminale\">PC01</parameter>\n        <parameter name=\"DataOraInizio\">2017-11-15
        17:37:49 +0100</parameter>\n        <parameter name=\"DataOraFine\">2017-11-15
        17:37:49 +0100</parameter>\n        <parameter name=\"IncludiDebug\">True</parameter>\n
        \   </RequestParameters>\n\t<RequestData Rows=\"1\">\n\t\t<Anagrafica>\n\t\t
        \   <GUID>4d7a7349-e9b2-4729-878f-0d48180c3cc7</GUID>\n\t\t\t<Codice>811</Codice>\n
        \           <Tipo>CF</Tipo>\n            <CodIntermedio>4</CodIntermedio>\n
        \           <PrivatoAzienda>A</PrivatoAzienda>\n            <RagioneSociale><![CDATA[Ulrico
        Sorrentino]]></RagioneSociale>\n            <Cognome><![CDATA[]]></Cognome>\n
        \           <Nome><![CDATA[]]></Nome>\n            <PartitaIVA>46118179017</PartitaIVA>\n
        \           <CodiceFiscale></CodiceFiscale>\n            <Indirizzo><![CDATA[Incrocio
        Vienna, 171]]></Indirizzo>\n            <CAP>45271</CAP>\n            <Citta><![CDATA[Ferri
        lido]]></Citta>\n            <Provincia>MM</Provincia>\n            <CodCatastale></CodCatastale>\n
        \           <Nazione>IT</Nazione>\n\t\t\t<Telefono></Telefono>\n\t\t\t<Cellulare></Cellulare>\n\t\t\t<Fax></Fax>\n
        \           <Email></Email>\n            <Web></Web>\n            <CodPagamento>BFM</CodPagamento>\n
        \           <IBAN></IBAN>\n            <ExtraFido></ExtraFido>\n            <CodIva>22</CodIva>\n
        \           <CodRitenuta>0</CodRitenuta>\n            <DataAcquisizione>2017-11-15</DataAcquisizione>\n
        \           <Dismissione><![CDATA[]]></Dismissione>\n            <Catena><![CDATA[]]></Catena>\n
        \           <Note><![CDATA[anagrafica di test]]></Note>\n            \n\t\t</Anagrafica>\n\t</RequestData>\n</WebServiceRequest>\n"
    headers:
      Accept:
      - application/xml
      Accept-Encoding:
      - gzip, deflate
      User-Agent:
      - rest-client/2.0.2 (linux-gnu x86_64) ruby/2.3.1p112
      Content-Type:
      - text/xml; charset=utf-8
      Content-Length:
      - '1877'
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private
      Content-Type:
      - text/xml; Charset=ISO-8859-1
      Expires:
      - Wed, 15 Nov 2017 16:37:49 GMT
      Server:
      - Microsoft-IIS/8.5
      Set-Cookie:
      - ASPSESSIONIDASQQQABA=GKEMMJHBNFKAGOGHHGFFDLLL; path=/
      X-Powered-By:
      - ASP.NET
      Date:
      - Wed, 15 Nov 2017 16:37:55 GMT
      Content-Length:
      - '1430'
    body:
      encoding: UTF-8
      string: "<?xml version=\"1.0\" encoding=\"ISO-8859-1\" standalone=\"no\"?>\r\n<WebServiceResponse>\r\n\t<SessionCookie>37286c0b-4f31-48d2-bdde-de01db76ab9c</SessionCookie>\r\n\t<RequestAction>REGANAG</RequestAction>\r\n\t<ActionResult>1</ActionResult>\r\n\t<Results
        Count=\"1\" Size=\"232 bytes\">\r\n\t\t<Anagrafica GUID=\"4d7a7349-e9b2-4729-878f-0d48180c3cc7\">\r\n\t\t\t<Result>1</Result>\r\n\t\t\t<CodAnagrafico>39763</CodAnagrafico>\r\n\t\t\t<CodCliente>39193</CodCliente>\r\n\t\t\t<CodFornitore>5288</CodFornitore>\r\n\t\t</Anagrafica>\r\n\t</Results>\r\n\t<UserMessages
        Count=\"2\" ErrorCount=\"0\">\r\n\t\t<Message ID=\"\" Group=\"clsRegAnagrafiche.ExecuteRequest\"
        Field=\"4d7a7349-e9b2-4729-878f-0d48180c3cc7\" Type=\"info\" TypeUI=\"\"><![CDATA[Elaboro
        Anagrafica 1/1:]]></Message>\r\n\t\t<Message ID=\"\" Group=\"clsRegAnagrafiche.ExecuteRequest\"
        Field=\"4d7a7349-e9b2-4729-878f-0d48180c3cc7\" Type=\"confirm\" TypeUI=\"\"><![CDATA[Anagrafica
        [Ulrico Sorrentino] ==&gt; OK!]]></Message>\r\n\t</UserMessages>\r\n\t<DebugMessages
        Count=\"3\" ErrorCount=\"0\">\r\n\t\t<Message ID=\"\" Group=\"\" Field=\"\"
        Type=\"info\" TypeUI=\"\"><![CDATA[[0,00 ms | 0,00 ms]: inizio sessione]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[94,00 ms
        | 94,00 ms]: Eseguita funzione: [Valida_ParametriStandard]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[6,17 s |
        6,27 s]: fine sessione]]></Message>\r\n\t</DebugMessages>\r\n\t<ProcessingTime>6.266,00ms</ProcessingTime>\r\n</WebServiceResponse>\r\n\r\n"
    http_version: 
  recorded_at: Wed, 15 Nov 2017 16:37:55 GMT
recorded_with: VCR 3.0.3
