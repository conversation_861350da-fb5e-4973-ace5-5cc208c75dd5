---
http_interactions:
- request:
    method: post
    uri: http://localhost:8084/
    body:
      encoding: UTF-8
      string: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n<WebServiceRequest>\n\t<SessionCookie>de5e0889-9210-4464-9ecc-ad76d0550be9</SessionCookie>\n\t<RequestAction>REGANAG</RequestAction>\n\t<RequestParameters>\n
        \       <parameter name=\"Operatore\">kolme.0002</parameter>\n        <parameter
        name=\"Terminale\">PC01</parameter>\n        <parameter name=\"DataOraInizio\">2017-05-16
        15:09:20 +0200</parameter>\n        <parameter name=\"DataOraFine\">2017-05-16
        15:09:20 +0200</parameter>\n        <parameter name=\"IncludiDebug\">True</parameter>\n
        \   </RequestParameters>\n\t<RequestData Rows=\"1\">\n\t\t<Anagrafica>\n\t\t
        \   <GUID>9c797361-c971-4d20-a866-ddd8ed12558a</GUID>\n\t\t\t<Codice>1</Codice>\n
        \           <Tipo>CF</Tipo>\n            <CodIntermedio>4</CodIntermedio>\n
        \           <PrivatoAzienda>A</PrivatoAzienda>\n            <RagioneSociale><![CDATA[Architrave
        3000]]></RagioneSociale>\n            <Cognome><![CDATA[]]></Cognome>\n            <Nome><![CDATA[]]></Nome>\n
        \           <PartitaIVA>89864149378</PartitaIVA>\n            <CodiceFiscale></CodiceFiscale>\n
        \           <Indirizzo><![CDATA[Viale Famagosta, 75]]></Indirizzo>\n            <CAP>20142</CAP>\n
        \           <Citta><![CDATA[Milano]]></Citta>\n            <Provincia>MI</Provincia>\n
        \           <CodCatastale></CodCatastale>\n            <Nazione>IT</Nazione>\n\t\t\t<Telefono></Telefono>\n\t\t\t<Cellulare></Cellulare>\n\t\t\t<Fax></Fax>\n
        \           <Email></Email>\n            <Web></Web>\n            <CodPagamento>BFM</CodPagamento>\n
        \           <IBAN></IBAN>\n            <ExtraFido>0</ExtraFido>\n            <CodIva>1</CodIva>\n
        \           <CodRitenuta>0</CodRitenuta>\n            <DataAcquisizione>2017-05-16</DataAcquisizione>\n
        \           <Dismissione><![CDATA[]]></Dismissione>\n            <Catena><![CDATA[]]></Catena>\n
        \           <Note><![CDATA[anagrafica di test]]></Note>\n            \n\t\t</Anagrafica>\n\t</RequestData>\n</WebServiceRequest>\n\n\n"
    headers:
      Accept:
      - application/xml
      Accept-Encoding:
      - gzip, deflate
      User-Agent:
      - rest-client/2.0.0 (darwin16.0.0 x86_64) ruby/2.3.1p112
      Content-Type:
      - text/xml; charset=utf-8
      Content-Length:
      - '1870'
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private
      Content-Length:
      - '1608'
      Content-Type:
      - text/xml; Charset=ISO-8859-1
      Expires:
      - Tue, 16 May 2017 13:09:20 GMT
      Server:
      - Microsoft-IIS/8.5
      Set-Cookie:
      - ASPSESSIONIDSAARSTTT=FKMLNCEBMKOEPNNDALBIPBCI; path=/
      X-Powered-By:
      - ASP.NET
      Date:
      - Tue, 16 May 2017 13:09:21 GMT
    body:
      encoding: UTF-8
      string: "<?xml version=\"1.0\" encoding=\"ISO-8859-1\" standalone=\"no\"?>\r\n<WebServiceResponse>\r\n\t<SessionCookie>de5e0889-9210-4464-9ecc-ad76d0550be9</SessionCookie>\r\n\t<RequestAction>REGANAG</RequestAction>\r\n\t<ActionResult>0</ActionResult>\r\n\t<Results
        Count=\"1\" Size=\"133 bytes\">\r\n\t\t<Anagrafica GUID=\"9c797361-c971-4d20-a866-ddd8ed12558a\">\r\n\t\t\t<Result>0</Result>\r\n\t\t</Anagrafica>\r\n\t</Results>\r\n\t<UserMessages
        Count=\"2\" ErrorCount=\"0\">\r\n\t\t<Message ID=\"\" Group=\"clsRegAnagrafiche.ExecuteRequest\"
        Field=\"9c797361-c971-4d20-a866-ddd8ed12558a\" Type=\"info\" TypeUI=\"\"><![CDATA[Elaboro
        Anagrafica 1/1:]]></Message>\r\n\t\t<Message ID=\"\" Group=\"clsRegAnagrafiche.ExecuteRequest\"
        Field=\"9c797361-c971-4d20-a866-ddd8ed12558a\" Type=\"warning\" TypeUI=\"\"><![CDATA[
        ==&gt; ERRORE!]]></Message>\r\n\t</UserMessages>\r\n\t<DebugMessages Count=\"5\"
        ErrorCount=\"2\">\r\n\t\t<Message ID=\"\" Group=\"\" Field=\"\" Type=\"info\"
        TypeUI=\"\"><![CDATA[[0,00 ms | 0,00 ms]: inizio sessione]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[125,00 ms
        | 125,00 ms]: Eseguita funzione: [Valida_ParametriStandard]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"error\" TypeUI=\"\"><![CDATA[[265,00
        ms | 390,00 ms]: [9c797361-c971-4d20-a866-ddd8ed12558a - Architrave 3000]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"ValidaContenutiAnagrafica\" Field=\"CodIva\" Type=\"error\"
        TypeUI=\"\"><![CDATA[Codice IVA non esistente in GAMMA: [1]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[0,00 ms
        | 390,00 ms]: fine sessione]]></Message>\r\n\t</DebugMessages>\r\n\t<ProcessingTime>390,00ms</ProcessingTime>\r\n</WebServiceResponse>\r\n\r\n"
    http_version: 
  recorded_at: Tue, 16 May 2017 13:09:21 GMT
- request:
    method: post
    uri: http://localhost:8084/
    body:
      encoding: UTF-8
      string: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n<WebServiceRequest>\n\t<SessionCookie>acc402a8-afdc-4730-bff8-f524643c360b</SessionCookie>\n\t<RequestAction>REGANAG</RequestAction>\n\t<RequestParameters>\n
        \       <parameter name=\"Operatore\">kolme.0002</parameter>\n        <parameter
        name=\"Terminale\">PC01</parameter>\n        <parameter name=\"DataOraInizio\">2017-05-16
        15:09:21 +0200</parameter>\n        <parameter name=\"DataOraFine\">2017-05-16
        15:09:21 +0200</parameter>\n        <parameter name=\"IncludiDebug\">True</parameter>\n
        \   </RequestParameters>\n\t<RequestData Rows=\"1\">\n\t\t<Anagrafica>\n\t\t
        \   <GUID>9e8588b5-df82-4398-96b0-d6a17ffe049c</GUID>\n\t\t\t<Codice>2</Codice>\n
        \           <Tipo>CF</Tipo>\n            <CodIntermedio>4</CodIntermedio>\n
        \           <PrivatoAzienda>A</PrivatoAzienda>\n            <RagioneSociale><![CDATA[Elio
        Pellegrini]]></RagioneSociale>\n            <Cognome><![CDATA[]]></Cognome>\n
        \           <Nome><![CDATA[]]></Nome>\n            <PartitaIVA>94323169695</PartitaIVA>\n
        \           <CodiceFiscale></CodiceFiscale>\n            <Indirizzo><![CDATA[Piazza
        Sala, 4]]></Indirizzo>\n            <CAP>22162</CAP>\n            <Citta><![CDATA[Alighieri
        lido]]></Citta>\n            <Provincia>GE</Provincia>\n            <CodCatastale></CodCatastale>\n
        \           <Nazione>IT</Nazione>\n\t\t\t<Telefono></Telefono>\n\t\t\t<Cellulare></Cellulare>\n\t\t\t<Fax></Fax>\n
        \           <Email></Email>\n            <Web></Web>\n            <CodPagamento>BFM</CodPagamento>\n
        \           <IBAN></IBAN>\n            <ExtraFido>0</ExtraFido>\n            <CodIva>1</CodIva>\n
        \           <CodRitenuta>0</CodRitenuta>\n            <DataAcquisizione>2017-05-16</DataAcquisizione>\n
        \           <Dismissione><![CDATA[]]></Dismissione>\n            <Catena><![CDATA[]]></Catena>\n
        \           <Note><![CDATA[anagrafica di test]]></Note>\n            \n\t\t</Anagrafica>\n\t</RequestData>\n</WebServiceRequest>\n\n\n"
    headers:
      Accept:
      - application/xml
      Accept-Encoding:
      - gzip, deflate
      User-Agent:
      - rest-client/2.0.0 (darwin16.0.0 x86_64) ruby/2.3.1p112
      Content-Type:
      - text/xml; charset=utf-8
      Content-Length:
      - '1873'
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private
      Content-Length:
      - '1608'
      Content-Type:
      - text/xml; Charset=ISO-8859-1
      Expires:
      - Tue, 16 May 2017 13:09:21 GMT
      Server:
      - Microsoft-IIS/8.5
      Set-Cookie:
      - ASPSESSIONIDSAARSTTT=GKMLNCEBHGEBCFLJLCEBGOME; path=/
      X-Powered-By:
      - ASP.NET
      Date:
      - Tue, 16 May 2017 13:09:21 GMT
    body:
      encoding: UTF-8
      string: "<?xml version=\"1.0\" encoding=\"ISO-8859-1\" standalone=\"no\"?>\r\n<WebServiceResponse>\r\n\t<SessionCookie>acc402a8-afdc-4730-bff8-f524643c360b</SessionCookie>\r\n\t<RequestAction>REGANAG</RequestAction>\r\n\t<ActionResult>0</ActionResult>\r\n\t<Results
        Count=\"1\" Size=\"133 bytes\">\r\n\t\t<Anagrafica GUID=\"9e8588b5-df82-4398-96b0-d6a17ffe049c\">\r\n\t\t\t<Result>0</Result>\r\n\t\t</Anagrafica>\r\n\t</Results>\r\n\t<UserMessages
        Count=\"2\" ErrorCount=\"0\">\r\n\t\t<Message ID=\"\" Group=\"clsRegAnagrafiche.ExecuteRequest\"
        Field=\"9e8588b5-df82-4398-96b0-d6a17ffe049c\" Type=\"info\" TypeUI=\"\"><![CDATA[Elaboro
        Anagrafica 1/1:]]></Message>\r\n\t\t<Message ID=\"\" Group=\"clsRegAnagrafiche.ExecuteRequest\"
        Field=\"9e8588b5-df82-4398-96b0-d6a17ffe049c\" Type=\"warning\" TypeUI=\"\"><![CDATA[
        ==&gt; ERRORE!]]></Message>\r\n\t</UserMessages>\r\n\t<DebugMessages Count=\"5\"
        ErrorCount=\"2\">\r\n\t\t<Message ID=\"\" Group=\"\" Field=\"\" Type=\"info\"
        TypeUI=\"\"><![CDATA[[0,00 ms | 0,00 ms]: inizio sessione]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[157,00 ms
        | 157,00 ms]: Eseguita funzione: [Valida_ParametriStandard]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"error\" TypeUI=\"\"><![CDATA[[281,00
        ms | 438,00 ms]: [9e8588b5-df82-4398-96b0-d6a17ffe049c - Elio Pellegrini]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"ValidaContenutiAnagrafica\" Field=\"CodIva\" Type=\"error\"
        TypeUI=\"\"><![CDATA[Codice IVA non esistente in GAMMA: [1]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[0,00 ms
        | 438,00 ms]: fine sessione]]></Message>\r\n\t</DebugMessages>\r\n\t<ProcessingTime>438,00ms</ProcessingTime>\r\n</WebServiceResponse>\r\n\r\n"
    http_version: 
  recorded_at: Tue, 16 May 2017 13:09:22 GMT
- request:
    method: post
    uri: http://localhost:8084/
    body:
      encoding: UTF-8
      string: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n<WebServiceRequest>\n\t<SessionCookie>2761f127-fe28-44c6-aa66-ae2c07c9b4cf</SessionCookie>\n\t<RequestAction>REGANAG</RequestAction>\n\t<RequestParameters>\n
        \       <parameter name=\"Operatore\">kolme.0002</parameter>\n        <parameter
        name=\"Terminale\">PC01</parameter>\n        <parameter name=\"DataOraInizio\">2017-05-16
        15:09:22 +0200</parameter>\n        <parameter name=\"DataOraFine\">2017-05-16
        15:09:22 +0200</parameter>\n        <parameter name=\"IncludiDebug\">True</parameter>\n
        \   </RequestParameters>\n\t<RequestData Rows=\"1\">\n\t\t<Anagrafica>\n\t\t
        \   <GUID>8dc2b6f6-52f1-4226-ba90-6c2b47662a4f</GUID>\n\t\t\t<Codice>3</Codice>\n
        \           <Tipo>CF</Tipo>\n            <CodIntermedio>4</CodIntermedio>\n
        \           <PrivatoAzienda>A</PrivatoAzienda>\n            <RagioneSociale><![CDATA[Ivonne
        Longo]]></RagioneSociale>\n            <Cognome><![CDATA[]]></Cognome>\n            <Nome><![CDATA[]]></Nome>\n
        \           <PartitaIVA>39914906977</PartitaIVA>\n            <CodiceFiscale></CodiceFiscale>\n
        \           <Indirizzo><![CDATA[Incrocio Zelida, 367]]></Indirizzo>\n            <CAP>88291</CAP>\n
        \           <Citta><![CDATA[Parisi veneto]]></Citta>\n            <Provincia>ZM</Provincia>\n
        \           <CodCatastale></CodCatastale>\n            <Nazione>IT</Nazione>\n\t\t\t<Telefono></Telefono>\n\t\t\t<Cellulare></Cellulare>\n\t\t\t<Fax></Fax>\n
        \           <Email></Email>\n            <Web></Web>\n            <CodPagamento>BFM</CodPagamento>\n
        \           <IBAN></IBAN>\n            <ExtraFido>0</ExtraFido>\n            <CodIva>1</CodIva>\n
        \           <CodRitenuta>0</CodRitenuta>\n            <DataAcquisizione>2017-05-16</DataAcquisizione>\n
        \           <Dismissione><![CDATA[]]></Dismissione>\n            <Catena><![CDATA[]]></Catena>\n
        \           <Note><![CDATA[anagrafica di test]]></Note>\n            \n\t\t</Anagrafica>\n\t</RequestData>\n</WebServiceRequest>\n\n\n"
    headers:
      Accept:
      - application/xml
      Accept-Encoding:
      - gzip, deflate
      User-Agent:
      - rest-client/2.0.0 (darwin16.0.0 x86_64) ruby/2.3.1p112
      Content-Type:
      - text/xml; charset=utf-8
      Content-Length:
      - '1875'
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private
      Content-Length:
      - '1603'
      Content-Type:
      - text/xml; Charset=ISO-8859-1
      Expires:
      - Tue, 16 May 2017 13:09:22 GMT
      Server:
      - Microsoft-IIS/8.5
      Set-Cookie:
      - ASPSESSIONIDSAARSTTT=HKMLNCEBDCBPPIBGDJHCNOJL; path=/
      X-Powered-By:
      - ASP.NET
      Date:
      - Tue, 16 May 2017 13:09:22 GMT
    body:
      encoding: UTF-8
      string: "<?xml version=\"1.0\" encoding=\"ISO-8859-1\" standalone=\"no\"?>\r\n<WebServiceResponse>\r\n\t<SessionCookie>2761f127-fe28-44c6-aa66-ae2c07c9b4cf</SessionCookie>\r\n\t<RequestAction>REGANAG</RequestAction>\r\n\t<ActionResult>0</ActionResult>\r\n\t<Results
        Count=\"1\" Size=\"133 bytes\">\r\n\t\t<Anagrafica GUID=\"8dc2b6f6-52f1-4226-ba90-6c2b47662a4f\">\r\n\t\t\t<Result>0</Result>\r\n\t\t</Anagrafica>\r\n\t</Results>\r\n\t<UserMessages
        Count=\"2\" ErrorCount=\"0\">\r\n\t\t<Message ID=\"\" Group=\"clsRegAnagrafiche.ExecuteRequest\"
        Field=\"8dc2b6f6-52f1-4226-ba90-6c2b47662a4f\" Type=\"info\" TypeUI=\"\"><![CDATA[Elaboro
        Anagrafica 1/1:]]></Message>\r\n\t\t<Message ID=\"\" Group=\"clsRegAnagrafiche.ExecuteRequest\"
        Field=\"8dc2b6f6-52f1-4226-ba90-6c2b47662a4f\" Type=\"warning\" TypeUI=\"\"><![CDATA[
        ==&gt; ERRORE!]]></Message>\r\n\t</UserMessages>\r\n\t<DebugMessages Count=\"5\"
        ErrorCount=\"2\">\r\n\t\t<Message ID=\"\" Group=\"\" Field=\"\" Type=\"info\"
        TypeUI=\"\"><![CDATA[[0,00 ms | 0,00 ms]: inizio sessione]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[78,00 ms
        | 78,00 ms]: Eseguita funzione: [Valida_ParametriStandard]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"error\" TypeUI=\"\"><![CDATA[[281,00
        ms | 359,00 ms]: [8dc2b6f6-52f1-4226-ba90-6c2b47662a4f - Ivonne Longo]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"ValidaContenutiAnagrafica\" Field=\"CodIva\" Type=\"error\"
        TypeUI=\"\"><![CDATA[Codice IVA non esistente in GAMMA: [1]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[0,00 ms
        | 359,00 ms]: fine sessione]]></Message>\r\n\t</DebugMessages>\r\n\t<ProcessingTime>359,00ms</ProcessingTime>\r\n</WebServiceResponse>\r\n\r\n"
    http_version: 
  recorded_at: Tue, 16 May 2017 13:09:23 GMT
- request:
    method: post
    uri: http://localhost:8084/
    body:
      encoding: UTF-8
      string: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n<WebServiceRequest>\n\t<SessionCookie>0c5b8e9b-5a9e-489f-b5fb-2000d04e0780</SessionCookie>\n\t<RequestAction>REGANAG</RequestAction>\n\t<RequestParameters>\n
        \       <parameter name=\"Operatore\">kolme.0002</parameter>\n        <parameter
        name=\"Terminale\">PC01</parameter>\n        <parameter name=\"DataOraInizio\">2017-05-16
        15:09:23 +0200</parameter>\n        <parameter name=\"DataOraFine\">2017-05-16
        15:09:23 +0200</parameter>\n        <parameter name=\"IncludiDebug\">True</parameter>\n
        \   </RequestParameters>\n\t<RequestData Rows=\"1\">\n\t\t<Anagrafica>\n\t\t
        \   <GUID>8dc2b6f6-52f1-4226-ba90-6c2b47662a4f</GUID>\n\t\t\t<Codice>3</Codice>\n
        \           <Tipo>CF</Tipo>\n            <CodIntermedio>4</CodIntermedio>\n
        \           <PrivatoAzienda>A</PrivatoAzienda>\n            <RagioneSociale><![CDATA[Ivonne
        Longo]]></RagioneSociale>\n            <Cognome><![CDATA[]]></Cognome>\n            <Nome><![CDATA[]]></Nome>\n
        \           <PartitaIVA>39914906977</PartitaIVA>\n            <CodiceFiscale></CodiceFiscale>\n
        \           <Indirizzo><![CDATA[Incrocio Zelida, 367]]></Indirizzo>\n            <CAP>88291</CAP>\n
        \           <Citta><![CDATA[Parisi veneto]]></Citta>\n            <Provincia>ZM</Provincia>\n
        \           <CodCatastale></CodCatastale>\n            <Nazione>IT</Nazione>\n\t\t\t<Telefono></Telefono>\n\t\t\t<Cellulare></Cellulare>\n\t\t\t<Fax></Fax>\n
        \           <Email></Email>\n            <Web></Web>\n            <CodPagamento>BFM</CodPagamento>\n
        \           <IBAN></IBAN>\n            <ExtraFido>0</ExtraFido>\n            <CodIva>1</CodIva>\n
        \           <CodRitenuta>0</CodRitenuta>\n            <DataAcquisizione>2017-05-16</DataAcquisizione>\n
        \           <Dismissione><![CDATA[]]></Dismissione>\n            <Catena><![CDATA[]]></Catena>\n
        \           <Note><![CDATA[anagrafica di test]]></Note>\n            \n                <Destinazione>\n
        \                   <GUID>948f807c-14e2-4091-8e3f-389e4e033ae5</GUID>\n                    <Tipo>R</Tipo>\n
        \                   <RagioneSociale><![CDATA[Kolme]]></RagioneSociale>\n                    <Contatto><![CDATA[]]></Contatto>\n
        \                   <Indirizzo>Viale Famagosta, 11</Indirizzo>\n                    <CAP>20100</CAP>\n
        \                   <Citta><![CDATA[MILANO]]></Citta>\n                    <Provincia>MI</Provincia>\n
        \                   <Nazione>IT</Nazione>\n                    <Telefono><![CDATA[]]></Telefono>\n
        \                   <Cellulare><![CDATA[]]></Cellulare>\n                    <Fax><![CDATA[]]></Fax>\n
        \                   <Email><![CDATA[]]></Email>\n                    <Note></Note>\n
        \               </Destinazione>\n            \n\t\t</Anagrafica>\n\t</RequestData>\n</WebServiceRequest>\n\n\n"
    headers:
      Accept:
      - application/xml
      Accept-Encoding:
      - gzip, deflate
      User-Agent:
      - rest-client/2.0.0 (darwin16.0.0 x86_64) ruby/2.3.1p112
      Content-Type:
      - text/xml; charset=utf-8
      Content-Length:
      - '2659'
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private
      Content-Length:
      - '1603'
      Content-Type:
      - text/xml; Charset=ISO-8859-1
      Expires:
      - Tue, 16 May 2017 13:09:23 GMT
      Server:
      - Microsoft-IIS/8.5
      Set-Cookie:
      - ASPSESSIONIDSAARSTTT=IKMLNCEBINOGCDMKEOHJINKG; path=/
      X-Powered-By:
      - ASP.NET
      Date:
      - Tue, 16 May 2017 13:09:22 GMT
    body:
      encoding: UTF-8
      string: "<?xml version=\"1.0\" encoding=\"ISO-8859-1\" standalone=\"no\"?>\r\n<WebServiceResponse>\r\n\t<SessionCookie>0c5b8e9b-5a9e-489f-b5fb-2000d04e0780</SessionCookie>\r\n\t<RequestAction>REGANAG</RequestAction>\r\n\t<ActionResult>0</ActionResult>\r\n\t<Results
        Count=\"1\" Size=\"133 bytes\">\r\n\t\t<Anagrafica GUID=\"8dc2b6f6-52f1-4226-ba90-6c2b47662a4f\">\r\n\t\t\t<Result>0</Result>\r\n\t\t</Anagrafica>\r\n\t</Results>\r\n\t<UserMessages
        Count=\"2\" ErrorCount=\"0\">\r\n\t\t<Message ID=\"\" Group=\"clsRegAnagrafiche.ExecuteRequest\"
        Field=\"8dc2b6f6-52f1-4226-ba90-6c2b47662a4f\" Type=\"info\" TypeUI=\"\"><![CDATA[Elaboro
        Anagrafica 1/1:]]></Message>\r\n\t\t<Message ID=\"\" Group=\"clsRegAnagrafiche.ExecuteRequest\"
        Field=\"8dc2b6f6-52f1-4226-ba90-6c2b47662a4f\" Type=\"warning\" TypeUI=\"\"><![CDATA[
        ==&gt; ERRORE!]]></Message>\r\n\t</UserMessages>\r\n\t<DebugMessages Count=\"5\"
        ErrorCount=\"2\">\r\n\t\t<Message ID=\"\" Group=\"\" Field=\"\" Type=\"info\"
        TypeUI=\"\"><![CDATA[[0,00 ms | 0,00 ms]: inizio sessione]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[47,00 ms
        | 47,00 ms]: Eseguita funzione: [Valida_ParametriStandard]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"error\" TypeUI=\"\"><![CDATA[[141,00
        ms | 188,00 ms]: [8dc2b6f6-52f1-4226-ba90-6c2b47662a4f - Ivonne Longo]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"ValidaContenutiAnagrafica\" Field=\"CodIva\" Type=\"error\"
        TypeUI=\"\"><![CDATA[Codice IVA non esistente in GAMMA: [1]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[0,00 ms
        | 188,00 ms]: fine sessione]]></Message>\r\n\t</DebugMessages>\r\n\t<ProcessingTime>219,00ms</ProcessingTime>\r\n</WebServiceResponse>\r\n\r\n"
    http_version: 
  recorded_at: Tue, 16 May 2017 13:09:23 GMT
- request:
    method: post
    uri: http://localhost:8084/
    body:
      encoding: UTF-8
      string: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n<WebServiceRequest>\n\t<SessionCookie>6b53517d-55ce-4c08-8c76-9c2660a63134</SessionCookie>\n\t<RequestAction>REGANAG</RequestAction>\n\t<RequestParameters>\n
        \       <parameter name=\"Operatore\">kolme.0002</parameter>\n        <parameter
        name=\"Terminale\">PC01</parameter>\n        <parameter name=\"DataOraInizio\">2017-05-16
        15:09:23 +0200</parameter>\n        <parameter name=\"DataOraFine\">2017-05-16
        15:09:23 +0200</parameter>\n        <parameter name=\"IncludiDebug\">True</parameter>\n
        \   </RequestParameters>\n\t<RequestData Rows=\"1\">\n\t\t<Anagrafica>\n\t\t
        \   <GUID>f6f76c26-07c6-4aeb-aa03-93849a394314</GUID>\n\t\t\t<Codice>4</Codice>\n
        \           <Tipo>CF</Tipo>\n            <CodIntermedio>4</CodIntermedio>\n
        \           <PrivatoAzienda>A</PrivatoAzienda>\n            <RagioneSociale><![CDATA[Guido
        Vitale]]></RagioneSociale>\n            <Cognome><![CDATA[]]></Cognome>\n
        \           <Nome><![CDATA[]]></Nome>\n            <PartitaIVA>67130395502</PartitaIVA>\n
        \           <CodiceFiscale></CodiceFiscale>\n            <Indirizzo><![CDATA[Piazza
        Rosalba, 964]]></Indirizzo>\n            <CAP>40347</CAP>\n            <Citta><![CDATA[Borgo
        Ione terme]]></Citta>\n            <Provincia>KZ</Provincia>\n            <CodCatastale></CodCatastale>\n
        \           <Nazione>IT</Nazione>\n\t\t\t<Telefono></Telefono>\n\t\t\t<Cellulare></Cellulare>\n\t\t\t<Fax></Fax>\n
        \           <Email></Email>\n            <Web></Web>\n            <CodPagamento>BFM</CodPagamento>\n
        \           <IBAN></IBAN>\n            <ExtraFido>0</ExtraFido>\n            <CodIva>1</CodIva>\n
        \           <CodRitenuta>0</CodRitenuta>\n            <DataAcquisizione>2017-05-16</DataAcquisizione>\n
        \           <Dismissione><![CDATA[]]></Dismissione>\n            <Catena><![CDATA[]]></Catena>\n
        \           <Note><![CDATA[anagrafica di test]]></Note>\n            \n\t\t</Anagrafica>\n\t</RequestData>\n</WebServiceRequest>\n\n\n"
    headers:
      Accept:
      - application/xml
      Accept-Encoding:
      - gzip, deflate
      User-Agent:
      - rest-client/2.0.0 (darwin16.0.0 x86_64) ruby/2.3.1p112
      Content-Type:
      - text/xml; charset=utf-8
      Content-Length:
      - '1877'
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private
      Content-Length:
      - '1605'
      Content-Type:
      - text/xml; Charset=ISO-8859-1
      Expires:
      - Tue, 16 May 2017 13:09:23 GMT
      Server:
      - Microsoft-IIS/8.5
      Set-Cookie:
      - ASPSESSIONIDSAARSTTT=JKMLNCEBLEGDODDNNGHEPFEO; path=/
      X-Powered-By:
      - ASP.NET
      Date:
      - Tue, 16 May 2017 13:09:24 GMT
    body:
      encoding: UTF-8
      string: "<?xml version=\"1.0\" encoding=\"ISO-8859-1\" standalone=\"no\"?>\r\n<WebServiceResponse>\r\n\t<SessionCookie>6b53517d-55ce-4c08-8c76-9c2660a63134</SessionCookie>\r\n\t<RequestAction>REGANAG</RequestAction>\r\n\t<ActionResult>0</ActionResult>\r\n\t<Results
        Count=\"1\" Size=\"133 bytes\">\r\n\t\t<Anagrafica GUID=\"f6f76c26-07c6-4aeb-aa03-93849a394314\">\r\n\t\t\t<Result>0</Result>\r\n\t\t</Anagrafica>\r\n\t</Results>\r\n\t<UserMessages
        Count=\"2\" ErrorCount=\"0\">\r\n\t\t<Message ID=\"\" Group=\"clsRegAnagrafiche.ExecuteRequest\"
        Field=\"f6f76c26-07c6-4aeb-aa03-93849a394314\" Type=\"info\" TypeUI=\"\"><![CDATA[Elaboro
        Anagrafica 1/1:]]></Message>\r\n\t\t<Message ID=\"\" Group=\"clsRegAnagrafiche.ExecuteRequest\"
        Field=\"f6f76c26-07c6-4aeb-aa03-93849a394314\" Type=\"warning\" TypeUI=\"\"><![CDATA[
        ==&gt; ERRORE!]]></Message>\r\n\t</UserMessages>\r\n\t<DebugMessages Count=\"5\"
        ErrorCount=\"2\">\r\n\t\t<Message ID=\"\" Group=\"\" Field=\"\" Type=\"info\"
        TypeUI=\"\"><![CDATA[[0,00 ms | 0,00 ms]: inizio sessione]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[141,00 ms
        | 141,00 ms]: Eseguita funzione: [Valida_ParametriStandard]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"error\" TypeUI=\"\"><![CDATA[[313,00
        ms | 454,00 ms]: [f6f76c26-07c6-4aeb-aa03-93849a394314 - Guido Vitale]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"ValidaContenutiAnagrafica\" Field=\"CodIva\" Type=\"error\"
        TypeUI=\"\"><![CDATA[Codice IVA non esistente in GAMMA: [1]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[0,00 ms
        | 454,00 ms]: fine sessione]]></Message>\r\n\t</DebugMessages>\r\n\t<ProcessingTime>469,00ms</ProcessingTime>\r\n</WebServiceResponse>\r\n\r\n"
    http_version: 
  recorded_at: Tue, 16 May 2017 13:09:24 GMT
- request:
    method: post
    uri: http://localhost:8084/
    body:
      encoding: UTF-8
      string: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n<WebServiceRequest>\n\t<SessionCookie>967e5090-9d0d-4b66-a1f2-367be7fea056</SessionCookie>\n\t<RequestAction>REGANAG</RequestAction>\n\t<RequestParameters>\n
        \       <parameter name=\"Operatore\">kolme.0002</parameter>\n        <parameter
        name=\"Terminale\">PC01</parameter>\n        <parameter name=\"DataOraInizio\">2017-05-16
        15:09:24 +0200</parameter>\n        <parameter name=\"DataOraFine\">2017-05-16
        15:09:24 +0200</parameter>\n        <parameter name=\"IncludiDebug\">True</parameter>\n
        \   </RequestParameters>\n\t<RequestData Rows=\"1\">\n\t\t<Anagrafica>\n\t\t
        \   <GUID>f6f76c26-07c6-4aeb-aa03-93849a394314</GUID>\n\t\t\t<Codice>4</Codice>\n
        \           <Tipo>CF</Tipo>\n            <CodIntermedio>4</CodIntermedio>\n
        \           <PrivatoAzienda>A</PrivatoAzienda>\n            <RagioneSociale><![CDATA[Guido
        Vitale]]></RagioneSociale>\n            <Cognome><![CDATA[]]></Cognome>\n
        \           <Nome><![CDATA[]]></Nome>\n            <PartitaIVA>67130395502</PartitaIVA>\n
        \           <CodiceFiscale></CodiceFiscale>\n            <Indirizzo><![CDATA[Piazza
        Rosalba, 964]]></Indirizzo>\n            <CAP>40347</CAP>\n            <Citta><![CDATA[Borgo
        Ione terme]]></Citta>\n            <Provincia>KZ</Provincia>\n            <CodCatastale></CodCatastale>\n
        \           <Nazione>IT</Nazione>\n\t\t\t<Telefono></Telefono>\n\t\t\t<Cellulare></Cellulare>\n\t\t\t<Fax></Fax>\n
        \           <Email></Email>\n            <Web></Web>\n            <CodPagamento>BFM</CodPagamento>\n
        \           <IBAN></IBAN>\n            <ExtraFido>0</ExtraFido>\n            <CodIva>1</CodIva>\n
        \           <CodRitenuta>0</CodRitenuta>\n            <DataAcquisizione>2017-05-16</DataAcquisizione>\n
        \           <Dismissione><![CDATA[]]></Dismissione>\n            <Catena><![CDATA[]]></Catena>\n
        \           <Note><![CDATA[anagrafica di test]]></Note>\n            \n                <Destinazione>\n
        \                   <GUID>8612940d-794d-419f-9c2c-c38fcf324529</GUID>\n                    <Tipo>R</Tipo>\n
        \                   <RagioneSociale><![CDATA[Hardware & Servizi]]></RagioneSociale>\n
        \                   <Contatto><![CDATA[]]></Contatto>\n                    <Indirizzo>Via
        Alessandro Maragliano, 10</Indirizzo>\n                    <CAP>27058</CAP>\n
        \                   <Citta><![CDATA[Milano]]></Citta>\n                    <Provincia>MI</Provincia>\n
        \                   <Nazione>IT</Nazione>\n                    <Telefono><![CDATA[]]></Telefono>\n
        \                   <Cellulare><![CDATA[]]></Cellulare>\n                    <Fax><![CDATA[]]></Fax>\n
        \                   <Email><![CDATA[]]></Email>\n                    <Note></Note>\n
        \               </Destinazione>\n            \n\t\t</Anagrafica>\n\t</RequestData>\n</WebServiceRequest>\n\n\n"
    headers:
      Accept:
      - application/xml
      Accept-Encoding:
      - gzip, deflate
      User-Agent:
      - rest-client/2.0.0 (darwin16.0.0 x86_64) ruby/2.3.1p112
      Content-Type:
      - text/xml; charset=utf-8
      Content-Length:
      - '2684'
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - private
      Content-Length:
      - '1603'
      Content-Type:
      - text/xml; Charset=ISO-8859-1
      Expires:
      - Tue, 16 May 2017 13:09:24 GMT
      Server:
      - Microsoft-IIS/8.5
      Set-Cookie:
      - ASPSESSIONIDSAARSTTT=KKMLNCEBOKJPHOOLNLFEEIOO; path=/
      X-Powered-By:
      - ASP.NET
      Date:
      - Tue, 16 May 2017 13:09:24 GMT
    body:
      encoding: UTF-8
      string: "<?xml version=\"1.0\" encoding=\"ISO-8859-1\" standalone=\"no\"?>\r\n<WebServiceResponse>\r\n\t<SessionCookie>967e5090-9d0d-4b66-a1f2-367be7fea056</SessionCookie>\r\n\t<RequestAction>REGANAG</RequestAction>\r\n\t<ActionResult>0</ActionResult>\r\n\t<Results
        Count=\"1\" Size=\"133 bytes\">\r\n\t\t<Anagrafica GUID=\"f6f76c26-07c6-4aeb-aa03-93849a394314\">\r\n\t\t\t<Result>0</Result>\r\n\t\t</Anagrafica>\r\n\t</Results>\r\n\t<UserMessages
        Count=\"2\" ErrorCount=\"0\">\r\n\t\t<Message ID=\"\" Group=\"clsRegAnagrafiche.ExecuteRequest\"
        Field=\"f6f76c26-07c6-4aeb-aa03-93849a394314\" Type=\"info\" TypeUI=\"\"><![CDATA[Elaboro
        Anagrafica 1/1:]]></Message>\r\n\t\t<Message ID=\"\" Group=\"clsRegAnagrafiche.ExecuteRequest\"
        Field=\"f6f76c26-07c6-4aeb-aa03-93849a394314\" Type=\"warning\" TypeUI=\"\"><![CDATA[
        ==&gt; ERRORE!]]></Message>\r\n\t</UserMessages>\r\n\t<DebugMessages Count=\"5\"
        ErrorCount=\"2\">\r\n\t\t<Message ID=\"\" Group=\"\" Field=\"\" Type=\"info\"
        TypeUI=\"\"><![CDATA[[0,00 ms | 0,00 ms]: inizio sessione]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[47,00 ms
        | 47,00 ms]: Eseguita funzione: [Valida_ParametriStandard]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"error\" TypeUI=\"\"><![CDATA[[125,00
        ms | 172,00 ms]: [f6f76c26-07c6-4aeb-aa03-93849a394314 - Guido Vitale]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"ValidaContenutiAnagrafica\" Field=\"CodIva\" Type=\"error\"
        TypeUI=\"\"><![CDATA[Codice IVA non esistente in GAMMA: [1]]]></Message>\r\n\t\t<Message
        ID=\"\" Group=\"\" Field=\"\" Type=\"info\" TypeUI=\"\"><![CDATA[[0,00 ms
        | 172,00 ms]: fine sessione]]></Message>\r\n\t</DebugMessages>\r\n\t<ProcessingTime>187,00ms</ProcessingTime>\r\n</WebServiceResponse>\r\n\r\n"
    http_version: 
  recorded_at: Tue, 16 May 2017 13:09:25 GMT
recorded_with: VCR 3.0.3
