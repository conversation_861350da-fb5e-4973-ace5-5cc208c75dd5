---
http_interactions:
- request:
    method: get
    uri: https://maps.googleapis.com/maps/api/geocode/json?address=34%20Haymarket%20%20Sheffield%20Regno%20Unito&key=AIzaSyCa8wUQ0yENUeS4LeFNb84_4rxU1HVN1uE&language=it&sensor=false
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Thu, 03 Mar 2022 14:45:18 GMT
      Pragma:
      - no-cache
      Expires:
      - Fri, 01 Jan 1990 00:00:00 GMT
      Cache-Control:
      - no-cache, must-revalidate
      Access-Control-Allow-Origin:
      - "*"
      Server:
      - mafe
      X-Xss-Protection:
      - '0'
      X-Frame-Options:
      - SAMEORIGIN
      Server-Timing:
      - gfet4t7; dur=26
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443";
        ma=2592000,h3-Q043=":443"; ma=2592000,quic=":443"; ma=2592000; v="46,43"
      Transfer-Encoding:
      - chunked
    body:
      encoding: ASCII-8BIT
      string: |
        {
           "results" : [
              {
                 "address_components" : [
                    {
                       "long_name" : "Sheffield",
                       "short_name" : "Sheffield",
                       "types" : [ "locality", "political" ]
                    },
                    {
                       "long_name" : "South Yorkshire",
                       "short_name" : "South Yorkshire",
                       "types" : [ "administrative_area_level_2", "political" ]
                    },
                    {
                       "long_name" : "Inghilterra",
                       "short_name" : "Inghilterra",
                       "types" : [ "administrative_area_level_1", "political" ]
                    },
                    {
                       "long_name" : "Regno Unito",
                       "short_name" : "GB",
                       "types" : [ "country", "political" ]
                    }
                 ],
                 "formatted_address" : "Sheffield, Regno Unito",
                 "geometry" : {
                    "bounds" : {
                       "northeast" : {
                          "lat" : 53.4868828,
                          "lng" : -1.334953
                       },
                       "southwest" : {
                          "lat" : 53.3045505,
                          "lng" : -1.6639593
                       }
                    },
                    "location" : {
                       "lat" : 53.38112899999999,
                       "lng" : -1.470085
                    },
                    "location_type" : "APPROXIMATE",
                    "viewport" : {
                       "northeast" : {
                          "lat" : 53.4868828,
                          "lng" : -1.334953
                       },
                       "southwest" : {
                          "lat" : 53.3045505,
                          "lng" : -1.6639593
                       }
                    }
                 },
                 "partial_match" : true,
                 "place_id" : "ChIJFb7o-qkKeUgReLAGr_UnKD4",
                 "types" : [ "locality", "political" ]
              },
              {
                 "address_components" : [
                    {
                       "long_name" : "Haymarket",
                       "short_name" : "Haymarket",
                       "types" : [ "neighborhood", "political" ]
                    },
                    {
                       "long_name" : "Edinburgh",
                       "short_name" : "Edinburgh",
                       "types" : [ "postal_town" ]
                    },
                    {
                       "long_name" : "Edinburgh",
                       "short_name" : "Edinburgh",
                       "types" : [ "administrative_area_level_2", "political" ]
                    },
                    {
                       "long_name" : "Scotland",
                       "short_name" : "Scotland",
                       "types" : [ "administrative_area_level_1", "political" ]
                    },
                    {
                       "long_name" : "Regno Unito",
                       "short_name" : "GB",
                       "types" : [ "country", "political" ]
                    }
                 ],
                 "formatted_address" : "Haymarket, Edinburgh, Regno Unito",
                 "geometry" : {
                    "bounds" : {
                       "northeast" : {
                          "lat" : 55.9472506,
                          "lng" : -3.2107935
                       },
                       "southwest" : {
                          "lat" : 55.943918,
                          "lng" : -3.2261301
                       }
                    },
                    "location" : {
                       "lat" : 55.9460913,
                       "lng" : -3.2168546
                    },
                    "location_type" : "APPROXIMATE",
                    "viewport" : {
                       "northeast" : {
                          "lat" : 55.9472506,
                          "lng" : -3.2107935
                       },
                       "southwest" : {
                          "lat" : 55.943918,
                          "lng" : -3.2261301
                       }
                    }
                 },
                 "partial_match" : true,
                 "place_id" : "ChIJzWHbP6_Hh0gRICAHxPPeyec",
                 "types" : [ "neighborhood", "political" ]
              }
           ],
           "status" : "OK"
        }
    http_version: 
  recorded_at: Thu, 03 Mar 2022 14:45:18 GMT
- request:
    method: get
    uri: https://maps.googleapis.com/maps/api/geocode/json?address=Borgarello%20(PV)%20%20&key=AIzaSyCa8wUQ0yENUeS4LeFNb84_4rxU1HVN1uE&language=it&sensor=false
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Thu, 03 Mar 2022 14:45:18 GMT
      Pragma:
      - no-cache
      Expires:
      - Fri, 01 Jan 1990 00:00:00 GMT
      Cache-Control:
      - no-cache, must-revalidate
      Access-Control-Allow-Origin:
      - "*"
      Server:
      - mafe
      X-Xss-Protection:
      - '0'
      X-Frame-Options:
      - SAMEORIGIN
      Server-Timing:
      - gfet4t7; dur=19
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443";
        ma=2592000,h3-Q043=":443"; ma=2592000,quic=":443"; ma=2592000; v="46,43"
      Transfer-Encoding:
      - chunked
    body:
      encoding: ASCII-8BIT
      string: |
        {
           "results" : [
              {
                 "address_components" : [
                    {
                       "long_name" : "Borgarello",
                       "short_name" : "Borgarello",
                       "types" : [ "administrative_area_level_3", "political" ]
                    },
                    {
                       "long_name" : "Provincia di Pavia",
                       "short_name" : "PV",
                       "types" : [ "administrative_area_level_2", "political" ]
                    },
                    {
                       "long_name" : "Lombardia",
                       "short_name" : "Lombardia",
                       "types" : [ "administrative_area_level_1", "political" ]
                    },
                    {
                       "long_name" : "Italia",
                       "short_name" : "IT",
                       "types" : [ "country", "political" ]
                    },
                    {
                       "long_name" : "27010",
                       "short_name" : "27010",
                       "types" : [ "postal_code" ]
                    }
                 ],
                 "formatted_address" : "27010 Borgarello PV, Italia",
                 "geometry" : {
                    "bounds" : {
                       "northeast" : {
                          "lat" : 45.2525458,
                          "lng" : 9.169338399999999
                       },
                       "southwest" : {
                          "lat" : 45.2288495,
                          "lng" : 9.132060599999999
                       }
                    },
                    "location" : {
                       "lat" : 45.2410196,
                       "lng" : 9.149351899999999
                    },
                    "location_type" : "APPROXIMATE",
                    "viewport" : {
                       "northeast" : {
                          "lat" : 45.2525458,
                          "lng" : 9.169338399999999
                       },
                       "southwest" : {
                          "lat" : 45.2288495,
                          "lng" : 9.132060599999999
                       }
                    }
                 },
                 "place_id" : "ChIJt0j2M4TYhkcRcBlnLgJ4BgQ",
                 "types" : [ "administrative_area_level_3", "political" ]
              }
           ],
           "status" : "OK"
        }
    http_version: 
  recorded_at: Thu, 03 Mar 2022 14:45:18 GMT
- request:
    method: get
    uri: https://maps.googleapis.com/maps/api/geocode/json?address=Viale%20Monza%2014%20Milano%20Italia&key=AIzaSyCa8wUQ0yENUeS4LeFNb84_4rxU1HVN1uE&language=it&sensor=false
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Thu, 03 Mar 2022 14:45:19 GMT
      Pragma:
      - no-cache
      Expires:
      - Fri, 01 Jan 1990 00:00:00 GMT
      Cache-Control:
      - no-cache, must-revalidate
      Access-Control-Allow-Origin:
      - "*"
      X-Goog-Maps-Metro-Area:
      - Milan
      Server:
      - mafe
      X-Xss-Protection:
      - '0'
      X-Frame-Options:
      - SAMEORIGIN
      Server-Timing:
      - gfet4t7; dur=18
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443";
        ma=2592000,h3-Q043=":443"; ma=2592000,quic=":443"; ma=2592000; v="46,43"
      Transfer-Encoding:
      - chunked
    body:
      encoding: ASCII-8BIT
      string: !binary |-
        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
    http_version: 
  recorded_at: Thu, 03 Mar 2022 14:45:19 GMT
- request:
    method: get
    uri: https://maps.googleapis.com/maps/api/geocode/json?address=Taormina%20Me%20&key=AIzaSyCa8wUQ0yENUeS4LeFNb84_4rxU1HVN1uE&language=it&sensor=false
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Thu, 03 Mar 2022 14:45:19 GMT
      Pragma:
      - no-cache
      Expires:
      - Fri, 01 Jan 1990 00:00:00 GMT
      Cache-Control:
      - no-cache, must-revalidate
      Access-Control-Allow-Origin:
      - "*"
      Server:
      - mafe
      X-Xss-Protection:
      - '0'
      X-Frame-Options:
      - SAMEORIGIN
      Server-Timing:
      - gfet4t7; dur=49
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443";
        ma=2592000,h3-Q043=":443"; ma=2592000,quic=":443"; ma=2592000; v="46,43"
      Transfer-Encoding:
      - chunked
    body:
      encoding: ASCII-8BIT
      string: !binary |-
        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
    http_version: 
  recorded_at: Thu, 03 Mar 2022 14:45:19 GMT
recorded_with: VCR 5.0.0
