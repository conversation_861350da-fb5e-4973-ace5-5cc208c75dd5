---
http_interactions:
- request:
    method: post
    uri: https://kolmedev.it-cpi023-rt.cfapps.eu20-001.hana.ondemand.com/http/DEV/IFI206_BusinessPartner
    body:
      encoding: UTF-8
      string: '{"BusinessPartner":"K122","BusinessPartnerCategory":"2","BusinessPartnerFullName":"<PERSON>","BusinessPartnerGrouping":"BPAB","BusinessPartnerName":"<PERSON> Rossi","BusinessPartnerType":null,"FirstName":null,"IsFemale":null,"IsMale":null,"GenderCodeName":null,"LastName":null,"CorrespondenceLanguage":null,"OrganizationBPName1":"Pericle
        Costa","SearchTerm1":null,"BusinessPartnerBirthplaceName":null,"to_BusinessPartnerAddress":{"results":[{"BusinessPartner":"K122","AddressTimeZone":"CET","CityName":"Milano","Country":"IT","FullName":"<PERSON>","HouseNumber":"1","Language":"IT","POBoxIsWithoutNumber":false,"PostalCode":"90614","Region":"MI","StreetName":"Borgo
        Romano","to_EmailAddress":{"results":[{"OrdinalNumber":"1","IsDefaultEmailAddress":true,"EmailAddress":"<EMAIL>","AddressCommunicationRemarkText":""},{"OrdinalNumber":"2","IsDefaultEmailAddress":false,"EmailAddress":"<EMAIL>","AddressCommunicationRemarkText":"PEC"}]},"to_PhoneNumber":{"results":[{"OrdinalNumber":"1","IsDefaultPhoneNumber":true,"PhoneNumber":"0123456789"}]}}]},"to_BusinessPartnerRole":{"results":[{"BusinessPartner":"K122","BusinessPartnerRole":"FLCU00"},{"BusinessPartner":"K122","BusinessPartnerRole":"FLCU01"},{"BusinessPartner":"K122","BusinessPartnerRole":"FLVN00"},{"BusinessPartner":"K122","BusinessPartnerRole":"FLVN01"}]},"to_BusinessPartnerTax":{"results":[{"BusinessPartner":"K122","BPTaxType":"IT0","BPTaxNumber":"*************"},{"BusinessPartner":"K122","BPTaxType":"IT1","BPTaxNumber":"***********"},{"BusinessPartner":"K122","BPTaxType":"IT4","BPTaxNumber":"AFDER45"}]},"to_BusinessPartnerBank":{"results":[{"BusinessPartner":"K122","BankIdentification":"","BankCountryKey":"IT","BankName":"","BankNumber":"**********","SWIFTCode":null,"BankControlKey":"A","IBAN":"***************************","BankAccount":"************","CollectionAuthInd":false}]},"to_Customer":{"Customer":"K122","BPCustomerFullName":"Mario
        Rossi","CustomerName":"Mario Rossi","BPCustomerName":"Mario Rossi","TaxNumber1":"","TaxNumber4":"AFDER45","PostingIsBlocked":false,"VATRegistration":"*************","DeletionIndicator":false,"to_CustomerCompany":{"results":[{"Customer":"K122","CompanyCode":"2310","CustomerSupplierClearingIsUsed":false,"HouseBank":"CRA01","LayoutSortingRule":"002","PaymentMethodsList":"E","PaymentTerms":"SEP3","PaytAdviceIsSentbyEDI":false,"PhysicalInventoryBlockInd":false,"ReconciliationAccount":"********","RecordPaymentHistoryIndicator":false,"DeletionIndicator":false,"CashPlanningGroup":"E2","to_CustomerDunning":{"results":[{"Customer":"K122","CompanyCode":"2310","DunningProcedure":"K001","DunningClerk":""}]}}]},"to_CustomerSalesArea":{"results":[{"Customer":"K122","SalesOrganization":"2310","DistributionChannel":"10","Division":"01","CompleteDeliveryIsDefined":false,"Currency":"EUR","CustIsRlvtForSettlmtMgmt":false,"CustomerAccountAssignmentGroup":"Z1","CustomerGroup":"Z1","CustomerIsRebateRelevant":false,"CustomerPaymentTerms":"SEP3","CustomerPricingProcedure":"01","IncotermsClassification":"EXW","DeletionIndicator":false,"IncotermsTransferLocation":".","InspSbstHasNoTimeOrQuantity":false,"ItemOrderProbabilityInPercent":"100","ManualInvoiceMaintIsRelevant":false,"OrderCombinationIsAllowed":true,"ShippingCondition":"01","SlsDocIsRlvtForProofOfDeliv":false,"SlsUnlmtdOvrdelivIsAllwd":false,"SupplyingPlant":"2310","to_SalesAreaTax":{"results":[{"Customer":"K122","SalesOrganization":"2310","DistributionChannel":"10","Division":"01","DepartureCountry":"IT","CustomerTaxCategory":"LCIT","CustomerTaxClassification":"1"},{"Customer":"K122","SalesOrganization":"2310","DistributionChannel":"10","Division":"01","DepartureCountry":"IT","CustomerTaxCategory":"MTX1","CustomerTaxClassification":"0"},{"Customer":"K122","SalesOrganization":"2310","DistributionChannel":"10","Division":"01","DepartureCountry":"IT","CustomerTaxCategory":"TTX1","CustomerTaxClassification":"1"}]}}]}},"to_Supplier":{"Supplier":"K122","PaymentIsBlockedForSupplier":false,"PostingIsBlocked":false,"PurchasingIsBlocked":false,"SupplierName":"Mario
        Rossi","VATRegistration":"*************","DeletionIndicator":false,"TaxNumber1":"","TaxNumber4":"AFDER45","BR_TaxIsSplit":false,"to_SupplierCompany":{"results":[{"Supplier":"K122","CompanyCode":"2310","SupplierIsBlockedForPosting":false,"PaymentMethodsList":"T","PaymentTerms":"FM00","ClearCustomerSupplier":false,"IsToBeLocallyProcessed":false,"ItemIsToBePaidSeparately":false,"PaymentIsToBeSentByEDI":false,"HouseBank":"CRA01","Currency":"EUR","ReconciliationAccount":"********","LayoutSortingRule":"009","DeletionIndicator":false,"CashPlanningGroup":"A1","IsToBeCheckedForDuplicates":true,"to_SupplierWithHoldingTax":{"results":[{"Supplier":"K122","CompanyCode":"2310","WithholdingTaxType":"1D","IsWithholdingTaxSubject":true,"WithholdingTaxCode":"1Y"}]}}]},"to_SupplierPurchasingOrg":{"results":[{"Supplier":"K122","PurchasingOrganization":"2310","AutomaticEvaluatedRcptSettlmt":false,"DeletionIndicator":false,"EvaldReceiptSettlementIsActive":false,"InvoiceIsGoodsReceiptBased":true,"InvoiceIsMMServiceEntryBased":false,"PaymentTerms":"FM00","PurOrdAutoGenerationIsAllowed":false,"PurchaseOrderCurrency":"EUR","PurchasingIsBlockedForSupplier":false,"SuplrDiscountInKindIsGranted":false,"SuplrInvcRevalIsAllowed":false,"SuplrIsRlvtForSettlmtMgmt":false,"SuplrPurgOrgIsRlvtForPriceDetn":false,"SupplierIsReturnsSupplier":false,"IsOrderAcknRqd":false,"to_PartnerFunction":{"results":[{"Supplier":"K122","PurchasingOrganization":"2310","PartnerFunction":"OA","DefaultPartner":false},{"Supplier":"K122","PurchasingOrganization":"2310","PartnerFunction":"VN","DefaultPartner":false},{"Supplier":"K122","PurchasingOrganization":"2310","PartnerFunction":"PI","DefaultPartner":false}]}}]}}}'
    headers:
      Accept:
      - application/json
      Content-Type:
      - application/json
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      User-Agent:
      - Ruby
      Authorization:
      - Basic c2ItZWE4OWMyYWYtOThiOS00NDg5LWFiZGEtZWM4NTIzNjViMjEyIWIzMzIyNHxpdC1ydC1rb2xtZWRldiFiMTg2MzE6YTA1OGQ5ZjktNDI1Mi00ZTA2LWFmZTgtMzE0M2U3OWYwYjI5JFRXR012T1RONVo2VEtuczN2dWNseS01YlpURnB1dWVSRjh3NXlMV2dBY3M9
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - no-cache, no-store, max-age=0, must-revalidate
      Content-Type:
      - application/json;charset=utf-8
      Dataserviceversion:
      - '2.0'
      Date:
      - Wed, 27 Nov 2024 15:38:36 GMT
      Expires:
      - '0'
      Pragma:
      - no-cache
      Sap-Cache-Control:
      - "-u"
      Sap-Metadata-Last-Modified:
      - Tue, 19 Nov 2024 11:36:08 GMT
      Sap-Passport-Component:
      - '0016'
      Sap-Perf-Fesrec:
      - '366485.000000'
      Sap-Processing-Info:
      - ODataBEP=,crp=,RAL=,st=,MedCacheHub=Table,codeployed=X,softstate=
      Sap-Server:
      - 'true'
      Sap-Messageprocessinglogid:
      - AGdHPPlOL-dp4EebCQc9Kt95YnmZ
      Sap-Mplcorrelationid:
      - AGdHPPkKadgQgob0KIZkOU4s4uQf
      Sap-Pregeneratedmplid:
      - AGdHPPy7YsURieb5V71Crd4DqkS3
      Sapenablemplattachments:
      - 'true'
      Sapgroup:
      - '1'
      Sapsplitexpression:
      - "/elm/root"
      Server:
      - SAP
      X-Content-Type-Options:
      - nosniff
      X-Correlationid:
      - b155dc6e-9252-4cfe-4403-23bebf01d00f
      X-Csrf-Token:
      - YwxpzpU90B5kSgNmbVLGiA==
      X-Frame-Options:
      - DENY
      X-Vcap-Request-Id:
      - b155dc6e-9252-4cfe-4403-23bebf01d00f
      X-Xss-Protection:
      - 1; mode=block
      Transfer-Encoding:
      - chunked
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains; preload;
    body:
      encoding: UTF-8
      string: '{"d":{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')","type":"API_BUSINESS_PARTNER.A_BusinessPartnerType"},"BusinessPartner":"K122","Customer":"K122","Supplier":"K122","AcademicTitle":"","AuthorizationGroup":"","BusinessPartnerCategory":"2","BusinessPartnerFullName":"Pericle
        Costa","BusinessPartnerGrouping":"BPAB","BusinessPartnerName":"Pericle Costa","BusinessPartnerUUID":"114b213b-d600-1eef-ab9a-b529ce447f31","CorrespondenceLanguage":"","CreatedByUser":"CC********03","CreationDate":"\/Date(*************)\/","CreationTime":"PT15H38M34S","FirstName":"","FormOfAddress":"","Industry":"","InternationalLocationNumber1":"0","InternationalLocationNumber2":"0","IsFemale":false,"IsMale":false,"IsNaturalPerson":"","IsSexUnknown":false,"GenderCodeName":"","Language":"","LastChangeDate":null,"LastChangeTime":"PT00H00M00S","LastChangedByUser":"","LastName":"","LegalForm":"","OrganizationBPName1":"Pericle
        Costa","OrganizationBPName2":"","OrganizationBPName3":"","OrganizationBPName4":"","OrganizationFoundationDate":null,"OrganizationLiquidationDate":null,"SearchTerm1":"","SearchTerm2":"","AdditionalLastName":"","BirthDate":null,"BusinessPartnerBirthDateStatus":"","BusinessPartnerBirthplaceName":"","BusinessPartnerDeathDate":null,"BusinessPartnerIsBlocked":false,"BusinessPartnerType":"","ETag":"CC********0320241127153834","GroupBusinessPartnerName1":"","GroupBusinessPartnerName2":"","IndependentAddressID":"","InternationalLocationNumber3":"0","MiddleName":"","NameCountry":"","NameFormat":"","PersonFullName":"","PersonNumber":"","IsMarkedForArchiving":false,"BusinessPartnerIDByExtSystem":"","BusinessPartnerPrintFormat":"","BusinessPartnerOccupation":"","BusPartMaritalStatus":"","BusPartNationality":"","BusinessPartnerBirthName":"","BusinessPartnerSupplementName":"","NaturalPersonEmployerName":"","LastNamePrefix":"","LastNameSecondPrefix":"","Initials":"","BPDataControllerIsNotRequired":false,"TradingPartner":"","to_AddressIndependentEmail":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')/to_AddressIndependentEmail"}},"to_AddressIndependentFax":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')/to_AddressIndependentFax"}},"to_AddressIndependentMobile":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')/to_AddressIndependentMobile"}},"to_AddressIndependentPhone":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')/to_AddressIndependentPhone"}},"to_AddressIndependentWebsite":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')/to_AddressIndependentWebsite"}},"to_BPCreditWorthiness":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')/to_BPCreditWorthiness"}},"to_BPDataController":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')/to_BPDataController"}},"to_BPEmployment":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')/to_BPEmployment"}},"to_BPFinServicesReporting":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')/to_BPFinServicesReporting"}},"to_BPFiscalYearInformation":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')/to_BPFiscalYearInformation"}},"to_BPRelationship":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')/to_BPRelationship"}},"to_BuPaIdentification":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')/to_BuPaIdentification"}},"to_BuPaIndustry":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')/to_BuPaIndustry"}},"to_BusinessPartner":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')/to_BusinessPartner"}},"to_BusinessPartnerAddress":{"results":[{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerAddress(BusinessPartner=''K122'',AddressID=''93513'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerAddress(BusinessPartner=''K122'',AddressID=''93513'')","type":"API_BUSINESS_PARTNER.A_BusinessPartnerAddressType"},"BusinessPartner":"K122","AddressID":"93513","ValidityStartDate":"\/Date(*************+0000)\/","ValidityEndDate":"\/Date(***************+0000)\/","AuthorizationGroup":"","AddressUUID":"114b213b-d600-1eef-ab9a-b529ce449f31","AdditionalStreetPrefixName":"","AdditionalStreetSuffixName":"","AddressTimeZone":"CET","CareOfName":"","CityCode":"","CityName":"Milano","CompanyPostalCode":"","Country":"IT","County":"","DeliveryServiceNumber":"","DeliveryServiceTypeCode":"","District":"","FormOfAddress":"","FullName":"Pericle
        Costa","HomeCityName":"","HouseNumber":"1","HouseNumberSupplementText":"","Language":"IT","POBox":"","POBoxDeviatingCityName":"","POBoxDeviatingCountry":"","POBoxDeviatingRegion":"","POBoxIsWithoutNumber":false,"POBoxLobbyName":"","POBoxPostalCode":"","Person":"","PostalCode":"90614","PrfrdCommMediumType":"","Region":"MI","StreetName":"Borgo
        Romano","StreetPrefixName":"","StreetSuffixName":"","TaxJurisdiction":"","TransportZone":"","AddressIDByExternalSystem":"","CountyCode":"","TownshipCode":"","TownshipName":"","to_AddressUsage":{"results":[{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BuPaAddressUsage(BusinessPartner=''K122'',ValidityEndDate=datetimeoffset''9999-12-31T23%3A59%3A59Z'',AddressUsage=''XXDEFAULT'',AddressID=''93513'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BuPaAddressUsage(BusinessPartner=''K122'',ValidityEndDate=datetimeoffset''9999-12-31T23%3A59%3A59Z'',AddressUsage=''XXDEFAULT'',AddressID=''93513'')","type":"API_BUSINESS_PARTNER.A_BuPaAddressUsageType"},"BusinessPartner":"K122","ValidityEndDate":"\/Date(***************+0000)\/","AddressUsage":"XXDEFAULT","AddressID":"93513","ValidityStartDate":"\/Date(*************+0000)\/","StandardUsage":false,"AuthorizationGroup":""}]},"to_BPAddrDepdntIntlLocNumber":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerAddress(BusinessPartner=''K122'',AddressID=''93513'')/to_BPAddrDepdntIntlLocNumber"}},"to_BPIntlAddressVersion":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerAddress(BusinessPartner=''K122'',AddressID=''93513'')/to_BPIntlAddressVersion"}},"to_EmailAddress":{"results":[{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_AddressEmailAddress(AddressID=''93513'',Person='''',OrdinalNumber=''1'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_AddressEmailAddress(AddressID=''93513'',Person='''',OrdinalNumber=''1'')","type":"API_BUSINESS_PARTNER.A_AddressEmailAddressType"},"AddressID":"93513","Person":"","OrdinalNumber":"1","IsDefaultEmailAddress":true,"EmailAddress":"<EMAIL>","SearchEmailAddress":"TEST@TESTAMMINISTRAT","AddressCommunicationRemarkText":""},{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_AddressEmailAddress(AddressID=''93513'',Person='''',OrdinalNumber=''2'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_AddressEmailAddress(AddressID=''93513'',Person='''',OrdinalNumber=''2'')","type":"API_BUSINESS_PARTNER.A_AddressEmailAddressType"},"AddressID":"93513","Person":"","OrdinalNumber":"2","IsDefaultEmailAddress":false,"EmailAddress":"<EMAIL>","SearchEmailAddress":"<EMAIL>","AddressCommunicationRemarkText":"PEC"}]},"to_FaxNumber":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerAddress(BusinessPartner=''K122'',AddressID=''93513'')/to_FaxNumber"}},"to_MobilePhoneNumber":{"results":[]},"to_PhoneNumber":{"results":[{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_AddressPhoneNumber(AddressID=''93513'',Person='''',OrdinalNumber=''1'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_AddressPhoneNumber(AddressID=''93513'',Person='''',OrdinalNumber=''1'')","type":"API_BUSINESS_PARTNER.A_AddressPhoneNumberType"},"AddressID":"93513","Person":"","OrdinalNumber":"1","DestinationLocationCountry":"IT","IsDefaultPhoneNumber":true,"PhoneNumber":"0123456789","PhoneNumberExtension":"","InternationalPhoneNumber":"+************","PhoneNumberType":"1","AddressCommunicationRemarkText":""}]},"to_URLAddress":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerAddress(BusinessPartner=''K122'',AddressID=''93513'')/to_URLAddress"}}}]},"to_BusinessPartnerAlias":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')/to_BusinessPartnerAlias"}},"to_BusinessPartnerBank":{"results":[{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerBank(BusinessPartner=''K122'',BankIdentification=''0001'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerBank(BusinessPartner=''K122'',BankIdentification=''0001'')","type":"API_BUSINESS_PARTNER.A_BusinessPartnerBankType"},"BusinessPartner":"K122","BankIdentification":"0001","BankCountryKey":"IT","BankName":"Uncredit","BankNumber":"**********","SWIFTCode":"UNCRITMM","BankControlKey":"A","BankAccountHolderName":"","BankAccountName":"","ValidityStartDate":"\/Date(*************+0000)\/","ValidityEndDate":"\/Date(***************+0000)\/","IBAN":"***************************","IBANValidityStartDate":"\/Date(*************)\/","BankAccount":"************","BankAccountReferenceText":"","CollectionAuthInd":false,"CityName":"","AuthorizationGroup":""}]},"to_BusinessPartnerContact":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')/to_BusinessPartnerContact"}},"to_BusinessPartnerIsBank":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')/to_BusinessPartnerIsBank"}},"to_BusinessPartnerRating":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')/to_BusinessPartnerRating"}},"to_BusinessPartnerRole":{"results":[{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerRole(BusinessPartner=''K122'',BusinessPartnerRole=''FLCU00'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerRole(BusinessPartner=''K122'',BusinessPartnerRole=''FLCU00'')","type":"API_BUSINESS_PARTNER.A_BusinessPartnerRoleType"},"BusinessPartner":"K122","BusinessPartnerRole":"FLCU00","ValidFrom":"\/Date(*************+0000)\/","ValidTo":"\/Date(***************+0000)\/","AuthorizationGroup":""},{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerRole(BusinessPartner=''K122'',BusinessPartnerRole=''FLCU01'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerRole(BusinessPartner=''K122'',BusinessPartnerRole=''FLCU01'')","type":"API_BUSINESS_PARTNER.A_BusinessPartnerRoleType"},"BusinessPartner":"K122","BusinessPartnerRole":"FLCU01","ValidFrom":"\/Date(*************+0000)\/","ValidTo":"\/Date(***************+0000)\/","AuthorizationGroup":""},{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerRole(BusinessPartner=''K122'',BusinessPartnerRole=''FLVN00'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerRole(BusinessPartner=''K122'',BusinessPartnerRole=''FLVN00'')","type":"API_BUSINESS_PARTNER.A_BusinessPartnerRoleType"},"BusinessPartner":"K122","BusinessPartnerRole":"FLVN00","ValidFrom":"\/Date(*************+0000)\/","ValidTo":"\/Date(***************+0000)\/","AuthorizationGroup":""},{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerRole(BusinessPartner=''K122'',BusinessPartnerRole=''FLVN01'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerRole(BusinessPartner=''K122'',BusinessPartnerRole=''FLVN01'')","type":"API_BUSINESS_PARTNER.A_BusinessPartnerRoleType"},"BusinessPartner":"K122","BusinessPartnerRole":"FLVN01","ValidFrom":"\/Date(*************+0000)\/","ValidTo":"\/Date(***************+0000)\/","AuthorizationGroup":""}]},"to_BusinessPartnerTax":{"results":[{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerTaxNumber(BusinessPartner=''K122'',BPTaxType=''IT0'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerTaxNumber(BusinessPartner=''K122'',BPTaxType=''IT0'')","type":"API_BUSINESS_PARTNER.A_BusinessPartnerTaxNumberType"},"BusinessPartner":"K122","BPTaxType":"IT0","BPTaxNumber":"*************","BPTaxLongNumber":"","AuthorizationGroup":""},{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerTaxNumber(BusinessPartner=''K122'',BPTaxType=''IT1'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerTaxNumber(BusinessPartner=''K122'',BPTaxType=''IT1'')","type":"API_BUSINESS_PARTNER.A_BusinessPartnerTaxNumberType"},"BusinessPartner":"K122","BPTaxType":"IT1","BPTaxNumber":"***********","BPTaxLongNumber":"","AuthorizationGroup":""},{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerTaxNumber(BusinessPartner=''K122'',BPTaxType=''IT4'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartnerTaxNumber(BusinessPartner=''K122'',BPTaxType=''IT4'')","type":"API_BUSINESS_PARTNER.A_BusinessPartnerTaxNumberType"},"BusinessPartner":"K122","BPTaxType":"IT4","BPTaxNumber":"AFDER45","BPTaxLongNumber":"","AuthorizationGroup":""}]},"to_BusPartAddrDepdntTaxNmbr":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')/to_BusPartAddrDepdntTaxNmbr"}},"to_Customer":{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_Customer(''K122'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_Customer(''K122'')","type":"API_BUSINESS_PARTNER.A_CustomerType"},"Customer":"K122","AuthorizationGroup":"","BillingIsBlockedForCustomer":"","CreatedByUser":"CC********03","CreationDate":"\/Date(*************)\/","CustomerAccountGroup":"CUST","CustomerClassification":"","CustomerFullName":"
        Pericle Costa/90614 Milano","BPCustomerFullName":"Pericle Costa","CustomerName":"Pericle
        Costa","BPCustomerName":"Pericle Costa","DeliveryIsBlocked":"","FreeDefinedAttribute01":"","FreeDefinedAttribute02":"","FreeDefinedAttribute03":"","FreeDefinedAttribute04":"","FreeDefinedAttribute05":"","FreeDefinedAttribute06":"","FreeDefinedAttribute07":"","FreeDefinedAttribute08":"","FreeDefinedAttribute09":"","FreeDefinedAttribute10":"","NFPartnerIsNaturalPerson":"","OrderIsBlockedForCustomer":"","PostingIsBlocked":false,"Supplier":"","CustomerCorporateGroup":"","FiscalAddress":"","Industry":"","IndustryCode1":"","IndustryCode2":"","IndustryCode3":"","IndustryCode4":"","IndustryCode5":"","InternationalLocationNumber1":"0","InternationalLocationNumber2":"0","InternationalLocationNumber3":"0","NielsenRegion":"","PaymentReason":"","ResponsibleType":"","TaxNumber1":"***********","TaxNumber2":"","TaxNumber3":"","TaxNumber4":"AFDER45","TaxNumber5":"","TaxNumberType":"","VATRegistration":"*************","DeletionIndicator":false,"ExpressTrainStationName":"","TrainStationName":"","CityCode":"","County":"","to_CustAddrDepdntExtIdentifier":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_Customer(''K122'')/to_CustAddrDepdntExtIdentifier"}},"to_CustAddrDepdntInformation":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_Customer(''K122'')/to_CustAddrDepdntInformation"}},"to_CustomerCompany":{"results":[{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustomerCompany(Customer=''K122'',CompanyCode=''2310'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustomerCompany(Customer=''K122'',CompanyCode=''2310'')","type":"API_BUSINESS_PARTNER.A_CustomerCompanyType"},"Customer":"K122","CompanyCode":"2310","APARToleranceGroup":"","AccountByCustomer":"","AccountingClerk":"","AccountingClerkFaxNumber":"","AccountingClerkInternetAddress":"","AccountingClerkPhoneNumber":"","AlternativePayerAccount":"","AuthorizationGroup":"","CollectiveInvoiceVariant":"","CustomerAccountNote":"","CustomerHeadOffice":"","CustomerSupplierClearingIsUsed":false,"HouseBank":"CRA01","InterestCalculationCode":"","InterestCalculationDate":null,"IntrstCalcFrequencyInMonths":"0","IsToBeLocallyProcessed":false,"ItemIsToBePaidSeparately":false,"LayoutSortingRule":"002","PaymentBlockingReason":"","PaymentMethodsList":"E","PaymentReason":"","PaymentTerms":"SEP3","PaytAdviceIsSentbyEDI":false,"PhysicalInventoryBlockInd":false,"ReconciliationAccount":"********","RecordPaymentHistoryIndicator":false,"UserAtCustomer":"","DeletionIndicator":false,"CashPlanningGroup":"E2","KnownOrNegotiatedLeave":"","ValueAdjustmentKey":"","CustomerAccountGroup":"CUST","to_CompanyText":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustomerCompany(Customer=''K122'',CompanyCode=''2310'')/to_CompanyText"}},"to_CustomerDunning":{"results":[{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustomerDunning(Customer=''K122'',CompanyCode=''2310'',DunningArea='''')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustomerDunning(Customer=''K122'',CompanyCode=''2310'',DunningArea='''')","type":"API_BUSINESS_PARTNER.A_CustomerDunningType"},"Customer":"K122","CompanyCode":"2310","DunningArea":"","DunningBlock":"","DunningLevel":"0","DunningProcedure":"K001","DunningRecipient":"","LastDunnedOn":null,"LegDunningProcedureOn":null,"DunningClerk":"","AuthorizationGroup":"","CustomerAccountGroup":"CUST"}]},"to_WithHoldingTax":{"results":[]}}]},"to_CustomerSalesArea":{"results":[{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustomerSalesArea(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustomerSalesArea(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'')","type":"API_BUSINESS_PARTNER.A_CustomerSalesAreaType"},"Customer":"K122","SalesOrganization":"2310","DistributionChannel":"10","Division":"01","AccountByCustomer":"","AuthorizationGroup":"","BillingIsBlockedForCustomer":"","CompleteDeliveryIsDefined":false,"CreditControlArea":"","Currency":"EUR","CustIsRlvtForSettlmtMgmt":false,"CustomerABCClassification":"","CustomerAccountAssignmentGroup":"Z1","CustomerGroup":"Z1","CustomerIsRebateRelevant":false,"CustomerPaymentTerms":"SEP3","CustomerPriceGroup":"","CustomerPricingProcedure":"01","CustProdProposalProcedure":"","DeliveryIsBlockedForCustomer":"","DeliveryPriority":"0","IncotermsClassification":"EXW","IncotermsLocation2":"","IncotermsVersion":"","IncotermsLocation1":".","IncotermsSupChnLoc1AddlUUID":"********-0000-0000-0000-************","IncotermsSupChnLoc2AddlUUID":"********-0000-0000-0000-************","IncotermsSupChnDvtgLocAddlUUID":"********-0000-0000-0000-************","DeletionIndicator":false,"IncotermsTransferLocation":".","InspSbstHasNoTimeOrQuantity":false,"InvoiceDate":"","ItemOrderProbabilityInPercent":"100","ManualInvoiceMaintIsRelevant":false,"MaxNmbrOfPartialDelivery":"0","OrderCombinationIsAllowed":true,"OrderIsBlockedForCustomer":"","OverdelivTolrtdLmtRatioInPct":"0.0","PartialDeliveryIsAllowed":"","PriceListType":"","ProductUnitGroup":"","ProofOfDeliveryTimeValue":"0.00","SalesGroup":"","SalesItemProposal":"","SalesOffice":"","ShippingCondition":"01","SlsDocIsRlvtForProofOfDeliv":false,"SlsUnlmtdOvrdelivIsAllwd":false,"SupplyingPlant":"2310","SalesDistrict":"","UnderdelivTolrtdLmtRatioInPct":"0.0","InvoiceListSchedule":"","ExchangeRateType":"","AdditionalCustomerGroup1":"","AdditionalCustomerGroup2":"","AdditionalCustomerGroup3":"","AdditionalCustomerGroup4":"","AdditionalCustomerGroup5":"","PaymentGuaranteeProcedure":"","CustomerAccountGroup":"CUST","to_PartnerFunction":{"results":[{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustSalesPartnerFunc(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'',PartnerCounter=''0'',PartnerFunction=''SP'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustSalesPartnerFunc(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'',PartnerCounter=''0'',PartnerFunction=''SP'')","type":"API_BUSINESS_PARTNER.A_CustSalesPartnerFuncType"},"Customer":"K122","SalesOrganization":"2310","DistributionChannel":"10","Division":"01","PartnerCounter":"0","PartnerFunction":"SP","BPCustomerNumber":"K122","CustomerPartnerDescription":"","DefaultPartner":false,"Supplier":"","PersonnelNumber":"0","ContactPerson":"0","AddressID":"","AuthorizationGroup":""},{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustSalesPartnerFunc(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'',PartnerCounter=''0'',PartnerFunction=''BP'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustSalesPartnerFunc(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'',PartnerCounter=''0'',PartnerFunction=''BP'')","type":"API_BUSINESS_PARTNER.A_CustSalesPartnerFuncType"},"Customer":"K122","SalesOrganization":"2310","DistributionChannel":"10","Division":"01","PartnerCounter":"0","PartnerFunction":"BP","BPCustomerNumber":"K122","CustomerPartnerDescription":"","DefaultPartner":false,"Supplier":"","PersonnelNumber":"0","ContactPerson":"0","AddressID":"","AuthorizationGroup":""},{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustSalesPartnerFunc(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'',PartnerCounter=''0'',PartnerFunction=''PY'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustSalesPartnerFunc(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'',PartnerCounter=''0'',PartnerFunction=''PY'')","type":"API_BUSINESS_PARTNER.A_CustSalesPartnerFuncType"},"Customer":"K122","SalesOrganization":"2310","DistributionChannel":"10","Division":"01","PartnerCounter":"0","PartnerFunction":"PY","BPCustomerNumber":"K122","CustomerPartnerDescription":"","DefaultPartner":false,"Supplier":"","PersonnelNumber":"0","ContactPerson":"0","AddressID":"","AuthorizationGroup":""},{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustSalesPartnerFunc(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'',PartnerCounter=''0'',PartnerFunction=''SH'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustSalesPartnerFunc(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'',PartnerCounter=''0'',PartnerFunction=''SH'')","type":"API_BUSINESS_PARTNER.A_CustSalesPartnerFuncType"},"Customer":"K122","SalesOrganization":"2310","DistributionChannel":"10","Division":"01","PartnerCounter":"0","PartnerFunction":"SH","BPCustomerNumber":"K122","CustomerPartnerDescription":"","DefaultPartner":false,"Supplier":"","PersonnelNumber":"0","ContactPerson":"0","AddressID":"","AuthorizationGroup":""}]},"to_SalesAreaTax":{"results":[{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustomerSalesAreaTax(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'',DepartureCountry=''IT'',CustomerTaxCategory=''LCIT'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustomerSalesAreaTax(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'',DepartureCountry=''IT'',CustomerTaxCategory=''LCIT'')","type":"API_BUSINESS_PARTNER.A_CustomerSalesAreaTaxType"},"Customer":"K122","SalesOrganization":"2310","DistributionChannel":"10","Division":"01","DepartureCountry":"IT","CustomerTaxCategory":"LCIT","CustomerTaxClassification":"1","to_SlsAreaAddrDepdntTax":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustomerSalesAreaTax(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'',DepartureCountry=''IT'',CustomerTaxCategory=''LCIT'')/to_SlsAreaAddrDepdntTax"}}},{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustomerSalesAreaTax(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'',DepartureCountry=''IT'',CustomerTaxCategory=''MTX1'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustomerSalesAreaTax(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'',DepartureCountry=''IT'',CustomerTaxCategory=''MTX1'')","type":"API_BUSINESS_PARTNER.A_CustomerSalesAreaTaxType"},"Customer":"K122","SalesOrganization":"2310","DistributionChannel":"10","Division":"01","DepartureCountry":"IT","CustomerTaxCategory":"MTX1","CustomerTaxClassification":"0","to_SlsAreaAddrDepdntTax":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustomerSalesAreaTax(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'',DepartureCountry=''IT'',CustomerTaxCategory=''MTX1'')/to_SlsAreaAddrDepdntTax"}}},{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustomerSalesAreaTax(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'',DepartureCountry=''IT'',CustomerTaxCategory=''TTX1'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustomerSalesAreaTax(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'',DepartureCountry=''IT'',CustomerTaxCategory=''TTX1'')","type":"API_BUSINESS_PARTNER.A_CustomerSalesAreaTaxType"},"Customer":"K122","SalesOrganization":"2310","DistributionChannel":"10","Division":"01","DepartureCountry":"IT","CustomerTaxCategory":"TTX1","CustomerTaxClassification":"1","to_SlsAreaAddrDepdntTax":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustomerSalesAreaTax(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'',DepartureCountry=''IT'',CustomerTaxCategory=''TTX1'')/to_SlsAreaAddrDepdntTax"}}}]},"to_SalesAreaText":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustomerSalesArea(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'')/to_SalesAreaText"}},"to_SlsAreaAddrDepdntInfo":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_CustomerSalesArea(Customer=''K122'',SalesOrganization=''2310'',DistributionChannel=''10'',Division=''01'')/to_SlsAreaAddrDepdntInfo"}}}]},"to_CustomerTaxGrouping":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_Customer(''K122'')/to_CustomerTaxGrouping"}},"to_CustomerText":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_Customer(''K122'')/to_CustomerText"}},"to_CustomerUnloadingPoint":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_Customer(''K122'')/to_CustomerUnloadingPoint"}},"to_CustUnldgPtAddrDepdntInfo":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_Customer(''K122'')/to_CustUnldgPtAddrDepdntInfo"}}},"to_PaymentCard":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_BusinessPartner(''K122'')/to_PaymentCard"}},"to_Supplier":{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_Supplier(''K122'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_Supplier(''K122'')","type":"API_BUSINESS_PARTNER.A_SupplierType"},"Supplier":"K122","AlternativePayeeAccountNumber":"","AuthorizationGroup":"","BusinessPartnerPanNumber":"","CreatedByUser":"CC********03","CreationDate":"\/Date(*************)\/","Customer":"","PaymentIsBlockedForSupplier":false,"PostingIsBlocked":false,"PurchasingIsBlocked":false,"SupplierAccountGroup":"SUPL","SupplierFullName":"
        Pericle Costa/90614 Milano","SupplierName":"Pericle Costa","VATRegistration":"*************","BirthDate":null,"ConcatenatedInternationalLocNo":"0000000
        &00000 &0","DeletionIndicator":false,"FiscalAddress":"","Industry":"","InternationalLocationNumber1":"0","InternationalLocationNumber2":"0","InternationalLocationNumber3":"0","IsNaturalPerson":"","PaymentReason":"","ResponsibleType":"","SuplrQltyInProcmtCertfnValidTo":null,"SuplrQualityManagementSystem":"","SupplierCorporateGroup":"","SupplierProcurementBlock":"","TaxNumber1":"***********","TaxNumber2":"","TaxNumber3":"","TaxNumber4":"AFDER45","TaxNumber5":"","TaxNumberResponsible":"","TaxNumberType":"","SuplrProofOfDelivRlvtCode":"","BR_TaxIsSplit":false,"DataExchangeInstructionKey":"","to_SupplierCompany":{"results":[{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_SupplierCompany(Supplier=''K122'',CompanyCode=''2310'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_SupplierCompany(Supplier=''K122'',CompanyCode=''2310'')","type":"API_BUSINESS_PARTNER.A_SupplierCompanyType"},"Supplier":"K122","CompanyCode":"2310","AuthorizationGroup":"","CompanyCodeName":"Kolme
        Srl","PaymentBlockingReason":"","SupplierIsBlockedForPosting":false,"AccountingClerk":"","AccountingClerkFaxNumber":"","AccountingClerkPhoneNumber":"","SupplierClerk":"","SupplierClerkURL":"","PaymentMethodsList":"T","PaymentReason":"","PaymentTerms":"FM00","ClearCustomerSupplier":false,"IsToBeLocallyProcessed":false,"ItemIsToBePaidSeparately":false,"PaymentIsToBeSentByEDI":false,"HouseBank":"CRA01","CheckPaidDurationInDays":"0","Currency":"EUR","BillOfExchLmtAmtInCoCodeCrcy":"0.00","SupplierClerkIDBySupplier":"","ReconciliationAccount":"********","InterestCalculationCode":"","InterestCalculationDate":null,"IntrstCalcFrequencyInMonths":"0","SupplierHeadOffice":"","AlternativePayee":"","LayoutSortingRule":"009","APARToleranceGroup":"","SupplierCertificationDate":null,"SupplierAccountNote":"","WithholdingTaxCountry":"","DeletionIndicator":false,"CashPlanningGroup":"A1","IsToBeCheckedForDuplicates":true,"MinorityGroup":"","SupplierAccountGroup":"SUPL","to_CompanyText":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_SupplierCompany(Supplier=''K122'',CompanyCode=''2310'')/to_CompanyText"}},"to_Supplier":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_SupplierCompany(Supplier=''K122'',CompanyCode=''2310'')/to_Supplier"}},"to_SupplierDunning":{"results":[]},"to_SupplierWithHoldingTax":{"results":[{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_SupplierWithHoldingTax(Supplier=''K122'',CompanyCode=''2310'',WithholdingTaxType=''1D'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_SupplierWithHoldingTax(Supplier=''K122'',CompanyCode=''2310'',WithholdingTaxType=''1D'')","type":"API_BUSINESS_PARTNER.A_SupplierWithHoldingTaxType"},"Supplier":"K122","CompanyCode":"2310","WithholdingTaxType":"1D","ExemptionDateBegin":null,"ExemptionDateEnd":null,"ExemptionReason":"","IsWithholdingTaxSubject":true,"RecipientType":"","WithholdingTaxCertificate":"","WithholdingTaxCode":"1Y","WithholdingTaxExmptPercent":"0.00","WithholdingTaxNumber":"","AuthorizationGroup":""}]}}]},"to_SupplierPurchasingOrg":{"results":[{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_SupplierPurchasingOrg(Supplier=''K122'',PurchasingOrganization=''2310'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_SupplierPurchasingOrg(Supplier=''K122'',PurchasingOrganization=''2310'')","type":"API_BUSINESS_PARTNER.A_SupplierPurchasingOrgType"},"Supplier":"K122","PurchasingOrganization":"2310","AutomaticEvaluatedRcptSettlmt":false,"CalculationSchemaGroupCode":"","DeletionIndicator":false,"EvaldReceiptSettlementIsActive":false,"IncotermsClassification":"","IncotermsTransferLocation":"","IncotermsVersion":"","IncotermsLocation1":"","IncotermsLocation2":"","IncotermsSupChnLoc1AddlUUID":"********-0000-0000-0000-************","IncotermsSupChnLoc2AddlUUID":"********-0000-0000-0000-************","IncotermsSupChnDvtgLocAddlUUID":"********-0000-0000-0000-************","IntrastatCrsBorderTrMode":"","InvoiceIsGoodsReceiptBased":true,"InvoiceIsMMServiceEntryBased":false,"MaterialPlannedDeliveryDurn":"0","MinimumOrderAmount":"0.00","PaymentTerms":"FM00","PlanningCycle":"","PricingDateControl":"","ProdStockAndSlsDataTransfPrfl":"","ProductUnitGroup":"","PurOrdAutoGenerationIsAllowed":false,"PurchaseOrderCurrency":"EUR","PurchasingGroup":"","PurchasingIsBlockedForSupplier":false,"RoundingProfile":"","ShippingCondition":"","SuplrDiscountInKindIsGranted":false,"SuplrInvcRevalIsAllowed":false,"SuplrIsRlvtForSettlmtMgmt":false,"SuplrPurgOrgIsRlvtForPriceDetn":false,"SupplierABCClassificationCode":"","SupplierAccountNumber":"","SupplierIsReturnsSupplier":false,"SupplierPhoneNumber":"","SupplierRespSalesPersonName":"","SupplierConfirmationControlKey":"","IsOrderAcknRqd":false,"AuthorizationGroup":"","SupplierAccountGroup":"SUPL","to_PartnerFunction":{"results":[{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_SupplierPartnerFunc(Supplier=''K122'',PurchasingOrganization=''2310'',SupplierSubrange='''',Plant='''',PartnerFunction=''OA'',PartnerCounter=''0'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_SupplierPartnerFunc(Supplier=''K122'',PurchasingOrganization=''2310'',SupplierSubrange='''',Plant='''',PartnerFunction=''OA'',PartnerCounter=''0'')","type":"API_BUSINESS_PARTNER.A_SupplierPartnerFuncType"},"Supplier":"K122","PurchasingOrganization":"2310","SupplierSubrange":"","Plant":"","PartnerFunction":"OA","PartnerCounter":"0","DefaultPartner":false,"CreationDate":"\/Date(*************)\/","CreatedByUser":"CC********03","ReferenceSupplier":"K122","AuthorizationGroup":""},{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_SupplierPartnerFunc(Supplier=''K122'',PurchasingOrganization=''2310'',SupplierSubrange='''',Plant='''',PartnerFunction=''VN'',PartnerCounter=''0'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_SupplierPartnerFunc(Supplier=''K122'',PurchasingOrganization=''2310'',SupplierSubrange='''',Plant='''',PartnerFunction=''VN'',PartnerCounter=''0'')","type":"API_BUSINESS_PARTNER.A_SupplierPartnerFuncType"},"Supplier":"K122","PurchasingOrganization":"2310","SupplierSubrange":"","Plant":"","PartnerFunction":"VN","PartnerCounter":"0","DefaultPartner":false,"CreationDate":"\/Date(*************)\/","CreatedByUser":"CC********03","ReferenceSupplier":"K122","AuthorizationGroup":""},{"__metadata":{"id":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_SupplierPartnerFunc(Supplier=''K122'',PurchasingOrganization=''2310'',SupplierSubrange='''',Plant='''',PartnerFunction=''PI'',PartnerCounter=''0'')","uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_SupplierPartnerFunc(Supplier=''K122'',PurchasingOrganization=''2310'',SupplierSubrange='''',Plant='''',PartnerFunction=''PI'',PartnerCounter=''0'')","type":"API_BUSINESS_PARTNER.A_SupplierPartnerFuncType"},"Supplier":"K122","PurchasingOrganization":"2310","SupplierSubrange":"","Plant":"","PartnerFunction":"PI","PartnerCounter":"0","DefaultPartner":false,"CreationDate":"\/Date(*************)\/","CreatedByUser":"CC********03","ReferenceSupplier":"K122","AuthorizationGroup":""}]},"to_PurchasingOrgText":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_SupplierPurchasingOrg(Supplier=''K122'',PurchasingOrganization=''2310'')/to_PurchasingOrgText"}}}]},"to_SupplierText":{"__deferred":{"uri":"https://my404601-api.s4hana.cloud.sap/sap/opu/odata/sap/API_BUSINESS_PARTNER/A_Supplier(''K122'')/to_SupplierText"}}}}}'
  recorded_at: Wed, 27 Nov 2024 15:38:36 GMT
recorded_with: VCR 6.2.0
