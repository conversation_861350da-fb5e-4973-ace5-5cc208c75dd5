---
http_interactions:
- request:
    method: get
    uri: https://maps.googleapis.com/maps/api/geocode/json?address=Viale%20Monza%2014%20%20%20Italia&key=AIzaSyCa8wUQ0yENUeS4LeFNb84_4rxU1HVN1uE&language=it&sensor=false
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Wed, 09 Dec 2020 19:28:11 GMT
      Pragma:
      - no-cache
      Expires:
      - Fri, 01 Jan 1990 00:00:00 GMT
      Cache-Control:
      - no-cache, must-revalidate
      Access-Control-Allow-Origin:
      - "*"
      X-Goog-Maps-Metro-Area:
      - Milan
      Server:
      - mafe
      X-Xss-Protection:
      - '0'
      X-Frame-Options:
      - SAMEORIGIN
      Server-Timing:
      - gfet4t7; dur=12
      Alt-Svc:
      - h3-29=":443"; ma=2592000,h3-T051=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443";
        ma=2592000,h3-Q043=":443"; ma=2592000,quic=":443"; ma=2592000; v="46,43"
      Transfer-Encoding:
      - chunked
    body:
      encoding: ASCII-8BIT
      string: !binary |-
        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
    http_version: 
  recorded_at: Wed, 09 Dec 2020 19:28:11 GMT
- request:
    method: get
    uri: https://maps.googleapis.com/maps/api/geocode/json?address=Borgarello(Pv)%20%20&key=AIzaSyCa8wUQ0yENUeS4LeFNb84_4rxU1HVN1uE&language=it&sensor=false
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Wed, 09 Dec 2020 19:28:12 GMT
      Pragma:
      - no-cache
      Expires:
      - Fri, 01 Jan 1990 00:00:00 GMT
      Cache-Control:
      - no-cache, must-revalidate
      Access-Control-Allow-Origin:
      - "*"
      Server:
      - mafe
      X-Xss-Protection:
      - '0'
      X-Frame-Options:
      - SAMEORIGIN
      Server-Timing:
      - gfet4t7; dur=34
      Alt-Svc:
      - h3-29=":443"; ma=2592000,h3-T051=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443";
        ma=2592000,h3-Q043=":443"; ma=2592000,quic=":443"; ma=2592000; v="46,43"
      Transfer-Encoding:
      - chunked
    body:
      encoding: ASCII-8BIT
      string: |
        {
           "results" : [
              {
                 "address_components" : [
                    {
                       "long_name" : "Borgarello",
                       "short_name" : "Borgarello",
                       "types" : [ "administrative_area_level_3", "political" ]
                    },
                    {
                       "long_name" : "Provincia di Pavia",
                       "short_name" : "PV",
                       "types" : [ "administrative_area_level_2", "political" ]
                    },
                    {
                       "long_name" : "Lombardia",
                       "short_name" : "Lombardia",
                       "types" : [ "administrative_area_level_1", "political" ]
                    },
                    {
                       "long_name" : "Italia",
                       "short_name" : "IT",
                       "types" : [ "country", "political" ]
                    },
                    {
                       "long_name" : "27010",
                       "short_name" : "27010",
                       "types" : [ "postal_code" ]
                    }
                 ],
                 "formatted_address" : "27010 Borgarello PV, Italia",
                 "geometry" : {
                    "bounds" : {
                       "northeast" : {
                          "lat" : 45.2525458,
                          "lng" : 9.169338399999999
                       },
                       "southwest" : {
                          "lat" : 45.2288495,
                          "lng" : 9.132060599999999
                       }
                    },
                    "location" : {
                       "lat" : 45.2410196,
                       "lng" : 9.149351899999999
                    },
                    "location_type" : "APPROXIMATE",
                    "viewport" : {
                       "northeast" : {
                          "lat" : 45.2525458,
                          "lng" : 9.169338399999999
                       },
                       "southwest" : {
                          "lat" : 45.2288495,
                          "lng" : 9.132060599999999
                       }
                    }
                 },
                 "place_id" : "ChIJt0j2M4TYhkcRcBlnLgJ4BgQ",
                 "types" : [ "administrative_area_level_3", "political" ]
              }
           ],
           "status" : "OK"
        }
    http_version: 
  recorded_at: Wed, 09 Dec 2020 19:28:12 GMT
recorded_with: VCR 5.0.0
