---
http_interactions:
- request:
    method: get
    uri: https://kolme-centralino-test.s3.amazonaws.com/
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - fog/2.2.0 fog-core/2.1.0
      X-Amz-Date:
      - 20191015T110053Z
      X-Amz-Content-Sha256:
      - e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
      Authorization:
      - AWS4-HMAC-SHA256 Credential=AKIAIQMZNKCZHKSHR5ZQ/20191015/us-east-1/s3/aws4_request,
        SignedHeaders=host;x-amz-content-sha256;x-amz-date, Signature=8b17c1e26f13bcf736adb3c3884ef807601fcc47bdc07f6c13698949467f20fc
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Amz-Id-2:
      - LQVmx9CkKzzS/PetzOxv/mEgXuN3DR3/SD51TSZk1g+t3RZQSaIXX0mRCTCyKvaXkoMGeK9EFj4=
      X-Amz-Request-Id:
      - B6ED74584E235A6C
      Date:
      - Tue, 15 Oct 2019 11:00:55 GMT
      X-Amz-Bucket-Region:
      - us-east-1
      Content-Type:
      - application/xml
      Server:
      - AmazonS3
    body:
      encoding: ASCII-8BIT
      string: |-
        <?xml version="1.0" encoding="UTF-8"?>
        <ListBucketResult xmlns="http://s3.amazonaws.com/doc/2006-03-01/"><Name>kolme-centralino-test</Name><Prefix></Prefix><Marker></Marker><MaxKeys>1000</MaxKeys><IsTruncated>false</IsTruncated><Contents><Key>phone-call-1.mp3</Key><LastModified>2016-08-29T14:52:51.000Z</LastModified><ETag>&quot;ffd401fca819adb4eafc5e0975ffb4ef&quot;</ETag><Size>821469</Size><Owner><ID>64e9af4bc9f9775efd294404bbfdcb5d4e2a67893cd0d13e084199967bfd93f0</ID><DisplayName>fornitori</DisplayName></Owner><StorageClass>STANDARD</StorageClass></Contents><Contents><Key>phone-calls.csv</Key><LastModified>2016-09-01T12:58:52.000Z</LastModified><ETag>&quot;a1a86e3d45669738b3a6cf99c789a5a2&quot;</ETag><Size>387</Size><Owner><ID>64e9af4bc9f9775efd294404bbfdcb5d4e2a67893cd0d13e084199967bfd93f0</ID><DisplayName>fornitori</DisplayName></Owner><StorageClass>STANDARD</StorageClass></Contents><Contents><Key>resume.html</Key><LastModified>2016-08-31T12:59:37.000Z</LastModified><ETag>&quot;f867a4fdb09b251cca05d0a8937af271&quot;</ETag><Size>12</Size><Owner><ID>64e9af4bc9f9775efd294404bbfdcb5d4e2a67893cd0d13e084199967bfd93f0</ID><DisplayName>fornitori</DisplayName></Owner><StorageClass>STANDARD</StorageClass></Contents></ListBucketResult>
    http_version:
  recorded_at: Tue, 15 Oct 2019 11:00:54 GMT
- request:
    method: head
    uri: https://kolme-centralino-test.s3.amazonaws.com/phone_call_file.wav
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - fog/2.2.0 fog-core/2.1.0
      X-Amz-Date:
      - 20191015T110054Z
      X-Amz-Content-Sha256:
      - e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
      Authorization:
      - AWS4-HMAC-SHA256 Credential=AKIAIQMZNKCZHKSHR5ZQ/20191015/us-east-1/s3/aws4_request,
        SignedHeaders=host;x-amz-content-sha256;x-amz-date, Signature=afecaf917cadf562550e266d4a393367fab6c09c9d61f16b9c860cd305fd748a
  response:
    status:
      code: 404
      message: Not Found
    headers:
      X-Amz-Request-Id:
      - F2AE31FB209763C2
      X-Amz-Id-2:
      - XmWy6gOZ20/JqG4U4iP4pbB9FbtSKf4yfIq6F7PDUnEAKTxuGipaAod1axhYE7e3tGYiDacg92I=
      Content-Type:
      - application/xml
      Transfer-Encoding:
      - chunked
      Date:
      - Tue, 15 Oct 2019 11:00:54 GMT
      Server:
      - AmazonS3
    body:
      encoding: ASCII-8BIT
      string: ''
    http_version:
  recorded_at: Tue, 15 Oct 2019 11:00:55 GMT
- request:
    method: head
    uri: https://kolme-centralino-test.s3.amazonaws.com/phone_call_file.wav
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - fog/2.2.0 fog-core/2.1.0
      X-Amz-Date:
      - 20191015T110054Z
      X-Amz-Content-Sha256:
      - e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
      Authorization:
      - AWS4-HMAC-SHA256 Credential=AKIAIQMZNKCZHKSHR5ZQ/20191015/us-east-1/s3/aws4_request,
        SignedHeaders=host;x-amz-content-sha256;x-amz-date, Signature=afecaf917cadf562550e266d4a393367fab6c09c9d61f16b9c860cd305fd748a
  response:
    status:
      code: 404
      message: Not Found
    headers:
      X-Amz-Request-Id:
      - 18651297C357638E
      X-Amz-Id-2:
      - 8hkRF2wSq4g2Qotqp073LG69+dUyG9LcaSMmeJSbl+QfWAbgqmhXGP2bc4k0GDyRqYWc0elvCpY=
      Content-Type:
      - application/xml
      Transfer-Encoding:
      - chunked
      Date:
      - Tue, 15 Oct 2019 11:00:56 GMT
      Server:
      - AmazonS3
    body:
      encoding: ASCII-8BIT
      string: ''
    http_version:
  recorded_at: Tue, 15 Oct 2019 11:00:56 GMT
- request:
    method: head
    uri: https://kolme-centralino-test.s3.amazonaws.com/phone_call_file.wav
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - fog/2.2.0 fog-core/2.1.0
      X-Amz-Date:
      - 20191015T110054Z
      X-Amz-Content-Sha256:
      - e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
      Authorization:
      - AWS4-HMAC-SHA256 Credential=AKIAIQMZNKCZHKSHR5ZQ/20191015/us-east-1/s3/aws4_request,
        SignedHeaders=host;x-amz-content-sha256;x-amz-date, Signature=afecaf917cadf562550e266d4a393367fab6c09c9d61f16b9c860cd305fd748a
  response:
    status:
      code: 404
      message: Not Found
    headers:
      X-Amz-Request-Id:
      - 171C87F5BAFF838C
      X-Amz-Id-2:
      - At6Ii5DWFzAy5QK9wtPiM2uj3kNtnnwP2aTRBVpFm5K0mMfEu62Lw5frCxHh0jX8OILWzmvZZrM=
      Content-Type:
      - application/xml
      Transfer-Encoding:
      - chunked
      Date:
      - Tue, 15 Oct 2019 11:00:56 GMT
      Server:
      - AmazonS3
    body:
      encoding: ASCII-8BIT
      string: ''
    http_version:
  recorded_at: Tue, 15 Oct 2019 11:00:57 GMT
- request:
    method: head
    uri: https://kolme-centralino-test.s3.amazonaws.com/phone_call_file.wav
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - fog/2.2.0 fog-core/2.1.0
      X-Amz-Date:
      - 20191015T110054Z
      X-Amz-Content-Sha256:
      - e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
      Authorization:
      - AWS4-HMAC-SHA256 Credential=AKIAIQMZNKCZHKSHR5ZQ/20191015/us-east-1/s3/aws4_request,
        SignedHeaders=host;x-amz-content-sha256;x-amz-date, Signature=afecaf917cadf562550e266d4a393367fab6c09c9d61f16b9c860cd305fd748a
  response:
    status:
      code: 404
      message: Not Found
    headers:
      X-Amz-Request-Id:
      - 4A551B6C9CAE28AA
      X-Amz-Id-2:
      - aCpSWmQQ3IozP9uTaE03VZ2IdyCJUOt45HCFmAT07MKbXWFWQ6mPjqAmbXyPkey2N5sL6xoapBw=
      Content-Type:
      - application/xml
      Transfer-Encoding:
      - chunked
      Date:
      - Tue, 15 Oct 2019 11:00:58 GMT
      Server:
      - AmazonS3
    body:
      encoding: ASCII-8BIT
      string: ''
    http_version:
  recorded_at: Tue, 15 Oct 2019 11:00:58 GMT
recorded_with: VCR 5.0.0
