---
http_interactions:
- request:
    method: get
    uri: https://windtre-test.signanywhere.com/Api/v4.0/envelope/d0affe6c-be94-462f-9615-c02e1c1f4a82
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept:
      - "*/*"
      Accept-Encoding:
      - gzip, deflate
      User-Agent:
      - rest-client/2.0.2 (darwin22 x86_64) ruby/3.0.5p211
      Userloginname:
      - <EMAIL>
      Organizationkey:
      - 7eedaa43-37ab-4c73-a7e4-09042fe45063
  response:
    status:
      code: 404
      message: Not Found
    headers:
      Date:
      - Mon, 25 Sep 2023 11:40:49 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '134'
      Connection:
      - keep-alive
      Set-Cookie:
      - AWSALB=u6EUxZQe9aR8bzsWRTXw33DXIGWk3Fju7P0FJ4mBjVsweqtSGaWSyXYuPg+sMxdO0xmIzdbl1h4DeZGZPO4MExfX6tw4oxdSlfCoqkgfYek5aTsiqMj+K7TF087a;
        Expires=Mon, 02 Oct 2023 11:40:49 GMT; Path=/
      - AWSALBCORS=u6EUxZQe9aR8bzsWRTXw33DXIGWk3Fju7P0FJ4mBjVsweqtSGaWSyXYuPg+sMxdO0xmIzdbl1h4DeZGZPO4MExfX6tw4oxdSlfCoqkgfYek5aTsiqMj+K7TF087a;
        Expires=Mon, 02 Oct 2023 11:40:49 GMT; Path=/; SameSite=None; Secure
      Cache-Control:
      - no-cache,no-cache, no-store, must-revalidate
      Pragma:
      - no-cache
      Expires:
      - "-1"
      Server:
      - ''
      Api-Supported-Versions:
      - 1, 2, 3, 4, 5
      X-Powered-By:
      - ''
      Strict-Transport-Security:
      - max-age=31536000
      X-Content-Type-Options:
      - nosniff
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Permissions-Policy:
      - geolocation=*
    body:
      encoding: UTF-8
      string: '{"ErrorId":"ERR0007","Message":"ERR0007 - Envelope missing - [EnvelopeId:
        d0affe6c-be94-462f-9615-c02e1c1f4a82]","SupportId":"505589"}'
  recorded_at: Mon, 25 Sep 2023 11:40:49 GMT
- request:
    method: get
    uri: https://windtre-test.signanywhere.com/Api/v4/envelope/downloadCompletedDocument/
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept:
      - "*/*"
      Accept-Encoding:
      - gzip, deflate
      User-Agent:
      - rest-client/2.0.2 (darwin22 x86_64) ruby/3.0.5p211
      Userloginname:
      - <EMAIL>
      Organizationkey:
      - 7eedaa43-37ab-4c73-a7e4-09042fe45063
  response:
    status:
      code: 400
      message: Bad Request
    headers:
      Date:
      - Mon, 25 Sep 2023 11:40:50 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '182'
      Connection:
      - keep-alive
      Set-Cookie:
      - AWSALB=VN2JGnScJxy3xJKbcNiYre33JQ/Y3hr4/L7W4+NsM89LBnBzK5TPp4maLftGorwCmu9T+MHavWdoHHfn2IeaQX5zDLununQT3tzAOcFV0T/mHi30ZH2S7SLnrNLd;
        Expires=Mon, 02 Oct 2023 11:40:50 GMT; Path=/
      - AWSALBCORS=VN2JGnScJxy3xJKbcNiYre33JQ/Y3hr4/L7W4+NsM89LBnBzK5TPp4maLftGorwCmu9T+MHavWdoHHfn2IeaQX5zDLununQT3tzAOcFV0T/mHi30ZH2S7SLnrNLd;
        Expires=Mon, 02 Oct 2023 11:40:50 GMT; Path=/; SameSite=None; Secure
      Cache-Control:
      - no-cache,no-cache, no-store, must-revalidate
      Pragma:
      - no-cache
      Expires:
      - "-1"
      Server:
      - ''
      Api-Supported-Versions:
      - 1, 2, 3, 4, 5
      X-Powered-By:
      - ''
      Strict-Transport-Security:
      - max-age=31536000
      X-Content-Type-Options:
      - nosniff
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Permissions-Policy:
      - geolocation=*
    body:
      encoding: UTF-8
      string: '{"ErrorId":"ERR0011","Message":"ERR0011 - Parameter is invalid - [ApiInvalidParameterLocation:
        envelopeId] [ApiInvalidParameterReason: Unable to parse as GUID]","SupportId":"943592"}'
  recorded_at: Mon, 25 Sep 2023 11:40:50 GMT
- request:
    method: get
    uri: https://windtre-test.signanywhere.com/Api/v4/envelope/downloadCompletedDocument/
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept:
      - "*/*"
      Accept-Encoding:
      - gzip, deflate
      User-Agent:
      - rest-client/2.0.2 (darwin22 x86_64) ruby/3.0.5p211
      Userloginname:
      - <EMAIL>
      Organizationkey:
      - 7eedaa43-37ab-4c73-a7e4-09042fe45063
  response:
    status:
      code: 400
      message: Bad Request
    headers:
      Date:
      - Mon, 25 Sep 2023 11:40:51 GMT
      Content-Type:
      - application/json; charset=utf-8
      Content-Length:
      - '182'
      Connection:
      - keep-alive
      Set-Cookie:
      - AWSALB=8SZ3fJMnE9q++FsYDQ1mHtEze8CpuhiA3tJTwBxLwDIlJMc5WatKx/JkKEqFLbQInPTh7WjwoUtsqFeUFfskBAhnV6jnVgX7RQUgbBIRxrS3N+4HmmLvmCBcMoSk;
        Expires=Mon, 02 Oct 2023 11:40:51 GMT; Path=/
      - AWSALBCORS=8SZ3fJMnE9q++FsYDQ1mHtEze8CpuhiA3tJTwBxLwDIlJMc5WatKx/JkKEqFLbQInPTh7WjwoUtsqFeUFfskBAhnV6jnVgX7RQUgbBIRxrS3N+4HmmLvmCBcMoSk;
        Expires=Mon, 02 Oct 2023 11:40:51 GMT; Path=/; SameSite=None; Secure
      Cache-Control:
      - no-cache,no-cache, no-store, must-revalidate
      Pragma:
      - no-cache
      Expires:
      - "-1"
      Server:
      - ''
      Api-Supported-Versions:
      - 1, 2, 3, 4, 5
      X-Powered-By:
      - ''
      Strict-Transport-Security:
      - max-age=31536000
      X-Content-Type-Options:
      - nosniff
      Referrer-Policy:
      - strict-origin-when-cross-origin
      Permissions-Policy:
      - geolocation=*
    body:
      encoding: UTF-8
      string: '{"ErrorId":"ERR0011","Message":"ERR0011 - Parameter is invalid - [ApiInvalidParameterLocation:
        envelopeId] [ApiInvalidParameterReason: Unable to parse as GUID]","SupportId":"256439"}'
  recorded_at: Mon, 25 Sep 2023 11:40:51 GMT
recorded_with: VCR 6.2.0
