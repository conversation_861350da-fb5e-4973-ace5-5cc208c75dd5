---
http_interactions:
- request:
    method: get
    uri: https://maps.googleapis.com/maps/api/geocode/json?address=Borgarello%20(Pv)%20%20&key=AIzaSyCa8wUQ0yENUeS4LeFNb84_4rxU1HVN1uE&language=it&sensor=false
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Mon, 09 Jan 2023 13:09:55 GMT
      Pragma:
      - no-cache
      Expires:
      - Fri, 01 Jan 1990 00:00:00 GMT
      Cache-Control:
      - no-cache, must-revalidate
      Access-Control-Allow-Origin:
      - "*"
      Server:
      - mafe
      X-Xss-Protection:
      - '0'
      X-Frame-Options:
      - SAMEORIGIN
      Server-Timing:
      - gfet4t7; dur=35
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443";
        ma=2592000,h3-Q043=":443"; ma=2592000,quic=":443"; ma=2592000; v="46,43"
      Transfer-Encoding:
      - chunked
    body:
      encoding: ASCII-8BIT
      string: |
        {
           "results" : [
              {
                 "address_components" : [
                    {
                       "long_name" : "Borgarello",
                       "short_name" : "Borgarello",
                       "types" : [ "administrative_area_level_3", "political" ]
                    },
                    {
                       "long_name" : "Provincia di Pavia",
                       "short_name" : "PV",
                       "types" : [ "administrative_area_level_2", "political" ]
                    },
                    {
                       "long_name" : "Lombardia",
                       "short_name" : "Lombardia",
                       "types" : [ "administrative_area_level_1", "political" ]
                    },
                    {
                       "long_name" : "Italia",
                       "short_name" : "IT",
                       "types" : [ "country", "political" ]
                    },
                    {
                       "long_name" : "27010",
                       "short_name" : "27010",
                       "types" : [ "postal_code" ]
                    }
                 ],
                 "formatted_address" : "27010 Borgarello PV, Italia",
                 "geometry" : {
                    "bounds" : {
                       "northeast" : {
                          "lat" : 45.2525458,
                          "lng" : 9.169338399999999
                       },
                       "southwest" : {
                          "lat" : 45.2288495,
                          "lng" : 9.132060599999999
                       }
                    },
                    "location" : {
                       "lat" : 45.2410196,
                       "lng" : 9.149351899999999
                    },
                    "location_type" : "APPROXIMATE",
                    "viewport" : {
                       "northeast" : {
                          "lat" : 45.2525458,
                          "lng" : 9.169338399999999
                       },
                       "southwest" : {
                          "lat" : 45.2288495,
                          "lng" : 9.132060599999999
                       }
                    }
                 },
                 "place_id" : "ChIJt0j2M4TYhkcRcBlnLgJ4BgQ",
                 "types" : [ "administrative_area_level_3", "political" ]
              }
           ],
           "status" : "OK"
        }
    http_version: 
  recorded_at: Mon, 09 Jan 2023 13:09:55 GMT
recorded_with: VCR 5.0.0
