---
http_interactions:
- request:
    method: get
    uri: https://maps.googleapis.com/maps/api/geocode/json?address=Borgarello%20(Pv)%20%20&key=AIzaSyCa8wUQ0yENUeS4LeFNb84_4rxU1HVN1uE&language=it&sensor=false
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Tu<PERSON>, 10 Jan 2023 11:42:57 GMT
      Pragma:
      - no-cache
      Expires:
      - Fri, 01 Jan 1990 00:00:00 GMT
      Cache-Control:
      - no-cache, must-revalidate
      Access-Control-Allow-Origin:
      - "*"
      Server:
      - mafe
      X-Xss-Protection:
      - '0'
      X-Frame-Options:
      - SAMEORIGIN
      Server-Timing:
      - gfet4t7; dur=56
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443";
        ma=2592000,h3-Q043=":443"; ma=2592000,quic=":443"; ma=2592000; v="46,43"
      Transfer-Encoding:
      - chunked
    body:
      encoding: ASCII-8BIT
      string: |
        {
           "results" : [
              {
                 "address_components" : [
                    {
                       "long_name" : "Borgarello",
                       "short_name" : "Borgarello",
                       "types" : [ "administrative_area_level_3", "political" ]
                    },
                    {
                       "long_name" : "Provincia di Pavia",
                       "short_name" : "PV",
                       "types" : [ "administrative_area_level_2", "political" ]
                    },
                    {
                       "long_name" : "Lombardia",
                       "short_name" : "Lombardia",
                       "types" : [ "administrative_area_level_1", "political" ]
                    },
                    {
                       "long_name" : "Italia",
                       "short_name" : "IT",
                       "types" : [ "country", "political" ]
                    },
                    {
                       "long_name" : "27010",
                       "short_name" : "27010",
                       "types" : [ "postal_code" ]
                    }
                 ],
                 "formatted_address" : "27010 Borgarello PV, Italia",
                 "geometry" : {
                    "bounds" : {
                       "northeast" : {
                          "lat" : 45.2525458,
                          "lng" : 9.169338399999999
                       },
                       "southwest" : {
                          "lat" : 45.2288495,
                          "lng" : 9.132060599999999
                       }
                    },
                    "location" : {
                       "lat" : 45.2410196,
                       "lng" : 9.149351899999999
                    },
                    "location_type" : "APPROXIMATE",
                    "viewport" : {
                       "northeast" : {
                          "lat" : 45.2525458,
                          "lng" : 9.169338399999999
                       },
                       "southwest" : {
                          "lat" : 45.2288495,
                          "lng" : 9.132060599999999
                       }
                    }
                 },
                 "place_id" : "ChIJt0j2M4TYhkcRcBlnLgJ4BgQ",
                 "types" : [ "administrative_area_level_3", "political" ]
              }
           ],
           "status" : "OK"
        }
    http_version: 
  recorded_at: Tue, 10 Jan 2023 11:42:57 GMT
- request:
    method: get
    uri: https://maps.googleapis.com/maps/api/geocode/json?address=Via%20Cadore%2075%20%20%20%20Italia&key=AIzaSyCa8wUQ0yENUeS4LeFNb84_4rxU1HVN1uE&language=it&sensor=false
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Tue, 10 Jan 2023 11:42:59 GMT
      Pragma:
      - no-cache
      Expires:
      - Fri, 01 Jan 1990 00:00:00 GMT
      Cache-Control:
      - no-cache, must-revalidate
      Access-Control-Allow-Origin:
      - "*"
      Server:
      - mafe
      X-Xss-Protection:
      - '0'
      X-Frame-Options:
      - SAMEORIGIN
      Server-Timing:
      - gfet4t7; dur=76
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000,h3-Q050=":443"; ma=2592000,h3-Q046=":443";
        ma=2592000,h3-Q043=":443"; ma=2592000,quic=":443"; ma=2592000; v="46,43"
      Transfer-Encoding:
      - chunked
    body:
      encoding: ASCII-8BIT
      string: |
        {
           "results" : [
              {
                 "address_components" : [
                    {
                       "long_name" : "75",
                       "short_name" : "75",
                       "types" : [ "street_number" ]
                    },
                    {
                       "long_name" : "Via Cadore",
                       "short_name" : "Via Cadore",
                       "types" : [ "route" ]
                    },
                    {
                       "long_name" : "Pra' De Roda",
                       "short_name" : "Pra' De Roda",
                       "types" : [ "locality", "political" ]
                    },
                    {
                       "long_name" : "Caerano di San Marco",
                       "short_name" : "Caerano di San Marco",
                       "types" : [ "administrative_area_level_3", "political" ]
                    },
                    {
                       "long_name" : "Provincia di Treviso",
                       "short_name" : "TV",
                       "types" : [ "administrative_area_level_2", "political" ]
                    },
                    {
                       "long_name" : "Veneto",
                       "short_name" : "Veneto",
                       "types" : [ "administrative_area_level_1", "political" ]
                    },
                    {
                       "long_name" : "Italia",
                       "short_name" : "IT",
                       "types" : [ "country", "political" ]
                    },
                    {
                       "long_name" : "31031",
                       "short_name" : "31031",
                       "types" : [ "postal_code" ]
                    }
                 ],
                 "formatted_address" : "Via Cadore, 75, 31031 Pra' De Roda TV, Italia",
                 "geometry" : {
                    "location" : {
                       "lat" : 45.8046646,
                       "lng" : 12.0038321
                    },
                    "location_type" : "ROOFTOP",
                    "viewport" : {
                       "northeast" : {
                          "lat" : 45.8059869302915,
                          "lng" : 12.0052327802915
                       },
                       "southwest" : {
                          "lat" : 45.8032889697085,
                          "lng" : 12.0025348197085
                       }
                    }
                 },
                 "place_id" : "ChIJh6rS76omeUcR9h19WrnN594",
                 "plus_code" : {
                    "compound_code" : "R233+VG Pra' De Roda TV, Italia",
                    "global_code" : "8FQJR233+VG"
                 },
                 "types" : [ "street_address" ]
              }
           ],
           "status" : "OK"
        }
    http_version: 
  recorded_at: Tue, 10 Jan 2023 11:42:59 GMT
recorded_with: VCR 5.0.0
