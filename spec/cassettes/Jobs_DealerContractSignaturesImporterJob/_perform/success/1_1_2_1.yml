---
http_interactions:
- request:
    method: post
    uri: https://qdst.itagile.it/dse/rest/Wind3/getEnvelopesUrl
    body:
      encoding: UTF-8
      string: AdminCredentials={"userid":"wind3.test","password":"T357ml40","customerKey":"WIND3-DEALER"}&envelopeFilter={"partner":"KOLME","fieldValues":["01010101012","01010101013"]}
    headers:
      Accept:
      - "*/*"
      Accept-Encoding:
      - gzip, deflate
      User-Agent:
      - rest-client/2.0.2 (darwin19.3.0 x86_64) ruby/2.5.3p105
      Content-Length:
      - '170'
  response:
    status:
      code: 200
      message: ''
    headers:
      Content-Type:
      - application/json;charset=ISO-8859-1
      Content-Length:
      - '587'
      Date:
      - Fri, 05 Mar 2021 08:00:23 GMT
      Connection:
      - close
      Strict-Transport-Security:
      - max-age=63072000; includeSubdomains; preload
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Referrer-Policy:
      - no-referrer-when-downgrade
      Content-Security-Policy:
      - 'default-src ''self'' data: blob: ''unsafe-inline'' ''unsafe-eval'' stats.g.doubleclick.net
        www.googletagmanager.com unpkg.com www.google.it stackpath.bootstrapcdn.com
        www.google.com www.google-analytics.com fonts.gstatic.com fonts.googleapis.com
        www.gstatic.com js.stripe.com; frame-ancestors https://demo.docusign.net https://eu.docusign.net
        https://www.docusign.net https://na2.docusign.net https://sa.trustpro.eu https://eni-demo.synertrade.com
        https://signaturefolder.itagile.it'
      Cache-Control:
      - no-cache, no-store, must-revalidate
      Pragma:
      - no-cache
    body:
      encoding: UTF-8
      string: |-
        {
          "envelopesURL": {
            "01010101013": [
              {
                "envelopeId": "82fa51d3-eca7-4059-950a-2d7049e14ea1",
                "subject": "Test contratto Wind3 Test tre Srl",
                "url": "https://qdsdev.itagile.it/dse/signEnevelope?envelopeId\u003d82fa51d3-eca7-4059-950a-2d7049e14ea1\u0026recipientId\u003d9373c527-e03d-40f1-9c46-499c3da2af02",
                "partitaIVA": "01010101013",
                "cod8mld": "8000000013",
                "email": "<EMAIL>",
                "name": "Andrea Verdi",
                "ragioneSociale": "Test Tre Srl"
              }
            ]
          },
          "success": true,
          "elapsed": 9
        }
    http_version: 
  recorded_at: Fri, 05 Mar 2021 08:00:22 GMT
recorded_with: VCR 5.0.0
