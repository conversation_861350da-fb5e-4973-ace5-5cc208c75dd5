---
http_interactions:
- request:
    method: post
    uri: https://secure.apisms.it/http/send_sms
    body:
      encoding: UTF-8
      string: destination=393738600944&body=MDAzMTAg6CBpbCBjb2RpY2UgZGEgY29tdW5pY2FyZSBwZXIgcHJvc2VndWly%0AZSBjb24gbCdpbnNlcmltZW50byBkZWwgY29udHJhdHRvIGVuZXJnaWEu&sender=S29sbWU%3D&id_api=3523&authlogin=kolmesrl%40aimon.it&authpasswd=udhe6TRW5rw%21z7P
    headers:
      Accept:
      - "*/*"
      Accept-Encoding:
      - gzip, deflate
      User-Agent:
      - rest-client/2.0.2 (darwin23 arm64) ruby/3.0.5p211
      Content-Length:
      - '238'
      Content-Type:
      - application/x-www-form-urlencoded
  response:
    status:
      code: 200
      message: OK
    headers:
      Server:
      - nginx/1.14.2
      Date:
      - Wed, 09 Oct 2024 17:52:24 GMT
      Content-Type:
      - text/html; charset=ISO-8859-1
      Transfer-Encoding:
      - chunked
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
    body:
      encoding: ASCII-8BIT
      string: !binary |-
        H4sIAAAAAAAAA9M2MFQI9g1WCCxNLU1NUdBV8HSxUjA0MjE0MLA0tDQ1MjIxsTAzNjAFAIQJHdIoAAAA
  recorded_at: Wed, 09 Oct 2024 17:52:24 GMT
recorded_with: VCR 6.2.0
