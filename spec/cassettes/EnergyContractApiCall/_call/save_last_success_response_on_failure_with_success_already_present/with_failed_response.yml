---
http_interactions:
- request:
    method: post
    uri: https://accounts.eu1.gigya.com/accounts.getJWT
    body:
      encoding: US-ASCII
      string: apiKey=3_SF-TKzU9b87TpkMNzw1Limm2VitYI3hcl_N1r0qjhmVVjB6LsJJzDkPERVXC1vVM&secret=5XtDjycFTzhZTmhPwMAKim2m09WUrfBl&userKey=ALqFLcHJxgSV&targetUID=24660300749c43e0b6a50955e6eeeeeb&expiration=3600
    headers:
      Content-Type:
      - application/x-www-form-urlencoded
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Length:
      - '796'
      Content-Type:
      - text/javascript; charset=utf-8
      Date:
      - Mon, 29 Jul 2024 12:08:04 GMT
      Cache-Control:
      - private
      Vary:
      - Accept-Encoding
      P3p:
      - CP="IDC COR PSA DEV ADM OUR IND ONL"
      X-Error-Code:
      - '0'
      X-Soa:
      - true, Gator
      X-Server:
      - eu1a-nomad-t19
      X-Callid:
      - 9b53145b1703143a30c7508d011fa26c
      X-Robots-Tag:
      - none
    body:
      encoding: ASCII-8BIT
      string: "{\r\n  \"callId\": \"9b53145b1703143a30c7508d011fa26c\",\r\n  \"errorCode\":
        0,\r\n  \"apiVersion\": 2,\r\n  \"statusCode\": 200,\r\n  \"statusReason\":
        \"OK\",\r\n  \"time\": \"2024-07-29T12:08:05.381Z\",\r\n  \"id_token\": \"***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\"\r\n}"
  recorded_at: Mon, 29 Jul 2024 12:08:05 GMT
- request:
    method: post
    uri: https://qa-selfservice.aceaspa.it/RB/funnel/checkTaxCode
    body:
      encoding: UTF-8
      string: '{"taxCode":"JNECRL76L17F205C","birthDate":"1976-07-17","firstName":"Carlo","lastName":"Jean","placeOfBirth":null,"gender":"M"}'
    headers:
      Authorization:
      - Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      Content-Type:
      - application/json; charset=utf8
      X-Subchannel:
      - KOLME
      X-Channeldetail:
      - '9233333333'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - no-cache, no-store, max-age=0, must-revalidate
      Content-Type:
      - application/json
      Date:
      - Mon, 29 Jul 2024 12:08:06 GMT
      Expires:
      - '0'
      Pragma:
      - no-cache
      Server:
      - envoy
      Strict-Transport-Security:
      - max-age=31536000 ; includeSubDomains
      Vary:
      - Origin,Access-Control-Request-Method,Access-Control-Request-Headers
      X-Content-Type-Options:
      - nosniff
      X-Envoy-Decorator-Operation:
      - ingress CheckTaxCodeUsingPOST
      X-Frame-Options:
      - DENY
      X-Xss-Protection:
      - 1; mode=block
      Via:
      - 1.1 google
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: '{"result":"OK","message":"Codice fiscale dell''utente verificato","body":{"birthDate":"1976-07-17","placeOfBirth":"Milano","provinceOfBirth":"MI","gender":"M","age":48}}'
  recorded_at: Mon, 29 Jul 2024 12:08:06 GMT
- request:
    method: post
    uri: https://accounts.eu1.gigya.com/accounts.getJWT
    body:
      encoding: US-ASCII
      string: apiKey=3_SF-TKzU9b87TpkMNzw1Limm2VitYI3hcl_N1r0qjhmVVjB6LsJJzDkPERVXC1vVM&secret=5XtDjycFTzhZTmhPwMAKim2m09WUrfBl&userKey=ALqFLcHJxgSV&targetUID=24660300749c43e0b6a50955e6eeeeeb&expiration=3600
    headers:
      Content-Type:
      - application/x-www-form-urlencoded
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Length:
      - '798'
      Content-Type:
      - text/javascript; charset=utf-8
      Date:
      - Mon, 29 Jul 2024 12:08:06 GMT
      Cache-Control:
      - private
      Vary:
      - Accept-Encoding
      P3p:
      - CP="IDC COR PSA DEV ADM OUR IND ONL"
      X-Error-Code:
      - '0'
      X-Soa:
      - true, Gator
      X-Server:
      - eu1b-nomad-t21
      X-Callid:
      - dbbb0bca8aebdfc6dcb3001c990ccbcc
      X-Robots-Tag:
      - none
    body:
      encoding: ASCII-8BIT
      string: "{\r\n  \"callId\": \"dbbb0bca8aebdfc6dcb3001c990ccbcc\",\r\n  \"errorCode\":
        0,\r\n  \"apiVersion\": 2,\r\n  \"statusCode\": 200,\r\n  \"statusReason\":
        \"OK\",\r\n  \"time\": \"2024-07-29T12:08:06.476Z\",\r\n  \"id_token\": \"***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\"\r\n}"
  recorded_at: Mon, 29 Jul 2024 12:08:06 GMT
- request:
    method: post
    uri: https://qa-selfservice.aceaspa.it/RB/funnel/checkTaxCode
    body:
      encoding: UTF-8
      string: '{"taxCode":"****************","birthDate":"1976-07-17","firstName":"Carlo","lastName":"Jean","placeOfBirth":"Milano","gender":"M"}'
    headers:
      Authorization:
      - Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      Content-Type:
      - application/json; charset=utf8
      X-Subchannel:
      - KOLME
      X-Channeldetail:
      - '9233333333'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 400
      message: Bad Request
    headers:
      Cache-Control:
      - no-cache, no-store, max-age=0, must-revalidate
      Content-Type:
      - application/json
      Date:
      - Mon, 29 Jul 2024 12:08:06 GMT
      Expires:
      - '0'
      Pragma:
      - no-cache
      Server:
      - envoy
      Strict-Transport-Security:
      - max-age=31536000 ; includeSubDomains
      Vary:
      - Origin,Access-Control-Request-Method,Access-Control-Request-Headers
      X-Content-Type-Options:
      - nosniff
      X-Envoy-Decorator-Operation:
      - ingress CheckTaxCodeUsingPOST
      X-Frame-Options:
      - DENY
      X-Xss-Protection:
      - 1; mode=block
      Via:
      - 1.1 google
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: '{"result":"KO","message":"VALIDATION_ERROR","body":{"errorCode":"INVALID_CF","errorClass":"PORTAL","errorMessage":"CF[****************]:
        presenta un carattere errato nel codice ISTAT.","nonBlocking":false,"errorFields":["taxCode"]}}'
  recorded_at: Mon, 29 Jul 2024 12:08:06 GMT
- request:
    method: post
    uri: https://accounts.eu1.gigya.com/accounts.getJWT
    body:
      encoding: US-ASCII
      string: apiKey=3_SF-TKzU9b87TpkMNzw1Limm2VitYI3hcl_N1r0qjhmVVjB6LsJJzDkPERVXC1vVM&secret=5XtDjycFTzhZTmhPwMAKim2m09WUrfBl&userKey=ALqFLcHJxgSV&targetUID=24660300749c43e0b6a50955e6eeeeeb&expiration=3600
    headers:
      Content-Type:
      - application/x-www-form-urlencoded
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Length:
      - '798'
      Content-Type:
      - text/javascript; charset=utf-8
      Date:
      - Mon, 29 Jul 2024 12:08:06 GMT
      Cache-Control:
      - private
      Vary:
      - Accept-Encoding
      P3p:
      - CP="IDC COR PSA DEV ADM OUR IND ONL"
      X-Error-Code:
      - '0'
      X-Soa:
      - true, Gator
      X-Server:
      - eu1b-nomad-t22
      X-Callid:
      - 925f456cda5cc96815fd1526cb04e78c
      X-Robots-Tag:
      - none
    body:
      encoding: ASCII-8BIT
      string: "{\r\n  \"callId\": \"925f456cda5cc96815fd1526cb04e78c\",\r\n  \"errorCode\":
        0,\r\n  \"apiVersion\": 2,\r\n  \"statusCode\": 200,\r\n  \"statusReason\":
        \"OK\",\r\n  \"time\": \"2024-07-29T12:08:06.728Z\",\r\n  \"id_token\": \"***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\"\r\n}"
  recorded_at: Mon, 29 Jul 2024 12:08:06 GMT
- request:
    method: post
    uri: https://qa-selfservice.aceaspa.it/RB/funnel/checkTaxCode
    body:
      encoding: UTF-8
      string: '{"taxCode":"****************","birthDate":"1976-07-17","firstName":"Carlo","lastName":"Jean","placeOfBirth":null,"gender":"M"}'
    headers:
      Authorization:
      - Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      Content-Type:
      - application/json; charset=utf8
      X-Subchannel:
      - KOLME
      X-Channeldetail:
      - '9233333333'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 400
      message: Bad Request
    headers:
      Cache-Control:
      - no-cache, no-store, max-age=0, must-revalidate
      Content-Type:
      - application/json
      Date:
      - Mon, 29 Jul 2024 12:08:06 GMT
      Expires:
      - '0'
      Pragma:
      - no-cache
      Server:
      - envoy
      Strict-Transport-Security:
      - max-age=31536000 ; includeSubDomains
      Vary:
      - Origin,Access-Control-Request-Method,Access-Control-Request-Headers
      X-Content-Type-Options:
      - nosniff
      X-Envoy-Decorator-Operation:
      - ingress CheckTaxCodeUsingPOST
      X-Frame-Options:
      - DENY
      X-Xss-Protection:
      - 1; mode=block
      Via:
      - 1.1 google
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: '{"result":"KO","message":"VALIDATION_ERROR","body":{"errorCode":"INVALID_CF","errorClass":"PORTAL","errorMessage":"CF[****************]:
        presenta un carattere errato nel codice ISTAT.","nonBlocking":false,"errorFields":["taxCode"]}}'
  recorded_at: Mon, 29 Jul 2024 12:08:06 GMT
recorded_with: VCR 6.2.0
