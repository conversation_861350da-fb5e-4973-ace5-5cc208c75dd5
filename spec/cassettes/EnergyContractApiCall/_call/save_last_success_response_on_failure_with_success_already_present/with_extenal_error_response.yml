---
http_interactions:
- request:
    method: post
    uri: https://accounts.eu1.gigya.com/accounts.getJWT
    body:
      encoding: US-ASCII
      string: apiKey=3_SF-TKzU9b87TpkMNzw1Limm2VitYI3hcl_N1r0qjhmVVjB6LsJJzDkPERVXC1vVM&secret=5XtDjycFTzhZTmhPwMAKim2m09WUrfBl&userKey=ALqFLcHJxgSV&targetUID=24660300749c43e0b6a50955e6eeeeeb&expiration=3600
    headers:
      Content-Type:
      - application/x-www-form-urlencoded
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Length:
      - '799'
      Content-Type:
      - text/javascript; charset=utf-8
      Date:
      - Mon, 29 Jul 2024 12:08:06 GMT
      Cache-Control:
      - private
      Vary:
      - Accept-Encoding
      P3p:
      - CP="IDC COR PSA DEV ADM OUR IND ONL"
      X-Error-Code:
      - '0'
      X-Soa:
      - true, Gator
      X-Server:
      - eu1b-nomad-t28
      X-Callid:
      - 83dd833addd5abfbceb5a54068f782ee
      X-Robots-Tag:
      - none
    body:
      encoding: ASCII-8BIT
      string: "{\r\n  \"callId\": \"83dd833addd5abfbceb5a54068f782ee\",\r\n  \"errorCode\":
        0,\r\n  \"apiVersion\": 2,\r\n  \"statusCode\": 200,\r\n  \"statusReason\":
        \"OK\",\r\n  \"time\": \"2024-07-29T12:08:07.168Z\",\r\n  \"id_token\": \"***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\"\r\n}"
  recorded_at: Mon, 29 Jul 2024 12:08:07 GMT
- request:
    method: post
    uri: https://qa-selfservice.aceaspa.it/RB/funnel/checkTaxCode
    body:
      encoding: UTF-8
      string: '{"taxCode":"JNECRL76L17F205C","birthDate":"1976-07-17","firstName":"Carlo","lastName":"Jean","placeOfBirth":null,"gender":"M"}'
    headers:
      Authorization:
      - Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      Content-Type:
      - application/json; charset=utf8
      X-Subchannel:
      - KOLME
      X-Channeldetail:
      - '9233333333'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - no-cache, no-store, max-age=0, must-revalidate
      Content-Type:
      - application/json
      Date:
      - Mon, 29 Jul 2024 12:08:07 GMT
      Expires:
      - '0'
      Pragma:
      - no-cache
      Server:
      - envoy
      Strict-Transport-Security:
      - max-age=31536000 ; includeSubDomains
      Vary:
      - Origin,Access-Control-Request-Method,Access-Control-Request-Headers
      X-Content-Type-Options:
      - nosniff
      X-Envoy-Decorator-Operation:
      - ingress CheckTaxCodeUsingPOST
      X-Frame-Options:
      - DENY
      X-Xss-Protection:
      - 1; mode=block
      Via:
      - 1.1 google
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: '{"result":"OK","message":"Codice fiscale dell''utente verificato","body":{"birthDate":"1976-07-17","placeOfBirth":"Milano","provinceOfBirth":"MI","gender":"M","age":48}}'
  recorded_at: Mon, 29 Jul 2024 12:08:07 GMT
recorded_with: VCR 6.2.0
