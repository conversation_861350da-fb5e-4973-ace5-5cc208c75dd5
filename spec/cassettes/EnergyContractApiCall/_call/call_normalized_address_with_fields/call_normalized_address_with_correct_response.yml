---
http_interactions:
- request:
    method: post
    uri: https://accounts.eu1.gigya.com/accounts.getJWT
    body:
      encoding: US-ASCII
      string: apiKey=3_SF-TKzU9b87TpkMNzw1Limm2VitYI3hcl_N1r0qjhmVVjB6LsJJzDkPERVXC1vVM&secret=5XtDjycFTzhZTmhPwMAKim2m09WUrfBl&userKey=ALqFLcHJxgSV&targetUID=24660300749c43e0b6a50955e6eeeeeb&expiration=3600
    headers:
      Content-Type:
      - application/x-www-form-urlencoded
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Content-Length:
      - '798'
      Content-Type:
      - text/javascript; charset=utf-8
      Date:
      - Thu, 14 Nov 2024 08:16:45 GMT
      Cache-Control:
      - private
      Vary:
      - Accept-Encoding
      P3p:
      - CP="IDC COR PSA DEV ADM OUR IND ONL"
      X-Error-Code:
      - '0'
      X-Soa:
      - true, Gator
      X-Server:
      - eu1b-nomad-t7
      X-Callid:
      - ea2ed70aaeab4243ba6e4fe5df699c47
      X-Robots-Tag:
      - none
    body:
      encoding: ASCII-8BIT
      string: "{\r\n  \"callId\": \"ea2ed70aaeab4243ba6e4fe5df699c47\",\r\n  \"errorCode\":
        0,\r\n  \"apiVersion\": 2,\r\n  \"statusCode\": 200,\r\n  \"statusReason\":
        \"OK\",\r\n  \"time\": \"2024-11-14T08:16:45.731Z\",\r\n  \"id_token\": \"***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\"\r\n}"
  recorded_at: Fri, 15 Nov 2024 08:16:45 GMT
- request:
    method: get
    uri: https://qa-selfservice.aceaspa.it/RB/funnel/normalizedAddressByFields?city=Milano&commodity=ELE&operation=ATT_01&postalCode=20133&province=MI&street=Viale%20Corsica&streetNumber=2
    body:
      encoding: US-ASCII
      string: ''
    headers:
      Authorization:
      - Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      X-Subchannel:
      - KOLME
      X-Channeldetail:
      - '9233333333'
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
      User-Agent:
      - Ruby
  response:
    status:
      code: 200
      message: OK
    headers:
      Cache-Control:
      - no-cache, no-store, max-age=0, must-revalidate
      Content-Type:
      - application/json
      Date:
      - Thu, 14 Nov 2024 08:16:47 GMT
      Expires:
      - '0'
      Pragma:
      - no-cache
      Server:
      - envoy
      Strict-Transport-Security:
      - max-age=31536000 ; includeSubDomains
      Vary:
      - Origin
      - Origin,Access-Control-Request-Method,Access-Control-Request-Headers
      X-Content-Type-Options:
      - nosniff
      X-Envoy-Decorator-Operation:
      - ingress GetNormalizedAddressByFieldsUsingGET
      X-Frame-Options:
      - DENY
      X-Xss-Protection:
      - 1; mode=block
      Via:
      - 1.1 google
      Access-Control-Allow-Origin:
      - "*"
      Access-Control-Allow-Methods:
      - POST, PUT, GET, OPTIONS, DELETE, HEAD
      Access-Control-Allow-Headers:
      - "*"
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      Transfer-Encoding:
      - chunked
    body:
      encoding: ASCII-8BIT
      string: !binary |-
        eyJyZXN1bHQiOiJPSyIsIm1lc3NhZ2UiOiJOb3JtYWxpemVkIGFkZHJlc3MgZm91bmQiLCJib2R5Ijp7ImNvdW50cnkiOiJJVCIsInBvc3RhbENvZGUiOiIyMDEzNyIsInN0cmVldE5hbWUiOiJDb3JzaWNhIiwic3RyZWV0TnVtYmVyIjoiMiIsInRvd24iOiJNaWxhbm8iLCJwcm92aW5jZUlzb0NvZGUiOiJNSSIsInN0cmVldFByZWZpeCI6IlZpYWxlIiwic3RyZWV0TnVtYmVyVHlwZSI6Ik7CsCIsImZ1bGxTdHJlZXROYW1lIjoiVmlhbGUgQ29yc2ljYSIsImlzdGF0Q29kZSI6IjAxNTE0NiIsInBvaW50Y2FkYXN0cmFsY29kZSI6IkYyMDUiLCJwb2ludGNvZGUiOiJNaWxhbm8iLCJkaXN0cmlidXRvck5hbWUiOiJVTkFSRVRJIFNQQSIsInJlbWlDb2RlIjoiMzQ0MDM3MDAiLCJkaXN0cmlidXRvclZhdE51bWJlciI6IjEyODgzNDUwMTUyIiwibWFnaWMiOmZhbHNlfX0=
  recorded_at: Fri, 15 Nov 2024 08:16:47 GMT
recorded_with: VCR 6.2.0
