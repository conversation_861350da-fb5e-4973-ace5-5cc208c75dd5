require 'rails_helper'

describe Dealers::IndexFilter, :solr do
  let(:admin) { create(:user, :admin) }
  let(:user) { stub_model(User) }

  let(:barcellona_pozzo_digotto) { create(:city, :barcellona_pozzo_digotto) }

  let(:first_dealer) { create(:dealer, name: 'FM Informatica', billing_address_city: barcellona_pozzo_digotto, collection: true) }
  let(:second_dealer) { create(:dealer, :foreign_dealer, name: 'Babbani Informatica', foreign_city: 'Barcellona') }
  let(:md_very_dealer) { create(:dealer, :foreign_dealer, kind: 'md_very', name: 'MdVery Informatica', foreign_city: 'Barcellona') }
  let(:md_very_affiliated_dealer) { create(:dealer, :foreign_dealer, kind: 'md_very', name: 'MdVery Informatica Affiliated', foreign_city: 'Barcellona') }
  let(:md_very_awaiting_digital_signature_dealer) { create(:dealer, :foreign_dealer, kind: 'md_very', name: 'MdVery Informatica Awaiting digital signature', foreign_city: 'Barcellona') }
  let(:kolme_master_dealer) { create(:dealer, kind: 'kolme_master') }

  let(:contract_term) { create(:contract_term) }
  let(:warehouse) { create(:warehouse, ensign: 'FM Informatica') }

  let(:dealer_with_contract_to_sign) { create(:dealer, :with_dealer_contact, name: 'Dealer with contract', foreign_city: 'Barcellona', warehouses: [warehouse]) }
  let!(:signed_contract) { create(:signed_contract, contract_terms: [contract_term], dealer: dealer_with_contract_to_sign) }
  let(:tls_dealer) { create(:dealer, kind: 'operator_tls', name: 'TLS Informatica', foreign_city: 'Barcellona') }
  let(:third_dealer) { create(:dealer, name: 'Babbani', warehouses: [warehouse]) }
  let(:dealer_without_operators) { create(:dealer, name: 'Dealer without operators', warehouses: [warehouse]) }

  let(:fwa_installer_only_dealer) { create(:dealer, kind: 'fwa_installer_only', name: 'FWA only Informatica') }

  let(:admin_access) { UserAccess.new(admin) }
  let(:user_access) { UserAccess.new(user) }

  let(:operator_3) { create(:operator, :tre, access_level: AccessLevel::ONLY_WIND_TRE) }
  let(:operator_fastweb) { create(:operator, :fastweb, access_level: AccessLevel::ONLY_MD) }
  let(:operator_w3) { create(:operator, :w3) }
  let(:operator_very) { create(:operator, :very_mobile) }

  before { Audited.auditing_enabled = true }
  before { allow(admin).to receive(:internal_user?).and_return true }
  before { allow(admin).to receive_message_chain(:internal_user_detail, :access_level).and_return AccessLevel::ALL }
  before { allow(user).to receive(:internal_user?).and_return true }
  before { allow(user).to receive_message_chain(:internal_user_detail, :access_level).and_return AccessLevel::ONLY_WIND_TRE }

  before do
    md_very_affiliated_dealer.update_columns(mdv_affiliation_status: 'affiliated')
    md_very_awaiting_digital_signature_dealer.update_columns(mdv_affiliation_status: 'awaiting_digital_signature')

    first_dealer.operators << operator_3
    first_dealer.operators << operator_w3
    second_dealer.operators << operator_3
    second_dealer.operators << operator_very
    third_dealer.operators << operator_fastweb
    md_very_dealer.operators << operator_very
    kolme_master_dealer.operators << operator_3
    kolme_master_dealer.operators << operator_w3
  end

  before do
    Sunspot.index [first_dealer,
                   second_dealer,
                   third_dealer,
                   md_very_dealer,
                   md_very_affiliated_dealer,
                   md_very_awaiting_digital_signature_dealer,
                   kolme_master_dealer,
                   tls_dealer,
                   fwa_installer_only_dealer,
                   dealer_without_operators,
                   dealer_with_contract_to_sign];
    Sunspot.commit(true)
  end

  before { Audited.auditing_enabled = false }

  it do
    # only finds exact matches in full text search and insegna
    search = described_class.new(admin_access, { dealer_name: 'FM Informatica', show_disabled: true })
    expect(search.results).to include first_dealer, third_dealer, dealer_without_operators
    expect(search.results).not_to include second_dealer

    # show only user enabled access level
    search = described_class.new(user_access, { show_disabled: true })
    expect(search.results).to include first_dealer, second_dealer, dealer_without_operators
    expect(search.results).not_to include third_dealer

    # show only collection" do
    search = described_class.new(admin_access, show: ['collection'])
    expect(search.results).to include first_dealer
    expect(search.results).not_to include second_dealer

    # show only w3 or very enabled
    search = described_class.new(admin_access, show: ['w3_or_very'])
    expect(search.results).to include first_dealer
    expect(search.results).to include second_dealer
    expect(search.results).not_to include third_dealer
    expect(search.results).not_to include dealer_without_operators
    expect(search.results).not_to include md_very_dealer
    expect(search.results).not_to include tls_dealer
    expect(search.results).not_to include fwa_installer_only_dealer
    expect(search.results).not_to include kolme_master_dealer

    # show only md_very
    search = described_class.new(admin_access, show: ['md_very'])
    result = search.results

    expect(result).not_to include first_dealer
    expect(result).not_to include second_dealer
    expect(result).not_to include third_dealer
    expect(result).not_to include dealer_without_operators
    expect(result).to include md_very_dealer
    expect(result).to include md_very_affiliated_dealer
    expect(result).to include md_very_awaiting_digital_signature_dealer
    expect(result).not_to include tls_dealer
    expect(result).not_to include fwa_installer_only_dealer

    # show only operator_tls
    search = described_class.new(admin_access, show: ['operator_tls'])
    expect(search.results).not_to include first_dealer
    expect(search.results).not_to include second_dealer
    expect(search.results).not_to include third_dealer
    expect(search.results).not_to include dealer_without_operators
    expect(search.results).not_to include md_very_dealer
    expect(search.results).to include tls_dealer
    expect(search.results).not_to include fwa_installer_only_dealer

    # show only fwa_installer_only
    search = described_class.new(admin_access, show: ['fwa_installer_only'])
    expect(search.results).not_to include first_dealer
    expect(search.results).not_to include second_dealer
    expect(search.results).not_to include third_dealer
    expect(search.results).not_to include dealer_without_operators
    expect(search.results).not_to include md_very_dealer
    expect(search.results).not_to include tls_dealer
    expect(search.results).to include fwa_installer_only_dealer

    # show only kolme_partner (without operators)
    search = described_class.new(admin_access, show: ['kolme_partner'])
    expect(search.results).not_to include first_dealer
    expect(search.results).not_to include second_dealer
    expect(search.results).not_to include third_dealer
    expect(search.results).to include dealer_without_operators
    expect(search.results).not_to include md_very_dealer

    # mdv_affiliation_status\
    md_very_dealer.assign_mdv_affiliation_status
    md_very_dealer.save
    Sunspot.index(md_very_dealer)
    Sunspot.commit(true)
    search = described_class.new(admin_access, mdv_affiliation_status: 'awaiting_documents')

    expect(search.results).not_to include first_dealer
    expect(search.results).not_to include second_dealer
    expect(search.results).not_to include third_dealer
    expect(search.results).not_to include dealer_without_operators
    expect(search.results).to include md_very_dealer
    expect(search.results).not_to include md_very_affiliated_dealer
    expect(search.results).not_to include md_very_awaiting_digital_signature_dealer
    expect(search.results).not_to include dealer_with_contract_to_sign

    search = described_class.new(admin_access, mdv_affiliation_status: 'awaiting_digital_signature')
    expect(search.results).not_to include first_dealer
    expect(search.results).not_to include second_dealer
    expect(search.results).not_to include third_dealer
    expect(search.results).not_to include dealer_without_operators
    expect(search.results).not_to include md_very_dealer
    expect(search.results).not_to include md_very_affiliated_dealer
    expect(search.results).to include md_very_awaiting_digital_signature_dealer
    expect(search.results).to include dealer_with_contract_to_sign

    search = described_class.new(admin_access, mdv_affiliation_status: 'affiliated')
    expect(search.results).not_to include first_dealer
    expect(search.results).not_to include second_dealer
    expect(search.results).not_to include third_dealer
    expect(search.results).not_to include dealer_without_operators
    expect(search.results).not_to include md_very_dealer
    expect(search.results).to include md_very_affiliated_dealer
    expect(search.results).not_to include md_very_awaiting_digital_signature_dealer
    expect(search.results).not_to include dealer_with_contract_to_sign

    # search exactly by billing address city
    search = described_class.new(admin_access, billing_address_city: 'Barcellona Pozzo Digotto')
    expect(search.results).to include first_dealer
    expect(search.results).not_to include(second_dealer)

    search_foreign = described_class.new(admin_access, billing_address_city: 'Barcellona')
    expect(search_foreign.results).not_to include(first_dealer)
    expect(search_foreign.results).to include(second_dealer)

    search_foreign_downcase = described_class.new(admin_access, billing_address_city: 'barcellona')
    expect(search_foreign_downcase.results).not_to include(first_dealer)
    expect(search_foreign_downcase.results).to include(second_dealer)

    search_without_results = described_class.new(admin_access, billing_address_city: 'Bar')
    expect(search_without_results.results).to be_empty

    # find by billing_address_province
    params = { billing_address_province: 'Messina' }
    search = described_class.new(admin_access, params)
    expect(search.results).to include first_dealer

    # find franchising
    search = described_class.new(admin_access, show: ['franchising'])
    expect(search.results).to be_empty

    # find kolme_agent
    search = described_class.new(admin_access, show: ['kolme_agent'])
    expect(search.results).to be_empty

    # find kolme_master
    search = described_class.new(admin_access, show: ['kolme_master'])
    expect(search.results).to eq [kolme_master_dealer]
  end

  context 'kolme_agent' do
    it do
      search = described_class.new(admin_access, show: ['kolme_agent'])
      first_dealer.update(kind: 'kolme_agent')
      Sunspot.index first_dealer
      Sunspot.commit(true)

      expect(search.results).to match_array [first_dealer]
    end
  end
end
