require "rails_helper"
require "support/attiva_service_helper"

describe AttivaService::DataStoreService do
  include AttivaServiceHelper

  let(:attiva_product_import_session) { create(:attiva_product_import_session) }
  let(:product) { create(:attiva_imported) }
  let(:attiva_product) { create(:attiva_product, import_session: attiva_product_import_session, product: product) }
  let!(:product_not_present) { create(:imported_completed, code: "notinimport", origin: "attiva", can_be_requested: true, moderated: true, visibility: "y") }
  let!(:product_dismissed) { create(:imported_completed, code: "dismissed", origin: "attiva", can_be_requested: false, moderated: true, dismissed_at: Time.now) }

  subject { described_class.new(import_session: attiva_product_import_session) }

  before { AttivaProductMailer.deliveries.clear }

  context "with orphan products" do
    it "change can be requested" do
      subject.notify_unknown
      expect(product_not_present.reload.visibility).to eq "n"
      expect(product_not_present.reload.can_be_requested).to be_falsey
      expect(product_not_present.reload.moderated).to be_truthy
    end

    it "send email if moderated" do
      subject.notify_unknown
      expect(AttivaProductMailer.deliveries.count).to eq 1
    end

    it "change can be requested if not moderated" do
      product_not_present.update_column(:moderated, false)
      subject.notify_unknown

      expect(product_not_present.reload.can_be_requested).to be_falsey
      expect(product_not_present.reload.dismissed_at).not_to be_nil
      expect(product_not_present.reload.moderated).to be_falsey
      expect(product_not_present.reload.visibility).to eq "n"
    end

    it "not send email if not moderated" do
      product_not_present.update_column(:moderated, false)
      subject.notify_unknown
      expect(AttivaProductMailer.deliveries.count).to eq 0
    end
  end
end
