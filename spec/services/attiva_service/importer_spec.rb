# encoding: utf-8

require "rails_helper"

describe AttivaService::Importer do
  let(:file_name) { "product_list.xml" }

  subject { described_class.new }

  describe "#import" do
    context "on generic error" do
      let(:error) { StandardError.new "No such file" }
      let(:mail) { double }

      before do
        allow(Zlib::GzipReader).to receive(:open)
          .and_raise error
        allow(AttivaProductMailer).to receive(:generic_error).and_return mail
      end

      it "sends an email" do
        expect(AttivaProductMailer).to receive(:generic_error).with(error)
        expect(mail).to receive(:deliver_now)
        
        subject.import(file_name)
      end
    end
  end
end
