require "rails_helper"

describe InvoiceNumberService do
  context "end_user_invoice_number_for(resource)" do
    context "phone_activation" do
      let(:resource) { build_stubbed(:phone_activation) }

      context "when require_end_user_invoice_page? is true" do
        before { allow(resource).to receive(:require_end_user_invoice_page?).and_return true }
        before { allow(AutomaticInvoiceNumber).to receive(:get).and_return 1 }

        it "is the existing invoice number if present" do
          allow(resource).to receive_message_chain(:fattura_cliente_finale, :invoice_number).and_return "ST00001"
          expect(described_class.end_user_invoice_number_for(resource)).to eq "ST00001"
        end

        it "is a new invoice number if none is found" do
          allow(resource).to receive(:fattura_cliente_finale).and_return nil
          expect(described_class.end_user_invoice_number_for(resource)).to eq "ST00001"
          expect(described_class.end_user_invoice_number_for(resource).length).to eq 7
        end
      end

      context "when require_end_user_invoice_page? is false" do
        before { allow(resource).to receive(:require_end_user_invoice_page?).and_return false }

        it "is nil" do
          expect(described_class.end_user_invoice_number_for(resource)).to be_nil
        end
      end
    end

    context "imei_reservation" do
      let(:resource) { build_stubbed(:imei_reservation) }

      context "when require_end_user_invoice_page? is true" do
        before { allow(resource).to receive(:require_end_user_invoice_page?).and_return true }
        before { allow(AutomaticInvoiceNumber).to receive(:get).and_return 1 }

        it "is a new invoice number if none is found" do
          allow(resource).to receive(:fattura_cliente_finale).and_return nil
          expect(described_class.end_user_invoice_number_for(resource)).to eq "SG00001"
          expect(described_class.end_user_invoice_number_for(resource).length).to eq 7
        end
      end
    end
  end

  context "get_upfront_invoice_number_for(resource)" do
    let(:resource) { double }

    context "when require_upfront_invoice_page? is true" do
      before { allow(resource).to receive(:require_upfront_invoice_page?).and_return true }
      before { allow(resource).to receive(:dealer_id).and_return 1 }
      before { allow(DealerInvoiceNumber).to receive(:get).with(DealerInvoiceNumber::UPFRONT, 1).and_return 1 }

      it "is the existing invoice number if present" do
        allow(resource).to receive_message_chain(:fattura_cliente_finale, :upfront_invoice_number).and_return "KX0001"
        expect(described_class.upfront_number_for(resource)).to eq "KX0001"
      end

      it "is a new invoice number if none is found" do
        allow(resource).to receive(:fattura_cliente_finale).and_return nil
        expect(described_class.upfront_number_for(resource)).to eq "KX0001"
        expect(described_class.upfront_number_for(resource).length).to eq 6
      end
    end

    context "when require_upfront_invoice_page? is false" do
      before { allow(resource).to receive(:require_upfront_invoice_page?).and_return false }

      it "is nil" do
        expect(described_class.upfront_number_for(resource)).to be_nil
      end
    end
  end
end
