# encoding: utf-8

require 'rails_helper'

describe PdaService do

  describe 'generate attributes_for_pda' do
    let!(:pda_kind) { create(:pda_kind, :eolo) }
    let(:recharge_size) { create(:recharge_size, :tre_euro_10) }
    let!(:operation_inserita_da_partner) { create(:operation, :inserita_da_partner) }
    let!(:message) { create(:alert_message, :phone_activation_upload_documents) }
    let(:stored_plan) { create(:stored_plan) }
    let(:phone_activation) { create(:phone_activation, :wind_fisso, :with_dealer_contact, signature_data: { 'signature_kind' => 'paper' }) }
    let(:ii_row_storno) { create(:ii_row, :rimborso, invoiceable_origin: phone_activation, referred_date: Time.now) }
    let(:number_rebinding_detail) { create(:number_rebinding_detail, phone_activation: phone_activation) }
    let(:fixed_line_detail) { create(:fixed_line_detail, phone_activation: phone_activation) }
    let(:payment_method_no) { create(:payment_method, :no) }

    before do
      number_rebinding_detail.update_column(:phone_activation_id, phone_activation.id)
    end

    let(:attributes) do
      attributes, _templates = PdaService.new(phone_activation).provide_attributes_and_templates
      attributes
    end

    before do
      phone_activation.update_columns(
        payment_method_id:         payment_method_no.id,
        product_id:                nil,
        recharge_size_id:          recharge_size.id,
        product_final_installment: 10
      )
      stored_plan.update_column(:phone_activation_id, phone_activation.id)
      phone_activation.warehouse.update_attribute(:tre_code, '9233333333')
    end

    it 'is correctly generated' do
      attributes = PdaService.new(phone_activation).provide_attributes
      templates = PdaService.new(phone_activation).provide_templates

      expect(attributes).not_to be_nil
      expect(templates).not_to be_nil
      expect(templates.select { |t| t.include?(pda_kind.pf_pda) }.any?).to be_truthy
      %i[stored_plan_pda_text stored_plan_description].each do |attribute|
        expect(attributes[attribute]).not_to be_nil
      end
      expect(attributes[:stored_plan_pda_text]).to eq(stored_plan.pda_text)
      expect(attributes[:stored_plan_description]).to eq(stored_plan.plan_description)
      expect(attributes[:stored_plan_all_description]).to eq(stored_plan.all_description)
      expect(attributes[:rebinding_phone_number]).to eq(number_rebinding_detail.phone_number)
      expect(attributes[:recharge_size_amount_text]).not_to be_nil
      expect(attributes[:recharge_size_amount_text]).to eq("Credito telefonico: € #{phone_activation.recharge_size.try(:amount)}")
      expect(attributes[:gender]).to eq(phone_activation.customer.gender.upcase)
      expect(attributes[:portability_y]).to be_blank
      expect(attributes[:portability_n]).to eq('1')
      expect(attributes[:upfront]).not_to be_nil
      expect(attributes[:upfront]).to have_currency_format
      expect(attributes[:upfront]).to have_currency_format
      expect(attributes[:doc_country]).not_to be_nil
      expect(attributes[:dealer_code]).to eq '9233333333'
      expect(attributes[:product_final_installment]).to eq '€ 10,00'
    end

    it 'location_data,sim_data,sim_data_mnp_form with number_portability_detail and different owner' do
      number_portability_detail                  = create(:number_portability_detail, :wind_fisso, :different_owner, phone_activation: phone_activation, voice_migration_code: '000000000000000001A')
      phone_activation.number_portability_detail = number_portability_detail

      attributes = PdaService.new(phone_activation).provide_attributes

      expect(attributes[:location_data]).not_to include("#{number_portability_detail.owner_first_name} true")
      expect(attributes[:location_data]).to include("#{number_portability_detail.owner_first_name} #{number_portability_detail.owner_last_name}")
      expect(attributes[:sim_data]).not_to include("#{number_portability_detail.owner_first_name} true")
      expect(attributes[:sim_data]).to include("#{number_portability_detail.owner_first_name} #{number_portability_detail.owner_last_name}")
      expect(attributes[:sim_data_mnp_form]).not_to include("#{number_portability_detail.owner_first_name} true")
      expect(attributes[:sim_data_mnp_form]).to include("#{number_portability_detail.owner_first_name} #{number_portability_detail.owner_last_name}")
    end

    it 'location_data,sim_data,sim_data_mnp_form with number_portability_detail without different owner' do
      number_portability_detail                  = create(:number_portability_detail, :wind_fisso, phone_activation: phone_activation, voice_migration_code: '000000000000000001A')
      phone_activation.number_portability_detail = number_portability_detail

      attributes = PdaService.new(phone_activation).provide_attributes

      expect(attributes[:location_data]).to include(phone_activation.customer.name.to_s)
      expect(attributes[:sim_data]).to include(phone_activation.customer.name.to_s)
      expect(attributes[:sim_data_mnp_form]).to include(phone_activation.customer.name.to_s)
    end

    it 'format date for customer_data and customer_data_mnp_form' do
      attributes = PdaService.new(phone_activation).provide_attributes
      expect(attributes[:customer_data]).to include('06/12/1939')
      expect(attributes[:customer_data]).to include('22/07/2015')
      expect(attributes[:customer_data_mnp_form]).to include('06/12/1939')
      expect(attributes[:customer_data_mnp_form]).to include('22/07/2015')
    end
  end

  context 'pda_templates(phone_activation)' do
    let(:none_payment_method) { stub_model(PaymentMethod, code: 'nessuno') }
    let(:customer) { stub_model(Customer, customer_kind: stub_model(CustomerKind, code: 'pf')) }
    let(:dealer) { build_stubbed(:dealer) }
    let(:plan) { stub_model(Plan, pda_kind: build_stubbed(:pda_kind, :eolo)) }
    let(:payment_method_detail) { stub_model(PaymentMethodDetail, different_accountholder: false) }
    let(:offer_category) { stub_model(OfferCategory, code: 'rateale') }
    let(:phone_activation) do
      stub_model(PhoneActivation,
                 dealer:                dealer,
                 plan:                  plan,
                 customer:              customer,
                 payment_method:        none_payment_method,
                 payment_method_detail: payment_method_detail,
                 offer_category:        offer_category,
                 operator:              stub_model(Operator, code: 'very_mobile'))
    end

    it do
      expect(described_class.new(phone_activation).provide_templates).to include "#{Settings.document_templates.path}/PF_none.pdf"
      expect(described_class.new(phone_activation).provide_templates.last).to eq "#{Settings.document_templates.path}/Cover.pdf"
    end

    it 'automatic recharge very mobile' do
      offer_category.code                 = 'solo_sim'
      phone_activation.operator           = build_stubbed(:operator, :very_mobile)

      phone_activation.automatic_recharge = true
      templates                           = described_class.new(phone_activation).provide_templates

      expect(templates).to include "#{Settings.document_templates.path}/pf_solo_sim_cdc.pdf"
      expect(templates).not_to include "#{Settings.document_templates.path}/pf_solo_sim_none.pdf"

      phone_activation.automatic_recharge = false
      templates                           = described_class.new(phone_activation).provide_templates

      expect(templates).not_to include "#{Settings.document_templates.path}/pf_solo_sim_cdc.pdf"
      expect(templates).to include "#{Settings.document_templates.path}/pf_solo_sim_none.pdf"
    end

    context 'return pda none template without payment_method' do
      before { phone_activation.payment_method = nil }

      it do
        expect(described_class.new(phone_activation).provide_templates).to include "#{Settings.document_templates.path}/PF_none.pdf"
        expect(described_class.new(phone_activation).provide_templates.last).to eq "#{Settings.document_templates.path}/Cover.pdf"
      end
    end

    context 'return pda none template with postal payment_method' do
      before { phone_activation.payment_method = stub_model(PaymentMethod, code: 'postal_slip') }

      it do
        expect(described_class.new(phone_activation).provide_templates).to include "#{Settings.document_templates.path}/PF_none.pdf"
        expect(described_class.new(phone_activation).provide_templates.last).to eq "#{Settings.document_templates.path}/Cover.pdf"
      end
    end

    context 'with different account holder' do
      before { phone_activation.payment_method_detail.different_accountholder = true }

      it do
        expect(described_class.new(phone_activation).provide_templates).to include "#{Settings.document_templates.path}/pf_cdc_div_int.pdf"
        expect(described_class.new(phone_activation).provide_templates.last).to eq "#{Settings.document_templates.path}/Cover.pdf"
      end
    end

    context 'turista customer' do
      before { customer.customer_kind.code = 'tu' }

      it do
        expect(described_class.new(phone_activation).provide_templates).to include "#{Settings.document_templates.path}/PF_none.pdf"
        expect(described_class.new(phone_activation).provide_templates.last).to eq "#{Settings.document_templates.path}/Cover.pdf"
      end
    end

    context 'transfer documents' do
      before { plan.pda_kind.pf_generic_transfer = 'pf_generic_transfer.pdf' }

      ['rateale', 'rete_fissa'].each do |offer_category_code|
        before { offer_category.code = offer_category_code }

        it do
          dealer.kind = 'generic'
          expect(described_class.new(phone_activation).provide_templates).to include "#{Settings.document_templates.path}/pf_generic_transfer.pdf"
          expect(described_class.new(phone_activation).provide_templates).not_to include "#{Settings.document_templates.path}/pf_kolme_master_transfer.pdf"

          dealer.kind = 'kolme_master'
          expect(described_class.new(phone_activation).provide_templates).not_to include "#{Settings.document_templates.path}/pf_generic_transfer.pdf"
          expect(described_class.new(phone_activation).provide_templates).to include "#{Settings.document_templates.path}/pf_kolme_master_transfer.pdf"
        end

        context 'with bollettino transfer documents are not included' do
          before { phone_activation.payment_method = create(:payment_method, :postal_slip) }

          it do
            dealer.kind = 'generic'
            expect(described_class.new(phone_activation).provide_templates).not_to include "#{Settings.document_templates.path}/pf_generic_transfer.pdf"
            expect(described_class.new(phone_activation).provide_templates).not_to include "#{Settings.document_templates.path}/pf_kolme_master_transfer.pdf"

            dealer.kind = 'kolme_master'
            expect(described_class.new(phone_activation).provide_templates).not_to include "#{Settings.document_templates.path}/pf_generic_transfer.pdf"
            expect(described_class.new(phone_activation).provide_templates).not_to include "#{Settings.document_templates.path}/pf_kolme_master_transfer.pdf"
          end
        end
      end

      it do
        dealer.kind = 'generic'
        offer_category.code = 'finanziamento'

        expect(described_class.new(phone_activation).provide_templates).not_to include "#{Settings.document_templates.path}/pf_generic_transfer.pdf"
        expect(described_class.new(phone_activation).provide_templates).not_to include "#{Settings.document_templates.path}/pf_kolme_master_transfer.pdf"

        dealer.kind = 'kolme_master'
        expect(described_class.new(phone_activation).provide_templates).not_to include "#{Settings.document_templates.path}/pf_generic_transfer.pdf"
        expect(described_class.new(phone_activation).provide_templates).not_to include "#{Settings.document_templates.path}/pf_kolme_master_transfer.pdf"
      end
    end

    context 'beyond_pda_kind_threshold' do
      let(:offer_length_threshold_pda_kind) { build_stubbed(:pda_kind, :eolo, :offer_length_threshold) }

      before { phone_activation.plan.pda_kind = offer_length_threshold_pda_kind }
      before { allow(phone_activation).to receive(:require_rid_pda_template?).and_return true }

      it do
        phone_activation.length = 24
        expect(described_class.new(phone_activation).provide_templates).to include "#{Settings.document_templates.path}/Eolo - Abbonamento PF.pdf"
        expect(described_class.new(phone_activation).provide_templates).to include "#{Settings.document_templates.path}/Modulo 3 - RID.pdf"

        phone_activation.length = 30
        allow(phone_activation).to receive(:require_credit_card_pda_template?).and_return true
        expect(described_class.new(phone_activation).provide_templates).to include "#{Settings.document_templates.path}/Eolo - Abbonamento PF OLT.pdf"
        expect(described_class.new(phone_activation).provide_templates).to include "#{Settings.document_templates.path}/Modulo 3 - RID.pdf"
        expect(described_class.new(phone_activation).provide_templates).to include "#{Settings.document_templates.path}/CDC PF OLT.pdf"

        # without required file
        offer_length_threshold_pda_kind.pf_cdc_offer_length_threshold = ""
        expect(described_class.new(phone_activation).provide_templates).not_to include nil
      end
    end
  end
end
