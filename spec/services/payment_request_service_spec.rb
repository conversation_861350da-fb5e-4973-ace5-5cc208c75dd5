require "rails_helper"

describe PaymentRequestService do
  let(:dealer) { create(:dealer, :with_warehouse) }
  let(:payment_request) { create(:payment_request, :generic, dealer: dealer) }

  context "#add_creation_notification" do
    let(:params) { { payment_request_id: payment_request.id } }

    subject { described_class.new(params).add_creation_notification }

    it { expect { subject }.to change { Alert.count }.by(1) }
  end

  context "#archive_all_notifications" do
    let(:params) { { payment_request_id: payment_request.id } }
    let!(:alert) do
      create(:alert,
                alertable_id: payment_request.id,
                alertable_type: PaymentRequest.name,
                archived: false,
                todo: true,
                dealer: payment_request.dealer,
                warehouse: payment_request.dealer.warehouses.first)
    end

    before { described_class.new(params).archive_all_notifications }
    it     { expect(alert.reload.archived).to be_truthy }
  end
end
