require 'rails_helper'

describe ActivationDocumentsPushNotificationService do
  let(:phone_activation) { build_stubbed(:phone_activation, :abbonamento_ricaricabile) }

  let(:device) { create(:device) }

  describe '#send_notification(payload)' do
    let(:fcm_service) { double }
    before do
      allow(fcm_service).to receive(:send_message_to)
      allow(fcm_service).to receive(:error)
    end

    let(:time) { Time.now }

    before { Timecop.freeze(time) }
    after { Timecop.return }

    let(:token) do
      JwtService.new(
        expiration_in: time.to_i + Settings.push_notifications.expiration_elapse
      ).encode(device.user.email)
    end

    let(:fcm_payload) do
      {
        android:      { notification: { sound: 'default' } },
        apns:         { payload: { aps: { content_available: true, sound: 'default' } } },
        data:         { auth_token: token,
                        phone_activation_id: phone_activation.id.to_s,
                        priority:   'high',
                        scope:      'activationDocuments' },
        notification: { body: 'Procedi all\'acquisizione dei documenti cliente', title: 'Nuova attivazione' }
      }
    end

    before do
      allow(PhoneActivation).to receive(:find).and_return phone_activation
    end

    before do
      expect(PhoneActivation).to receive(:find).with phone_activation.id
      expect(fcm_service).to receive(:send_message_to).with(device.firebase_token, fcm_payload)
    end

    subject do
      described_class.new([device], fcm_service: fcm_service)
    end

    it 'remaps correctly the options to the FCM client and locks the device' do
      expect(fcm_service).to receive(:error)

      subject.send_notification(notifiable_id: phone_activation.id)

      expect(device.reload.locked?).to be_truthy
      expect(device.push_notification_data['data']['auth_token']).to eq token
      expect(device.push_notification_data['data']['phone_activation_id']).to eq phone_activation.id.to_s
    end

    context 'FcmService in error' do
      before { allow(fcm_service).to receive(:response).and_return({status_code: 400}) }
      before { allow(fcm_service).to receive(:error).and_return 'error' }

      it 'destroys the (logical) device, reports an error and destroys draft people' do
        expect(phone_activation).to receive(:destroy_draft_people!)

        subject.send_notification(notifiable_id: phone_activation.id)

        expect(device.destroyed?).to be_truthy
        expect(subject.errors).to eq device.uuid => I18n.t('errors.fcm_service.invalid_registration')
      end
    end
  end
end
