require 'rails_helper'
require 'axlsx'

RSpec.describe EnergyContracts::ExportService, type: :service do
  let(:user) { create(:user, :dealer_user) }
  let(:params) { { search: {} } }
  let(:repository) { instance_double(Repositories::EnergyContractRepository) }
  let(:energy_contracts_relation) { instance_double('ActiveRecord::Relation') }
  let(:export_service) { described_class.new(user, params) }
  let(:xlsx_package) { export_service.export_to_xlsx }
  let(:batch_size) { EnergyContracts::ExportService::BATCH_SIZE }

  before do
    allow(Repositories::EnergyContractRepository).to receive(:new).with(current_user: user).and_return(repository)
    allow(repository).to receive(:search).with(params[:search]).and_return(energy_contracts_relation)
  end

  describe '#initialize' do
    it 'assigns the user and params' do
      expect(export_service.instance_variable_get(:@user)).to eq(user)
      expect(export_service.instance_variable_get(:@params)).to eq(params)
    end

    it 'initializes @energy_contracts with searched results' do
      expect(export_service.instance_variable_get(:@energy_contracts)).to eq(energy_contracts_relation)
    end
  end

  describe '#export_to_xlsx' do
    let!(:energy_contracts) { create_list(:energy_contract, 3) }

    # Creating a double for energy_plan
    let(:energy_plan) do
      double('EnergyPlan', position: 1, plan_kind: 'energy', destination: 'domestic', name: 'Plan Name')
    end

    let(:ecep_state) { 'active' }
    let(:ecep) do
      instance_double(
        'EnergyContractEnergyPlan',
        state:       ecep_state,
        energy_plan: energy_plan
      )
    end

    before do
      allow(energy_contracts_relation).to receive(:find_in_batches).with(batch_size: batch_size).and_yield(energy_contracts)
      energy_contracts.each do |contract|
        allow(contract).to receive(:energy_contract_energy_plans).and_return([ecep])
        allow(contract).to receive(:user).and_return(user)
        allow(contract).to receive(:customer).and_return(create(:customer))
        allow(contract).to receive_message_chain(:energy_contract_payment, :payment_kind).and_return('iban')
        allow(contract).to receive(:energy_total_reward).and_return(BigDecimal('100.0'))
        allow(contract).to receive(:energy_extra_reward).and_return(BigDecimal('20.0'))
        allow(contract).to receive(:gas_total_reward).and_return(BigDecimal('80.0'))
        allow(contract).to receive(:gas_extra_reward).and_return(BigDecimal('15.0'))
      end

      # Stub I18n methods
      allow(I18n).to receive(:l).and_call_original
      allow(I18n).to receive(:t).and_call_original
      allow(I18n).to receive(:t).with("activerecord.attributes.energy_contract_energy_plan/state.#{ecep_state}").and_return('Translated State')
    end

    it 'creates an Axlsx package' do
      expect(xlsx_package).to be_an_instance_of(Axlsx::Package)
    end

    it 'adds a worksheet named "Energy Contracts"' do
      expect(xlsx_package.workbook.worksheets.first.name).to eq('Energy Contracts')
    end

    it 'writes the correct headers' do
      workbook = xlsx_package.workbook
      sheet = workbook.worksheets.first
      headers = ['ID attivazione', 'Codice contratto', 'Data inserimento', 'Stato avanzamento pratica',
                 'Data attivazione', 'Utente Inserimento', 'Cliente', 'Tipo fornitura', 'Tipo listino',
                 'Nome Tariffa', 'MDP', 'Compenso Luce', 'Extracompenso Luce', 'Compenso Gas', 'Extracompenso Gas']

      expect(sheet.rows.first.cells.map(&:value)).to eq(headers)
    end

    it 'writes the correct data rows' do
      workbook = xlsx_package.workbook
      sheet = workbook.worksheets.first
      data_rows = sheet.rows[1..-1] # Exclude header row

      expect(data_rows.size).to eq(energy_contracts.size)

      energy_contracts.each_with_index do |contract, index|
        row = data_rows[index].cells.map(&:value)

        expected_row = [
          contract.hex_id,
          contract.pdc_code,
          contract.created_at ? I18n.l(contract.created_at.to_date) : nil,
          'Translated State',
          contract.activated_at ? I18n.l(contract.activated_at.to_date) : nil,
          contract.user&.username,
          contract.customer&.uniq_name,
          export_service.send(:plan_kind, ecep),
          export_service.send(:destination, ecep),
          ecep.energy_plan&.name, # This will return 'Plan Name'
          export_service.send(:payment_kind, contract),
          BigDecimal('100.0'),
          BigDecimal('20.0'),
          BigDecimal('80.0'),
          BigDecimal('15.0')
        ]

        expect(row.map { |c| c.is_a?(String) && c.match?(/\A\d+\.\d+\z/) ? BigDecimal(c) : c }).to eq(expected_row)
      end
    end
  end

  describe 'private methods' do
    let(:ecep) { instance_double('EnergyContractEnergyPlan', energy_plan: energy_plan) }
    let(:energy_plan) { instance_double('EnergyPlan') }
    let(:contract) { instance_double('EnergyContract') }

    describe '#plan_kind' do
      it 'returns "Luce" when plan_kind is "energy"' do
        allow(energy_plan).to receive(:plan_kind).and_return('energy')
        expect(export_service.send(:plan_kind, ecep)).to eq('Luce')
      end

      it 'returns "Gas" when plan_kind is "gas"' do
        allow(energy_plan).to receive(:plan_kind).and_return('gas')
        expect(export_service.send(:plan_kind, ecep)).to eq('Gas')
      end

      it 'returns nil when plan_kind is nil' do
        allow(energy_plan).to receive(:plan_kind).and_return(nil)
        expect(export_service.send(:plan_kind, ecep)).to be_nil
      end
    end

    describe '#destination' do
      it 'returns "Domestico" when destination is "domestic"' do
        allow(energy_plan).to receive(:destination).and_return('domestic')
        expect(export_service.send(:destination, ecep)).to eq('Domestico')
      end

      it 'returns "Altri Usi" when destination is "other"' do
        allow(energy_plan).to receive(:destination).and_return('other')
        expect(export_service.send(:destination, ecep)).to eq('Altri Usi')
      end

      it 'returns nil when destination is nil' do
        allow(energy_plan).to receive(:destination).and_return(nil)
        expect(export_service.send(:destination, ecep)).to be_nil
      end
    end

    describe '#payment_kind' do
      let(:payment) { instance_double('EnergyContractPayment') }

      before do
        allow(contract).to receive(:energy_contract_payment).and_return(payment)
      end

      it 'returns "Bollettino Postale" when payment_kind is "postal_payment_slip"' do
        allow(payment).to receive(:payment_kind).and_return('postal_payment_slip')
        expect(export_service.send(:payment_kind, contract)).to eq('Bollettino Postale')
      end

      it 'returns "SDD" when payment_kind is "iban"' do
        allow(payment).to receive(:payment_kind).and_return('iban')
        expect(export_service.send(:payment_kind, contract)).to eq('SDD')
      end

      it 'returns nil when payment_kind is nil' do
        allow(payment).to receive(:payment_kind).and_return(nil)
        expect(export_service.send(:payment_kind, contract)).to be_nil
      end
    end
  end
end
