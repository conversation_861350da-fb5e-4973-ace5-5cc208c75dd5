require 'rails_helper'

describe Graphql::DocumentsUploadService do
  describe '::upload_document_file' do
    let(:activation) { create(:wind_ngpos_activation) }

    let(:document_name) { "DOC#{activation.id}" }
    let(:files) { [double] }
    let(:uploaded_files) { [double] }

    let(:args) do
      {
        documentable_id:   activation.id,
        documentable_type: 'WindNgposActivation',
        document_type:     'contract',
        context:           'upload',
        files:             files
      }
    end

    subject { described_class.upload_document_file(args) }

    context 'Success' do
      let(:documents) { double }
      let(:pdf_handler) { double }
      let(:file_full_names) do
        ['document_name_3.pdf', 'document_name_4.jpg']
      end

      before do
        allow(Document).to receive(:name_for).and_return document_name
        allow(Document).to receive(:create_tmp_files).and_return [uploaded_files, file_full_names]
        allow(Util::PdfHandler).to receive(:new).and_return pdf_handler
        allow(pdf_handler).to receive(:check_pdf_format)
      end

      it 'mimicks the corresponding DocumentsController#create method' do
        expect(Document).to receive(:name_for).with('contract', activation, 'upload')
        expect(Document).to receive(:create_tmp_files).with(document_name, 'contract', files, activation, 'upload')
        expect(pdf_handler).to receive(:check_pdf_format).with(files: uploaded_files)

        expect(described_class.upload_document_file(args)).to eq(obj:     { uploaded_file_names: file_full_names },
                                                                 success: true,
                                                                 message: 'Files uploaded correctly')
      end
    end

    context 'Error' do
      context 'Documentable not found' do
        let(:args) do
          {
            documentable_id:   activation.id,
            documentable_type: 'PhoneActivation',
            document_type:     'contract',
            context:           'upload',
            files:             files
          }
        end

        it 'traps the error and raises a dedicated GraphQL error' do
          expect { subject }.to raise_error(GraphQL::ExecutionError, 'Documentable not found')
        end
      end

      context 'Error creating temporary files' do
        before do
          allow(Document).to receive(:name_for).and_return document_name
          allow(Document).to receive(:create_tmp_files).and_return false
        end

        it 'traps the error and raises a dedicated GraphQL error' do
          expect(subject).to eq(success: false,
                                errors:  ['Hai caricato dei file non supportati'])
        end
      end

      context 'Generic error' do
        before do
          allow(Document).to receive(:name_for).and_raise 'Unexpected error'
        end

        it 'does not trap the error' do
          expect { subject }.to raise_error(StandardError, 'Unexpected error')
        end
      end
    end
  end

  describe '::create_document' do
    let(:user) { create(:user, :dealer_user) }

    let(:handler) { double }

    let(:args) { variables.merge(current_user: user, app_version: 'iOS 1.0.1') }

    before do
      allow(Documents::DocumentsHandler).to receive(:new).and_return handler
      allow(handler).to receive(:prepare_documents)
      allow(handler).to receive(:create_documents)
    end

    subject { described_class.create_document(args) }

    context 'with an NG POS activation' do
      let(:activation) { create(:wind_ngpos_activation) }
      let(:variables) do
        {
          documentable_id:   activation.id,
          documentable_type: activation.class.name,
          context:           'upload'
        }
      end

      it 'calls Documents::DocumentsHandler#create_documents without the operation outcome-skipping option for phone activations' do
        expect(Documents::DocumentsHandler).to receive(:new).with({
          documentable_type: 'wind_ngpos_activations',
          documentable_id:   activation.id,
          context:           'upload'
        })
        expect(handler).to receive(:prepare_documents)
        expect(handler).to receive(:create_documents).with(current_user_id: user.id, app_version: 'iOS 1.0.1')

        subject
      end
    end

    context 'with an Order' do
      let(:order) { stub_model(Order, aasm_state: :to_pay) }
      let(:variables) do
        {
          documentable_id:   order.id,
          documentable_type: order.class.name,
          context:           'upload'
        }
      end

      before do
        allow(Order).to receive(:find).and_return order
      end

      it 'calls Documents::DocumentsHandler#create_documents' do
        expect(Documents::DocumentsHandler).to receive(:new).with({
          documentable_type: 'orders',
          documentable_id:   order.id,
          context:           'upload'
        })
        expect(handler).to receive(:prepare_documents)
        expect(handler).to receive(:create_documents).with(current_user_id: user.id, app_version: 'iOS 1.0.1')

        expect(order).to receive(:uploaded_bank_transfer!)

        subject
      end
    end

    context 'with a phone activation' do
      let(:activation) { stub_model(PhoneActivation) }
      let(:operation_outcome) { build(:operation_outcome, operation_id: 12) }

      let(:variables) do
        {
          documentable_id:   activation.id,
          documentable_type: activation.class.name,
          context:           'upload'
        }
      end

      before do
        allow(PhoneActivation).to receive(:find).and_return activation
        allow(handler).to receive(:create_documents).and_return operation_outcome
      end

      it 'calls Documents::DocumentsHandler#create_documents without the operation outcome-skipping option for phone activations and refreshes the views' do
        expect(Documents::DocumentsHandler).to receive(:new).with({
          documentable_type: 'phone_activations',
          documentable_id:   activation.id,
          context:           'upload'
        })
        expect(handler).to receive(:prepare_documents)
        expect(handler).to receive(:create_documents).with(current_user_id: user.id, app_version: 'iOS 1.0.1')

        expect(activation).to receive(:refresh_views_after_operation).with(12, false)

        subject
      end

      context 'with a not reserved nor recovered operation' do
        let(:operation_outcome) { nil }

        it 'refreshes the view with the right parameter' do
          expect(activation).not_to receive(:refresh_views_after_operation)

          subject
        end
      end

      context 'with a recovered operation' do
        let(:operation_outcome) { build(:operation_outcome, operation_id: 12, recovered_by_dealer: true) }

        it 'refreshes the view with the right parameter' do
          expect(activation).to receive(:refresh_views_after_operation).with(12, true)

          subject
        end
      end
    end
  end

  describe '::upload_document_file_and_create_document' do
    let(:user) { stub_model(User) }
    let(:doc_pages) { [double] }
    let(:doc_pages_names) { ['page_1.png', 'page_2.png'] }

    let(:args) do
      {
        documentable_id:   12,
        documentable_type: 'PhoneActivation',
        document_type:     'documents',
        context:           'upload',
        files:             doc_pages,
        current_user:      user,
        app_version:       'iOS 1.0.1'
      }
    end

    context 'success' do
      before do
        allow(described_class).to receive(:upload_document_file).and_return(obj:     { uploaded_file_names: doc_pages_names },
                                                                                   success: true,
                                                                                   message: 'Files uploaded correctly')
        allow(described_class).to receive(:create_document).and_return(success: true, message: 'Documenti caricati con successo!')
      end

      it 'calls upload_document_file and create_document in sequence' do
        expect(described_class).to receive(:upload_document_file).ordered.with(
          args.slice(:documentable_id, :documentable_type, :document_type, :context, :files)
        )
        expect(described_class).to receive(:create_document).ordered.with(
          args.slice(:documentable_id, :documentable_type, :document_type, :context, :current_user, :app_version)
        )

        expect(described_class.upload_document_file_and_create_document(args)).to eq(success: true, message: 'Documenti caricati con successo!')
      end
    end

    context 'failure in uploading files' do
      before do
        allow(described_class).to receive(:upload_document_file)
                                           .and_return(
                                             success: false,
                                             errors:  ['Hai caricato dei file non supportati']
                                           )
      end

      it 'does not call for the document creation and reports the error' do
        expect(described_class).to receive(:upload_document_file).ordered.with(
          args.slice(:documentable_id, :documentable_type, :document_type, :context, :files)
        )
        expect(described_class).not_to receive(:create_document)

        expect(described_class.upload_document_file_and_create_document(args)).to eq(success: false, errors: ['Hai caricato dei file non supportati'])
      end
    end
  end

  context 'virtual document' do
    let(:phone_activation) { create(:phone_activation, :fissa_w3_no_product) }
    let(:user) { stub_model(User) }
    let(:args) do
      {
        documentable_id:   phone_activation.id,
        documentable_type: 'PhoneActivation',
        document_type:     'sim_card',
        context:           'upload',
        files:             [''],
        virtual:           true,
        current_user:      user
      }
    end

    subject { described_class.upload_document_file_as_string_and_create_document(args) }

    it do
      subject

      expect(phone_activation.reload.documents.count).to eq 1
      expect(phone_activation.reload.documents.last.kind).to eq 'sim_card'
      expect(phone_activation.reload.documents.last.virtual?).to be_truthy
    end
  end
end