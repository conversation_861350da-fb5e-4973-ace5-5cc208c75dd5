require "rails_helper"

describe DealerService do
  let(:signature_data) {
    { 'dealer' => { 'email' => '<EMAIL>', 'number' => '3333333333' } }
  }
  let(:dealer) { create(:dealer, signature_data: signature_data) }

  describe '#get_tender_letter' do
    let(:infoanag_response_xml) { File.open("./spec/fixtures/gamma_calls/dealer/infoanag.xml", "r") }
    let(:infoanag_response) { Nokogiri::XML(File.open("./spec/fixtures/gamma_calls/dealer/infoanag.xml", "r")) }
    let(:gamma_call) { GammaCall.new(response_xml: infoanag_response) }

    subject { described_class.new(dealer_id: dealer.id, gamma_response: gamma_call) }

    context "get tender letter pdf" do
      it do
        dealer.update_column(:tipo_gara, "Starter")
        path = "#{Rails.root.join("spec/fixtures/data/lettere_di_gara/")}"
        expect(described_class.new(dealer_id: dealer.id).get_tender_letter(path)).to be_an_instance_of(CombinePDF::PDF)
      end
    end
  end

  describe '#generate_mdv_affiliation_contract' do
    it do
      expect(described_class.new(dealer_id: dealer.id).generate_mdv_affiliation_contract).to be_an_instance_of(Tempfile)
    end
  end

  describe '#operator_pdf' do
    subject(:pdf) do
      described_class.new(dealer_id: dealer.id)
        .operator_pdf(Rails.root.join('spec/fixtures/data/lettere_di_gara/').to_s)
    end

    before { dealer.operators << operator }

    context 'when dealer has w3 operator' do
      let(:operator) { create(:operator, :w3, pdf_name: 'Gara-Tre.pdf') }

      it 'returns the operator pdf' do
        expect(pdf).to be_an_instance_of(File)
      end
    end

    context 'when dealer has very operator' do
      let(:operator) { create(:operator, :very_mobile, pdf_name: 'Ciao.pdf') }

      it 'returns the operator pdf' do
        expect(pdf).to be_an_instance_of(File)
      end
    end
  end
end
