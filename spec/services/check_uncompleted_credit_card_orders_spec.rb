require "rails_helper"

RSpec.describe CheckUncompletedCreditCardOrders do
  let(:unknown_outcome_payment) { build_stubbed(:hipay) }
  let(:repository)              { instance_double(Repositories::PaymentRepository, credit_card_with_unknown_outcome_in_last_three_weeks: [unknown_outcome_payment]) }
  let(:client)                  { instance_double(Hipay::Client::Tpp) }
  let(:hipay_service)           { HipayService.new(client) }

  subject { described_class.new(repository, hipay_service) }

  before { allow(client).to receive(:get).and_return nil }

  context "#payments_to_check" do
    let(:payments) { subject.payments_to_check }
    specify { expect(payments).to eq([unknown_outcome_payment]) }
  end

  context '#run' do
    it do
      subject.run
    end
  end

  # context '#process_payment' do
  #   let(:order)    { mock_model(Order) }
  #   let(:response) { double }
  #
  #   it 'STATUS_COMPLETE_FUNCTION' do
  #     allow(unknown_outcome_payment).to receive(:order).and_return order
  #     expect(unknown_outcome_payment).to receive(.update).with(transaction_details: response, payment_status: Payments::Hipay::PAYMENT_COMPLETE)
  #     expect(order).to receive(:payment_done!)
  #     described_class::STATE_COMPLETE_FUNCTION.call(unknown_outcome_payment, response)
  #   end
  # end
end
