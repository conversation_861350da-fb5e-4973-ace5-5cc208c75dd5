require "rails_helper"

describe AgentSmsService do
  subject { described_class.new(agent_smsable) }

  describe "#send" do
    let(:agent) { create(:user, :agent_wind_tre, :with_internal_user_detail) }
    let(:warehouse) { create(:warehouse, agent_wind_tre: agent) }
    let(:agent_smsable) { create(:order, warehouse: warehouse) }

    context "when the sms should be sent" do
      it "sends an sms to the warehouse WindTre agent" do
        expect(SmsSender).to receive_message_chain(:delay, :forward)
          .with("AgentSms", instance_of(Integer))

        expect { subject.send }.to change {
          agent.reload.agent_smses.where(
            agent_smsable: agent_smsable,
            phone_number:  Phoner::Phone.parse(agent.internal_user_detail.phone)
                             .format("%c%a%n"),
            sender_alias:  AgentSms::SENDER_ALIAS
          ).count
        }.by(1)
      end

      context "Error on sms saving" do
        let(:sms) { double }

        before do
          allow(AgentSms).to receive(:new).and_return sms
          allow(sms).to receive(:save!).and_return false
          allow(sms).to receive_message_chain(:errors, :full_messages)
            .and_return ["that's an error"]
        end

        it "does not send an sms and logs the error" do
          expect(SmsSender).not_to receive(:delay)
          expect(KolmeLogger).to receive(:error).with("that's an error")
          expect { subject.send }.not_to change { AgentSms.count }
        end
      end

      context "Unforseen error" do
        let(:error) { StandardError.new "nasty error" }

        before { allow(AgentSms).to receive(:new).and_raise error }

        it "does not send an sms and logs the error" do
          expect(SmsSender).not_to receive(:delay)
          expect(KolmeLogger).to receive(:error).with error
          expect { subject.send }.not_to change { AgentSms.count }
        end
      end
    end
  end
end