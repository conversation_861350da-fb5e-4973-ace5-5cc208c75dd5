require "rails_helper"

describe WindNgposActivations::ImeiReservationStrategies::CancelStrategy, stub_imei_reservation_asyncs: true do
  let(:activation) { create(:wind_ngpos_activation, terminal_amount: "189,90", entry_fee: "49,99") }
  let(:imei_reservation) { create(:imei_reservation) }

  before {
    imei_reservation.assign_attributes(shipment_first_name: '<PERSON>', shipment_last_name: '<PERSON><PERSON>', shipment_address_street_name: 'Via Roma')

    WindNgposActivation.skip_callback(:commit, :after, :handle_associated_imei_reservation, raise: false)
    activation.update(item_id: imei_reservation.item_id)
  }

  subject { described_class.new(activation) }

  context "#applicable?" do
    it { expect(subject.applicable?).to be_falsey }

    context "if activation has no associated dealer" do
      before { activation.update(item_id: imei_reservation.item_id, warehouse_id: nil) }

      it { expect(subject.applicable?).to be_falsey }
    end

    context "if activation has no associated reservation" do
      before { imei_reservation.expire! }

      it { expect(subject.applicable?).to be_falsey }
    end

    context "if dealers mismatch" do
      let(:dealer) { create(:dealer, name: "<PERSON><PERSON><PERSON>") }
      let!(:primary_contact) { create(:dealer_contact, dealer: dealer, primary_contact: true) }
      let(:warehouse) { create(:warehouse, name: "Fermi Warehouse", dealer: dealer) }

      before { activation.update(warehouse_id: warehouse.id) }

      it { expect(subject.applicable?).to be_truthy }
    end

    context "if gallery reservation with upfront" do
      let(:warehouse) { create(:warehouse, name: "Fermi Warehouse", dealer: imei_reservation.dealer) }

      before do
        activation.update(warehouse: warehouse)
        imei_reservation.update(kind: "gallery")
      end

      it { expect(subject.applicable?).to be_truthy }
    end
  end

  context "#execute" do
    let(:imei_reservation) { create(:imei_reservation) }

    before do
      activation.update(item_id: imei_reservation.item_id, warehouse_id: nil)
    end

    context "if dealers mismatch" do
      let(:dealer) { create(:dealer, name: "Fermi Srl") }
      let!(:primary_contact) { create(:dealer_contact, dealer: dealer, primary_contact: true) }
      let(:warehouse) { create(:warehouse, name: "Fermi Warehouse", dealer: dealer) }

      before { activation.update(warehouse_id: warehouse.id) }

      it "sends a notification email" do
        expect { subject.execute }.to change {
          WindTreDatasImporterMailer.deliveries.select do |email|
            email.subject == "Attivazione POS Evo #{activation.id} con partner diverso da quello presente nella prenotazione seriale corrispondente"
          end.count
        }.by(1)
      end

      it 'cancel current reservation and create new one with warehouse of received activation' do
        expect(PrivatePub).to receive(:publish_to).with("/mismatching_dealer/#{imei_reservation.id}", anything)

        subject.execute

        expect(imei_reservation.reload.state).to eq 'canceled'
        expect(ImeiReservation.last.warehouse_id).to eq activation.warehouse_id
        expect(ImeiReservation.last.state).to eq 'sent_to_dhl'
        expect(imei_reservation.item.reload.state).to eq 'sold'
      end
    end

    context "if gallery reservation with upfront" do
      let(:warehouse) { create(:warehouse, name: "Fermi Warehouse", dealer: imei_reservation.dealer) }

      before do
        activation.update(warehouse: warehouse)
        imei_reservation.update(kind: "gallery")
      end

      it do
        expect { subject.execute }.not_to raise_error
        expect { subject.execute }.to change { ImeiReservationMailer.deliveries.count }.by(1)
        expect { subject.execute }.to change { Alert.count }.by(1)
        expect(PrivatePub).to receive(:publish_to).with("/notify_gallery_with_upfront_reservation/#{imei_reservation.id}", anything)

        subject.execute

        expect(imei_reservation.reload.state).to eq 'canceled'
        expect(imei_reservation.gallery_with_upfront).to be_truthy
        expect(imei_reservation.item.reload.state).to eq 'reserved'
        expect(imei_reservation.item.notes).to eq "[#{Time.now.strftime('%d/%m/%Y')}] Seriale legato a una Lok-Me Gallery e transitato nel flusso con un anticipo diverso da 0."
      end
    end
  end
end
