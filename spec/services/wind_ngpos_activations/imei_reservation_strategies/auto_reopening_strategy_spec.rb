require "rails_helper"

describe WindNgposActivations::ImeiReservationStrategies::AutoReopeningStrategy, stub_imei_reservation_jobs: true do
  let(:imei_reservation) { create(:imei_reservation, state: "canceled") }
  let(:activation) do
    create(:wind_ngpos_activation,
            warehouse: imei_reservation.warehouse,
            item: imei_reservation.item,
            system_registered_at: Time.now)
  end

  before { WindNgposActivation.skip_callback(:commit, :after, :handle_associated_imei_reservation, raise: false) }
  before { imei_reservation.item.update_attribute(:state, "instock") }
  subject { described_class.new(activation) }

  context "#excute" do
    context "success" do
      before { expect(Jobs::DhlDeliveryOrderExporterJob).to receive_message_chain(:new, :perform) }
      before { subject.execute }

      it { expect(imei_reservation.reload.sent_to_dhl?).to be_truthy }
      it { expect(activation.item.reload.sold?).to be_truthy }
      it { expect(activation.item.reload.sale_amount).not_to be_nil }
      it { expect(activation.item.reload.activated_at).not_to be_nil }
      it { expect(activation.item.reload.customer_name).not_to be_nil }
    end

    context "error" do
      let(:service_instance) { double }
      let(:response) { double }

      before do
        allow(ImeiReservationItemService).to receive(:new).and_return service_instance
        allow(service_instance).to receive(:autoconfirm_reservation)
        allow(service_instance).to receive(:response).and_return response
        allow(response).to receive(:error).and_return StandardError.new "autoconfirmation error"
      end

      before { expect(WindTreDatasImporterMailer).to receive_message_chain(:autoconfirm_error, :deliver_now) }
      it { subject.execute }
    end
  end
end