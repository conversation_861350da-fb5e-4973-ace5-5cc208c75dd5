require "rails_helper"

describe ShipmentService do
  let(:sender) { double }

  context "ship!" do
    let(:gsped_ws) { double }

    subject { described_class.new(shipment, sender, gsped_ws: gsped_ws) }

    context "GSped shipment" do
      let(:shipment) { stub_model(Shipment, gsped: true) }

      it "makes a request to GSped web service" do
        expect(gsped_ws).to receive(:post_shipment)

        subject.ship!
      end
    end

    context "non-GSped shipment" do
      let(:shipment) { stub_model(Shipment) }

      it "does not make a request to GSped web service" do
        expect(gsped_ws).not_to receive(:post_shipment)

        subject.ship!
      end
    end
  end
end