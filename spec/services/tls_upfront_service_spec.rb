require 'rails_helper'

describe TlsUpfrontService do
  let(:tls_upfront) { build_stubbed(:tls_upfront) }

  subject { described_class.new(tls_upfront) }

  context '#cancel_payment' do
    let(:payment) { stub_model(Payment) }
    let(:hipay_payment) { build_stubbed(:hipay, payment: payment) }
    let(:gateway) { double }

    before do
      payment.payment_implementation = hipay_payment
      tls_upfront.payments = [payment]
    end

    it 'does not call hipay if reference is missing' do
      expect(HipayService).not_to receive(:new)
      subject.cancel_payment
    end

    it 'call hipay with reference' do
      hipay_payment.transaction_reference = '800123123'

      expect(HipayService).to receive_message_chain(:new, :cancel_payment!)
      subject.cancel_payment
    end
  end
end
