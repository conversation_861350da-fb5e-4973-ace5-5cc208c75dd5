require "rails_helper"

describe DelayedJobService do

  context "#sync_dealer_with_gamma(dealer_id)" do
    context "test" do
      it { expect(described_class.sync_dealer_with_gamma(1)).to be_truthy }
    end

    xcontext "production" do
      before { expect(SendDealerToGammaJob).to receive_message_chain(:new, :delay, :run).with 1 }

      it { described_class.sync_dealer_with_gamma(1) }
    end
  end
end
