require "rails_helper"

describe OrderFulfillment::Utils::AttivaOrderDecorator do
  include IntegrationHelpers

  before do
    stub_geocoding_requests
    setup_default_enviroment
    order.item_orders.create(item_id:               warehouse_item.id,
                             shopping_cart_item_id: shopping_cart_item_second.id,
                             fulfill_id:            fulfill.id,
                             serial:                serial)
    shopping_cart.shopping_cart_items << shopping_cart_item
    shopping_cart.shopping_cart_items << shopping_cart_item_second
    shopping_cart.save # to trigger ShoppingCart#set_dealer_data callback
  end

  let(:dealer) do
    create(:dealer)
  end

  let(:warehouse) { create(:warehouse) }
  let(:user) { create(:user, :dealer_user) }
  let(:kolme_warehouse) { create(:warehouse, :system_owner_warehouse) }
  let(:attiva_brand) { create(:attiva_brand) }
  let(:attiva_category) { create(:attiva_category) }
  let(:attiva_subcategory) { create(:attiva_subcategory) }
  let(:company_brand) { create(:company_brand) }
  let(:product_code) { "BLK0147A1" }
  let(:product) do
    create(:product,
           :attiva_imported,
           code:               product_code,
           attiva_brand:       attiva_brand,
           attiva_category:    attiva_category,
           attiva_subcategory: attiva_subcategory,
           company_brand:      company_brand,
           gamma_purchase_price: 11.47)
  end

  let(:warehouse_item) { create(:item, :cable, :in_system_owner_warehouse, product: product) }
  let(:shopping_cart) { create(:shopping_cart, :with_dealer) }
  let(:shopping_cart_item) { create(:shopping_cart_item, :recharge, owner: shopping_cart) }
  let(:shopping_cart_item_second) { create(:shopping_cart_item, item: warehouse_item.product, owner: shopping_cart, quantity: 12) }
  let(:fulfill) { order.get_fulfill }
  let(:serial) { "785612862118758" }
  let(:order) { create(:order, :paid, warehouse: warehouse, shopping_cart: shopping_cart, dealer: dealer, user: user) }
  let(:afghanistan) { create(:country, :afghanistan) }

  let(:fake_attributes) do
    {
      order_id:         order.id,
      ordered_at:       Date.today.strftime("%d/%m/%Y"),
      siae_price:       true,
      company_address:  "Via Alessandro Maragliano, 10",
      company_name:     "Hardware & Servizi",
      company_zip_code: "27058",
      company_city:     "Milano",
      company_province: "MI",
      country:          "IT",
      contact_name:     nil,
      phone_number:     "03831912140",
      email:            nil,
      rows:             [
                          {
                            item_code:        product_code,
                            item_description: "CAVO MONITOR VGA/VGA MASCHIO MASCHIO 3 M NERO",
                            row_number:       1,
                            quantity:         12,
                            price:            "11.47"
                          }
                        ]
    }
  end

  describe "#attributes" do
    let(:order_decorator) { described_class.new(order) }
    subject { order_decorator.attributes }

    it "returns filled hash" do
      expect(subject).to eq fake_attributes
    end
  end

  describe "#attributes_for_foreign_dealer" do
    before do
      warehouse.update_column(:city_id, nil)
      warehouse.update_column(:foreign_city, "Kabul")
      warehouse.update_column(:country_id, afghanistan.id)
    end

    let(:order_decorator) { described_class.new(order) }
    subject { order_decorator.attributes }

    it "returns filled hash" do
      expect(subject[:company_province]).to eq Province::FOREIGN_CODE
      expect(subject[:company_city]).to eq "Kabul"
    end
  end

  describe "#payment_method_list" do
    context "kolme" do
      it { expect(order.payment_method_list(only_cc = false)).to eq [["Bonifico Bancario Anticipato (tradizionale con caricamento dell'evidenza contabile)", "bonifico", {}]] }
      it { expect(order.payment_method_list(only_cc = true)).to be_empty }

      context 'order without recharges' do
        before { shopping_cart_item.destroy }

        it { expect(order.payment_method_list(only_cc = false)).to eq [
                                                                        ['Bonifico Bancario Anticipato (tradizionale con caricamento dell\'evidenza contabile)', 'bonifico', {}],
                                                                        ['MyBank - Bonifico Bancario Immediato', 'mybank', {}],
                                                                        ['Visa', 'visa', {}],
                                                                        ['Mastercard', 'mastercard', {}],
                                                                        ['American Express', 'american-express', {}],
                                                                        ['SisalPay', 'sisal', {}]
                                                                      ] }
        it { expect(order.payment_method_list(only_cc = true)).to eq [
                                                                        ['MyBank - Bonifico Bancario Immediato', 'mybank', {}],
                                                                        ['Visa', 'visa', {}],
                                                                        ['Mastercard', 'mastercard', {}],
                                                                        ['American Express', 'american-express', {}],
                                                                        ['SisalPay', 'sisal', {}]
                                                                      ] }
      end
    end

    context 'drop pay' do
      let!(:drop_pay_account) { create(:drop_pay_account, :enabled_and_active, dealer: dealer) }
      let(:client) { double }

      before {
        expect(Atono::Client).to receive(:new).and_return client
        allow(client).to receive_message_chain(:balance, :body).and_return('{ "balance_available": 1000 }')
      }

      context 'order with recharges' do
        it {
          expect(order.payment_method_list(only_cc = false)).to eq [
                                                                     ['Addebito immediato Drop Pay', 'drop_pay', {}],
                                                                     ['Bonifico Bancario Anticipato (tradizionale con caricamento dell\'evidenza contabile)', 'bonifico', {}]
                                                                   ] }

        it {
          expect(order.payment_method_list(only_cc = true)).to eq [
                                                                     ['Addebito immediato Drop Pay', 'drop_pay', {}]
                                                                  ] }
      end

      context 'order without recharges' do
        before { shopping_cart_item.destroy }

        it {
          expect(order.payment_method_list(only_cc = false)).to eq [
                                                                     ['Addebito immediato Drop Pay', 'drop_pay', {}],
                                                                     ['Bonifico Bancario Anticipato (tradizionale con caricamento dell\'evidenza contabile)', 'bonifico', {}],
                                                                     ['MyBank - Bonifico Bancario Immediato', 'mybank', {}],
                                                                     ['Visa', 'visa', {}],
                                                                     ['Mastercard', 'mastercard', {}],
                                                                     ['American Express', 'american-express', {}],
                                                                     ['SisalPay', 'sisal', {}]
                                                                   ] }

        it {
          expect(order.payment_method_list(only_cc = true)).to eq [
                                                                     ['Addebito immediato Drop Pay', 'drop_pay', {}],
                                                                     ['MyBank - Bonifico Bancario Immediato', 'mybank', {}],
                                                                     ['Visa', 'visa', {}],
                                                                     ['Mastercard', 'mastercard', {}],
                                                                     ['American Express', 'american-express', {}],
                                                                     ['SisalPay', 'sisal', {}]
                                                                   ] }
      end
    end
  end

  describe "#status_color" do
    let(:order) { stub_model(Order) }

    it "returns 'progress-bar-warning' for suitable order states" do
      order.aasm_state = :to_pay
      expect(order.status_color).to eq "progress-bar-warning"
      order.aasm_state = :awaiting_cc_confirmation
      expect(order.status_color).to eq "progress-bar-warning"
      order.aasm_state = :to_pay_credit_card
      expect(order.status_color).to eq "progress-bar-warning"
      order.aasm_state = :awaiting_confirmation
      expect(order.status_color).to eq "progress-bar-warning"
      order.aasm_state = :paid
      expect(order.status_color).to eq "progress-bar-warning"
      order.aasm_state = :partially_processed
      expect(order.status_color).to eq "progress-bar-warning"
      order.aasm_state = :waiting_for_dhl
      expect(order.status_color).to eq "progress-bar-warning"
    end

    it "returns 'progress-bar-success' for :processed state" do
      order.aasm_state = :processed
      expect(order.status_color).to eq "progress-bar-success"
    end

    it "returns an empty string for suitable order states" do
      order.aasm_state = :cancelled
      expect(order.status_color).to eq ""
      order.aasm_state = :void
      expect(order.status_color).to eq ""
    end

    it "returns 'progress-bar-danger' for :payment_failed state" do
      order.aasm_state = :payment_failed
      expect(order.status_color).to eq "progress-bar-danger"
    end

    it "returns 'progress-bar-info' for any other state" do
      order.aasm_state = :address
      expect(order.status_color).to eq "progress-bar-info"
    end
  end

  describe "#progressbar_value" do
    let(:order) { stub_model(Order) }

    it "returns 10 for :credit_card state" do
      order.aasm_state = :credit_card
      expect(order.progressbar_value).to eq 10
    end

    it "returns 10 for :to_pay_credit_card state" do
      order.aasm_state = :to_pay_credit_card
      expect(order.progressbar_value).to eq 10
    end

    it "returns 20 for :to_pay and :awaiting_cc_confirmation states" do
      order.aasm_state = :to_pay
      expect(order.progressbar_value).to eq 20
      order.aasm_state = :awaiting_cc_confirmation
      expect(order.progressbar_value).to eq 20
    end

    it "returns 40 for :awaiting_confirmation state" do
      order.aasm_state = :awaiting_confirmation
      expect(order.progressbar_value).to eq 40
    end

    it "returns 50 for :paid state" do
      order.aasm_state = :paid
      expect(order.progressbar_value).to eq 50
    end

    it "returns 75 for :waiting_for_dhl state" do
      order.aasm_state = :waiting_for_dhl
      expect(order.progressbar_value).to eq 75
    end

    it "returns 80 for :partially_processed state" do
      order.aasm_state = :partially_processed
      expect(order.progressbar_value).to eq 80
    end

    it "returns 100 for :processed state" do
      order.aasm_state = :processed
      expect(order.progressbar_value).to eq 100
    end

    it "returns 0 for any other state" do
      order.aasm_state = :cancelled
      expect(order.progressbar_value).to eq 0
      order.aasm_state = :payment_failed
      expect(order.progressbar_value).to eq 0
      order.aasm_state = :address
      expect(order.progressbar_value).to eq 0
    end
  end
end