require 'rails_helper'

describe FloaFinancingService do
  let(:floa_financing) { create(:floa_financing) }

  subject { described_class.new(floa_financing) }

  context '#set_valid_prices' do
    let!(:additional_product) { create(:floa_financing_product, :additional_product, floa_financing: floa_financing) }

    before { allow(floa_financing).to receive(:build_floa_commissions).and_return 5 }

    it do
      floa_financing.product.update_columns(dealer_price_vat_excluded_a: 255)
      expect(floa_financing.errors).to be_empty

      subject.set_valid_prices
      expect(floa_financing.errors).to be_empty
      expect(floa_financing.amount).to eq 510
      expect(floa_financing.delta).to be_zero
      expect(floa_financing.profit).to eq 137.53

      floa_financing.delta = 200
      subject.set_valid_prices
      expect(floa_financing.errors).to be_empty
      expect(floa_financing.amount).to eq 710
      expect(floa_financing.delta).to eq 200
      expect(floa_financing.profit).to eq 291.47

      floa_financing.delta = 2500
      subject.set_valid_prices
      expect(floa_financing.errors).to be_empty
      expect(floa_financing.amount).to eq 3000
      expect(floa_financing.delta).to eq 2490
      expect(floa_financing.profit).to eq 2054.02

      #lower_zero_profit_amount_limit profit
      floa_financing.delta = -300
      subject.set_valid_prices
      expect(floa_financing.errors).to be_empty
      expect(floa_financing.amount).to eq 331.31
      expect(floa_financing.delta).to eq -178.69
      expect(floa_financing.profit).to be_zero

      #lower_generic_amount_limit
      floa_financing.product.update_columns(credit_note_amount: 600)
      floa_financing.delta = -430
      subject.set_valid_prices
      expect(floa_financing.errors).to be_empty
      expect(floa_financing.amount.to_f).to eq 82.53
      expect(floa_financing.delta).to eq -427.47
      expect(floa_financing.profit).to eq 408.52
    end

    context 'add imei_reservation_shipping_cost for financing with imei_reservation' do
      let!(:imei_reservation_shipping_cost_setting) { create(:application_setting, :imei_reservation_shipping_cost) }

      before { allow(floa_financing).to receive(:from_system_owner_warehouse?).and_return true }

      it do
        subject.set_valid_prices
        expect(floa_financing.errors).to be_empty
        expect(floa_financing.amount).to eq 510
        expect(floa_financing.delta).to be_zero
        expect(floa_financing.profit).to eq 126.63
  
        floa_financing.delta = 200
        subject.set_valid_prices
        expect(floa_financing.errors).to be_empty
        expect(floa_financing.amount).to eq 710
        expect(floa_financing.delta).to eq 200
        expect(floa_financing.profit).to eq 280.57

        floa_financing.delta = 2500
        subject.set_valid_prices
        expect(floa_financing.errors).to be_empty
        expect(floa_financing.amount).to eq 3000
        expect(floa_financing.delta).to eq 2490
        expect(floa_financing.profit).to eq 2043.12

        #lower_zero_profit_amount_limit profit
        floa_financing.delta = -300
        subject.set_valid_prices
        expect(floa_financing.errors).to be_empty
        expect(floa_financing.amount).to eq 345.47
        expect(floa_financing.delta).to eq -164.53
        expect(floa_financing.profit).to be_zero

        #lower_generic_amount_limit
        floa_financing.product.update_columns(credit_note_amount: 600)
        floa_financing.delta = -430
        subject.set_valid_prices
        expect(floa_financing.errors).to be_empty
        expect(floa_financing.amount.to_f).to eq 82.29
        expect(floa_financing.delta).to eq -427.71
        expect(floa_financing.profit).to eq 397.44
      end
    end

    context 'additional product prices must be below 25% of primary product price' do
      it do
        additional_product.update_columns(price: 126)

        subject.set_valid_prices
        expect(floa_financing.errors).not_to be_empty
      end
    end
  end

  context '#process_financing_response(response)' do
    before do
      floa_financing.update_columns(status: 'waiting')
      floa_financing.item.update_columns(state: 'reserved')
    end

    context 'success response' do
      let(:response) { { returnCode: '0' } }

      it do
        expect(PrivatePub).to receive(:publish_to).with("/floa_financings/#{floa_financing.id}/payment",
                                                        "location.href = '/floa_financings/payment_success'")
        expect(PrivatePub).to receive(:publish_to)
        expect(floa_financing).to receive(:create_automatic_invoice)

        subject.process_financing_response(response)
        expect(floa_financing.response).to eq response.as_json
        expect(floa_financing.status).to eq 'accepted'
        expect(floa_financing.return_code_label).to eq 'Successful'
      end

      context 'already processed' do
        let(:response) { { returnCode: '0' } }
        let(:previous_response) { 'previous response' }
  
        before { floa_financing.update_columns(status: 'accepted', response: previous_response) }
  
        it 'not to update anything' do
          expect(KolmeLogger).to receive(:info)
          expect(FloaFinancingMailer).not_to receive(:notify_payment_on_rejected)
          expect(PrivatePub).to receive(:publish_to).with("/floa_financings/#{floa_financing.id}/payment",
                                                          "location.href = '/floa_financings/payment_success'")
  
          subject.process_financing_response(response)
          expect(floa_financing.response).to eq previous_response
          expect(floa_financing.status).to eq 'accepted'
        end
      end

      context 'with already expired imei_reservation' do
        let!(:expired_imei_reservation) { create(:imei_reservation, origin: floa_financing, created_at: 2.hours.ago) }

        context 'not already marked as expired' do
          it do
            expect_any_instance_of(ImeiReservation).to receive(:send_termination_message)
            expect(FreeShippingService).to receive_message_chain(:new, :add_to)
            expect_any_instance_of(ImeiReservation).to receive(:schedule_shipment)
            expect(floa_financing).to receive(:create_automatic_invoice)
            expect(floa_financing).to receive(:update_list)
            expect(PrivatePub).to receive(:publish_to).with("/floa_financings/#{floa_financing.id}/payment", "location.href = '/floa_financings/payment_success'")

            subject.process_financing_response(response)
            expect(floa_financing.reload.status).to eq 'accepted'
            expect(expired_imei_reservation.reload.state).to eq 'confirmed'
            expect(expired_imei_reservation.item.reload.state).to eq 'sold'
          end
        end

        context 'already marked as expired' do
          before do
            floa_financing.update_columns(status: 'rejected', response: { returnCode: '1' })
            expired_imei_reservation.update_columns(state: 'expired')
          end

          it do
            expect(PrivatePub).to receive(:publish_to).with("/floa_financings/#{floa_financing.id}/payment", "location.href = '/floa_financings/payment_failed'")
            expect(FloaFinancingMailer).to receive_message_chain(:notify_payment_on_rejected, :deliver_now)

            subject.process_financing_response(response)
          end
        end
      end
    end

    context 'failed response' do
      let!(:imei_reservation) { create(:imei_reservation, origin: floa_financing, item: floa_financing.item) }
      let(:response) { { returnCode: '1' } }

      before do
        floa_financing.item.update_columns(state: 'booked')
        allow_any_instance_of(Floa::Client).to receive(:get_order)
      end

      it do
        expect(PrivatePub).to receive(:publish_to).with("/floa_financings/#{floa_financing.id}/payment",
                                                        "location.href = '/floa_financings/payment_failed'")
        expect(PrivatePub).to receive(:publish_to).with('/floa_financings', 'window.App.FloaFinancings.reloadLists()')
        expect(PrivatePub).to receive(:publish_to).with('/trigger_countdown_stop',
                                                        "window.App.ImeiReservationRowCoundownStopper.stopCountdown(#{imei_reservation.id});")

        subject.process_financing_response(response)
        expect(floa_financing.response).to eq response.as_json
        expect(floa_financing.reload.status).to eq 'rejected'
        expect(floa_financing.return_code_label).to eq 'Refused'
        expect(imei_reservation.reload.state).to eq 'canceled'
        expect(floa_financing.item.state).to eq 'instock'
      end

      context 'with floa status already paid' do
        let(:client) { double }
        let(:eligibility_tokens) { ['a8bc586f-24df-4ef3-ab84-c597361cb157', '89e15a8f-9e8e-48a8-bc06-679f56d34e39'] }
        let(:paid_response) { '{"paymentState": "Payed"}' }

        before { floa_financing.update_columns(eligibility_tokens: eligibility_tokens) }
        before { allow(Floa::Client).to receive(:new).and_return(client) }
        before { allow(client).to receive(:get_order).with(floa_financing, eligibility_tokens.first).and_raise(RestClient::NotFound) }
        before { allow(client).to receive(:get_order).with(floa_financing, eligibility_tokens.second).and_return paid_response }

        it do
          expect(KolmeLogger).to receive(:info)
          expect(FloaFinancingMailer).not_to receive(:notify_payment_on_rejected)
          expect(FloaFinancingMailer).to receive_message_chain(:reject_paid, :deliver_now)

          subject.process_financing_response(response)

          expect(floa_financing.reload.status).to eq 'waiting'
          expect(imei_reservation.reload.state).to eq 'requested'
          expect(floa_financing.item.state).to eq 'reserved'
          expect(KolmeLogger).not_to receive(:error)
        end
      end
    end
  end

  context '#send_to_customer' do
    let(:sms_client) { double }
    let(:jwt_token) { 'dummy_jwt_token' }

    it do
      expect(SecureRandom).to receive_message_chain(:hex).and_return jwt_token
      expect(SmsSender).to receive(:direct_forward).with({
        body: "A questo indirizzo puoi accettare le condizioni ed effettuare il pagamento della tua vendita a rate https://localhost/fin/#{jwt_token}",
        from: 'Kolme',
        to:   '393331476622'
      }, true)

      subject.send_to_customer
      expect(floa_financing.customer_token).to eq jwt_token
      expect(floa_financing.customer_token_sent_at).not_to be_nil
    end
  end

  context '#process_financing' do
    let(:floa_client) { double }
    let(:eligibility_token) { 'd7e29f9b-83d0-4ed0-98b7-ebd564027d4e' }
    let(:eligibility_response) { {
                                   body: {
                                     eligibilities: [{hasAgreement: true, token: eligibility_token}]
                                   },
                                   code: 200
                                 }.with_indifferent_access
                               }
    let(:payment_link) { 'http://payment_link' }
    let(:payment_link_response) { {
                                    body: {
                                      paymentLinkUrl: payment_link
                                    },
                                    code: 201
                                  }.with_indifferent_access
                                }

    before do
      floa_financing.update_columns(status: 'waiting')
      floa_financing.floa_financing_product.item.reserve!

      allow(PrivatePub).to receive :publish_to
      allow(Floa::Client).to receive(:new).and_return floa_client
      allow(floa_client).to receive(:get_order)
      expect(floa_client).to receive(:eligibility).and_return Floa::EligibilityResponse.new(eligibility_response)
    end

    it do
      expect(floa_client).to receive(:payment_link).and_return Floa::PaymentLinkResponse.new(payment_link_response)

      result = subject.process_financing
      expect(result).to eq result
    end

    context 'with ineligible response' do
      it do
        eligibility_response[:body][:eligibilities][0][:hasAgreement] = false

        expect { subject.process_financing }.to raise_error(Exceptions::FloaIneligibleException)
        expect(floa_financing.rejected?).to be_truthy
      end

      context 'with "NationalId is invalid" error' do
        let(:eligibility_response) { {
                                       body: {
                                         eligibilities: [{ errors: ['NationalId is invalid.'] }]
                                       },
                                       code: 200
                                     }.with_indifferent_access
                                   }

        it do
          expect { subject.process_financing }.to raise_error(Exceptions::FloaIneligibleException)
          expect(floa_financing.rejected?).to be_truthy
        end
      end
    end

    context 'with invalid responses' do
      before { eligibility_response[:code] = 500 }

      it do
        expect { subject.process_financing }.to raise_error(StandardError)
        expect(floa_financing.rejected?).to be_falsey
        expect(floa_financing.eligibility_tokens).to be_nil
      end
    end
  end

  describe '#paid?' do
    let(:paid_response) { '{"paymentState": "Payed"}' }

    before { allow_any_instance_of(Floa::Client).to receive(:get_order).and_return paid_response }

    it 'return false if no eligibiliy_tokes present' do
      expect(subject.paid?).to be_falsey

      floa_financing.update_columns(eligibility_tokens: [])
      expect(subject.paid?).to be_falsey

      floa_financing.update_columns(eligibility_tokens: ['f43fed21-f20e-4851-8ae1-ebf7662c5f18'])
      expect(subject.paid?).to be_truthy
    end
  end
end
