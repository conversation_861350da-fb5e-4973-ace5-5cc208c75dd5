require 'rails_helper'

describe CoverageCheck::ProductsService do
  let(:response) do
    {
      'data'       =>
                      {
                        'noCoverageSite'      => false,
                        'fwa'                 => false,
                        'ftth25gb'            => false,
                        'addressProvisioning' => false,
                        'products'            =>
                                                 [{ 'technologyType'     => 'FTTC_200',
                                                    'technologyDesc'     => 'FTTC 200',
                                                    'dataPriceList'      => 'PINF190',
                                                    'voipPriceList'      => 'VOIPONEB1_CNS',
                                                    'bundle'             => 'VOIPONEB1_CNS-PINF190',
                                                    'options'            =>
                                                                            ['LISAPP744',
                                                                             'ROPZ5055'],
                                                    'services'           => ['STS01', 'STS02', 'STS08'],
                                                    'device'             => ['LISAPP740', 'LISAPP745'],
                                                    'popIdbuilding'      => '',
                                                    'tecnologiaApparato' => '',
                                                    'provider'           => '',
                                                    'edr'                => '',
                                                    'voce'               => false,
                                                    'adsl'               => false,
                                                    'voip'               => true,
                                                    'fttc'               => false,
                                                    'fttc200'            => false,
                                                    'fttcNGA'            => false,
                                                    'fttcNGA200'         => false,
                                                    'ftts'               => false,
                                                    'ftts200'            => false,
                                                    'ftthFW'             => false,
                                                    'ftthOS'             => false,
                                                    'ftth'               => false,
                                                    'velocitaUp'         => '50',
                                                    'velocitaDown'       => '300',
                                                    'velocitaMinUp'      => '256 kbps',
                                                    'velocitaMinDown'    => '256 kbps',
                                                    'latenza'            => '70 ms',
                                                    'fibrapass'          => false },
                                                  { 'technologyType'     => 'FWA_OUT_5G_NETFLIX',
                                                    'technologyDesc'     => 'FWA 5G',
                                                    'dataPriceList'      => 'PINF190',
                                                    'voipPriceList'      => 'VOIPNTX_CNS',
                                                    'bundle'             => 'VOIPNTX_CNS-PINF190',
                                                    'options'            =>
                                                                            ['LISAPP744',
                                                                             'ROPZ5055',
                                                                             'ROPZ5099'],
                                                    'services'           => ['STS01', 'STS02', 'STS08'],
                                                    'device'             => ['LISAPP740', 'LISAPP745'],
                                                    'popIdbuilding'      => '',
                                                    'tecnologiaApparato' => '',
                                                    'provider'           => '',
                                                    'edr'                => '',
                                                    'voce'               => false,
                                                    'adsl'               => false,
                                                    'voip'               => true,
                                                    'fttc'               => false,
                                                    'fttc200'            => false,
                                                    'fttcNGA'            => false,
                                                    'fttcNGA200'         => false,
                                                    'ftts'               => false,
                                                    'ftts200'            => false,
                                                    'ftthFW'             => false,
                                                    'ftthOS'             => false,
                                                    'ftth'               => false,
                                                    'velocitaUp'         => '50',
                                                    'velocitaDown'       => '300',
                                                    'velocitaMinUp'      => '256 kbps',
                                                    'velocitaMinDown'    => '256 kbps',
                                                    'latenza'            => '70 ms',
                                                    'fibrapass'          => false }]
                      },
      'status'     => 'OK',
      'errorCodes' => [],
      'messages'   => []
    }
  end

  before { allow(W3::Client).to receive(:products).and_return response }
  let(:params) do
    {
      'addressName' => 'ALBONA', 'addressType' => 'VIA', 'city' => 'MILANO',
      'codeToponymic' => '15_146_25462_10', 'istatCode' => '03015146',
      'market' => 'CONSUMER', 'province' => 'MI', 'streetNumber' => '10', 'zipCode' => '20100',
      'tipoRichiesta' => 'PAM', 'egonAddress' => '38000093558', 'egonStreetNumber' => '380100012723565'
    }
  end
  subject { described_class.new(params.merge(market: 'CONSUMER')).fetch }

  context 'with success response' do
    let!(:product_category) { create(:product_category, :fissa) }

    context 'add phone activation params' do
      context 'when the coverage technology is available' do
        let!(:available_plan) { create(:plan, :eolo_casa, coverage_typology: 'FTTC 200 value') }
        let!(:fttc_200) { create(:coverage_technology, service_value: 'FTTC_200', coverage_typology: 'FTTC 200 value') }

        it do
          expect(subject.count).to eq 2
          expect(subject.map { |p| p['technologyType'] }).to eq ['FTTC_200', 'FWA_OUT_5G_NETFLIX']
          expect(subject.map { |p| p['technologyDesc'] }).to eq ['FTTC 200', 'FWA 5G']
          expect(subject.first.fetch(:phone_activation_params)).to eq({
                                                                        product_category_id: product_category.id,
                                                                        coverage_typology:   'FTTC 200 value',
                                                                        customer_kind_group: 'Consumer',
                                                                        accessory_data:      { address: 'ALBONA', sType: 'VIA', city: 'MILANO', number: '10', zip: '20100' }
                                                                      })

          expect(subject.last.fetch(:phone_activation_params)).to be_blank
        end
      end

      context 'when the coverage technology is not available' do
        let!(:fttc_300) { create(:coverage_technology, service_value: 'FTTC_300', coverage_typology: 'FTTC 200 value') }

        it do
          expect(subject.last.fetch(:phone_activation_params)).to be_blank
        end
      end
    end

    context 'exclude hidden coverage technologies' do
      let!(:hidden_coverage_technology) { create(:coverage_technology, service_value: 'VOCE', visible: false) }

      before { response['data']['products'][0]['technologyType'] = 'VOCE' }

      it do
        expect(subject.count).to eq 1
        expect(subject.map { |p| p['technologyType'] }).to eq ['FWA_OUT_5G_NETFLIX']
      end
    end

    context 'merge CoverageTechnology models' do
      let!(:coverage_technology) { create(:coverage_technology) }

      it do
        expect(subject.first[:coverage_technology]).to be_nil
        expect(subject.second[:coverage_technology]).to eq coverage_technology
      end
    end

    context 'row with two technologyType values' do
      before do
        response['data']['products'][0]['technologyType'] = 'ADSL|FTTC_200'
      end

      it do
        expect(subject.count).to eq 3

        expect(subject.map { |p| p['technologyType'] }).to eq ['ADSL', 'FTTC_200', 'FWA_OUT_5G_NETFLIX']
      end
    end

    context 'filter NGA when FWA exists for Consumer market' do
      let!(:fwa_coverage) { create(:coverage_technology, service_value: 'FWA_OUT_5G_NETFLIX', value: 'FWA 5G') }
      let!(:nga_coverage) { create(:coverage_technology, service_value: 'FTTC_200', value: 'NGA 200') }

      before do
        response['data']['products'] = [
          response['data']['products'][0].merge('technologyType' => 'FTTC_200'),
          response['data']['products'][1].merge('technologyType' => 'FWA_OUT_5G_NETFLIX')
        ]
      end

      context 'when market is CONSUMER and FWA is present' do
        it 'filters out NGA products' do
          expect(subject.count).to eq 1
          expect(subject.map { |p| p['technologyType'] }).to eq ['FWA_OUT_5G_NETFLIX']
        end
      end

      context 'when market is MICROBUSINESS' do
        subject { described_class.new(params.merge(market: 'MICROBUSINESS')).fetch }

        it 'does not filter NGA products' do
          expect(subject.count).to eq 2
          expect(subject.map { |p| p['technologyType'] }).to eq ['FTTC_200', 'FWA_OUT_5G_NETFLIX']
        end
      end

      context 'when no FWA is present' do
        before do
          response['data']['products'] = [
            response['data']['products'][0].merge('technologyType' => 'FTTC_200'),
            response['data']['products'][0].merge('technologyType' => 'ADSL')
          ]
        end
        let!(:adsl_coverage) { create(:coverage_technology, service_value: 'ADSL', value: 'ADSL') }

        it 'does not filter NGA products' do
          expect(subject.count).to eq 2
          expect(subject.map { |p| p['technologyType'] }).to eq ['FTTC_200', 'ADSL']
        end
      end
    end
  end

  context 'with failed response' do
    let(:response) do
      {
        "status": 400,
        "error":  'Bad Request'
      }
    end

    it { expect { subject }.to raise_error StandardError }
  end
end
