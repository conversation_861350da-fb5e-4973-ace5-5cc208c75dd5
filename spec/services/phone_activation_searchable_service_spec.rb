require "rails_helper"
describe PhoneActivationSearchableService do
  let(:phone_activation) { stub_model(PhoneActivation) }
  subject { described_class.new(phone_activation) }

  context "#next_operation_is_pdc_check" do
    context "include PDC_CONFERMATA" do
      before { allow(phone_activation).to receive(:operation_ids).and_return [OperationConstants::UPLOAD_DOCUMENTI_COMPASS, OperationConstants::UPLOAD_DOCUMENTI, OperationConstants::PDC_CONFERMATA] }

      context "status_ko" do
        before { phone_activation.ko_at = Time.now }
        it { expect(subject.next_operation_is_pdc_check).to be_falsey }
      end

      context "next_operation_is(PDC_CONFERMATA)" do
        before { allow(phone_activation).to receive(:next_operation_is).with(OperationConstants::PDC_CONFERMATA).and_return true }
        it { expect(subject.next_operation_is_pdc_check).to be_truthy }
      end

      xcontext "last completed operations include PDC_CONFERMATA in status ko_r" do
        let(:operation_outcome) { stub_model(OperationOutcome, status: "ko_r", operation_id: OperationConstants::PDC_CONFERMATA) }
        before { allow(phone_activation).to receive_message_chain(:last_completed_operations, :where).with(operation_id: OperationConstants::PDC_CONFERMATA, status: "ko_r").and_return [operation_outcome] }
        before { allow(phone_activation).to receive(:next_operation_is).with(OperationConstants::PDC_CONFERMATA).and_return false }
        it { expect(subject.next_operation_is_pdc_check).to be_truthy }
      end

      xcontext "last completed operations include PDC_CONFERMATA in status ko" do
        let(:operation_outcome) { stub_model(OperationOutcome, status: "ko", operation_id: OperationConstants::PDC_CONFERMATA) }
        before { allow(phone_activation).to receive_message_chain(:last_completed_operations, :where).with(operation_id: OperationConstants::PDC_CONFERMATA, status: "ko_r").and_return [] }
        before { allow(phone_activation).to receive(:next_operation_is).with(OperationConstants::PDC_CONFERMATA).and_return false }
        it { expect(subject.next_operation_is_pdc_check).to be_falsey }
      end
    end

    context "phone_activation doesn't contains PDC_CONFERMATA" do
      before { allow(phone_activation).to receive(:operation_ids).and_return [OperationConstants::UPLOAD_DOCUMENTI_COMPASS, OperationConstants::UPLOAD_DOCUMENTI] }
      it { expect(subject.next_operation_is_pdc_check).to be_falsey }
    end
  end
end
