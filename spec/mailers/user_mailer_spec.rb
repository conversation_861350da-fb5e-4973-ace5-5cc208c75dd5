require 'rails_helper'

describe User<PERSON>ailer, type: :mailer do
  let(:sender)  { build(:user, :admin) }
  let(:contact) { build(:dealer_contact) }
  let(:agent_emails) { ['<EMAIL>', '<EMAIL>'] }

  context 'dealer_user_welcome' do
    let(:mail) { described_class.dealer_user_welcome(contact, 'abcd1234', agent_emails: agent_emails).deliver_now }

    it 'has the correct address fields' do
      expect(mail.from).to eq [MailAddressProvider.from]
      expect(mail.to).to eq [contact.email]
      expect(mail.cc).to be_nil
      expect(mail.bcc).to eq [MailAddressProvider.partner, '<EMAIL>', '<EMAIL>']
    end
  end

  context 'internal_user_welcome' do
    let(:mail) { described_class.internal_user_welcome(contact, 'abcd1234').deliver_now }

    it 'has the correct address fields' do
      expect(mail.from).to eq [MailAddressProvider.from]
      expect(mail.to).to eq [contact.email]
      expect(mail.cc).to be_nil
      expect(mail.bcc).to eq [MailAddressProvider.partner]
    end
  end
end
