require "rails_helper"

describe ProvisioningDatasImporterMailer, type: :mailer do

  context "#import_result" do
    before { ProvisioningDatasImporterMailer.deliveries.clear }
    let(:filename) { "file.csv" }
    let(:errors) { ['first error', 'second error'] }

    context 'success' do
      before { described_class.import_result(filename, true, []).deliver_now }
      it do
        mail = ProvisioningDatasImporterMailer.deliveries.last

        expect(mail.from).to eq [MailAddressProvider.from]
        expect(mail.to).to eq [MailAddressProvider.reporting, MailAddressProvider.it]

        expect(ProvisioningDatasImporterMailer.deliveries.last.subject).to include('Importazione Avanzamento provisioning fisso avvenuta con successo')
        expect(ProvisioningDatasImporterMailer.deliveries.last.body).to include("L'importazione del file file.csv è avvenuta con successo.")
      end
    end

    context 'errors' do
      before { described_class.import_result(filename, false, errors).deliver_now }
      it do
        mail = ProvisioningDatasImporterMailer.deliveries.last

        expect(mail.from).to eq [MailAddressProvider.from]
        expect(mail.to).to eq [MailAddressProvider.reporting, MailAddressProvider.it]

        expect(ProvisioningDatasImporterMailer.deliveries.last.subject).to include('Importazione Avanzamento provisioning fisso avvenuta con errori')
        expect(ProvisioningDatasImporterMailer.deliveries.last.body).to include("Importazione file <code>file.csv</code> avvenuta con errori.</p>")
      end
    end
  end
end
