require "rails_helper"

describe ImeiReservationMailer, type: :mailer do
  let(:file) { Rack::Test::UploadedFile.new(Rails.root.join('spec/fixtures/automatic_invoice/automatic_invoice.pdf')) }
  let(:dealer) { create(:dealer, :with_warehouse, :gallery_gd_dealer) }
  let(:user) { create(:user, :with_dealer_contact, dealer: dealer) }
  let(:iphone) { build_stubbed(:item, :iphone_dealer_item_instock, serial: "776440993898975") }
  let(:imei_reservation) { build(:imei_reservation, :gallery_gd, :with_shipment_data, item: iphone, user: user, warehouse: dealer.warehouses.first) }
  let(:shipment) { build_stubbed(:shipment, shippable: imei_reservation, tracking_url: 'http://tracking_url') }
  let(:automatic_invoice) { build_stubbed(:automatic_invoice, attachment: file) }

  before do
    allow(imei_reservation).to receive(:shipment).and_return(shipment)
    allow(imei_reservation).to receive(:item).and_return(iphone)
    allow(imei_reservation).to receive(:fattura_cliente_finale).and_return(automatic_invoice)
  end

  context '#confirmed(imei_reservation)' do
    let(:mail) { described_class.confirmed(imei_reservation) }

    it do
      expect(mail.header[:from].value).to eq 'Servizio Clienti Windtre <<EMAIL>>'
      expect(mail.from).to eq ['<EMAIL>']
      expect(mail.subject).to eq "Conferma prenotazione Windtre #{imei_reservation.id}"
      expect(mail.to).to eq [imei_reservation.shipment_email]
    end
  end

  context '#welcome' do
    let(:mail) { described_class.welcome(imei_reservation) }

    it do
      expect(mail.header[:from].value).to eq 'Servizio Clienti Windtre <<EMAIL>>'
      expect(mail.from).to eq ['<EMAIL>']
      expect(mail.subject).to eq "Conferma spedizione Windtre #{imei_reservation.id}"
      expect(mail.to).to eq [imei_reservation.shipment_email]
      expect(mail.html_part.body.encoded).to include('http://tracking_url')
      expect(mail.attachments.count).to eq 1
    end
  end
end