require "rails_helper"

describe AttivaOrder<PERSON>ail<PERSON>, type: :mailer do
  let(:order) { stub_model(Order) }
  let(:response_body) { "Error" }
  let(:gamma_call) { create(:gamma_call) }

  let(:mail) { described_class.submit_order_failed(order, response_body, gamma_call.id).deliver_now }

  it "prints link to gamma call" do
    url = gamma_calls_url(gamma_call)
    expect(mail.body.encoded).to match(url)
  end

  it "has the correct address fields" do
    expect(mail.from).to eq [MailAddressProvider.from]
    expect(mail.to).to eq [MailAddressProvider.amministrazione]
    expect(mail.cc).to eq [MailAddressProvider.it]
  end
end
