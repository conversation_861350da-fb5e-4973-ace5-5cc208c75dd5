require "rails_helper"

describe NumberPortabilityDeta<PERSON><PERSON>elper, type: :helper do
  context "number_rebinding_message_for(phone_activation)" do
    let(:phone_activation_with_mnp) { stub_model(PhoneActivation, plan: stub_model(Plan, mandatory_mnp: true)) }
    let(:phone_activation_without_mnp) { stub_model(PhoneActivation, plan: stub_model(Plan, mandatory_mnp: false)) }

    it { expect(helper.number_rebinding_message_for(phone_activation_with_mnp)).to eq "Fornisci qui il numero di telefono provvisorio della sim Wind assegnata al cliente." }
    it { expect(helper.number_rebinding_message_for(phone_activation_without_mnp)).to eq "Fornisci qui il numero di telefono del cliente a portabilità avvenuta oppure, se senza portabilità, il nuovo numero di telefono assegnato al cliente." }
  end

  context '#number_rebinding_title_for(phone_activation)' do
    let(:tls_plan) { build_stubbed(:plan, tariffa_tls: true) }
    let(:phone_activation) { build_stubbed(:phone_activation) }
    let(:w3) { build_stubbed(:operator, :w3) }

    it do
      expect(helper.number_rebinding_title_for(phone_activation)).to eq 'number_rebinding_title'

      phone_activation.operator = w3
      expect(helper.number_rebinding_title_for(phone_activation)).to eq 'number_rebinding_wind_title'

      phone_activation.plan = tls_plan
      expect(helper.number_rebinding_title_for(phone_activation)).to eq 'number_rebinding_tls_title'
    end
  end
end