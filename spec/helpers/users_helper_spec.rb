require 'rails_helper'

RSpec.describe UsersHelper, type: :helper do
  describe '#login_ai_url' do
    let(:token) { 'abc123token456' }

    before do
      allow(helper).to receive(:cookies).and_return({ 'remember_user_token' => token })
    end

    it 'returns the correct AI assistant URL with the token as query parameter' do
      expected_url = "https://assistant.kolme.it/?token=#{token}"
      expect(helper.login_ai_url).to eq(expected_url)
    end
  end
end