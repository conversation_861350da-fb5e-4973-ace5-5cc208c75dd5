# encoding: utf-8

require "rails_helper"

describe WindNgposActivationsHelper, type: :helper do
  context "#can_mass_update?" do
    let(:params) { { search: { dealer_name: "FM informatica" } } }
    let(:wind_ngpos_activation) { stub_model(WindNgposActivation) }
    let(:wind_ngpos_activations) { [wind_ngpos_activation] }

    before do
      assign(:wind_ngpos_activations, wind_ngpos_activations)
      allow(helper).to receive(:params).and_return(params)
      allow(helper).to receive(:can?).with(:update_warehouses, WindNgposActivation)
        .and_return true
    end

    it "returns true for an enabled user and a search returning something" do
      expect(helper.can_mass_update?).to be_truthy
    end

    context "returns false for a non-enabled user and a search returning something" do
      before do
        allow(helper).to receive(:can?).with(:update_warehouses, WindNgposActivation)
          .and_return false
      end

      it do
        expect(helper.can_mass_update?).to be_falsey
      end
    end

    context "returns false for an enabled user who did not perform a search" do
      let(:params) { { page: 2 } }

      it do
        expect(helper.can_mass_update?).to be_falsey
      end
    end

    context "returns false for an enabled user and a search returning nothing" do
      let(:wind_ngpos_activations) { [] }

      it do
        expect(helper.can_mass_update?).to be_falsey
      end
    end
  end

  context "#ngpos_upload_context_for" do
    let(:activation) { build_stubbed(:wind_ngpos_activation, ngpos_activation_kind: nil) }

    subject { helper.ngpos_upload_context_for(activation) }

    it "returns nil for kindless activation" do
      expect(subject).to be_nil
    end

    context "activation with kind" do
      let(:activation) { build_stubbed(:wind_ngpos_activation, ngpos_activation_kind: activation_kind) }

      context "for non-RTL activation kind" do
        let(:activation_kind) { build_stubbed(:ngpos_activation_kind) }

        it "returns nil" do
          expect(subject).to eq nil
        end
      end

      context "for RTL activation kind" do
        let(:activation_kind) { build_stubbed(:ngpos_activation_kind, :rtl) }

        it "returns 'upload'" do
          expect(subject).to eq "upload"
        end
      end
    end
  end

  context "#product_name" do
    let(:activation) { build_stubbed(:wind_ngpos_activation) }
    let(:product) { build_stubbed(:product, :iphone_5s) }

    it { expect(helper.product_name(activation)).to eq "<span>#{activation.material_code}</span>" }

    context "with product" do
      before { activation.product = product }

      it { expect(helper.product_name(activation)).to eq "<span>#{activation.product_name}</span>" }
    end

    context "with associated_phone_activation" do
      let(:phone_activation) { build_stubbed(:phone_activation, :telefono_incluso_da_magazzino_kolme) }

      before { allow(activation).to receive(:associated_phone_activation).and_return phone_activation }
      before { activation.product = product }

      it { expect(helper.product_name(activation)).to eq "<span>#{activation.product_name}</span>" }

      it "activation has item_not_available flag and product_item is assigned" do
        phone_activation.item_not_available = true

        expect(helper.product_name(activation)).to eq "<span>#{activation.product_name}</span><span class=\"error\"> (Ex &quot;in arrivo&quot;)</span>"
      end
    end
  end
end
