require 'rails_helper'

describe ContractTermsHelper, type: :helper do
  context '#contract_term_kinds_select' do
    let!(:windtre) { create(:operator, :w3) }
    let!(:very) { create(:operator, :very_mobile) }

    it do
      expect(helper.contract_term_kinds_select).to match_array [
                                                            ["Manuale", ""],
                                                            ["Obbligatorio", "required"],
                                                            ["Obbligatorio WINDTRE", "required", { data: { operator_id: windtre.id } }],
                                                            ["Obbligatorio Very Mobile", "required", { data: { operator_id: very.id } }]
                                                          ]
    end
  end

  context '#contract_term_kind_label(contract_term)' do
    let!(:windtre) { build_stubbed(:operator, :w3) }
    let(:contract_term) { build_stubbed(:contract_term) }

    it do
      expect(helper.contract_term_kind_label(contract_term)).to eq 'Manuale'

      contract_term.kind = ContractTerm::REQUIRED_KIND
      expect(helper.contract_term_kind_label(contract_term)).to eq 'Obbligatorio'
    
      contract_term.operator = windtre
      expect(helper.contract_term_kind_label(contract_term)).to eq 'Obbligatorio WINDTRE'
    end
  end

  context '#contract_term_signed_label(signed_contract, contract_term)' do
    let(:dealer) { build_stubbed(:dealer) }
    let(:signed_contract) { build_stubbed(:signed_contract, dealer: dealer) }
    let(:contract_term) { build_stubbed(:contract_term) }
    let(:contract_document) { build_stubbed(:document, :signed_contract_file, created_at: Time.zone.at(Date.current.to_time).to_datetime) }

    it do
      expect(helper.contract_term_signed_label(signed_contract, contract_term)).to be_nil

      allow(dealer).to receive(:all_mdv_affiliation_documents_accepted?).and_return true
      contract_term.md_very_enabled = true
      expect(helper.contract_term_signed_label(signed_contract, contract_term)).to eq ' - Firmato nel processo di affiliazione'

      allow(signed_contract).to receive(:signed?).and_return true
      allow(signed_contract).to receive(:contract).and_return contract_document
      expect(helper.contract_term_signed_label(signed_contract, contract_term)).to eq " - Firmato il #{l(Date.today)} 00:00"
    end
  end
end
