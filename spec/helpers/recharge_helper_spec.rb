require 'rails_helper'

describe Recharges<PERSON>elper, type: :helper do

  context '#available_pins' do
    it { expect(helper.available_pins).to include(['PINDAZN (DAZN)', 'PINDAZN']) }
    it { expect(helper.available_pins).to include(['PIN14 (Fastweb)', 'PIN14']) }
    it { expect(helper.available_pins).to include(['PIN16 (UNOMOBILE)', 'PIN16']) }
    it { expect(helper.available_pins).to include(['PIN13 (DAILY TELECOM)', 'PIN13']) }
    it { expect(helper.available_pins).to include(['PINLNKM (LINKEM)', 'PINLNKM']) }
  end

  describe '#receipt_template_for(recharge)' do
    context 'recharge created before new template switch' do
      before { recharge.created_at = Settings.recharges.receipts.receipts_new_template_from - 1.day }

      context 'recharge paymat or manual' do
        let(:recharge) { build_stubbed(:recharge, :manual, provider: RechargeSize::PROVIDER_MANUAL) }

        it { expect(receipt_template_for(recharge)).to eq 'recharges/receipt_templates/receipt_paymat_old_template' }
      end

      context 'recharge pin' do
        let(:recharge) { build_stubbed(:recharge, :pin_vodafone) }

        it { expect(receipt_template_for(recharge)).to eq 'recharges/receipt_templates/receipt_pin_old_template' }
      end

      context 'recharge euronet' do
        let(:recharge) { build_stubbed(:recharge, :automatic_tre) }

        it { expect(receipt_template_for(recharge)).to eq 'recharges/receipt_templates/receipt_euronet_old_template' }
      end
    end

    context 'recharge created after new template switch' do
      before { recharge.created_at = Settings.recharges.receipts.receipts_new_template_from }
      
      context 'recharge paymat or manual' do
        let(:recharge) { build_stubbed(:recharge, :manual, provider: RechargeSize::PROVIDER_MANUAL) }

        it { expect(receipt_template_for(recharge)).to eq 'recharges/receipt_templates/receipt_paymat' }
      end

      context 'recharge pin' do
        let(:recharge) { build_stubbed(:recharge, :pin_vodafone) }

        it { expect(receipt_template_for(recharge)).to eq 'recharges/receipt_templates/receipt_pin' }
      end

      context 'recharge euronet' do
        let(:recharge) { build_stubbed(:recharge, :automatic_tre) }

        it { expect(receipt_template_for(recharge)).to eq 'recharges/receipt_templates/receipt_euronet' }
      end
    end
  end
end